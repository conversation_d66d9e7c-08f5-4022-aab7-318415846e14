import { useState, useCallback } from 'react';

/**
 * Hook personnalisé pour la gestion standardisée des pages
 * Fournit des fonctionnalités communes : pagination, modals, loading, etc.
 */
export const usePage = () => {
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [showModal, setShowModal] = useState(false);
  const [editingItem, setEditingItem] = useState<any>(null);

  // Gestion des modals
  const openModal = useCallback((item?: any) => {
    setEditingItem(item || null);
    setShowModal(true);
  }, []);

  const closeModal = useCallback(() => {
    setShowModal(false);
    setEditingItem(null);
  }, []);

  // Gestion de la pagination
  const goToPage = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  const nextPage = useCallback((totalPages: number) => {
    setCurrentPage(prev => Math.min(prev + 1, totalPages));
  }, []);

  const prevPage = useCallback(() => {
    setCurrentPage(prev => Math.max(prev - 1, 1));
  }, []);

  // Gestion du loading
  const withLoading = useCallback(async (fn: () => Promise<void>) => {
    setLoading(true);
    try {
      await fn();
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    // État
    loading,
    currentPage,
    showModal,
    editingItem,
    
    // Actions
    setLoading,
    openModal,
    closeModal,
    goToPage,
    nextPage,
    prevPage,
    withLoading,
  };
};

/**
 * Hook pour la gestion des formulaires dans les modals
 */
export const useModalForm = <T extends Record<string, any>>(
  initialData: T,
  onSubmit: (data: T) => Promise<void>,
  onClose: () => void
) => {
  const [formData, setFormData] = useState<T>(initialData);
  const [submitting, setSubmitting] = useState(false);

  const updateFormData = useCallback((updates: Partial<T>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  }, []);

  const resetForm = useCallback(() => {
    setFormData(initialData);
  }, [initialData]);

  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    try {
      await onSubmit(formData);
      onClose();
      resetForm();
    } finally {
      setSubmitting(false);
    }
  }, [formData, onSubmit, onClose, resetForm]);

  return {
    formData,
    submitting,
    updateFormData,
    resetForm,
    handleSubmit,
  };
};

export default usePage;
