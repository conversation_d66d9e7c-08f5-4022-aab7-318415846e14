/* Utilitaires CSS standardisés pour les pages */

/* Layout de page standard */
.page-container {
  @apply space-y-3 sm:space-y-4 lg:space-y-6 w-full;
}

/* En-tête de page responsive */
.page-header {
  @apply flex flex-col xs:flex-row xs:justify-between xs:items-start sm:items-center gap-3 sm:gap-4 w-full;
}

.page-header-content {
  @apply min-w-0 flex-1;
}

.page-header-title {
  @apply text-lg sm:text-xl lg:text-2xl xl:text-3xl font-bold text-gray-900 truncate;
}

.page-header-description {
  @apply text-xs sm:text-sm lg:text-base text-gray-600 mt-1;
}

.page-header-actions {
  @apply flex-shrink-0 w-full xs:w-auto;
}

/* Boutons d'action responsive */
.page-action-btn {
  @apply w-full xs:w-auto text-sm sm:text-base;
}

.page-action-icon {
  @apply w-4 h-4 sm:w-5 sm:h-5 mr-1.5 sm:mr-2;
}

.page-action-text-full {
  @apply hidden xs:inline;
}

.page-action-text-short {
  @apply xs:hidden;
}

/* Cartes de page */
.page-card {
  @apply card overflow-hidden w-full;
}

.page-card-scrollable {
  @apply overflow-x-auto;
}

/* Tables responsives */
.responsive-table {
  @apply min-w-full divide-y divide-gray-200;
}

.responsive-table-header {
  @apply bg-gray-50;
}

.responsive-table-header-cell {
  @apply px-2 xs:px-3 sm:px-4 lg:px-6 py-2 sm:py-3 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider;
}

.responsive-table-cell {
  @apply px-2 xs:px-3 sm:px-4 lg:px-6 py-3 sm:py-4;
}

.responsive-table-row {
  @apply hover:bg-gray-50 transition-colors;
}

/* Modals responsives */
.responsive-modal-overlay {
  @apply fixed inset-0 z-50 overflow-y-auto;
}

.responsive-modal-container {
  @apply flex items-end justify-center min-h-screen pt-2 px-2 pb-20 text-center sm:block sm:p-0;
}

.responsive-modal-backdrop {
  @apply fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity;
}

.responsive-modal-content {
  @apply inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle w-full mx-2 sm:mx-4;
}

.responsive-modal-content-sm {
  @apply max-w-sm xs:max-w-md;
}

.responsive-modal-content-md {
  @apply max-w-sm xs:max-w-md sm:max-w-lg;
}

.responsive-modal-content-lg {
  @apply max-w-sm xs:max-w-md sm:max-w-lg;
}

.responsive-modal-content-xl {
  @apply max-w-sm xs:max-w-md sm:max-w-lg lg:max-w-xl;
}

/* Formulaires dans les modals */
.modal-form-container {
  @apply bg-white px-3 xs:px-4 sm:px-6 pt-4 sm:pt-5 pb-4;
}

.modal-form-title {
  @apply text-base sm:text-lg leading-6 font-medium text-gray-900 mb-3 sm:mb-4;
}

.modal-form-fields {
  @apply space-y-3 sm:space-y-4;
}

.modal-form-field {
  @apply w-full;
}

.modal-form-label {
  @apply block text-xs sm:text-sm font-medium text-gray-700 mb-1;
}

.modal-form-input {
  @apply input-field text-sm;
}

.modal-form-actions {
  @apply bg-gray-50 px-3 xs:px-4 sm:px-6 py-3 flex flex-col-reverse xs:flex-row xs:justify-end gap-2 xs:gap-3;
}

.modal-form-button {
  @apply w-full xs:w-auto text-sm;
}

/* Pagination responsive */
.pagination-container {
  @apply bg-white px-3 sm:px-4 lg:px-6 py-3 flex items-center justify-between border-t border-gray-200;
}

.pagination-mobile {
  @apply flex-1 flex justify-between items-center sm:hidden;
}

.pagination-desktop {
  @apply hidden sm:flex sm:flex-1 sm:items-center sm:justify-between;
}

.pagination-info {
  @apply text-xs sm:text-sm text-gray-700;
}

.pagination-nav {
  @apply relative z-0 inline-flex rounded-md shadow-sm -space-x-px;
}

.pagination-button {
  @apply relative inline-flex items-center px-2 sm:px-3 py-1.5 sm:py-2 border border-gray-300 bg-white text-xs sm:text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 transition-colors;
}

.pagination-button-prev {
  @apply rounded-l-md;
}

.pagination-button-next {
  @apply rounded-r-md;
}

/* Utilitaires de visibilité responsive */
.hide-on-xs {
  @apply hidden xs:block;
}

.hide-on-sm {
  @apply hidden sm:block;
}

.hide-on-md {
  @apply hidden md:block;
}

.hide-on-lg {
  @apply hidden lg:block;
}

.hide-on-xl {
  @apply hidden xl:block;
}

.show-on-xs {
  @apply block xs:hidden;
}

.show-on-sm {
  @apply block sm:hidden;
}

.show-on-md {
  @apply block md:hidden;
}

.show-on-lg {
  @apply block lg:hidden;
}

.show-on-xl {
  @apply block xl:hidden;
}

/* Espacement responsive */
.spacing-xs {
  @apply space-y-2 sm:space-y-3;
}

.spacing-sm {
  @apply space-y-3 sm:space-y-4;
}

.spacing-md {
  @apply space-y-3 sm:space-y-4 lg:space-y-6;
}

.spacing-lg {
  @apply space-y-4 sm:space-y-6 lg:space-y-8;
}

.spacing-xl {
  @apply space-y-6 sm:space-y-8 lg:space-y-10;
}
