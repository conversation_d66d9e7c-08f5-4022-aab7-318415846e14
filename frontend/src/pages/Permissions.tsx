import React, { useState, useEffect } from 'react';
import {
  Box,
  <PERSON>pography,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  Tooltip,
  Chip,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Vpn<PERSON>ey as VpnKeyIcon,
} from '@mui/icons-material';
import { toast } from 'react-toastify';
import apiService from '../services/api';
import { Permission, PermissionCreate } from '../types';

const Permissions: React.FC = () => {
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingPermission, setEditingPermission] = useState<Permission | null>(null);
  const [formData, setFormData] = useState<PermissionCreate>({
    code: '',
    name: '',
    description: '',
  });

  useEffect(() => {
    fetchPermissions();
  }, []);

  const fetchPermissions = async () => {
    try {
      const permissionsData = await apiService.getAllPermissions();
      setPermissions(permissionsData);
      setLoading(false);
    } catch (error) {
      toast.error('Erreur lors du chargement des permissions');
      setLoading(false);
    }
  };

  const handleOpenDialog = (permission?: Permission) => {
    if (permission) {
      setEditingPermission(permission);
      setFormData({
        code: permission.code,
        name: permission.name,
        description: permission.description,
      });
    } else {
      setEditingPermission(null);
      setFormData({
        code: '',
        name: '',
        description: '',
      });
    }
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingPermission(null);
    setFormData({
      code: '',
      name: '',
      description: '',
    });
  };

  const handleSubmit = async () => {
    try {
      if (editingPermission) {
        await apiService.updatePermission(editingPermission.id, formData);
        toast.success('Permission modifiée avec succès');
      } else {
        await apiService.createPermission(formData);
        toast.success('Permission créée avec succès');
      }
      handleCloseDialog();
      fetchPermissions();
    } catch (error) {
      toast.error('Erreur lors de la sauvegarde de la permission');
    }
  };

  const handleDelete = async (permissionId: number) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette permission ?')) {
      try {
        await apiService.deletePermission(permissionId);
        toast.success('Permission supprimée avec succès');
        fetchPermissions();
      } catch (error) {
        toast.error('Erreur lors de la suppression de la permission');
      }
    }
  };

  const getPermissionTypeColor = (code: string) => {
    if (code.includes('create')) return 'success';
    if (code.includes('read')) return 'info';
    if (code.includes('update')) return 'warning';
    if (code.includes('delete')) return 'error';
    return 'default';
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <Typography>Chargement...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <VpnKeyIcon color="primary" />
          <Typography variant="h4" component="h1">
            Gestion des Permissions
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
        >
          Nouvelle Permission
        </Button>
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Code</TableCell>
              <TableCell>Nom</TableCell>
              <TableCell>Description</TableCell>
              <TableCell>Type</TableCell>
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {permissions.map((permission) => (
              <TableRow key={permission.id}>
                <TableCell>
                  <Typography variant="body2" fontFamily="monospace">
                    {permission.code}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="subtitle1" fontWeight="bold">
                    {permission.name}
                  </Typography>
                </TableCell>
                <TableCell>{permission.description}</TableCell>
                <TableCell>
                  <Chip
                    label={permission.code.split(':')[1] || 'other'}
                    size="small"
                    color={getPermissionTypeColor(permission.code) as any}
                    variant="outlined"
                  />
                </TableCell>
                <TableCell align="center">
                  <Tooltip title="Modifier">
                    <IconButton
                      color="primary"
                      onClick={() => handleOpenDialog(permission)}
                    >
                      <EditIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Supprimer">
                    <IconButton
                      color="error"
                      onClick={() => handleDelete(permission.id)}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Tooltip>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Dialog pour créer/modifier une permission */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingPermission ? 'Modifier la permission' : 'Créer une nouvelle permission'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <TextField
              fullWidth
              label="Code de la permission"
              value={formData.code}
              onChange={(e) => setFormData(prev => ({ ...prev, code: e.target.value }))}
              margin="normal"
              required
              placeholder="ex: user:create, role:read"
              helperText="Format recommandé: ressource:action (ex: user:create)"
            />
            <TextField
              fullWidth
              label="Nom de la permission"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              margin="normal"
              required
              placeholder="ex: Créer un utilisateur"
            />
            <TextField
              fullWidth
              label="Description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              margin="normal"
              multiline
              rows={3}
              placeholder="Description détaillée de la permission"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Annuler</Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={!formData.code.trim() || !formData.name.trim()}
          >
            {editingPermission ? 'Modifier' : 'Créer'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Permissions;
