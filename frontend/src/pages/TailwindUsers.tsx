import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import apiService from '../services/api';
import { User, Role, UserCreate } from '../types';

const TailwindUsers: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(true);
  const [showDialog, setShowDialog] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [formData, setFormData] = useState<UserCreate & { full_name: string }>({
    email: '',
    full_name: '',
    password: '',
    role_id: undefined,
    is_active: true,
  });

  useEffect(() => {
    fetchUsers();
    fetchRoles();
  }, [currentPage]); // eslint-disable-line react-hooks/exhaustive-deps

  const fetchUsers = async () => {
    try {
      const response = await apiService.getUsers(currentPage, 10);
      setUsers(response.items);
      setTotalPages(Math.ceil(response.total / 10));
      setLoading(false);
    } catch (error) {
      toast.error('Erreur lors du chargement des utilisateurs');
      setLoading(false);
    }
  };

  const fetchRoles = async () => {
    try {
      const rolesData = await apiService.getAllRoles();
      setRoles(rolesData);
    } catch (error) {
      toast.error('Erreur lors du chargement des rôles');
    }
  };

  const handleOpenDialog = (user?: User) => {
    if (user) {
      setEditingUser(user);
      setFormData({
        email: user.email,
        full_name: user.full_name,
        password: '',
        role_id: user.role?.id,
        is_active: user.is_active,
      });
    } else {
      setEditingUser(null);
      setFormData({
        email: '',
        full_name: '',
        password: '',
        role_id: undefined,
        is_active: true,
      });
    }
    setShowDialog(true);
  };

  const handleCloseDialog = () => {
    setShowDialog(false);
    setEditingUser(null);
    setFormData({
      email: '',
      full_name: '',
      password: '',
      role_id: undefined,
      is_active: true,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      // Convertir full_name en first_name et last_name pour l'API
      const nameParts = formData.full_name.split(' ');
      const apiData: UserCreate = {
        email: formData.email,
        password: formData.password,
        first_name: nameParts[0] || '',
        last_name: nameParts.slice(1).join(' ') || '',
        is_active: formData.is_active,
        role_id: formData.role_id,
      };

      if (editingUser) {
        await apiService.updateUser(editingUser.id, apiData);
        toast.success('Utilisateur modifié avec succès');
      } else {
        await apiService.createUser(apiData);
        toast.success('Utilisateur créé avec succès');
      }
      handleCloseDialog();
      fetchUsers();
    } catch (error) {
      toast.error('Erreur lors de la sauvegarde de l\'utilisateur');
    }
  };

  const handleDelete = async (userId: number) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cet utilisateur ?')) {
      try {
        await apiService.deleteUser(userId);
        toast.success('Utilisateur supprimé avec succès');
        fetchUsers();
      } catch (error) {
        toast.error('Erreur lors de la suppression de l\'utilisateur');
      }
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-3 sm:space-y-4 lg:space-y-6 w-full">
      {/* Header */}
      <div className="flex flex-col xs:flex-row xs:justify-between xs:items-start sm:items-center gap-3 sm:gap-4">
        <div className="min-w-0 flex-1">
          <h1 className="text-lg sm:text-xl lg:text-2xl xl:text-3xl font-bold text-gray-900 truncate">
            Gestion des Utilisateurs
          </h1>
          <p className="text-xs sm:text-sm lg:text-base text-gray-600 mt-1">
            Créez et gérez les comptes utilisateurs
          </p>
        </div>
        <div className="flex-shrink-0 w-full xs:w-auto">
          <button
            onClick={() => handleOpenDialog()}
            className="btn-primary w-full xs:w-auto text-sm sm:text-base"
          >
            <svg className="w-4 h-4 sm:w-5 sm:h-5 mr-1.5 sm:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            <span className="hidden xs:inline">Nouvel Utilisateur</span>
            <span className="xs:hidden">Nouveau</span>
          </button>
        </div>
      </div>

      {/* Users Table */}
      <div className="card overflow-hidden">
        <div className="overflow-x-auto">
          <div className="inline-block min-w-full align-middle">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-2 xs:px-3 sm:px-4 lg:px-6 py-2 sm:py-3 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                    Utilisateur
                  </th>
                  <th className="hidden sm:table-cell px-4 lg:px-6 py-2 sm:py-3 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                    Email
                  </th>
                  <th className="hidden md:table-cell px-4 lg:px-6 py-2 sm:py-3 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                    Rôle
                  </th>
                  <th className="hidden lg:table-cell px-4 lg:px-6 py-2 sm:py-3 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                    Statut
                  </th>
                  <th className="px-2 xs:px-3 sm:px-4 lg:px-6 py-2 sm:py-3 text-right text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {users.map((user) => (
                  <tr key={user.id} className="hover:bg-gray-50 transition-colors">
                    <td className="px-2 xs:px-3 sm:px-4 lg:px-6 py-3 sm:py-4">
                      <div className="flex items-center space-x-2 sm:space-x-3">
                        <div className="w-7 h-7 xs:w-8 xs:h-8 sm:w-10 sm:h-10 bg-primary-600 rounded-full flex items-center justify-center flex-shrink-0">
                          <span className="text-white text-xs sm:text-sm font-medium">
                            {user.full_name.charAt(0)}
                          </span>
                        </div>
                        <div className="min-w-0 flex-1">
                          <div className="text-xs sm:text-sm lg:text-base font-medium text-gray-900 truncate">
                            {user.full_name}
                          </div>
                          <div className="sm:hidden text-xs text-gray-500 truncate mt-0.5">
                            {user.email}
                          </div>
                          <div className="md:hidden text-xs text-gray-500 mt-0.5">
                            {user.role ? (
                              <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                                {user.role.name}
                              </span>
                            ) : (
                              <span className="text-gray-400">Aucun rôle</span>
                            )}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="hidden sm:table-cell px-4 lg:px-6 py-3 sm:py-4">
                      <div className="text-xs sm:text-sm text-gray-900 truncate max-w-xs">
                        {user.email}
                      </div>
                    </td>
                    <td className="hidden md:table-cell px-4 lg:px-6 py-3 sm:py-4">
                      {user.role ? (
                        <span className="inline-flex items-center px-2 sm:px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                          {user.role.name}
                        </span>
                      ) : (
                        <span className="text-gray-400 text-xs sm:text-sm">Aucun rôle</span>
                      )}
                    </td>
                    <td className="hidden lg:table-cell px-4 lg:px-6 py-3 sm:py-4">
                      <span className={`inline-flex items-center px-2 sm:px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        user.is_active
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {user.is_active ? 'Actif' : 'Inactif'}
                      </span>
                    </td>
                    <td className="px-2 xs:px-3 sm:px-4 lg:px-6 py-3 sm:py-4">
                      <div className="flex justify-end space-x-1 sm:space-x-2">
                        <button
                          onClick={() => handleOpenDialog(user)}
                          className="text-primary-600 hover:text-primary-900 p-1 sm:p-1.5 rounded-md hover:bg-primary-50 transition-colors"
                          title="Modifier"
                        >
                          <svg className="w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                        </button>
                        <button
                          onClick={() => handleDelete(user.id)}
                          className="text-red-600 hover:text-red-900 p-1 sm:p-1.5 rounded-md hover:bg-red-50 transition-colors"
                          title="Supprimer"
                        >
                          <svg className="w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="bg-white px-3 sm:px-4 lg:px-6 py-3 flex items-center justify-between border-t border-gray-200">
            {/* Mobile pagination */}
            <div className="flex-1 flex justify-between items-center sm:hidden">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="btn-secondary text-xs px-2 py-1.5 disabled:opacity-50"
              >
                ‹ Préc
              </button>
              <span className="text-xs text-gray-700 px-2">
                {currentPage} / {totalPages}
              </span>
              <button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className="btn-secondary text-xs px-2 py-1.5 disabled:opacity-50"
              >
                Suiv ›
              </button>
            </div>

            {/* Desktop pagination */}
            <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
              <div>
                <p className="text-xs sm:text-sm text-gray-700">
                  Page <span className="font-medium">{currentPage}</span> sur{' '}
                  <span className="font-medium">{totalPages}</span>
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-2 sm:px-3 py-1.5 sm:py-2 rounded-l-md border border-gray-300 bg-white text-xs sm:text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 transition-colors"
                  >
                    <span className="hidden sm:inline">Précédent</span>
                    <span className="sm:hidden">‹</span>
                  </button>
                  <button
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                    className="relative inline-flex items-center px-2 sm:px-3 py-1.5 sm:py-2 rounded-r-md border border-gray-300 bg-white text-xs sm:text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 transition-colors"
                  >
                    <span className="hidden sm:inline">Suivant</span>
                    <span className="sm:hidden">›</span>
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Modal */}
      {showDialog && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-2 px-2 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={handleCloseDialog}></div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle w-full max-w-sm xs:max-w-md sm:max-w-lg mx-2 sm:mx-4">
              <form onSubmit={handleSubmit}>
                <div className="bg-white px-3 xs:px-4 sm:px-6 pt-4 sm:pt-5 pb-4">
                  <div className="w-full">
                    <h3 className="text-base sm:text-lg leading-6 font-medium text-gray-900 mb-3 sm:mb-4">
                      {editingUser ? 'Modifier l\'Utilisateur' : 'Nouvel Utilisateur'}
                    </h3>

                    <div className="space-y-3 sm:space-y-4">
                      <div>
                        <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
                          Nom complet
                        </label>
                        <input
                          type="text"
                          value={formData.full_name}
                          onChange={(e) => setFormData({ ...formData, full_name: e.target.value })}
                          className="input-field text-sm"
                          required
                        />
                      </div>

                      <div>
                        <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
                          Email
                        </label>
                        <input
                          type="email"
                          value={formData.email}
                          onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                          className="input-field text-sm"
                          required
                        />
                      </div>

                      <div>
                        <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
                          {editingUser ? 'Nouveau mot de passe (optionnel)' : 'Mot de passe'}
                        </label>
                        <input
                          type="password"
                          value={formData.password}
                          onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                          className="input-field text-sm"
                          required={!editingUser}
                        />
                      </div>

                      <div>
                        <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
                          Rôle
                        </label>
                        <select
                          value={formData.role_id || ''}
                          onChange={(e) => setFormData({ ...formData, role_id: e.target.value ? parseInt(e.target.value) : undefined })}
                          className="input-field text-sm"
                        >
                          <option value="">Sélectionner un rôle</option>
                          {roles.map((role) => (
                            <option key={role.id} value={role.id}>
                              {role.name}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="is_active"
                          checked={formData.is_active}
                          onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                          className="h-3 w-3 sm:h-4 sm:w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        />
                        <label htmlFor="is_active" className="ml-2 block text-xs sm:text-sm text-gray-900">
                          Utilisateur actif
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="bg-gray-50 px-3 xs:px-4 sm:px-6 py-3 flex flex-col-reverse xs:flex-row xs:justify-end gap-2 xs:gap-3">
                  <button
                    type="button"
                    onClick={handleCloseDialog}
                    className="btn-secondary w-full xs:w-auto text-sm"
                  >
                    Annuler
                  </button>
                  <button
                    type="submit"
                    className="btn-primary w-full xs:w-auto text-sm"
                  >
                    {editingUser ? 'Modifier' : 'Créer'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TailwindUsers;
