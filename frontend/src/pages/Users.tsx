import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,

  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Alert,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridActionsCellItem } from '@mui/x-data-grid';
import { toast } from 'react-toastify';

import { useAuth } from '../contexts/AuthContext';
import apiService from '../services/api';
import { User, UserCreate, UserUpdate, Role, PaginatedResponse } from '../types';

const Users: React.FC = () => {
  const { hasPermission } = useAuth();
  
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(true);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(20);
  const [search, setSearch] = useState('');
  
  // Dialog states
  const [openDialog, setOpenDialog] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [formData, setFormData] = useState<UserCreate>({
    email: '',
    password: '',
    first_name: '',
    last_name: '',
    is_active: true,
    role_id: undefined,
  });
  const [formErrors, setFormErrors] = useState<string>('');

  // Charger les données
  useEffect(() => {
    fetchUsers();
    fetchRoles();
  }, [page, pageSize, search]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response: PaginatedResponse<User> = await apiService.getUsers(
        page + 1, // DataGrid utilise 0-based, API utilise 1-based
        pageSize,
        search || undefined
      );
      setUsers(response.items);
      setTotal(response.total);
    } catch (error) {
      console.error('Erreur lors du chargement des utilisateurs:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchRoles = async () => {
    try {
      const rolesData = await apiService.getAllRoles();
      setRoles(rolesData);
    } catch (error) {
      console.error('Erreur lors du chargement des rôles:', error);
    }
  };

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(event.target.value);
    setPage(0); // Reset to first page when searching
  };

  const handleOpenDialog = (user?: User) => {
    if (user) {
      setEditingUser(user);
      setFormData({
        email: user.email,
        password: '', // Ne pas pré-remplir le mot de passe
        first_name: user.first_name || '',
        last_name: user.last_name || '',
        is_active: user.is_active,
        role_id: user.role_id || undefined,
      });
    } else {
      setEditingUser(null);
      setFormData({
        email: '',
        password: '',
        first_name: '',
        last_name: '',
        is_active: true,
        role_id: undefined,
      });
    }
    setFormErrors('');
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingUser(null);
    setFormErrors('');
  };

  const handleFormChange = (field: keyof UserCreate, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
    setFormErrors('');
  };

  const handleSubmit = async () => {
    try {
      // Validation
      if (!formData.email) {
        setFormErrors('L\'email est requis.');
        return;
      }
      
      if (!editingUser && !formData.password) {
        setFormErrors('Le mot de passe est requis pour un nouvel utilisateur.');
        return;
      }

      if (editingUser) {
        // Mise à jour
        const updateData: UserUpdate = {
          email: formData.email,
          first_name: formData.first_name || undefined,
          last_name: formData.last_name || undefined,
          is_active: formData.is_active,
          role_id: formData.role_id || undefined,
        };
        
        if (formData.password) {
          updateData.password = formData.password;
        }

        await apiService.updateUser(editingUser.id, updateData);
        toast.success('Utilisateur mis à jour avec succès !');
      } else {
        // Création
        await apiService.createUser(formData);
        toast.success('Utilisateur créé avec succès !');
      }

      handleCloseDialog();
      fetchUsers();
    } catch (error: any) {
      setFormErrors(error.response?.data?.detail || 'Une erreur est survenue.');
    }
  };

  const handleDelete = async (userId: number) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cet utilisateur ?')) {
      try {
        await apiService.deleteUser(userId);
        toast.success('Utilisateur supprimé avec succès !');
        fetchUsers();
      } catch (error: any) {
        toast.error(error.response?.data?.detail || 'Erreur lors de la suppression.');
      }
    }
  };

  const columns: GridColDef[] = [
    {
      field: 'id',
      headerName: 'ID',
      width: 70,
    },
    {
      field: 'full_name',
      headerName: 'Nom complet',
      width: 200,
      renderCell: (params) => (
        <Box>
          <Typography variant="body2">{params.row.full_name}</Typography>
          <Typography variant="caption" color="text.secondary">
            {params.row.email}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'role',
      headerName: 'Rôle',
      width: 150,
      renderCell: (params) => (
        <Chip
          label={params.row.role?.name || 'Aucun rôle'}
          size="small"
          color={params.row.role ? 'primary' : 'default'}
          variant="outlined"
        />
      ),
    },
    {
      field: 'is_active',
      headerName: 'Statut',
      width: 120,
      renderCell: (params) => (
        <Chip
          label={params.row.is_active ? 'Actif' : 'Inactif'}
          size="small"
          color={params.row.is_active ? 'success' : 'default'}
        />
      ),
    },
    {
      field: 'created_at',
      headerName: 'Créé le',
      width: 150,
      renderCell: (params) => (
        new Date(params.row.created_at).toLocaleDateString('fr-FR')
      ),
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'Actions',
      width: 120,
      getActions: (params) => {
        const actions = [];
        
        if (hasPermission('user:update')) {
          actions.push(
            <GridActionsCellItem
              icon={<EditIcon />}
              label="Modifier"
              onClick={() => handleOpenDialog(params.row)}
            />
          );
        }
        
        if (hasPermission('user:delete')) {
          actions.push(
            <GridActionsCellItem
              icon={<DeleteIcon />}
              label="Supprimer"
              onClick={() => handleDelete(params.row.id)}
            />
          );
        }
        
        return actions;
      },
    },
  ];

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">Gestion des utilisateurs</Typography>
        
        {hasPermission('user:create') && (
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog()}
          >
            Nouvel utilisateur
          </Button>
        )}
      </Box>

      <Paper sx={{ p: 2, mb: 2 }}>
        <TextField
          fullWidth
          placeholder="Rechercher par email, prénom ou nom..."
          value={search}
          onChange={handleSearch}
          InputProps={{
            startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
          }}
        />
      </Paper>

      <Paper sx={{ height: 600, width: '100%' }}>
        <DataGrid
          rows={users}
          columns={columns}
          loading={loading}
          paginationModel={{ page, pageSize }}
          onPaginationModelChange={(model) => {
            setPage(model.page);
            setPageSize(model.pageSize);
          }}
          rowCount={total}
          paginationMode="server"
          pageSizeOptions={[10, 20, 50]}
          disableRowSelectionOnClick
        />
      </Paper>

      {/* Dialog de création/modification */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingUser ? 'Modifier l\'utilisateur' : 'Nouvel utilisateur'}
        </DialogTitle>
        
        <DialogContent>
          {formErrors && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {formErrors}
            </Alert>
          )}
          
          <TextField
            fullWidth
            label="Email"
            type="email"
            value={formData.email}
            onChange={(e) => handleFormChange('email', e.target.value)}
            margin="normal"
            required
          />
          
          <TextField
            fullWidth
            label={editingUser ? 'Nouveau mot de passe (optionnel)' : 'Mot de passe'}
            type="password"
            value={formData.password}
            onChange={(e) => handleFormChange('password', e.target.value)}
            margin="normal"
            required={!editingUser}
          />
          
          <TextField
            fullWidth
            label="Prénom"
            value={formData.first_name}
            onChange={(e) => handleFormChange('first_name', e.target.value)}
            margin="normal"
          />
          
          <TextField
            fullWidth
            label="Nom de famille"
            value={formData.last_name}
            onChange={(e) => handleFormChange('last_name', e.target.value)}
            margin="normal"
          />
          
          <FormControl fullWidth margin="normal">
            <InputLabel>Rôle</InputLabel>
            <Select
              value={formData.role_id || ''}
              onChange={(e) => handleFormChange('role_id', e.target.value || undefined)}
            >
              <MenuItem value="">Aucun rôle</MenuItem>
              {roles.map((role) => (
                <MenuItem key={role.id} value={role.id}>
                  {role.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          
          <FormControlLabel
            control={
              <Switch
                checked={formData.is_active}
                onChange={(e) => handleFormChange('is_active', e.target.checked)}
              />
            }
            label="Utilisateur actif"
            sx={{ mt: 2 }}
          />
        </DialogContent>
        
        <DialogActions>
          <Button onClick={handleCloseDialog}>Annuler</Button>
          <Button onClick={handleSubmit} variant="contained">
            {editingUser ? 'Mettre à jour' : 'Créer'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Users;
