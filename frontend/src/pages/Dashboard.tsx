import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Chip,
} from '@mui/material';
import {
  People as PeopleIcon,
  Security as SecurityIcon,
  VpnKey as VpnKeyIcon,
  TrendingUp as TrendingUpIcon,
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import apiService from '../services/api';
import { User } from '../types';

interface DashboardStats {
  totalUsers: number;
  totalRoles: number;
  totalPermissions: number;
  recentUsers: User[];
}

const Dashboard: React.FC = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    totalRoles: 0,
    totalPermissions: 0,
    recentUsers: [],
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        
        // Récupérer les statistiques en parallèle
        const [usersResponse, rolesResponse, permissionsResponse] = await Promise.all([
          apiService.getUsers(1, 5), // Récupérer les 5 premiers utilisateurs
          apiService.getAllRoles(),
          apiService.getAllPermissions(),
        ]);

        setStats({
          totalUsers: usersResponse.total,
          totalRoles: rolesResponse.length,
          totalPermissions: permissionsResponse.length,
          recentUsers: usersResponse.items,
        });
      } catch (error) {
        console.error('Erreur lors du chargement des données du dashboard:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  const StatCard: React.FC<{
    title: string;
    value: number;
    icon: React.ReactElement;
    color: string;
  }> = ({ title, value, icon, color }) => (
    <Card>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box>
            <Typography color="textSecondary" gutterBottom variant="body2">
              {title}
            </Typography>
            <Typography variant="h4" component="h2">
              {loading ? '...' : value}
            </Typography>
          </Box>
          <Avatar sx={{ bgcolor: color, width: 56, height: 56 }}>
            {icon}
          </Avatar>
        </Box>
      </CardContent>
    </Card>
  );

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Tableau de bord
      </Typography>
      
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Bienvenue {user?.full_name} ! Voici un aperçu de votre système de gestion des utilisateurs.
      </Typography>

      {/* Statistiques */}
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3, mb: 4 }}>
        <Box sx={{ flex: '1 1 250px' }}>
          <StatCard
            title="Utilisateurs"
            value={stats.totalUsers}
            icon={<PeopleIcon />}
            color="#1976d2"
          />
        </Box>

        <Box sx={{ flex: '1 1 250px' }}>
          <StatCard
            title="Rôles"
            value={stats.totalRoles}
            icon={<SecurityIcon />}
            color="#388e3c"
          />
        </Box>

        <Box sx={{ flex: '1 1 250px' }}>
          <StatCard
            title="Permissions"
            value={stats.totalPermissions}
            icon={<VpnKeyIcon />}
            color="#f57c00"
          />
        </Box>

        <Box sx={{ flex: '1 1 250px' }}>
          <StatCard
            title="Activité"
            value={100}
            icon={<TrendingUpIcon />}
            color="#7b1fa2"
          />
        </Box>
      </Box>

      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
        {/* Utilisateurs récents */}
        <Box sx={{ flex: '1 1 400px' }}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Utilisateurs récents
            </Typography>
            
            {loading ? (
              <Typography>Chargement...</Typography>
            ) : (
              <List>
                {stats.recentUsers.map((recentUser) => (
                  <ListItem key={recentUser.id}>
                    <ListItemAvatar>
                      <Avatar>
                        {recentUser.full_name.charAt(0).toUpperCase()}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={recentUser.full_name}
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            {recentUser.email}
                          </Typography>
                          <Box sx={{ mt: 0.5 }}>
                            <Chip
                              label={recentUser.role?.name || 'Aucun rôle'}
                              size="small"
                              color={recentUser.is_active ? 'success' : 'default'}
                              variant="outlined"
                            />
                          </Box>
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            )}
          </Paper>
        </Box>

        {/* Informations utilisateur */}
        <Box sx={{ flex: '1 1 400px' }}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Votre profil
            </Typography>
            
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Nom complet
              </Typography>
              <Typography variant="body1" sx={{ mb: 2 }}>
                {user?.full_name}
              </Typography>

              <Typography variant="body2" color="text.secondary">
                Email
              </Typography>
              <Typography variant="body1" sx={{ mb: 2 }}>
                {user?.email}
              </Typography>

              <Typography variant="body2" color="text.secondary">
                Rôle
              </Typography>
              <Chip
                label={user?.role?.name || 'Aucun rôle'}
                color="primary"
                sx={{ mb: 2 }}
              />

              <Typography variant="body2" color="text.secondary">
                Statut
              </Typography>
              <Chip
                label={user?.is_active ? 'Actif' : 'Inactif'}
                color={user?.is_active ? 'success' : 'default'}
                sx={{ mb: 2 }}
              />

              {user?.is_superuser && (
                <>
                  <Typography variant="body2" color="text.secondary">
                    Privilèges
                  </Typography>
                  <Chip
                    label="Super Administrateur"
                    color="error"
                    sx={{ mb: 2 }}
                  />
                </>
              )}

              <Typography variant="body2" color="text.secondary">
                Permissions ({user?.permissions.length || 0})
              </Typography>
              <Box sx={{ mt: 1 }}>
                {user?.permissions.slice(0, 5).map((permission) => (
                  <Chip
                    key={permission}
                    label={permission}
                    size="small"
                    variant="outlined"
                    sx={{ mr: 0.5, mb: 0.5 }}
                  />
                ))}
                {user && user.permissions.length > 5 && (
                  <Chip
                    label={`+${user.permissions.length - 5} autres`}
                    size="small"
                    variant="outlined"
                    sx={{ mr: 0.5, mb: 0.5 }}
                  />
                )}
              </Box>
            </Box>
          </Paper>
        </Box>
      </Box>
    </Box>
  );
};

export default Dashboard;
