import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import apiService from '../services/api';
import { Permission, PermissionCreate } from '../types';

const TailwindPermissions: React.FC = () => {
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(true);
  const [showDialog, setShowDialog] = useState(false);
  const [editingPermission, setEditingPermission] = useState<Permission | null>(null);
  const [formData, setFormData] = useState<PermissionCreate>({
    code: '',
    name: '',
    description: '',
  });

  useEffect(() => {
    fetchPermissions();
  }, []);

  const fetchPermissions = async () => {
    try {
      const permissionsData = await apiService.getAllPermissions();
      setPermissions(permissionsData);
      setLoading(false);
    } catch (error) {
      toast.error('Erreur lors du chargement des permissions');
      setLoading(false);
    }
  };

  const handleOpenDialog = (permission?: Permission) => {
    if (permission) {
      setEditingPermission(permission);
      setFormData({
        code: permission.code,
        name: permission.name,
        description: permission.description || '',
      });
    } else {
      setEditingPermission(null);
      setFormData({
        code: '',
        name: '',
        description: '',
      });
    }
    setShowDialog(true);
  };

  const handleCloseDialog = () => {
    setShowDialog(false);
    setEditingPermission(null);
    setFormData({
      code: '',
      name: '',
      description: '',
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      if (editingPermission) {
        await apiService.updatePermission(editingPermission.id, formData);
        toast.success('Permission modifiée avec succès');
      } else {
        await apiService.createPermission(formData);
        toast.success('Permission créée avec succès');
      }
      handleCloseDialog();
      fetchPermissions();
    } catch (error) {
      toast.error('Erreur lors de la sauvegarde de la permission');
    }
  };

  const handleDelete = async (permissionId: number) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette permission ?')) {
      try {
        await apiService.deletePermission(permissionId);
        toast.success('Permission supprimée avec succès');
        fetchPermissions();
      } catch (error) {
        toast.error('Erreur lors de la suppression de la permission');
      }
    }
  };

  const getPermissionTypeColor = (code: string) => {
    if (code.includes('create')) return 'bg-green-100 text-green-800';
    if (code.includes('read')) return 'bg-blue-100 text-blue-800';
    if (code.includes('update')) return 'bg-yellow-100 text-yellow-800';
    if (code.includes('delete')) return 'bg-red-100 text-red-800';
    return 'bg-gray-100 text-gray-800';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-3 sm:space-y-4 lg:space-y-6 w-full">
      {/* Header */}
      <div className="flex flex-col xs:flex-row xs:justify-between xs:items-start sm:items-center gap-3 sm:gap-4 w-full">
        <div className="min-w-0 flex-1">
          <h1 className="text-lg sm:text-xl lg:text-2xl xl:text-3xl font-bold text-gray-900 truncate">
            Gestion des Permissions
          </h1>
          <p className="text-xs sm:text-sm lg:text-base text-gray-600 mt-1">
            Créez et gérez les permissions du système
          </p>
        </div>
        <div className="flex-shrink-0 w-full xs:w-auto">
          <button
            onClick={() => handleOpenDialog()}
            className="btn-primary w-full xs:w-auto text-sm sm:text-base"
          >
            <svg className="w-4 h-4 sm:w-5 sm:h-5 mr-1.5 sm:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            <span className="hidden xs:inline">Nouvelle Permission</span>
            <span className="xs:hidden">Nouveau</span>
          </button>
        </div>
      </div>

      {/* Permissions Table */}
      <div className="card overflow-hidden w-full">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-2 xs:px-3 sm:px-4 lg:px-6 py-2 sm:py-3 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                  Permission
                </th>
                <th className="hidden sm:table-cell px-4 lg:px-6 py-2 sm:py-3 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                  Code
                </th>
                <th className="hidden md:table-cell px-4 lg:px-6 py-2 sm:py-3 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                  Description
                </th>
                <th className="px-2 xs:px-3 sm:px-4 lg:px-6 py-2 sm:py-3 text-right text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {permissions.map((permission) => (
                <tr key={permission.id} className="hover:bg-gray-50 transition-colors">
                  <td className="px-2 xs:px-3 sm:px-4 lg:px-6 py-3 sm:py-4">
                    <div className="flex-1 min-w-0">
                      <div className="text-xs sm:text-sm lg:text-base font-medium text-gray-900 truncate">
                        {permission.name}
                      </div>
                      {/* Affichage mobile du code */}
                      <div className="sm:hidden mt-0.5">
                        <span className={`inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium ${getPermissionTypeColor(permission.code)}`}>
                          {permission.code}
                        </span>
                      </div>
                      {/* Affichage mobile de la description */}
                      <div className="md:hidden text-xs text-gray-500 truncate mt-0.5">
                        {permission.description}
                      </div>
                    </div>
                  </td>
                  <td className="hidden sm:table-cell px-4 lg:px-6 py-3 sm:py-4">
                    <span className={`inline-flex items-center px-2 sm:px-2.5 py-0.5 rounded-full text-xs font-medium ${getPermissionTypeColor(permission.code)}`}>
                      {permission.code}
                    </span>
                  </td>
                  <td className="hidden md:table-cell px-4 lg:px-6 py-3 sm:py-4">
                    <div className="text-xs sm:text-sm text-gray-500 truncate max-w-xs">
                      {permission.description}
                    </div>
                  </td>
                  <td className="px-2 xs:px-3 sm:px-4 lg:px-6 py-3 sm:py-4">
                    <div className="flex justify-end space-x-1 sm:space-x-2">
                      <button
                        onClick={() => handleOpenDialog(permission)}
                        className="text-primary-600 hover:text-primary-900 p-1 sm:p-1.5 rounded-md hover:bg-primary-50 transition-colors"
                        title="Modifier"
                      >
                        <svg className="w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      </button>
                      <button
                        onClick={() => handleDelete(permission.id)}
                        className="text-red-600 hover:text-red-900 p-1 sm:p-1.5 rounded-md hover:bg-red-50 transition-colors"
                        title="Supprimer"
                      >
                        <svg className="w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Modal */}
      {showDialog && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={handleCloseDialog}></div>
            
            <span className="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>
            
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <form onSubmit={handleSubmit}>
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <div className="sm:flex sm:items-start">
                    <div className="w-full">
                      <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                        {editingPermission ? 'Modifier la Permission' : 'Nouvelle Permission'}
                      </h3>
                      
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Code de la permission
                          </label>
                          <input
                            type="text"
                            value={formData.code}
                            onChange={(e) => setFormData({ ...formData, code: e.target.value })}
                            className="input-field"
                            placeholder="ex: user:create, role:read"
                            required
                          />
                          <p className="text-xs text-gray-500 mt-1">
                            Format recommandé: ressource:action (ex: user:create, role:read)
                          </p>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Nom de la permission
                          </label>
                          <input
                            type="text"
                            value={formData.name}
                            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                            className="input-field"
                            placeholder="ex: Créer un utilisateur"
                            required
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Description
                          </label>
                          <textarea
                            value={formData.description}
                            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                            className="input-field"
                            rows={3}
                            placeholder="Description détaillée de la permission"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                  <button
                    type="submit"
                    className="btn-primary w-full sm:w-auto sm:ml-3"
                  >
                    {editingPermission ? 'Modifier' : 'Créer'}
                  </button>
                  <button
                    type="button"
                    onClick={handleCloseDialog}
                    className="btn-secondary w-full sm:w-auto mt-3 sm:mt-0"
                  >
                    Annuler
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TailwindPermissions;
