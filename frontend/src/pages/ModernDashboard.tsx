import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import apiService from '../services/api';
import { toast } from 'react-toastify';
import {
  Card,
  Badge,
  <PERSON>tar,
  <PERSON><PERSON>,
  Spinner,
} from 'flowbite-react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  HiShieldCheck,
  Hi<PERSON>ey,
  HiTrendingUp,
  <PERSON>Eye,
  HiUser<PERSON>dd,
  Hi<PERSON>lock,
} from 'react-icons/hi';

interface Stats {
  totalUsers: number;
  totalRoles: number;
  totalPermissions: number;
  activeUsers: number;
}

interface User {
  id: number;
  email: string;
  full_name: string;
  role: string;
  is_active: boolean;
  created_at: string;
}

const ModernDashboard: React.FC = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState<Stats>({
    totalUsers: 0,
    totalRoles: 0,
    totalPermissions: 0,
    activeUsers: 0,
  });
  const [recentUsers, setRecentUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const [usersData, rolesData, permissionsData] = await Promise.all([
        apiService.getUsers(1, 5),
        apiService.getAllRoles(),
        apiService.getAllPermissions(),
      ]);

      setStats({
        totalUsers: usersData.total,
        totalRoles: rolesData.length,
        totalPermissions: permissionsData.length,
        activeUsers: usersData.items.filter((u: User) => u.is_active).length,
      });

      setRecentUsers(usersData.items.slice(0, 5));
    } catch (error) {
      toast.error('Erreur lors du chargement des données');
    } finally {
      setLoading(false);
    }
  };

  const statCards = [
    {
      title: 'Utilisateurs',
      value: stats.totalUsers,
      icon: HiUsers,
      color: 'blue',
      bgColor: 'bg-blue-100',
      iconColor: 'text-blue-600',
    },
    {
      title: 'Rôles',
      value: stats.totalRoles,
      icon: HiShieldCheck,
      color: 'green',
      bgColor: 'bg-green-100',
      iconColor: 'text-green-600',
    },
    {
      title: 'Permissions',
      value: stats.totalPermissions,
      icon: HiKey,
      color: 'yellow',
      bgColor: 'bg-yellow-100',
      iconColor: 'text-yellow-600',
    },
    {
      title: 'Utilisateurs Actifs',
      value: stats.activeUsers,
      icon: HiTrendingUp,
      color: 'purple',
      bgColor: 'bg-purple-100',
      iconColor: 'text-purple-600',
    },
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spinner size="xl" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-lg p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold mb-2">
              Bienvenue, {user?.full_name} !
            </h1>
            <p className="text-primary-100">
              Voici un aperçu de votre système de gestion des utilisateurs
            </p>
          </div>
          <div className="hidden md:block">
            <Avatar
              img={`https://ui-avatars.com/api/?name=${encodeURIComponent(user?.full_name || 'User')}&background=ffffff&color=3b82f6&size=64`}
              size="lg"
              rounded
            />
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <Card key={index} className="hover:shadow-lg transition-shadow duration-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500 mb-1">
                    {stat.title}
                  </p>
                  <p className="text-3xl font-bold text-gray-900">
                    {stat.value}
                  </p>
                </div>
                <div className={`p-3 rounded-full ${stat.bgColor}`}>
                  <Icon className={`w-6 h-6 ${stat.iconColor}`} />
                </div>
              </div>
            </Card>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Users */}
        <Card>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">
              Utilisateurs Récents
            </h3>
            <Button size="xs" color="gray">
              <HiEye className="w-4 h-4 mr-1" />
              Voir tout
            </Button>
          </div>
          
          <div className="space-y-3">
            {recentUsers.length > 0 ? (
              recentUsers.map((recentUser) => (
                <div key={recentUser.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Avatar
                      img={`https://ui-avatars.com/api/?name=${encodeURIComponent(recentUser.full_name)}&background=3b82f6&color=fff`}
                      size="sm"
                      rounded
                    />
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {recentUser.full_name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {recentUser.email}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge color={recentUser.is_active ? 'success' : 'failure'} size="sm">
                      {recentUser.is_active ? 'Actif' : 'Inactif'}
                    </Badge>
                    <p className="text-xs text-gray-500 mt-1">
                      {recentUser.role}
                    </p>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8 text-gray-500">
                <HiUsers className="w-12 h-12 mx-auto mb-2 text-gray-300" />
                <p>Aucun utilisateur trouvé</p>
              </div>
            )}
          </div>
        </Card>

        {/* Quick Actions */}
        <Card>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Actions Rapides
          </h3>
          
          <div className="space-y-3">
            <Button className="w-full justify-start" color="blue">
              <HiUserAdd className="w-5 h-5 mr-2" />
              Créer un nouvel utilisateur
            </Button>
            
            <Button className="w-full justify-start" color="green">
              <HiShieldCheck className="w-5 h-5 mr-2" />
              Gérer les rôles
            </Button>
            
            <Button className="w-full justify-start" color="yellow">
              <HiKey className="w-5 h-5 mr-2" />
              Configurer les permissions
            </Button>
            
            <Button className="w-full justify-start" color="purple">
              <HiClock className="w-5 h-5 mr-2" />
              Voir l'activité récente
            </Button>
          </div>
        </Card>
      </div>

      {/* System Info */}
      <Card>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Informations Système
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-500">Version</p>
            <p className="text-lg font-semibold text-gray-900">v1.0.0</p>
          </div>
          
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-500">Base de données</p>
            <p className="text-lg font-semibold text-gray-900">MySQL</p>
          </div>
          
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-500">Statut</p>
            <Badge color="success" size="sm">
              Opérationnel
            </Badge>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default ModernDashboard;
