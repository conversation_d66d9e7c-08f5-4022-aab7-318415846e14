import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { toast } from 'react-toastify';
import {
  Card,
  Label,
  TextInput,
  <PERSON><PERSON>,
  Spinner,
  <PERSON>ert,
} from 'flowbite-react';
import {
  <PERSON><PERSON>ail,
  <PERSON><PERSON>ock<PERSON>losed,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ye<PERSON>ff,
  HiShieldCheck,
} from 'react-icons/hi';

const ModernLogin: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  const { login } = useAuth();
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      await login(email, password);
      toast.success('Connexion réussie !');
      navigate('/dashboard');
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || 'Erreur de connexion';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-primary-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Logo et titre */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-primary-600 rounded-full mb-4">
            <HiShieldCheck className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Gestion Utilisateurs
          </h1>
          <p className="text-gray-600">
            Connectez-vous à votre compte administrateur
          </p>
        </div>

        {/* Formulaire de connexion */}
        <Card className="shadow-xl border-0">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="text-center">
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                Connexion
              </h2>
              <p className="text-sm text-gray-500">
                Entrez vos identifiants pour accéder au panel d'administration
              </p>
            </div>

            {error && (
              <Alert color="failure" className="mb-4">
                <span className="font-medium">Erreur !</span> {error}
              </Alert>
            )}

            <div>
              <div className="mb-2 block">
                <Label htmlFor="email" value="Adresse email" />
              </div>
              <TextInput
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                icon={HiMail}
                required
                className="w-full"
                sizing="lg"
              />
            </div>

            <div>
              <div className="mb-2 block">
                <Label htmlFor="password" value="Mot de passe" />
              </div>
              <div className="relative">
                <TextInput
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="••••••••"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  icon={HiLockClosed}
                  required
                  className="w-full"
                  sizing="lg"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? (
                    <HiEyeOff className="w-5 h-5" />
                  ) : (
                    <HiEye className="w-5 h-5" />
                  )}
                </button>
              </div>
            </div>

            <Button
              type="submit"
              disabled={loading}
              className="w-full"
              size="lg"
              gradientDuoTone="purpleToBlue"
            >
              {loading ? (
                <>
                  <Spinner size="sm" light className="mr-2" />
                  Connexion en cours...
                </>
              ) : (
                'Se connecter'
              )}
            </Button>
          </form>
        </Card>

        {/* Informations de test */}
        <Card className="mt-6 bg-blue-50 border-blue-200">
          <div className="text-center">
            <h3 className="text-sm font-medium text-blue-900 mb-2">
              Compte de démonstration
            </h3>
            <div className="text-xs text-blue-700 space-y-1">
              <p><strong>Email :</strong> <EMAIL></p>
              <p><strong>Mot de passe :</strong> admin123</p>
            </div>
          </div>
        </Card>

        {/* Footer */}
        <div className="text-center mt-8">
          <p className="text-xs text-gray-500">
            © 2024 Gestion Utilisateurs. Tous droits réservés.
          </p>
        </div>
      </div>
    </div>
  );
};

export default ModernLogin;
