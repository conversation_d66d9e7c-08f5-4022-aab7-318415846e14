import React from 'react';

interface PageHeaderProps {
  title: string;
  description?: string;
  action?: React.ReactNode;
}

interface PageLayoutProps {
  children: React.ReactNode;
  header?: PageHeaderProps;
  className?: string;
}

/**
 * Composant de layout standardisé pour toutes les pages
 * Garantit une largeur complète et une responsivité cohérente
 */
export const PageLayout: React.FC<PageLayoutProps> = ({ 
  children, 
  header, 
  className = "" 
}) => {
  return (
    <div className={`space-y-3 sm:space-y-4 lg:space-y-6 w-full ${className}`}>
      {header && <PageHeader {...header} />}
      {children}
    </div>
  );
};

/**
 * Composant d'en-tête standardisé pour les pages
 * Responsive avec titre, description et action optionnelle
 */
export const PageHeader: React.FC<PageHeaderProps> = ({ 
  title, 
  description, 
  action 
}) => {
  return (
    <div className="flex flex-col xs:flex-row xs:justify-between xs:items-start sm:items-center gap-3 sm:gap-4 w-full">
      <div className="min-w-0 flex-1">
        <h1 className="text-lg sm:text-xl lg:text-2xl xl:text-3xl font-bold text-gray-900 truncate">
          {title}
        </h1>
        {description && (
          <p className="text-xs sm:text-sm lg:text-base text-gray-600 mt-1">
            {description}
          </p>
        )}
      </div>
      {action && (
        <div className="flex-shrink-0 w-full xs:w-auto">
          {action}
        </div>
      )}
    </div>
  );
};

/**
 * Composant de carte standardisé
 * Garantit une largeur complète et un overflow correct
 */
export const PageCard: React.FC<{ 
  children: React.ReactNode; 
  className?: string;
  overflow?: boolean;
}> = ({ 
  children, 
  className = "", 
  overflow = true 
}) => {
  return (
    <div className={`card ${overflow ? 'overflow-hidden' : ''} w-full ${className}`}>
      {overflow ? (
        <div className="overflow-x-auto">
          {children}
        </div>
      ) : (
        children
      )}
    </div>
  );
};

/**
 * Bouton d'action standardisé pour les en-têtes
 * Responsive avec texte adaptatif
 */
export const PageActionButton: React.FC<{
  onClick: () => void;
  icon?: React.ReactNode;
  fullText: string;
  shortText: string;
  variant?: 'primary' | 'secondary';
  className?: string;
}> = ({ 
  onClick, 
  icon, 
  fullText, 
  shortText, 
  variant = 'primary',
  className = "" 
}) => {
  const baseClasses = variant === 'primary' ? 'btn-primary' : 'btn-secondary';
  
  return (
    <button
      onClick={onClick}
      className={`${baseClasses} w-full xs:w-auto text-sm sm:text-base ${className}`}
    >
      {icon && (
        <span className="w-4 h-4 sm:w-5 sm:h-5 mr-1.5 sm:mr-2">
          {icon}
        </span>
      )}
      <span className="hidden xs:inline">{fullText}</span>
      <span className="xs:hidden">{shortText}</span>
    </button>
  );
};

/**
 * Table responsive standardisée
 * Avec colonnes qui se cachent progressivement
 */
export const ResponsiveTable: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className = "" }) => {
  return (
    <div className="inline-block min-w-full align-middle">
      <table className={`min-w-full divide-y divide-gray-200 ${className}`}>
        {children}
      </table>
    </div>
  );
};

/**
 * En-tête de table responsive
 */
export const ResponsiveTableHeader: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  return (
    <thead className="bg-gray-50">
      <tr>
        {children}
      </tr>
    </thead>
  );
};

/**
 * Cellule d'en-tête responsive
 */
export const ResponsiveTableHeaderCell: React.FC<{
  children: React.ReactNode;
  className?: string;
  hideOn?: 'sm' | 'md' | 'lg' | 'xl';
}> = ({ children, className = "", hideOn }) => {
  const hideClass = hideOn ? `hidden ${hideOn}:table-cell` : '';
  
  return (
    <th className={`px-2 xs:px-3 sm:px-4 lg:px-6 py-2 sm:py-3 text-left text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wider ${hideClass} ${className}`}>
      {children}
    </th>
  );
};

/**
 * Cellule de table responsive
 */
export const ResponsiveTableCell: React.FC<{
  children: React.ReactNode;
  className?: string;
  hideOn?: 'sm' | 'md' | 'lg' | 'xl';
}> = ({ children, className = "", hideOn }) => {
  const hideClass = hideOn ? `hidden ${hideOn}:table-cell` : '';
  
  return (
    <td className={`px-2 xs:px-3 sm:px-4 lg:px-6 py-3 sm:py-4 ${hideClass} ${className}`}>
      {children}
    </td>
  );
};

/**
 * Modal responsive standardisé
 */
export const ResponsiveModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl';
}> = ({ isOpen, onClose, children, maxWidth = 'lg' }) => {
  if (!isOpen) return null;

  const maxWidthClasses = {
    sm: 'max-w-sm xs:max-w-md',
    md: 'max-w-sm xs:max-w-md sm:max-w-lg',
    lg: 'max-w-sm xs:max-w-md sm:max-w-lg',
    xl: 'max-w-sm xs:max-w-md sm:max-w-lg lg:max-w-xl'
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-end justify-center min-h-screen pt-2 px-2 pb-20 text-center sm:block sm:p-0">
        <div 
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" 
          onClick={onClose}
        />
        
        <span className="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>
        
        <div className={`inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle w-full ${maxWidthClasses[maxWidth]} mx-2 sm:mx-4`}>
          {children}
        </div>
      </div>
    </div>
  );
};

export default PageLayout;
