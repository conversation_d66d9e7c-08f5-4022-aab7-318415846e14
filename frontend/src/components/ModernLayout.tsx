import React, { useState } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import {
  Sidebar,
  Navbar,
  Avatar,
  Dropdown,
  Button,
} from 'flowbite-react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  HiShieldCheck,
  HiKey,
  HiLogout,
  HiMenu,
  HiX,
} from 'react-icons/hi';

const ModernLayout: React.FC = () => {
  const { user, logout, hasPermission } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const menuItems = [
    {
      title: 'Dashboard',
      path: '/dashboard',
      icon: HiChartPie,
      permission: 'dashboard:access',
    },
    {
      title: 'Utilisateurs',
      path: '/users',
      icon: HiUsers,
      permission: 'user:read',
    },
    {
      title: 'R<PERSON><PERSON>',
      path: '/roles',
      icon: HiShi<PERSON><PERSON><PERSON><PERSON>,
      permission: 'role:read',
    },
    {
      title: 'Permissions',
      path: '/permissions',
      icon: <PERSON><PERSON><PERSON>,
      permission: 'permission:read',
    },
  ];

  const filteredMenuItems = menuItems.filter(item => 
    hasPermission(item.permission)
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile menu button */}
      <div className="lg:hidden">
        <div className="flex items-center justify-between p-4 bg-white border-b border-gray-200">
          <h1 className="text-xl font-semibold text-gray-900">
            Gestion Utilisateurs
          </h1>
          <Button
            color="gray"
            size="sm"
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="lg:hidden"
          >
            {sidebarOpen ? <HiX className="w-5 h-5" /> : <HiMenu className="w-5 h-5" />}
          </Button>
        </div>
      </div>

      <div className="flex">
        {/* Sidebar */}
        <div className={`
          fixed inset-y-0 left-0 z-50 w-64 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0
          ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
        `}>
          <div className="flex flex-col h-full">
            {/* Logo */}
            <div className="flex items-center justify-center h-16 px-4 bg-primary-600">
              <h1 className="text-xl font-bold text-white">
                Admin Panel
              </h1>
            </div>

            {/* Navigation */}
            <nav className="flex-1 px-4 py-6 space-y-2">
              {filteredMenuItems.map((item) => {
                const Icon = item.icon;
                const isActive = location.pathname === item.path;
                
                return (
                  <button
                    key={item.path}
                    onClick={() => {
                      navigate(item.path);
                      setSidebarOpen(false);
                    }}
                    className={`
                      w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200
                      ${isActive 
                        ? 'bg-primary-100 text-primary-700 border-r-2 border-primary-600' 
                        : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                      }
                    `}
                  >
                    <Icon className="w-5 h-5 mr-3" />
                    {item.title}
                  </button>
                );
              })}
            </nav>

            {/* User section */}
            <div className="p-4 border-t border-gray-200">
              <div className="flex items-center space-x-3">
                <Avatar
                  img={`https://ui-avatars.com/api/?name=${encodeURIComponent(user?.full_name || 'User')}&background=3b82f6&color=fff`}
                  rounded
                  size="sm"
                />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {user?.full_name}
                  </p>
                  <p className="text-xs text-gray-500 truncate">
                    {user?.email}
                  </p>
                </div>
                <Dropdown
                  arrowIcon={false}
                  inline
                  label={
                    <Button color="gray" size="xs" className="p-1">
                      <HiLogout className="w-4 h-4" />
                    </Button>
                  }
                >
                  <Dropdown.Item onClick={handleLogout}>
                    <HiLogout className="w-4 h-4 mr-2" />
                    Déconnexion
                  </Dropdown.Item>
                </Dropdown>
              </div>
            </div>
          </div>
        </div>

        {/* Overlay for mobile */}
        {sidebarOpen && (
          <div
            className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}

        {/* Main content */}
        <div className="flex-1 lg:ml-0">
          {/* Top bar for desktop */}
          <div className="hidden lg:block">
            <div className="bg-white border-b border-gray-200 px-6 py-4">
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-2xl font-semibold text-gray-900">
                    {filteredMenuItems.find(item => item.path === location.pathname)?.title || 'Dashboard'}
                  </h1>
                  <p className="text-sm text-gray-500 mt-1">
                    Bienvenue, {user?.full_name}
                  </p>
                </div>
                
                <div className="flex items-center space-x-4">
                  <Avatar
                    img={`https://ui-avatars.com/api/?name=${encodeURIComponent(user?.full_name || 'User')}&background=3b82f6&color=fff`}
                    rounded
                  />
                  <Dropdown
                    arrowIcon={false}
                    inline
                    label={
                      <span className="text-sm font-medium text-gray-700 hover:text-gray-900 cursor-pointer">
                        {user?.full_name}
                      </span>
                    }
                  >
                    <Dropdown.Header>
                      <span className="block text-sm">{user?.full_name}</span>
                      <span className="block truncate text-sm font-medium">{user?.email}</span>
                    </Dropdown.Header>
                    <Dropdown.Item onClick={handleLogout}>
                      <HiLogout className="w-4 h-4 mr-2" />
                      Déconnexion
                    </Dropdown.Item>
                  </Dropdown>
                </div>
              </div>
            </div>
          </div>

          {/* Page content */}
          <main className="p-6">
            <Outlet />
          </main>
        </div>
      </div>
    </div>
  );
};

export default ModernLayout;
