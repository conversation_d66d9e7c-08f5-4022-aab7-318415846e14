import React, { useState } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const TailwindLayout: React.FC = () => {
  const { user, logout, hasPermission } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const menuItems = [
    {
      title: 'Dashboard',
      path: '/dashboard',
      icon: '📊',
      permission: null, // Dashboard accessible à tous
    },
    {
      title: 'Utilisateurs',
      path: '/users',
      icon: '👥',
      permission: 'user:read',
    },
    {
      title: 'Rôles',
      path: '/roles',
      icon: '🛡️',
      permission: 'role:read',
    },
    {
      title: 'Permissions',
      path: '/permissions',
      icon: '🔑',
      permission: 'permission:read',
    },
  ];

  const filteredMenuItems = menuItems.filter(item =>
    !item.permission || hasPermission(item.permission)
  );

  return (
    <div className="min-h-screen bg-gray-50 flex w-full">
      {/* Mobile header */}
      <div className="lg:hidden fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 shadow-sm">
        <div className="flex items-center justify-between px-3 sm:px-4 py-2 sm:py-3">
          <div className="flex items-center space-x-2 sm:space-x-3">
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="p-1.5 sm:p-2 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors"
            >
              <span className="sr-only">Ouvrir le menu</span>
              <svg className="h-5 w-5 sm:h-6 sm:w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
            <h1 className="text-sm sm:text-lg font-semibold text-gray-900 truncate">
              🏢 <span className="hidden xs:inline">Users Management</span><span className="xs:hidden">Users</span>
            </h1>
          </div>

          <div className="flex items-center space-x-2 sm:space-x-3">
            <div className="w-7 h-7 sm:w-8 sm:h-8 bg-primary-600 rounded-full flex items-center justify-center">
              <span className="text-white text-xs sm:text-sm font-medium">
                {user?.full_name?.charAt(0) || 'U'}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="flex">
        {/* Sidebar */}
        <div className={`
          fixed inset-y-0 left-0 z-40 w-56 sm:w-64 xl:w-72 bg-white border-r border-gray-200 shadow-xl lg:shadow-none
          transform transition-all duration-300 ease-in-out
          lg:relative lg:translate-x-0 lg:flex lg:flex-col lg:h-screen
          ${sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
        `}>
          <div className="flex flex-col h-full">
            {/* Logo */}
            <div className="flex items-center justify-between h-14 sm:h-16 px-3 sm:px-4 xl:px-6 bg-primary-600 lg:bg-primary-700">
              <h1 className="text-sm sm:text-lg xl:text-xl font-bold text-white truncate">
                🏢 <span className="hidden sm:inline">Users Management</span><span className="sm:hidden">Users</span>
              </h1>
              {/* Close button for mobile */}
              <button
                className="lg:hidden p-1 rounded-md text-white/80 hover:text-white hover:bg-white/10 transition-colors"
                onClick={() => setSidebarOpen(false)}
              >
                <svg className="w-5 h-5 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Navigation */}
            <nav className="flex-1 px-2 sm:px-3 xl:px-4 py-3 sm:py-4 space-y-1 sm:space-y-2 overflow-y-auto">
              {filteredMenuItems.map((item) => {
                const isActive = location.pathname === item.path;

                return (
                  <button
                    key={item.path}
                    onClick={() => {
                      navigate(item.path);
                      setSidebarOpen(false);
                    }}
                    className={`
                      w-full flex items-center px-2 sm:px-3 py-2 sm:py-2.5 text-xs sm:text-sm xl:text-base font-medium rounded-lg transition-all duration-200
                      ${isActive
                        ? 'bg-primary-100 text-primary-700 border-r-2 border-primary-600 shadow-sm'
                        : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900 hover:shadow-sm'
                      }
                    `}
                  >
                    <span className="text-base sm:text-lg xl:text-xl mr-2 sm:mr-3 flex-shrink-0">{item.icon}</span>
                    <span className="truncate">{item.title}</span>
                  </button>
                );
              })}
            </nav>

            {/* User section */}
            <div className="p-2 sm:p-3 xl:p-4 border-t border-gray-200 mt-auto">
              <div className="flex items-center space-x-2 sm:space-x-3">
                <div className="w-7 h-7 sm:w-8 sm:h-8 xl:w-10 xl:h-10 bg-primary-600 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-white text-xs sm:text-sm xl:text-base font-medium">
                    {user?.full_name?.charAt(0) || 'U'}
                  </span>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-xs sm:text-sm xl:text-base font-medium text-gray-900 truncate">
                    {user?.full_name || 'Utilisateur'}
                  </p>
                  <p className="text-xs xl:text-sm text-gray-500 truncate">
                    {user?.role?.name || user?.email || 'Aucun rôle'}
                  </p>
                </div>
                <button
                  onClick={handleLogout}
                  className="p-1 sm:p-1.5 text-gray-400 hover:text-gray-500 rounded-md hover:bg-gray-100 transition-colors"
                  title="Déconnexion"
                >
                  <svg className="w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Overlay for mobile */}
        {sidebarOpen && (
          <div
            className="fixed inset-0 z-30 bg-black bg-opacity-50 lg:hidden transition-opacity duration-300"
            onClick={() => setSidebarOpen(false)}
          />
        )}

        {/* Main content */}
        <div className="flex-1 min-w-0 flex flex-col lg:ml-0">
          {/* Spacer for mobile header */}
          <div className="h-12 sm:h-16 lg:hidden flex-shrink-0"></div>
          {/* Top bar for desktop */}
          <div className="hidden lg:block">
            <div className="bg-white border-b border-gray-200 px-6 py-4">
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-2xl font-semibold text-gray-900">
                    {filteredMenuItems.find(item => item.path === location.pathname)?.title || 'Dashboard'}
                  </h1>
                  <p className="text-sm text-gray-500 mt-1">
                    Bienvenue, {user?.full_name}
                  </p>
                </div>
                
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center">
                    <span className="text-white font-medium">
                      {user?.full_name?.charAt(0) || 'U'}
                    </span>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-700">{user?.full_name}</p>
                    <p className="text-xs text-gray-500">{user?.email}</p>
                  </div>
                  <button
                    onClick={handleLogout}
                    className="p-2 text-gray-400 hover:text-gray-500 rounded-md hover:bg-gray-100"
                    title="Déconnexion"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Page content */}
          <main className="flex-1 p-3 sm:p-4 md:p-6 lg:p-8 overflow-auto w-full">
            <div className="w-full">
              <Outlet />
            </div>
          </main>
        </div>
      </div>
    </div>
  );
};

export default TailwindLayout;
