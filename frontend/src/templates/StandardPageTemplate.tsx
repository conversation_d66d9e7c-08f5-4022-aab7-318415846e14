import React from 'react';
import { 
  <PERSON>Lay<PERSON>, 
  <PERSON><PERSON><PERSON>er, 
  PageCard, 
  PageActionButton,
  ResponsiveTable,
  ResponsiveTableHeader,
  ResponsiveTableHeaderCell,
  ResponsiveTableCell,
  ResponsiveModal 
} from '../components/PageLayout';
import { usePage, useModalForm } from '../hooks/usePage';

/**
 * Template standardisé pour créer de nouvelles pages
 * 
 * INSTRUCTIONS D'UTILISATION :
 * 
 * 1. Copiez ce fichier et renommez-le selon votre page
 * 2. Remplacez les interfaces et types selon vos besoins
 * 3. Adaptez les colonnes du tableau
 * 4. Modifiez le formulaire du modal
 * 5. Implémentez les fonctions CRUD
 * 
 * Ce template garantit :
 * - Largeur complète (w-full) sur tous les écrans
 * - Responsivité parfaite
 * - Cohérence visuelle
 * - Accessibilité
 */

// ===== INTERFACES À ADAPTER =====
interface YourDataType {
  id: number;
  name: string;
  description?: string;
  created_at: string;
  // Ajoutez vos champs ici
}

interface YourFormData {
  name: string;
  description: string;
  // Ajoutez vos champs de formulaire ici
}

// ===== COMPOSANT PRINCIPAL =====
const YourPageName: React.FC = () => {
  // ===== HOOKS STANDARDISÉS =====
  const {
    loading,
    currentPage,
    showModal,
    editingItem,
    openModal,
    closeModal,
    goToPage,
    nextPage,
    prevPage,
    withLoading
  } = usePage();

  // ===== ÉTAT LOCAL =====
  const [items, setItems] = React.useState<YourDataType[]>([]);
  const [totalPages, setTotalPages] = React.useState(1);

  // ===== DONNÉES INITIALES DU FORMULAIRE =====
  const initialFormData: YourFormData = {
    name: '',
    description: '',
    // Initialisez vos champs ici
  };

  // ===== HOOK FORMULAIRE =====
  const {
    formData,
    submitting,
    updateFormData,
    resetForm,
    handleSubmit
  } = useModalForm(
    editingItem ? {
      name: editingItem.name || '',
      description: editingItem.description || '',
      // Mappez vos champs ici
    } : initialFormData,
    handleFormSubmit,
    closeModal
  );

  // ===== FONCTIONS CRUD À IMPLÉMENTER =====
  const fetchItems = async () => {
    // TODO: Implémentez la récupération des données
    // Exemple :
    // const response = await apiService.getYourItems(currentPage);
    // setItems(response.items);
    // setTotalPages(response.totalPages);
  };

  async function handleFormSubmit(data: YourFormData) {
    // TODO: Implémentez la création/modification
    // if (editingItem) {
    //   await apiService.updateYourItem(editingItem.id, data);
    // } else {
    //   await apiService.createYourItem(data);
    // }
    // await fetchItems();
  }

  const handleDelete = async (id: number) => {
    if (!window.confirm('Êtes-vous sûr de vouloir supprimer cet élément ?')) return;
    
    await withLoading(async () => {
      // TODO: Implémentez la suppression
      // await apiService.deleteYourItem(id);
      // await fetchItems();
    });
  };

  // ===== EFFETS =====
  React.useEffect(() => {
    withLoading(fetchItems);
  }, [currentPage]);

  // ===== RENDU CONDITIONNEL LOADING =====
  if (loading && items.length === 0) {
    return (
      <PageLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
      </PageLayout>
    );
  }

  // ===== RENDU PRINCIPAL =====
  return (
    <PageLayout
      header={{
        title: "Votre Titre de Page",
        description: "Description de votre page",
        action: (
          <PageActionButton
            onClick={() => openModal()}
            fullText="Nouvel Élément"
            shortText="Nouveau"
            icon={
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            }
          />
        )
      }}
    >
      {/* ===== TABLEAU PRINCIPAL ===== */}
      <PageCard>
        <ResponsiveTable>
          <ResponsiveTableHeader>
            <ResponsiveTableHeaderCell>Nom</ResponsiveTableHeaderCell>
            <ResponsiveTableHeaderCell hideOn="sm">Description</ResponsiveTableHeaderCell>
            <ResponsiveTableHeaderCell hideOn="md">Date de création</ResponsiveTableHeaderCell>
            <ResponsiveTableHeaderCell>Actions</ResponsiveTableHeaderCell>
          </ResponsiveTableHeader>
          <tbody className="bg-white divide-y divide-gray-200">
            {items.map((item) => (
              <tr key={item.id} className="hover:bg-gray-50 transition-colors">
                <ResponsiveTableCell>
                  <div className="flex-1 min-w-0">
                    <div className="text-xs sm:text-sm lg:text-base font-medium text-gray-900 truncate">
                      {item.name}
                    </div>
                    {/* Affichage mobile de la description */}
                    <div className="sm:hidden text-xs text-gray-500 truncate mt-0.5">
                      {item.description}
                    </div>
                  </div>
                </ResponsiveTableCell>
                
                <ResponsiveTableCell hideOn="sm">
                  <div className="text-xs sm:text-sm text-gray-900 truncate max-w-xs">
                    {item.description}
                  </div>
                </ResponsiveTableCell>
                
                <ResponsiveTableCell hideOn="md">
                  <div className="text-xs sm:text-sm text-gray-500">
                    {new Date(item.created_at).toLocaleDateString()}
                  </div>
                </ResponsiveTableCell>
                
                <ResponsiveTableCell>
                  <div className="flex justify-end space-x-1 sm:space-x-2">
                    <button
                      onClick={() => openModal(item)}
                      className="text-primary-600 hover:text-primary-900 p-1 sm:p-1.5 rounded-md hover:bg-primary-50 transition-colors"
                      title="Modifier"
                    >
                      <svg className="w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                    </button>
                    <button
                      onClick={() => handleDelete(item.id)}
                      className="text-red-600 hover:text-red-900 p-1 sm:p-1.5 rounded-md hover:bg-red-50 transition-colors"
                      title="Supprimer"
                    >
                      <svg className="w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                </ResponsiveTableCell>
              </tr>
            ))}
          </tbody>
        </ResponsiveTable>

        {/* ===== PAGINATION ===== */}
        {totalPages > 1 && (
          <div className="bg-white px-3 sm:px-4 lg:px-6 py-3 flex items-center justify-between border-t border-gray-200">
            {/* Mobile pagination */}
            <div className="flex-1 flex justify-between items-center sm:hidden">
              <button
                onClick={prevPage}
                disabled={currentPage === 1}
                className="btn-secondary text-xs px-2 py-1.5 disabled:opacity-50"
              >
                ‹ Préc
              </button>
              <span className="text-xs text-gray-700 px-2">
                {currentPage} / {totalPages}
              </span>
              <button
                onClick={() => nextPage(totalPages)}
                disabled={currentPage === totalPages}
                className="btn-secondary text-xs px-2 py-1.5 disabled:opacity-50"
              >
                Suiv ›
              </button>
            </div>
            
            {/* Desktop pagination */}
            <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
              <div>
                <p className="text-xs sm:text-sm text-gray-700">
                  Page <span className="font-medium">{currentPage}</span> sur{' '}
                  <span className="font-medium">{totalPages}</span>
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  <button
                    onClick={prevPage}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-2 sm:px-3 py-1.5 sm:py-2 rounded-l-md border border-gray-300 bg-white text-xs sm:text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 transition-colors"
                  >
                    <span className="hidden sm:inline">Précédent</span>
                    <span className="sm:hidden">‹</span>
                  </button>
                  <button
                    onClick={() => nextPage(totalPages)}
                    disabled={currentPage === totalPages}
                    className="relative inline-flex items-center px-2 sm:px-3 py-1.5 sm:py-2 rounded-r-md border border-gray-300 bg-white text-xs sm:text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 transition-colors"
                  >
                    <span className="hidden sm:inline">Suivant</span>
                    <span className="sm:hidden">›</span>
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </PageCard>

      {/* ===== MODAL FORMULAIRE ===== */}
      <ResponsiveModal
        isOpen={showModal}
        onClose={closeModal}
        maxWidth="lg"
      >
        <form onSubmit={handleSubmit}>
          <div className="bg-white px-3 xs:px-4 sm:px-6 pt-4 sm:pt-5 pb-4">
            <div className="w-full">
              <h3 className="text-base sm:text-lg leading-6 font-medium text-gray-900 mb-3 sm:mb-4">
                {editingItem ? 'Modifier l\'Élément' : 'Nouvel Élément'}
              </h3>
              
              <div className="space-y-3 sm:space-y-4">
                {/* ===== CHAMPS DU FORMULAIRE À ADAPTER ===== */}
                <div>
                  <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
                    Nom
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => updateFormData({ name: e.target.value })}
                    className="input-field text-sm"
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => updateFormData({ description: e.target.value })}
                    className="input-field text-sm"
                    rows={3}
                  />
                </div>
                
                {/* Ajoutez d'autres champs ici */}
              </div>
            </div>
          </div>
          
          <div className="bg-gray-50 px-3 xs:px-4 sm:px-6 py-3 flex flex-col-reverse xs:flex-row xs:justify-end gap-2 xs:gap-3">
            <button
              type="button"
              onClick={closeModal}
              className="btn-secondary w-full xs:w-auto text-sm"
              disabled={submitting}
            >
              Annuler
            </button>
            <button
              type="submit"
              className="btn-primary w-full xs:w-auto text-sm"
              disabled={submitting}
            >
              {submitting ? 'Enregistrement...' : (editingItem ? 'Modifier' : 'Créer')}
            </button>
          </div>
        </form>
      </ResponsiveModal>
    </PageLayout>
  );
};

export default YourPageName;
