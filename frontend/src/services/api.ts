import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';
import { toast } from 'react-toastify';
import {
  AuthToken,
  LoginCredentials,
  User,
  UserCreate,
  UserUpdate,
  Role,
  RoleCreate,
  RoleUpdate,
  Permission,
  PermissionCreate,
  PermissionUpdate,
  PaginatedResponse,
  ApiError
} from '../types';

// Configuration de base d'Axios
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api/v1';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Intercepteur pour ajouter le token d'authentification
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Intercepteur pour gérer les erreurs de réponse
    this.api.interceptors.response.use(
      (response: AxiosResponse) => response,
      (error: AxiosError<ApiError>) => {
        if (error.response?.status === 401) {
          // Token expiré ou invalide
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          window.location.href = '/login';
        } else if (error.response?.status === 403) {
          toast.error('Accès refusé. Permissions insuffisantes.');
        } else if (error.response?.status >= 500) {
          toast.error('Erreur serveur. Veuillez réessayer plus tard.');
        } else if (error.response?.data?.detail) {
          toast.error(error.response.data.detail);
        } else {
          toast.error('Une erreur est survenue.');
        }
        return Promise.reject(error);
      }
    );
  }

  // Méthodes d'authentification
  async login(credentials: LoginCredentials): Promise<AuthToken> {
    const response = await this.api.post<AuthToken>('/auth/login', credentials);
    return response.data;
  }

  async register(userData: UserCreate): Promise<User> {
    const response = await this.api.post<User>('/auth/register', userData);
    return response.data;
  }

  async getCurrentUser(): Promise<User> {
    const response = await this.api.get<User>('/auth/me');
    return response.data;
  }

  async logout(): Promise<void> {
    await this.api.post('/auth/logout');
  }

  // Méthodes pour les utilisateurs
  async getUsers(page: number = 1, size: number = 20, search?: string): Promise<PaginatedResponse<User>> {
    const params = new URLSearchParams({
      page: page.toString(),
      size: size.toString(),
    });
    if (search) {
      params.append('search', search);
    }
    const response = await this.api.get<PaginatedResponse<User>>(`/users?${params}`);
    return response.data;
  }

  async getUser(id: number): Promise<User> {
    const response = await this.api.get<User>(`/users/${id}`);
    return response.data;
  }

  async createUser(userData: UserCreate): Promise<User> {
    const response = await this.api.post<User>('/users', userData);
    return response.data;
  }

  async updateUser(id: number, userData: UserUpdate): Promise<User> {
    const response = await this.api.put<User>(`/users/${id}`, userData);
    return response.data;
  }

  async deleteUser(id: number): Promise<void> {
    await this.api.delete(`/users/${id}`);
  }

  // Méthodes pour les rôles
  async getRoles(page: number = 1, size: number = 20, search?: string): Promise<PaginatedResponse<Role>> {
    const params = new URLSearchParams({
      page: page.toString(),
      size: size.toString(),
    });
    if (search) {
      params.append('search', search);
    }
    const response = await this.api.get<PaginatedResponse<Role>>(`/roles?${params}`);
    return response.data;
  }

  async getAllRoles(): Promise<Role[]> {
    const response = await this.api.get<Role[]>('/roles/all');
    return response.data;
  }

  async getRole(id: number): Promise<Role> {
    const response = await this.api.get<Role>(`/roles/${id}`);
    return response.data;
  }

  async createRole(roleData: RoleCreate): Promise<Role> {
    const response = await this.api.post<Role>('/roles', roleData);
    return response.data;
  }

  async updateRole(id: number, roleData: RoleUpdate): Promise<Role> {
    const response = await this.api.put<Role>(`/roles/${id}`, roleData);
    return response.data;
  }

  async deleteRole(id: number): Promise<void> {
    await this.api.delete(`/roles/${id}`);
  }

  // Méthodes pour les permissions
  async getPermissions(page: number = 1, size: number = 20, search?: string): Promise<PaginatedResponse<Permission>> {
    const params = new URLSearchParams({
      page: page.toString(),
      size: size.toString(),
    });
    if (search) {
      params.append('search', search);
    }
    const response = await this.api.get<PaginatedResponse<Permission>>(`/permissions?${params}`);
    return response.data;
  }

  async getAllPermissions(): Promise<Permission[]> {
    const response = await this.api.get<Permission[]>('/permissions/all');
    return response.data;
  }

  async getPermission(id: number): Promise<Permission> {
    const response = await this.api.get<Permission>(`/permissions/${id}`);
    return response.data;
  }

  async createPermission(permissionData: PermissionCreate): Promise<Permission> {
    const response = await this.api.post<Permission>('/permissions', permissionData);
    return response.data;
  }

  async updatePermission(id: number, permissionData: PermissionUpdate): Promise<Permission> {
    const response = await this.api.put<Permission>(`/permissions/${id}`, permissionData);
    return response.data;
  }

  async deletePermission(id: number): Promise<void> {
    await this.api.delete(`/permissions/${id}`);
  }
}

// Instance singleton du service API
export const apiService = new ApiService();
export default apiService;
