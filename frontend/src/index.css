@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@layer base {
  body {
    font-family: 'Inter', sans-serif;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 focus:ring-primary-300 text-white font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center focus:ring-4 focus:outline-none transition-colors duration-200;
  }

  .btn-secondary {
    @apply text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 transition-colors duration-200;
  }

  .btn-danger {
    @apply text-white bg-red-600 hover:bg-red-700 focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center focus:ring-4 focus:outline-none transition-colors duration-200;
  }

  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
  }

  .input-field {
    @apply bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 transition-colors duration-200;
  }

  .sidebar-link {
    @apply flex items-center p-2 text-gray-900 rounded-lg hover:bg-gray-100 group transition-colors duration-200;
  }

  .sidebar-link.active {
    @apply bg-primary-100 text-primary-700;
  }
}
