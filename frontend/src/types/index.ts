// Types pour l'authentification
export interface User {
  id: number;
  email: string;
  first_name?: string;
  last_name?: string;
  full_name: string;
  is_active: boolean;
  is_superuser: boolean;
  role_id?: number;
  role?: Role;
  created_at: string;
  updated_at: string;
  last_login?: string;
  permissions: string[];
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  first_name?: string;
  last_name?: string;
  role_id?: number;
}

export interface AuthToken {
  access_token: string;
  token_type: string;
  expires_in: number;
  user: User;
}

// Types pour les rôles
export interface Role {
  id: number;
  name: string;
  description?: string;
  permissions: Permission[];
  created_at: string;
  updated_at: string;
}

export interface RoleCreate {
  name: string;
  description?: string;
  permission_ids?: number[];
}

export interface RoleUpdate {
  name?: string;
  description?: string;
  permission_ids?: number[];
}

// Types pour les permissions
export interface Permission {
  id: number;
  code: string;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface PermissionCreate {
  code: string;
  name: string;
  description?: string;
}

export interface PermissionUpdate {
  code?: string;
  name?: string;
  description?: string;
}

// Types pour les utilisateurs
export interface UserCreate {
  email: string;
  password: string;
  first_name?: string;
  last_name?: string;
  is_active?: boolean;
  role_id?: number;
}

export interface UserUpdate {
  email?: string;
  password?: string;
  first_name?: string;
  last_name?: string;
  is_active?: boolean;
  role_id?: number;
}

// Types pour la pagination
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
  has_next: boolean;
  has_prev: boolean;
}

// Types pour les erreurs API
export interface ApiError {
  detail: string;
}

// Types pour les contextes
export interface AuthContextType {
  user: User | null;
  token: string | null;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  isAuthenticated: boolean;
  hasPermission: (permission: string) => boolean;
  hasAnyPermission: (permissions: string[]) => boolean;
  loading: boolean;
}

// Types pour les composants
export interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermissions?: string[];
  requireAny?: boolean;
}

export interface DataTableColumn {
  field: string;
  headerName: string;
  width?: number;
  flex?: number;
  sortable?: boolean;
  filterable?: boolean;
  renderCell?: (params: any) => React.ReactNode;
}

// Types pour les formulaires
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'select' | 'multiselect' | 'checkbox';
  required?: boolean;
  options?: { value: any; label: string }[];
  validation?: {
    minLength?: number;
    maxLength?: number;
    pattern?: RegExp;
    custom?: (value: any) => string | null;
  };
}

// Types pour les notifications
export type NotificationType = 'success' | 'error' | 'warning' | 'info';

export interface NotificationOptions {
  type: NotificationType;
  message: string;
  duration?: number;
}
