import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/ProtectedRoute';
import TailwindLayout from './components/TailwindLayout';
import TailwindLogin from './pages/TailwindLogin';
import TailwindDashboard from './pages/TailwindDashboard';
import Users from './pages/Users';
import TailwindRoles from './pages/TailwindRoles';
import TailwindPermissions from './pages/TailwindPermissions';



function App() {
  return (
    <AuthProvider>
      <Router>
          <Routes>
            {/* Route de connexion */}
            <Route path="/login" element={<TailwindLogin />} />

            {/* Routes protégées */}
            <Route
              path="/"
              element={
                <ProtectedRoute>
                  <TailwindLayout />
                </ProtectedRoute>
              }
            >
              <Route index element={<Navigate to="/dashboard" replace />} />
              <Route
                path="dashboard"
                element={
                  <ProtectedRoute requiredPermissions={['dashboard:access']}>
                    <TailwindDashboard />
                  </ProtectedRoute>
                }
              />
              <Route
                path="users"
                element={
                  <ProtectedRoute requiredPermissions={['user:read']}>
                    <Users />
                  </ProtectedRoute>
                }
              />
              <Route
                path="roles"
                element={
                  <ProtectedRoute requiredPermissions={['role:read']}>
                    <Roles />
                  </ProtectedRoute>
                }
              />
              <Route
                path="permissions"
                element={
                  <ProtectedRoute requiredPermissions={['permission:read']}>
                    <Permissions />
                  </ProtectedRoute>
                }
              />
            </Route>

            {/* Redirection par défaut */}
            <Route path="*" element={<Navigate to="/dashboard" replace />} />
          </Routes>
        </Router>

        {/* Notifications toast */}
        <ToastContainer
          position="top-right"
          autoClose={5000}
          hideProgressBar={false}
          newestOnTop={false}
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
        />
      </AuthProvider>
  );
}

export default App;
