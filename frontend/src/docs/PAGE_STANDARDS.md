# Standards de Développement des Pages

## 🎯 Objectif

Ce document définit les standards obligatoires pour toutes les pages de l'application afin de garantir :
- **Largeur complète** sur tous les écrans (pas d'espace blanc)
- **Responsivité parfaite** sur tous les appareils
- **Coh<PERSON>rence visuelle** et d'expérience utilisateur
- **Accessibilité** et performance

## 📋 Checklist Obligatoire

### ✅ Structure de Base
- [ ] Utiliser `PageLayout` comme container principal
- [ ] Ajouter `w-full` à tous les containers principaux
- [ ] Utiliser les composants standardisés (`PageCard`, `ResponsiveTable`, etc.)
- [ ] Implémenter la responsivité avec les breakpoints définis

### ✅ En-tête de Page
- [ ] Utiliser `PageHeader` avec titre responsive
- [ ] Titre adaptatif : `text-lg sm:text-xl lg:text-2xl xl:text-3xl`
- [ ] Description responsive : `text-xs sm:text-sm lg:text-base`
- [ ] Bouton d'action avec `PageActionButton`

### ✅ Tableaux
- [ ] Utiliser `ResponsiveTable` et ses composants
- [ ] Colonnes qui se cachent progressivement (`hideOn`)
- [ ] Informations importantes visibles sur mobile
- [ ] Actions avec icônes responsive

### ✅ Modals
- [ ] Utiliser `ResponsiveModal`
- [ ] Formulaires avec classes standardisées
- [ ] Boutons responsive dans les actions

### ✅ Pagination
- [ ] Version mobile et desktop
- [ ] Boutons avec états disabled
- [ ] Texte adaptatif selon la taille

## 🛠️ Composants Standardisés

### PageLayout
```tsx
import { PageLayout } from '../components/PageLayout';

<PageLayout
  header={{
    title: "Titre de la Page",
    description: "Description optionnelle",
    action: <PageActionButton ... />
  }}
>
  {/* Contenu de la page */}
</PageLayout>
```

### PageCard
```tsx
import { PageCard } from '../components/PageLayout';

<PageCard overflow={true}>
  {/* Contenu avec scroll horizontal automatique */}
</PageCard>
```

### ResponsiveTable
```tsx
import { 
  ResponsiveTable, 
  ResponsiveTableHeader, 
  ResponsiveTableHeaderCell,
  ResponsiveTableCell 
} from '../components/PageLayout';

<ResponsiveTable>
  <ResponsiveTableHeader>
    <ResponsiveTableHeaderCell>Toujours visible</ResponsiveTableHeaderCell>
    <ResponsiveTableHeaderCell hideOn="sm">Caché sur mobile</ResponsiveTableHeaderCell>
    <ResponsiveTableHeaderCell hideOn="md">Caché sur tablet</ResponsiveTableHeaderCell>
  </ResponsiveTableHeader>
  <tbody>
    <tr>
      <ResponsiveTableCell>...</ResponsiveTableCell>
      <ResponsiveTableCell hideOn="sm">...</ResponsiveTableCell>
      <ResponsiveTableCell hideOn="md">...</ResponsiveTableCell>
    </tr>
  </tbody>
</ResponsiveTable>
```

## 📱 Breakpoints Standardisés

| Breakpoint | Taille | Usage |
|------------|--------|-------|
| `xs` | 475px+ | Très petits écrans |
| `sm` | 640px+ | Petits écrans |
| `md` | 768px+ | Tablettes |
| `lg` | 1024px+ | Desktop |
| `xl` | 1280px+ | Grand desktop |
| `3xl` | 1600px+ | Très grand écran |
| `4xl` | 1920px+ | Ultra-wide |

## 🎨 Classes CSS Utilitaires

### Layout
- `.page-container` : Container principal de page
- `.page-header` : En-tête responsive
- `.page-card` : Carte avec largeur complète

### Visibilité Responsive
- `.hide-on-xs` : Caché sur très petits écrans
- `.hide-on-sm` : Caché sur petits écrans
- `.hide-on-md` : Caché sur tablettes
- `.show-on-xs` : Visible uniquement sur très petits écrans

### Espacement
- `.spacing-xs` : Espacement minimal
- `.spacing-sm` : Espacement petit
- `.spacing-md` : Espacement moyen (défaut)
- `.spacing-lg` : Espacement large
- `.spacing-xl` : Espacement extra-large

## 🚀 Template de Démarrage

Utilisez le template `StandardPageTemplate.tsx` pour créer de nouvelles pages :

1. Copiez le fichier template
2. Renommez selon votre page
3. Adaptez les interfaces et types
4. Modifiez les colonnes du tableau
5. Implémentez les fonctions CRUD

## ⚠️ Erreurs à Éviter

### ❌ Ne PAS faire
```tsx
// Container avec largeur limitée
<div className="max-w-7xl mx-auto">

// Layout non responsive
<div className="flex justify-between">

// Tableau non responsive
<table className="w-full">

// Modal non responsive
<div className="fixed inset-0">
  <div className="max-w-lg mx-auto">
```

### ✅ À faire
```tsx
// Container pleine largeur
<div className="w-full">

// Layout responsive
<div className="flex flex-col xs:flex-row xs:justify-between">

// Tableau responsive
<ResponsiveTable>

// Modal responsive
<ResponsiveModal maxWidth="lg">
```

## 🔍 Tests de Responsivité

Testez OBLIGATOIREMENT sur ces tailles :
- [ ] 320px (iPhone SE)
- [ ] 375px (iPhone standard)
- [ ] 768px (iPad portrait)
- [ ] 1024px (iPad landscape)
- [ ] 1280px (Desktop)
- [ ] 1920px (Full HD)

## 📝 Hooks Standardisés

### usePage
```tsx
import { usePage } from '../hooks/usePage';

const {
  loading,
  currentPage,
  showModal,
  editingItem,
  openModal,
  closeModal,
  withLoading
} = usePage();
```

### useModalForm
```tsx
import { useModalForm } from '../hooks/usePage';

const {
  formData,
  submitting,
  updateFormData,
  handleSubmit
} = useModalForm(initialData, onSubmit, onClose);
```

## 🎯 Objectifs de Performance

- **Largeur complète** : 100% de l'écran utilisé
- **Temps de chargement** : < 2s sur 3G
- **Accessibilité** : Score WCAG AA
- **Responsive** : Parfait sur tous les appareils

## 📞 Support

En cas de questions ou problèmes :
1. Consultez le template `StandardPageTemplate.tsx`
2. Vérifiez les composants dans `PageLayout.tsx`
3. Utilisez les classes CSS dans `pageUtils.css`
4. Suivez les exemples des pages existantes

---

**⚠️ IMPORTANT** : Toute page qui ne respecte pas ces standards sera refusée en code review.
