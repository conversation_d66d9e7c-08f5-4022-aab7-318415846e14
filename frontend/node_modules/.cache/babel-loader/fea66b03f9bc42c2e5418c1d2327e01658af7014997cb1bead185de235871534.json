{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { styled } from '@mui/material/styles';\nimport composeClasses from '@mui/utils/composeClasses';\nimport capitalize from '@mui/utils/capitalize';\nimport useId from '@mui/utils/useId';\nimport useForkRef from '@mui/utils/useForkRef';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { vars } from \"../../constants/cssVariables.js\";\nimport { gridColumnLookupSelector } from \"../../hooks/features/columns/gridColumnsSelector.js\";\nimport { useGridSelector } from \"../../hooks/utils/useGridSelector.js\";\nimport { gridFilterActiveItemsSelector } from \"../../hooks/features/filter/gridFilterSelector.js\";\nimport { gridPreferencePanelStateSelector } from \"../../hooks/features/preferencesPanel/gridPreferencePanelSelector.js\";\nimport { GridPreferencePanelsValue } from \"../../hooks/features/preferencesPanel/gridPreferencePanelsValue.js\";\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { useGridPanelContext } from \"../panel/GridPanelContext.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['toolbarFilterList']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst GridToolbarFilterListRoot = styled('ul', {\n  name: 'MuiDataGrid',\n  slot: 'ToolbarFilterList'\n})({\n  margin: vars.spacing(1, 1, 0.5),\n  padding: vars.spacing(0, 1)\n});\n/**\n * @deprecated Use the {@link https://mui.com/x/react-data-grid/components/filter-panel/ Filter Panel Trigger} component instead. This component will be removed in a future major release.\n */\nconst GridToolbarFilterButton = forwardRef(function GridToolbarFilterButton(props, ref) {\n  const {\n    slotProps = {}\n  } = props;\n  const buttonProps = slotProps.button || {};\n  const tooltipProps = slotProps.tooltip || {};\n  const badgeProps = slotProps.badge || {};\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const activeFilters = useGridSelector(apiRef, gridFilterActiveItemsSelector);\n  const lookup = useGridSelector(apiRef, gridColumnLookupSelector);\n  const preferencePanel = useGridSelector(apiRef, gridPreferencePanelStateSelector);\n  const classes = useUtilityClasses(rootProps);\n  const filterButtonId = useId();\n  const filterPanelId = useId();\n  const {\n    filterPanelTriggerRef\n  } = useGridPanelContext();\n  const handleRef = useForkRef(ref, filterPanelTriggerRef);\n  const tooltipContentNode = React.useMemo(() => {\n    if (preferencePanel.open) {\n      return apiRef.current.getLocaleText('toolbarFiltersTooltipHide');\n    }\n    if (activeFilters.length === 0) {\n      return apiRef.current.getLocaleText('toolbarFiltersTooltipShow');\n    }\n    const getOperatorLabel = item => lookup[item.field].filterOperators.find(operator => operator.value === item.operator).label || apiRef.current.getLocaleText(`filterOperator${capitalize(item.operator)}`).toString();\n    const getFilterItemValue = item => {\n      const {\n        getValueAsString\n      } = lookup[item.field].filterOperators.find(operator => operator.value === item.operator);\n      return getValueAsString ? getValueAsString(item.value) : item.value;\n    };\n    return /*#__PURE__*/_jsxs(\"div\", {\n      children: [apiRef.current.getLocaleText('toolbarFiltersTooltipActive')(activeFilters.length), /*#__PURE__*/_jsx(GridToolbarFilterListRoot, {\n        className: classes.root,\n        ownerState: rootProps,\n        children: activeFilters.map((item, index) => _extends({}, lookup[item.field] && /*#__PURE__*/_jsx(\"li\", {\n          children: `${lookup[item.field].headerName || item.field}\n                  ${getOperatorLabel(item)}\n                  ${\n          // implicit check for null and undefined\n          item.value != null ? getFilterItemValue(item) : ''}`\n        }, index)))\n      })]\n    });\n  }, [apiRef, rootProps, preferencePanel.open, activeFilters, lookup, classes]);\n  const toggleFilter = event => {\n    const {\n      open,\n      openedPanelValue\n    } = preferencePanel;\n    if (open && openedPanelValue === GridPreferencePanelsValue.filters) {\n      apiRef.current.hidePreferences();\n    } else {\n      apiRef.current.showPreferences(GridPreferencePanelsValue.filters, filterPanelId, filterButtonId);\n    }\n    buttonProps.onClick?.(event);\n  };\n\n  // Disable the button if the corresponding is disabled\n  if (rootProps.disableColumnFilter) {\n    return null;\n  }\n  const isOpen = preferencePanel.open && preferencePanel.panelId === filterPanelId;\n  return /*#__PURE__*/_jsx(rootProps.slots.baseTooltip, _extends({\n    title: tooltipContentNode,\n    enterDelay: 1000\n  }, rootProps.slotProps?.baseTooltip, tooltipProps, {\n    children: /*#__PURE__*/_jsx(rootProps.slots.baseButton, _extends({\n      id: filterButtonId,\n      size: \"small\",\n      \"aria-label\": apiRef.current.getLocaleText('toolbarFiltersLabel'),\n      \"aria-controls\": isOpen ? filterPanelId : undefined,\n      \"aria-expanded\": isOpen,\n      \"aria-haspopup\": true,\n      startIcon: /*#__PURE__*/_jsx(rootProps.slots.baseBadge, _extends({\n        badgeContent: activeFilters.length,\n        color: \"primary\"\n      }, rootProps.slotProps?.baseBadge, badgeProps, {\n        children: /*#__PURE__*/_jsx(rootProps.slots.openFilterButtonIcon, {})\n      }))\n    }, rootProps.slotProps?.baseButton, buttonProps, {\n      onClick: toggleFilter,\n      onPointerUp: event => {\n        if (preferencePanel.open) {\n          event.stopPropagation();\n        }\n        buttonProps.onPointerUp?.(event);\n      },\n      ref: handleRef,\n      children: apiRef.current.getLocaleText('toolbarFilters')\n    }))\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridToolbarFilterButton.displayName = \"GridToolbarFilterButton\";\nprocess.env.NODE_ENV !== \"production\" ? GridToolbarFilterButton.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.object\n} : void 0;\nexport { GridToolbarFilterButton };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}