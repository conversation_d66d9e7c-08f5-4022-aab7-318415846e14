{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridApiMethod } from \"../utils/index.js\";\nimport { isFunction } from \"../../utils/utils.js\";\nexport const useGridStateInitialization = apiRef => {\n  const controlStateMapRef = React.useRef({});\n  const registerControlState = React.useCallback(controlStateItem => {\n    controlStateMapRef.current[controlStateItem.stateId] = controlStateItem;\n  }, []);\n  const setState = React.useCallback((state, reason) => {\n    let newState;\n    if (isFunction(state)) {\n      newState = state(apiRef.current.state);\n    } else {\n      newState = state;\n    }\n    if (apiRef.current.state === newState) {\n      return false;\n    }\n    const apiRefWithNewState = {\n      current: {\n        state: newState\n      }\n    };\n    let ignoreSetState = false;\n\n    // Apply the control state constraints\n    const updatedControlStateIds = [];\n    Object.keys(controlStateMapRef.current).forEach(stateId => {\n      const controlState = controlStateMapRef.current[stateId];\n      const oldSubState = controlState.stateSelector(apiRef);\n      const newSubState = controlState.stateSelector(apiRefWithNewState);\n      if (newSubState === oldSubState) {\n        return;\n      }\n      updatedControlStateIds.push({\n        stateId: controlState.stateId,\n        hasPropChanged: newSubState !== controlState.propModel\n      });\n\n      // The state is controlled, the prop should always win\n      if (controlState.propModel !== undefined && newSubState !== controlState.propModel) {\n        ignoreSetState = true;\n      }\n    });\n    if (updatedControlStateIds.length > 1) {\n      // Each hook modify its own state, and it should not leak\n      // Events are here to forward to other hooks and apply changes.\n      // You are trying to update several states in a no isolated way.\n      throw new Error(`You're not allowed to update several sub-state in one transaction. You already updated ${updatedControlStateIds[0].stateId}, therefore, you're not allowed to update ${updatedControlStateIds.map(el => el.stateId).join(', ')} in the same transaction.`);\n    }\n    if (!ignoreSetState) {\n      // We always assign it as we mutate rows for perf reason.\n      apiRef.current.state = newState;\n      apiRef.current.publishEvent('stateChange', newState);\n      apiRef.current.store.update(newState);\n    }\n    if (updatedControlStateIds.length === 1) {\n      const {\n        stateId,\n        hasPropChanged\n      } = updatedControlStateIds[0];\n      const controlState = controlStateMapRef.current[stateId];\n      const model = controlState.stateSelector(apiRefWithNewState);\n      if (controlState.propOnChange && hasPropChanged) {\n        controlState.propOnChange(model, {\n          reason,\n          api: apiRef.current\n        });\n      }\n      if (!ignoreSetState) {\n        apiRef.current.publishEvent(controlState.changeEvent, model, {\n          reason\n        });\n      }\n    }\n    return !ignoreSetState;\n  }, [apiRef]);\n  const updateControlState = React.useCallback((key, state, reason) => {\n    return apiRef.current.setState(previousState => {\n      return _extends({}, previousState, {\n        [key]: state(previousState[key])\n      });\n    }, reason);\n  }, [apiRef]);\n  const publicStateApi = {\n    setState\n  };\n  const privateStateApi = {\n    updateControlState,\n    registerControlState\n  };\n  useGridApiMethod(apiRef, publicStateApi, 'public');\n  useGridApiMethod(apiRef, privateStateApi, 'private');\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}