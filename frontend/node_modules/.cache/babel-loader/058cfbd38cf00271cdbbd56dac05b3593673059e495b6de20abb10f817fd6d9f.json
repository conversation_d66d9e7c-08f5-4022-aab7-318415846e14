{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { GRID_STRING_COL_DEF } from \"./gridStringColDef.js\";\nimport { renderActionsCell } from \"../components/cell/GridActionsCell.js\";\nexport const GRID_ACTIONS_COLUMN_TYPE = 'actions';\nexport const GRID_ACTIONS_COL_DEF = _extends({}, GRID_STRING_COL_DEF, {\n  sortable: false,\n  filterable: false,\n  // @ts-ignore\n  aggregable: false,\n  width: 100,\n  display: 'flex',\n  align: 'center',\n  headerAlign: 'center',\n  headerName: '',\n  disableColumnMenu: true,\n  disableExport: true,\n  renderCell: renderActionsCell,\n  getApplyQuickFilterFn: () => null\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}