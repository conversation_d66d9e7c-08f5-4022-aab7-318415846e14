{"ast": null, "code": "// Non printable keys have a name, for example \"ArrowR<PERSON>\", see the whole list:\n// https://developer.mozilla.org/en-US/docs/Web/API/UI_Events/Keyboard_event_key_values\n// So event.key.length === 1 is often enough.\n//\n// However, we also need to ignore shortcuts, for example: select all:\n// - Windows: Ctrl+A, event.ctrlKey is true\n// - macOS: ⌘ Command+A, event.metaKey is true\nexport function isPrintableKey(event) {\n  return event.key.length === 1 && !event.ctrlKey && !event.metaKey;\n}\nexport const GRID_MULTIPLE_SELECTION_KEYS = ['Meta', 'Control', 'Shift'];\nexport const GRID_CELL_EXIT_EDIT_MODE_KEYS = ['Enter', 'Escape', 'Tab'];\nexport const GRID_CELL_EDIT_COMMIT_KEYS = ['Enter', 'Tab'];\nexport const isMultipleKey = key => GRID_MULTIPLE_SELECTION_KEYS.indexOf(key) > -1;\nexport const isCellEnterEditModeKeys = event => isPrintableKey(event) || event.key === 'Enter' || event.key === 'Backspace' || event.key === 'Delete';\nexport const isCellExitEditModeKeys = key => GRID_CELL_EXIT_EDIT_MODE_KEYS.indexOf(key) > -1;\nexport const isCellEditCommitKeys = key => GRID_CELL_EDIT_COMMIT_KEYS.indexOf(key) > -1;\nexport const isNavigationKey = key => key.indexOf('Arrow') === 0 || key.indexOf('Page') === 0 || key === ' ' || key === 'Home' || key === 'End';\nexport const isKeyboardEvent = event => !!event.key;\nexport const isHideMenuKey = key => key === 'Tab' || key === 'Escape';\n\n// In theory, on macOS, ctrl + v doesn't trigger a paste, so the function should return false.\n// However, maybe it's overkill to fix, so let's be lazy.\nexport function isPasteShortcut(event) {\n  return (event.ctrlKey || event.metaKey) &&\n  // We can't use event.code === 'KeyV' as event.code assumes a QWERTY keyboard layout,\n  // for example, it would be another letter on a Dvorak physical keyboard.\n  // We can't use event.key === 'v' as event.key is not stable with key modifiers and keyboard layouts,\n  // for example, it would be ה on a Hebrew keyboard layout.\n  // https://github.com/w3c/uievents/issues/377 could be a long-term solution\n  String.fromCharCode(event.keyCode) === 'V' && !event.shiftKey && !event.altKey;\n}\n\n// Checks if the keyboard event corresponds to the copy shortcut (CTRL+C or CMD+C) across different localization keyboards.\nexport function isCopyShortcut(event) {\n  return (event.ctrlKey || event.metaKey) && String.fromCharCode(event.keyCode) === 'C' && !event.shiftKey && !event.altKey;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}