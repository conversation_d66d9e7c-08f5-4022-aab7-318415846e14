{"ast": null, "code": "import { gridColumnDefinitionsSelector, gridVisibleColumnDefinitionsSelector } from \"../columns/index.js\";\nimport { gridFilteredSortedRowIdsSelector } from \"../filter/index.js\";\nimport { gridPinnedRowsSelector, gridRowTreeSelector } from \"../rows/gridRowsSelector.js\";\nimport { gridRowSelectionCountSelector, gridRowSelectionIdsSelector } from \"../rowSelection/gridRowSelectionSelector.js\";\nexport const getColumnsToExport = ({\n  apiRef,\n  options\n}) => {\n  const columns = gridColumnDefinitionsSelector(apiRef);\n  if (options.fields) {\n    return options.fields.reduce((currentColumns, field) => {\n      const column = columns.find(col => col.field === field);\n      if (column) {\n        currentColumns.push(column);\n      }\n      return currentColumns;\n    }, []);\n  }\n  const validColumns = options.allColumns ? columns : gridVisibleColumnDefinitionsSelector(apiRef);\n  return validColumns.filter(column => !column.disableExport);\n};\nexport const defaultGetRowsToExport = ({\n  apiRef\n}) => {\n  const filteredSortedRowIds = gridFilteredSortedRowIdsSelector(apiRef);\n  const rowTree = gridRowTreeSelector(apiRef);\n  const selectedRowsCount = gridRowSelectionCountSelector(apiRef);\n  const bodyRows = filteredSortedRowIds.filter(id => rowTree[id].type !== 'footer');\n  const pinnedRows = gridPinnedRowsSelector(apiRef);\n  const topPinnedRowsIds = pinnedRows?.top?.map(row => row.id) || [];\n  const bottomPinnedRowsIds = pinnedRows?.bottom?.map(row => row.id) || [];\n  bodyRows.unshift(...topPinnedRowsIds);\n  bodyRows.push(...bottomPinnedRowsIds);\n  if (selectedRowsCount > 0) {\n    const selectedRows = gridRowSelectionIdsSelector(apiRef);\n    return bodyRows.filter(id => selectedRows.has(id));\n  }\n  return bodyRows;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}