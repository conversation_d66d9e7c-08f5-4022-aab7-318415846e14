{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { createSelector, createSelectorMemoized } from \"../../../utils/createSelector.js\";\nimport { gridVisibleColumnDefinitionsSelector } from \"../columns/gridColumnsSelector.js\";\nimport { gridRenderContextSelector } from \"./gridVirtualizationSelectors.js\";\nimport { gridFocusCellSelector } from \"../focus/index.js\";\nimport { gridVisibleRowsSelector } from \"../pagination/index.js\";\nconst gridIsFocusedCellOutOfContext = createSelector(gridFocusCellSelector, gridRenderContextSelector, gridVisibleRowsSelector, gridVisibleColumnDefinitionsSelector, (focusedCell, renderContext, currentPage, visibleColumns) => {\n  if (!focusedCell) {\n    return false;\n  }\n  const rowIndex = currentPage.rowIdToIndexMap.get(focusedCell.id);\n  const columnIndex = visibleColumns.slice(renderContext.firstColumnIndex, renderContext.lastColumnIndex).findIndex(column => column.field === focusedCell.field);\n  const isInRenderContext = rowIndex !== undefined && columnIndex !== -1 && rowIndex >= renderContext.firstRowIndex && rowIndex <= renderContext.lastRowIndex;\n  return !isInRenderContext;\n});\nexport const gridFocusedVirtualCellSelector = createSelectorMemoized(gridIsFocusedCellOutOfContext, gridVisibleColumnDefinitionsSelector, gridVisibleRowsSelector, gridFocusCellSelector, (isFocusedCellOutOfRenderContext, visibleColumns, currentPage, focusedCell) => {\n  if (!isFocusedCellOutOfRenderContext) {\n    return null;\n  }\n  const rowIndex = currentPage.rowIdToIndexMap.get(focusedCell.id);\n  if (rowIndex === undefined) {\n    return null;\n  }\n  const columnIndex = visibleColumns.findIndex(column => column.field === focusedCell.field);\n  if (columnIndex === -1) {\n    return null;\n  }\n  return _extends({}, focusedCell, {\n    rowIndex,\n    columnIndex\n  });\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}