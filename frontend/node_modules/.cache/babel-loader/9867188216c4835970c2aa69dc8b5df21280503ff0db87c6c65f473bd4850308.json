{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { GridColumnMenuHideItem } from \"./GridColumnMenuHideItem.js\";\nimport { GridColumnMenuManageItem } from \"./GridColumnMenuManageItem.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction GridColumnMenuColumnsItem(props) {\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(GridColumnMenuHideItem, _extends({}, props)), /*#__PURE__*/_jsx(GridColumnMenuManageItem, _extends({}, props))]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GridColumnMenuColumnsItem.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  colDef: PropTypes.object.isRequired,\n  onClick: PropTypes.func.isRequired\n} : void 0;\nexport { GridColumnMenuColumnsItem };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}