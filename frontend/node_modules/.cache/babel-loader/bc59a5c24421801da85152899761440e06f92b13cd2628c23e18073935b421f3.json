{"ast": null, "code": "// A guide to feature toggling (deprecated)\n//\n// The feature toggle is:\n// - independent from the NODE_ENV\n// - isn't pruning code in production, as the objective is to eventually ship the code.\n// - doesn't allow to cherry-pick which feature to enable\n//\n// By default, the experimental features are only enabled in:\n// - the local environment\n// - the pull request previews\n//\n// Reviewers can force the value with the local storage and the GRID_EXPERIMENTAL_ENABLED key:\n// - 'true' => force it to be enabled\n// - 'false' => force it to be disabled\n//\n// Developers (users) are discouraged to enable the experimental feature by setting the GRID_EXPERIMENTAL_ENABLED env.\n// Instead, prefer exposing experimental APIs, for instance, a prop or a new `unstable_` module.\n\nexport const GRID_EXPERIMENTAL_ENABLED = false;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}