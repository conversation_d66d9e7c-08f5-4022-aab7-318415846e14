{"ast": null, "code": "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"item\", \"applyValue\", \"type\", \"apiRef\", \"focusElementRef\", \"tabIndex\", \"isFilterActive\", \"clearButton\", \"headerFilterMenu\", \"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport useId from '@mui/utils/useId';\nimport { useGridRootProps } from \"../../../hooks/utils/useGridRootProps.js\";\nimport { getValueFromValueOptions, getValueOptions, isSingleSelectColDef } from \"./filterPanelUtils.js\";\nimport { createElement as _createElement } from \"react\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst renderSingleSelectOptions = ({\n  column,\n  OptionComponent,\n  getOptionLabel,\n  getOptionValue,\n  isSelectNative,\n  baseSelectOptionProps\n}) => {\n  const iterableColumnValues = ['', ...(getValueOptions(column) || [])];\n  return iterableColumnValues.map(option => {\n    const value = getOptionValue(option);\n    let label = getOptionLabel(option);\n    if (label === '') {\n      label = ' '; // To force the height of the empty option\n    }\n    return /*#__PURE__*/_createElement(OptionComponent, _extends({}, baseSelectOptionProps, {\n      native: isSelectNative,\n      key: value,\n      value: value\n    }), label);\n  });\n};\nfunction GridFilterInputSingleSelect(props) {\n  const {\n      item,\n      applyValue,\n      type,\n      apiRef,\n      focusElementRef,\n      tabIndex,\n      clearButton,\n      headerFilterMenu,\n      slotProps\n    } = props,\n    others = _objectWithoutPropertiesLoose(props, _excluded);\n  const filterValue = item.value ?? '';\n  const id = useId();\n  const labelId = useId();\n  const rootProps = useGridRootProps();\n  const isSelectNative = rootProps.slotProps?.baseSelect?.native ?? false;\n  let resolvedColumn = null;\n  if (item.field) {\n    const column = apiRef.current.getColumn(item.field);\n    if (isSingleSelectColDef(column)) {\n      resolvedColumn = column;\n    }\n  }\n  const getOptionValue = resolvedColumn?.getOptionValue;\n  const getOptionLabel = resolvedColumn?.getOptionLabel;\n  const currentValueOptions = React.useMemo(() => {\n    return getValueOptions(resolvedColumn);\n  }, [resolvedColumn]);\n  const onFilterChange = React.useCallback(event => {\n    let value = event.target.value;\n\n    // NativeSelect casts the value to a string.\n    value = getValueFromValueOptions(value, currentValueOptions, getOptionValue);\n    applyValue(_extends({}, item, {\n      value\n    }));\n  }, [currentValueOptions, getOptionValue, applyValue, item]);\n  if (!isSingleSelectColDef(resolvedColumn)) {\n    return null;\n  }\n  const label = slotProps?.root.label ?? apiRef.current.getLocaleText('filterPanelInputLabel');\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(rootProps.slots.baseSelect, _extends({\n      fullWidth: true,\n      id: id,\n      label: label,\n      labelId: labelId,\n      value: filterValue,\n      onChange: onFilterChange,\n      slotProps: {\n        htmlInput: _extends({\n          tabIndex,\n          ref: focusElementRef,\n          type: type || 'text',\n          placeholder: slotProps?.root.placeholder ?? apiRef.current.getLocaleText('filterPanelInputPlaceholder')\n        }, slotProps?.root.slotProps?.htmlInput)\n      },\n      native: isSelectNative\n    }, rootProps.slotProps?.baseSelect, others, slotProps?.root, {\n      children: renderSingleSelectOptions({\n        column: resolvedColumn,\n        OptionComponent: rootProps.slots.baseSelectOption,\n        getOptionLabel,\n        getOptionValue,\n        isSelectNative,\n        baseSelectOptionProps: rootProps.slotProps?.baseSelectOption\n      })\n    })), headerFilterMenu, clearButton]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GridFilterInputSingleSelect.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  apiRef: PropTypes.shape({\n    current: PropTypes.object.isRequired\n  }).isRequired,\n  applyValue: PropTypes.func.isRequired,\n  className: PropTypes.string,\n  clearButton: PropTypes.node,\n  disabled: PropTypes.bool,\n  focusElementRef: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.func, PropTypes.object]),\n  headerFilterMenu: PropTypes.node,\n  inputRef: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n    current: (props, propName) => {\n      if (props[propName] == null) {\n        return null;\n      }\n      if (typeof props[propName] !== 'object' || props[propName].nodeType !== 1) {\n        return new Error(`Expected prop '${propName}' to be of type Element`);\n      }\n      return null;\n    }\n  })]),\n  /**\n   * It is `true` if the filter either has a value or an operator with no value\n   * required is selected (for example `isEmpty`)\n   */\n  isFilterActive: PropTypes.bool,\n  item: PropTypes.shape({\n    field: PropTypes.string.isRequired,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    operator: PropTypes.string.isRequired,\n    value: PropTypes.any\n  }).isRequired,\n  onBlur: PropTypes.func,\n  onFocus: PropTypes.func,\n  slotProps: PropTypes.object,\n  tabIndex: PropTypes.number,\n  type: PropTypes.oneOf(['singleSelect'])\n} : void 0;\nexport { GridFilterInputSingleSelect };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}