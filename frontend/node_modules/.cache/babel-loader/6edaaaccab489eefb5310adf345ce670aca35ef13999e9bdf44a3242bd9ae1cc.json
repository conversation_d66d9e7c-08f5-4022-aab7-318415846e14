{"ast": null, "code": "import * as React from 'react';\nexport const useGridLocaleText = (apiRef, props) => {\n  const getLocaleText = React.useCallback(key => {\n    if (props.localeText[key] == null) {\n      throw new Error(`Missing translation for key ${key}.`);\n    }\n    return props.localeText[key];\n  }, [props.localeText]);\n  apiRef.current.register('public', {\n    getLocaleText\n  });\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}