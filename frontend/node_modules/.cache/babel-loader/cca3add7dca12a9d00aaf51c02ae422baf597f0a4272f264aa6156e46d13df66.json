{"ast": null, "code": "import { createSelector, createRootSelector } from \"../../../utils/createSelector.js\";\nexport const gridHeaderFilteringStateSelector = createRootSelector(state => state.headerFiltering);\nexport const gridHeaderFilteringEnabledSelector = createSelector(gridHeaderFilteringStateSelector,\n// No initialization in MIT, so we need to default to false to be used by `getTotalHeaderHeight`\nheaderFilteringState => headerFilteringState?.enabled ?? false);\nexport const gridHeaderFilteringEditFieldSelector = createSelector(gridHeaderFilteringStateSelector, headerFilteringState => headerFilteringState.editing);\nexport const gridHeaderFilteringMenuSelector = createSelector(gridHeaderFilteringStateSelector, headerFilteringState => headerFilteringState.menuOpen);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}