{"ast": null, "code": "import * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { useGridApiContext } from \"../../../../hooks/utils/useGridApiContext.js\";\nimport { useGridRootProps } from \"../../../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction GridColumnMenuFilterItem(props) {\n  const {\n    colDef,\n    onClick\n  } = props;\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const showFilter = React.useCallback(event => {\n    onClick(event);\n    apiRef.current.showFilterPanel(colDef.field);\n  }, [apiRef, colDef.field, onClick]);\n  if (rootProps.disableColumnFilter || !colDef.filterable) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(rootProps.slots.baseMenuItem, {\n    onClick: showFilter,\n    iconStart: /*#__PURE__*/_jsx(rootProps.slots.columnMenuFilterIcon, {\n      fontSize: \"small\"\n    }),\n    children: apiRef.current.getLocaleText('columnMenuFilter')\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GridColumnMenuFilterItem.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  colDef: PropTypes.object.isRequired,\n  onClick: PropTypes.func.isRequired\n} : void 0;\nexport { GridColumnMenuFilterItem };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}