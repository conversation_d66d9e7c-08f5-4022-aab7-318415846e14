{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/Users.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Paper, Typography, Button, TextField, Chip, Dialog, DialogTitle, DialogContent, DialogActions, FormControl, InputLabel, Select, MenuItem, FormControlLabel, Switch, Alert } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, Search as SearchIcon } from '@mui/icons-material';\nimport { DataGrid, GridActionsCellItem } from '@mui/x-data-grid';\nimport { toast } from 'react-toastify';\nimport { useAuth } from '../contexts/AuthContext';\nimport apiService from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Users = () => {\n  _s();\n  const {\n    hasPermission\n  } = useAuth();\n  const [users, setUsers] = useState([]);\n  const [roles, setRoles] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [total, setTotal] = useState(0);\n  const [page, setPage] = useState(0);\n  const [pageSize, setPageSize] = useState(20);\n  const [search, setSearch] = useState('');\n\n  // Dialog states\n  const [openDialog, setOpenDialog] = useState(false);\n  const [editingUser, setEditingUser] = useState(null);\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    first_name: '',\n    last_name: '',\n    is_active: true,\n    role_id: undefined\n  });\n  const [formErrors, setFormErrors] = useState('');\n\n  // Charger les données\n  useEffect(() => {\n    fetchUsers();\n    fetchRoles();\n  }, [page, pageSize, search]);\n  const fetchUsers = async () => {\n    try {\n      setLoading(true);\n      const response = await apiService.getUsers(page + 1,\n      // DataGrid utilise 0-based, API utilise 1-based\n      pageSize, search || undefined);\n      setUsers(response.items);\n      setTotal(response.total);\n    } catch (error) {\n      console.error('Erreur lors du chargement des utilisateurs:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchRoles = async () => {\n    try {\n      const rolesData = await apiService.getAllRoles();\n      setRoles(rolesData);\n    } catch (error) {\n      console.error('Erreur lors du chargement des rôles:', error);\n    }\n  };\n  const handleSearch = event => {\n    setSearch(event.target.value);\n    setPage(0); // Reset to first page when searching\n  };\n  const handleOpenDialog = user => {\n    if (user) {\n      setEditingUser(user);\n      setFormData({\n        email: user.email,\n        password: '',\n        // Ne pas pré-remplir le mot de passe\n        first_name: user.first_name || '',\n        last_name: user.last_name || '',\n        is_active: user.is_active,\n        role_id: user.role_id || undefined\n      });\n    } else {\n      setEditingUser(null);\n      setFormData({\n        email: '',\n        password: '',\n        first_name: '',\n        last_name: '',\n        is_active: true,\n        role_id: undefined\n      });\n    }\n    setFormErrors('');\n    setOpenDialog(true);\n  };\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setEditingUser(null);\n    setFormErrors('');\n  };\n  const handleFormChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    setFormErrors('');\n  };\n  const handleSubmit = async () => {\n    try {\n      // Validation\n      if (!formData.email) {\n        setFormErrors('L\\'email est requis.');\n        return;\n      }\n      if (!editingUser && !formData.password) {\n        setFormErrors('Le mot de passe est requis pour un nouvel utilisateur.');\n        return;\n      }\n      if (editingUser) {\n        // Mise à jour\n        const updateData = {\n          email: formData.email,\n          first_name: formData.first_name || undefined,\n          last_name: formData.last_name || undefined,\n          is_active: formData.is_active,\n          role_id: formData.role_id || undefined\n        };\n        if (formData.password) {\n          updateData.password = formData.password;\n        }\n        await apiService.updateUser(editingUser.id, updateData);\n        toast.success('Utilisateur mis à jour avec succès !');\n      } else {\n        // Création\n        await apiService.createUser(formData);\n        toast.success('Utilisateur créé avec succès !');\n      }\n      handleCloseDialog();\n      fetchUsers();\n    } catch (error) {\n      var _error$response, _error$response$data;\n      setFormErrors(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || 'Une erreur est survenue.');\n    }\n  };\n  const handleDelete = async userId => {\n    if (window.confirm('Êtes-vous sûr de vouloir supprimer cet utilisateur ?')) {\n      try {\n        await apiService.deleteUser(userId);\n        toast.success('Utilisateur supprimé avec succès !');\n        fetchUsers();\n      } catch (error) {\n        var _error$response2, _error$response2$data;\n        toast.error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || 'Erreur lors de la suppression.');\n      }\n    }\n  };\n  const columns = [{\n    field: 'id',\n    headerName: 'ID',\n    width: 70\n  }, {\n    field: 'full_name',\n    headerName: 'Nom complet',\n    width: 200,\n    renderCell: params => /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        children: params.row.full_name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        color: \"text.secondary\",\n        children: params.row.email\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 9\n    }, this)\n  }, {\n    field: 'role',\n    headerName: 'Rôle',\n    width: 150,\n    renderCell: params => {\n      var _params$row$role;\n      return /*#__PURE__*/_jsxDEV(Chip, {\n        label: ((_params$row$role = params.row.role) === null || _params$row$role === void 0 ? void 0 : _params$row$role.name) || 'Aucun rôle',\n        size: \"small\",\n        color: params.row.role ? 'primary' : 'default',\n        variant: \"outlined\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this);\n    }\n  }, {\n    field: 'is_active',\n    headerName: 'Statut',\n    width: 120,\n    renderCell: params => /*#__PURE__*/_jsxDEV(Chip, {\n      label: params.row.is_active ? 'Actif' : 'Inactif',\n      size: \"small\",\n      color: params.row.is_active ? 'success' : 'default'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 9\n    }, this)\n  }, {\n    field: 'created_at',\n    headerName: 'Créé le',\n    width: 150,\n    renderCell: params => new Date(params.row.created_at).toLocaleDateString('fr-FR')\n  }, {\n    field: 'actions',\n    type: 'actions',\n    headerName: 'Actions',\n    width: 120,\n    getActions: params => {\n      const actions = [];\n      if (hasPermission('user:update')) {\n        actions.push(/*#__PURE__*/_jsxDEV(GridActionsCellItem, {\n          icon: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 21\n          }, this),\n          label: \"Modifier\",\n          onClick: () => handleOpenDialog(params.row)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 13\n        }, this));\n      }\n      if (hasPermission('user:delete')) {\n        actions.push(/*#__PURE__*/_jsxDEV(GridActionsCellItem, {\n          icon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 21\n          }, this),\n          label: \"Supprimer\",\n          onClick: () => handleDelete(params.row.id)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 13\n        }, this));\n      }\n      return actions;\n    }\n  }];\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n      mb: 3,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        children: \"Gestion des utilisateurs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 9\n      }, this), hasPermission('user:create') && /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 24\n        }, this),\n        onClick: () => handleOpenDialog(),\n        children: \"Nouvel utilisateur\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 2,\n        mb: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(TextField, {\n        fullWidth: true,\n        placeholder: \"Rechercher par email, pr\\xE9nom ou nom...\",\n        value: search,\n        onChange: handleSearch,\n        InputProps: {\n          startAdornment: /*#__PURE__*/_jsxDEV(SearchIcon, {\n            sx: {\n              mr: 1,\n              color: 'text.secondary'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 29\n          }, this)\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 291,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        height: 600,\n        width: '100%'\n      },\n      children: /*#__PURE__*/_jsxDEV(DataGrid, {\n        rows: users,\n        columns: columns,\n        loading: loading,\n        pagination: true,\n        paginationMode: \"server\",\n        rowCount: total,\n        page: page,\n        pageSize: pageSize,\n        onPageChange: setPage,\n        onPageSizeChange: setPageSize,\n        rowsPerPageOptions: [10, 20, 50],\n        disableSelectionOnClick: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDialog,\n      onClose: handleCloseDialog,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: editingUser ? 'Modifier l\\'utilisateur' : 'Nouvel utilisateur'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [formErrors && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          sx: {\n            mb: 2\n          },\n          children: formErrors\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Email\",\n          type: \"email\",\n          value: formData.email,\n          onChange: e => handleFormChange('email', e.target.value),\n          margin: \"normal\",\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: editingUser ? 'Nouveau mot de passe (optionnel)' : 'Mot de passe',\n          type: \"password\",\n          value: formData.password,\n          onChange: e => handleFormChange('password', e.target.value),\n          margin: \"normal\",\n          required: !editingUser\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Pr\\xE9nom\",\n          value: formData.first_name,\n          onChange: e => handleFormChange('first_name', e.target.value),\n          margin: \"normal\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Nom de famille\",\n          value: formData.last_name,\n          onChange: e => handleFormChange('last_name', e.target.value),\n          margin: \"normal\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n          fullWidth: true,\n          margin: \"normal\",\n          children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n            children: \"R\\xF4le\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: formData.role_id || '',\n            onChange: e => handleFormChange('role_id', e.target.value || undefined),\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"\",\n              children: \"Aucun r\\xF4le\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 15\n            }, this), roles.map(role => /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: role.id,\n              children: role.name\n            }, role.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n          control: /*#__PURE__*/_jsxDEV(Switch, {\n            checked: formData.is_active,\n            onChange: e => handleFormChange('is_active', e.target.checked)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 15\n          }, this),\n          label: \"Utilisateur actif\",\n          sx: {\n            mt: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 384,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseDialog,\n          children: \"Annuler\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmit,\n          variant: \"contained\",\n          children: editingUser ? 'Mettre à jour' : 'Créer'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 321,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 276,\n    columnNumber: 5\n  }, this);\n};\n_s(Users, \"ccDXltMBP0MIksmiz/lpT97Jq4k=\", false, function () {\n  return [useAuth];\n});\n_c = Users;\nexport default Users;\nvar _c;\n$RefreshReg$(_c, \"Users\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Paper", "Typography", "<PERSON><PERSON>", "TextField", "Chip", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "FormControl", "InputLabel", "Select", "MenuItem", "FormControlLabel", "Switch", "<PERSON><PERSON>", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Search", "SearchIcon", "DataGrid", "GridActionsCellItem", "toast", "useAuth", "apiService", "jsxDEV", "_jsxDEV", "Users", "_s", "hasPermission", "users", "setUsers", "roles", "setRoles", "loading", "setLoading", "total", "setTotal", "page", "setPage", "pageSize", "setPageSize", "search", "setSearch", "openDialog", "setOpenDialog", "editingUser", "setEditingUser", "formData", "setFormData", "email", "password", "first_name", "last_name", "is_active", "role_id", "undefined", "formErrors", "setFormErrors", "fetchUsers", "fetchRoles", "response", "getUsers", "items", "error", "console", "rolesData", "getAllRoles", "handleSearch", "event", "target", "value", "handleOpenDialog", "user", "handleCloseDialog", "handleFormChange", "field", "prev", "handleSubmit", "updateData", "updateUser", "id", "success", "createUser", "_error$response", "_error$response$data", "data", "detail", "handleDelete", "userId", "window", "confirm", "deleteUser", "_error$response2", "_error$response2$data", "columns", "headerName", "width", "renderCell", "params", "children", "variant", "row", "full_name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "_params$row$role", "label", "role", "name", "size", "Date", "created_at", "toLocaleDateString", "type", "getActions", "actions", "push", "icon", "onClick", "display", "justifyContent", "alignItems", "mb", "startIcon", "sx", "p", "fullWidth", "placeholder", "onChange", "InputProps", "startAdornment", "mr", "height", "rows", "pagination", "paginationMode", "rowCount", "onPageChange", "onPageSizeChange", "rowsPerPageOptions", "disableSelectionOnClick", "open", "onClose", "max<PERSON><PERSON><PERSON>", "severity", "e", "margin", "required", "map", "control", "checked", "mt", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/Users.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Paper,\n  Typography,\n  Button,\n  TextField,\n  IconButton,\n  Chip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  FormControlLabel,\n  Switch,\n  Alert,\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Search as SearchIcon,\n} from '@mui/icons-material';\nimport { DataGrid, GridColDef, GridActionsCellItem } from '@mui/x-data-grid';\nimport { toast } from 'react-toastify';\n\nimport { useAuth } from '../contexts/AuthContext';\nimport apiService from '../services/api';\nimport { User, UserCreate, UserUpdate, Role, PaginatedResponse } from '../types';\n\nconst Users: React.FC = () => {\n  const { hasPermission } = useAuth();\n  \n  const [users, setUsers] = useState<User[]>([]);\n  const [roles, setRoles] = useState<Role[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [total, setTotal] = useState(0);\n  const [page, setPage] = useState(0);\n  const [pageSize, setPageSize] = useState(20);\n  const [search, setSearch] = useState('');\n  \n  // Dialog states\n  const [openDialog, setOpenDialog] = useState(false);\n  const [editingUser, setEditingUser] = useState<User | null>(null);\n  const [formData, setFormData] = useState<UserCreate>({\n    email: '',\n    password: '',\n    first_name: '',\n    last_name: '',\n    is_active: true,\n    role_id: undefined,\n  });\n  const [formErrors, setFormErrors] = useState<string>('');\n\n  // Charger les données\n  useEffect(() => {\n    fetchUsers();\n    fetchRoles();\n  }, [page, pageSize, search]);\n\n  const fetchUsers = async () => {\n    try {\n      setLoading(true);\n      const response: PaginatedResponse<User> = await apiService.getUsers(\n        page + 1, // DataGrid utilise 0-based, API utilise 1-based\n        pageSize,\n        search || undefined\n      );\n      setUsers(response.items);\n      setTotal(response.total);\n    } catch (error) {\n      console.error('Erreur lors du chargement des utilisateurs:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchRoles = async () => {\n    try {\n      const rolesData = await apiService.getAllRoles();\n      setRoles(rolesData);\n    } catch (error) {\n      console.error('Erreur lors du chargement des rôles:', error);\n    }\n  };\n\n  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {\n    setSearch(event.target.value);\n    setPage(0); // Reset to first page when searching\n  };\n\n  const handleOpenDialog = (user?: User) => {\n    if (user) {\n      setEditingUser(user);\n      setFormData({\n        email: user.email,\n        password: '', // Ne pas pré-remplir le mot de passe\n        first_name: user.first_name || '',\n        last_name: user.last_name || '',\n        is_active: user.is_active,\n        role_id: user.role_id || undefined,\n      });\n    } else {\n      setEditingUser(null);\n      setFormData({\n        email: '',\n        password: '',\n        first_name: '',\n        last_name: '',\n        is_active: true,\n        role_id: undefined,\n      });\n    }\n    setFormErrors('');\n    setOpenDialog(true);\n  };\n\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setEditingUser(null);\n    setFormErrors('');\n  };\n\n  const handleFormChange = (field: keyof UserCreate, value: any) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value,\n    }));\n    setFormErrors('');\n  };\n\n  const handleSubmit = async () => {\n    try {\n      // Validation\n      if (!formData.email) {\n        setFormErrors('L\\'email est requis.');\n        return;\n      }\n      \n      if (!editingUser && !formData.password) {\n        setFormErrors('Le mot de passe est requis pour un nouvel utilisateur.');\n        return;\n      }\n\n      if (editingUser) {\n        // Mise à jour\n        const updateData: UserUpdate = {\n          email: formData.email,\n          first_name: formData.first_name || undefined,\n          last_name: formData.last_name || undefined,\n          is_active: formData.is_active,\n          role_id: formData.role_id || undefined,\n        };\n        \n        if (formData.password) {\n          updateData.password = formData.password;\n        }\n\n        await apiService.updateUser(editingUser.id, updateData);\n        toast.success('Utilisateur mis à jour avec succès !');\n      } else {\n        // Création\n        await apiService.createUser(formData);\n        toast.success('Utilisateur créé avec succès !');\n      }\n\n      handleCloseDialog();\n      fetchUsers();\n    } catch (error: any) {\n      setFormErrors(error.response?.data?.detail || 'Une erreur est survenue.');\n    }\n  };\n\n  const handleDelete = async (userId: number) => {\n    if (window.confirm('Êtes-vous sûr de vouloir supprimer cet utilisateur ?')) {\n      try {\n        await apiService.deleteUser(userId);\n        toast.success('Utilisateur supprimé avec succès !');\n        fetchUsers();\n      } catch (error: any) {\n        toast.error(error.response?.data?.detail || 'Erreur lors de la suppression.');\n      }\n    }\n  };\n\n  const columns: GridColDef[] = [\n    {\n      field: 'id',\n      headerName: 'ID',\n      width: 70,\n    },\n    {\n      field: 'full_name',\n      headerName: 'Nom complet',\n      width: 200,\n      renderCell: (params) => (\n        <Box>\n          <Typography variant=\"body2\">{params.row.full_name}</Typography>\n          <Typography variant=\"caption\" color=\"text.secondary\">\n            {params.row.email}\n          </Typography>\n        </Box>\n      ),\n    },\n    {\n      field: 'role',\n      headerName: 'Rôle',\n      width: 150,\n      renderCell: (params) => (\n        <Chip\n          label={params.row.role?.name || 'Aucun rôle'}\n          size=\"small\"\n          color={params.row.role ? 'primary' : 'default'}\n          variant=\"outlined\"\n        />\n      ),\n    },\n    {\n      field: 'is_active',\n      headerName: 'Statut',\n      width: 120,\n      renderCell: (params) => (\n        <Chip\n          label={params.row.is_active ? 'Actif' : 'Inactif'}\n          size=\"small\"\n          color={params.row.is_active ? 'success' : 'default'}\n        />\n      ),\n    },\n    {\n      field: 'created_at',\n      headerName: 'Créé le',\n      width: 150,\n      renderCell: (params) => (\n        new Date(params.row.created_at).toLocaleDateString('fr-FR')\n      ),\n    },\n    {\n      field: 'actions',\n      type: 'actions',\n      headerName: 'Actions',\n      width: 120,\n      getActions: (params) => {\n        const actions = [];\n        \n        if (hasPermission('user:update')) {\n          actions.push(\n            <GridActionsCellItem\n              icon={<EditIcon />}\n              label=\"Modifier\"\n              onClick={() => handleOpenDialog(params.row)}\n            />\n          );\n        }\n        \n        if (hasPermission('user:delete')) {\n          actions.push(\n            <GridActionsCellItem\n              icon={<DeleteIcon />}\n              label=\"Supprimer\"\n              onClick={() => handleDelete(params.row.id)}\n            />\n          );\n        }\n        \n        return actions;\n      },\n    },\n  ];\n\n  return (\n    <Box>\n      <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={3}>\n        <Typography variant=\"h4\">Gestion des utilisateurs</Typography>\n        \n        {hasPermission('user:create') && (\n          <Button\n            variant=\"contained\"\n            startIcon={<AddIcon />}\n            onClick={() => handleOpenDialog()}\n          >\n            Nouvel utilisateur\n          </Button>\n        )}\n      </Box>\n\n      <Paper sx={{ p: 2, mb: 2 }}>\n        <TextField\n          fullWidth\n          placeholder=\"Rechercher par email, prénom ou nom...\"\n          value={search}\n          onChange={handleSearch}\n          InputProps={{\n            startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,\n          }}\n        />\n      </Paper>\n\n      <Paper sx={{ height: 600, width: '100%' }}>\n        <DataGrid\n          rows={users}\n          columns={columns}\n          loading={loading}\n          pagination\n          paginationMode=\"server\"\n          rowCount={total}\n          page={page}\n          pageSize={pageSize}\n          onPageChange={setPage}\n          onPageSizeChange={setPageSize}\n          rowsPerPageOptions={[10, 20, 50]}\n          disableSelectionOnClick\n        />\n      </Paper>\n\n      {/* Dialog de création/modification */}\n      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n        <DialogTitle>\n          {editingUser ? 'Modifier l\\'utilisateur' : 'Nouvel utilisateur'}\n        </DialogTitle>\n        \n        <DialogContent>\n          {formErrors && (\n            <Alert severity=\"error\" sx={{ mb: 2 }}>\n              {formErrors}\n            </Alert>\n          )}\n          \n          <TextField\n            fullWidth\n            label=\"Email\"\n            type=\"email\"\n            value={formData.email}\n            onChange={(e) => handleFormChange('email', e.target.value)}\n            margin=\"normal\"\n            required\n          />\n          \n          <TextField\n            fullWidth\n            label={editingUser ? 'Nouveau mot de passe (optionnel)' : 'Mot de passe'}\n            type=\"password\"\n            value={formData.password}\n            onChange={(e) => handleFormChange('password', e.target.value)}\n            margin=\"normal\"\n            required={!editingUser}\n          />\n          \n          <TextField\n            fullWidth\n            label=\"Prénom\"\n            value={formData.first_name}\n            onChange={(e) => handleFormChange('first_name', e.target.value)}\n            margin=\"normal\"\n          />\n          \n          <TextField\n            fullWidth\n            label=\"Nom de famille\"\n            value={formData.last_name}\n            onChange={(e) => handleFormChange('last_name', e.target.value)}\n            margin=\"normal\"\n          />\n          \n          <FormControl fullWidth margin=\"normal\">\n            <InputLabel>Rôle</InputLabel>\n            <Select\n              value={formData.role_id || ''}\n              onChange={(e) => handleFormChange('role_id', e.target.value || undefined)}\n            >\n              <MenuItem value=\"\">Aucun rôle</MenuItem>\n              {roles.map((role) => (\n                <MenuItem key={role.id} value={role.id}>\n                  {role.name}\n                </MenuItem>\n              ))}\n            </Select>\n          </FormControl>\n          \n          <FormControlLabel\n            control={\n              <Switch\n                checked={formData.is_active}\n                onChange={(e) => handleFormChange('is_active', e.target.checked)}\n              />\n            }\n            label=\"Utilisateur actif\"\n            sx={{ mt: 2 }}\n          />\n        </DialogContent>\n        \n        <DialogActions>\n          <Button onClick={handleCloseDialog}>Annuler</Button>\n          <Button onClick={handleSubmit} variant=\"contained\">\n            {editingUser ? 'Mettre à jour' : 'Créer'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default Users;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,MAAM,EACNC,SAAS,EAETC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,gBAAgB,EAChBC,MAAM,EACNC,KAAK,QACA,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,MAAM,IAAIC,UAAU,QACf,qBAAqB;AAC5B,SAASC,QAAQ,EAAcC,mBAAmB,QAAQ,kBAAkB;AAC5E,SAASC,KAAK,QAAQ,gBAAgB;AAEtC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,UAAU,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGzC,MAAMC,KAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM;IAAEC;EAAc,CAAC,GAAGN,OAAO,CAAC,CAAC;EAEnC,MAAM,CAACO,KAAK,EAAEC,QAAQ,CAAC,GAAGtC,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACuC,KAAK,EAAEC,QAAQ,CAAC,GAAGxC,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACyC,OAAO,EAAEC,UAAU,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2C,KAAK,EAAEC,QAAQ,CAAC,GAAG5C,QAAQ,CAAC,CAAC,CAAC;EACrC,MAAM,CAAC6C,IAAI,EAAEC,OAAO,CAAC,GAAG9C,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAAC+C,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiD,MAAM,EAAEC,SAAS,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;;EAExC;EACA,MAAM,CAACmD,UAAU,EAAEC,aAAa,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACqD,WAAW,EAAEC,cAAc,CAAC,GAAGtD,QAAQ,CAAc,IAAI,CAAC;EACjE,MAAM,CAACuD,QAAQ,EAAEC,WAAW,CAAC,GAAGxD,QAAQ,CAAa;IACnDyD,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,EAAE;IACdC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAEC;EACX,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGjE,QAAQ,CAAS,EAAE,CAAC;;EAExD;EACAC,SAAS,CAAC,MAAM;IACdiE,UAAU,CAAC,CAAC;IACZC,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACtB,IAAI,EAAEE,QAAQ,EAAEE,MAAM,CAAC,CAAC;EAE5B,MAAMiB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFxB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM0B,QAAiC,GAAG,MAAMrC,UAAU,CAACsC,QAAQ,CACjExB,IAAI,GAAG,CAAC;MAAE;MACVE,QAAQ,EACRE,MAAM,IAAIc,SACZ,CAAC;MACDzB,QAAQ,CAAC8B,QAAQ,CAACE,KAAK,CAAC;MACxB1B,QAAQ,CAACwB,QAAQ,CAACzB,KAAK,CAAC;IAC1B,CAAC,CAAC,OAAO4B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;IACrE,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMyB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMM,SAAS,GAAG,MAAM1C,UAAU,CAAC2C,WAAW,CAAC,CAAC;MAChDlC,QAAQ,CAACiC,SAAS,CAAC;IACrB,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;IAC9D;EACF,CAAC;EAED,MAAMI,YAAY,GAAIC,KAA0C,IAAK;IACnE1B,SAAS,CAAC0B,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;IAC7BhC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC;EAED,MAAMiC,gBAAgB,GAAIC,IAAW,IAAK;IACxC,IAAIA,IAAI,EAAE;MACR1B,cAAc,CAAC0B,IAAI,CAAC;MACpBxB,WAAW,CAAC;QACVC,KAAK,EAAEuB,IAAI,CAACvB,KAAK;QACjBC,QAAQ,EAAE,EAAE;QAAE;QACdC,UAAU,EAAEqB,IAAI,CAACrB,UAAU,IAAI,EAAE;QACjCC,SAAS,EAAEoB,IAAI,CAACpB,SAAS,IAAI,EAAE;QAC/BC,SAAS,EAAEmB,IAAI,CAACnB,SAAS;QACzBC,OAAO,EAAEkB,IAAI,CAAClB,OAAO,IAAIC;MAC3B,CAAC,CAAC;IACJ,CAAC,MAAM;MACLT,cAAc,CAAC,IAAI,CAAC;MACpBE,WAAW,CAAC;QACVC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE,EAAE;QACZC,UAAU,EAAE,EAAE;QACdC,SAAS,EAAE,EAAE;QACbC,SAAS,EAAE,IAAI;QACfC,OAAO,EAAEC;MACX,CAAC,CAAC;IACJ;IACAE,aAAa,CAAC,EAAE,CAAC;IACjBb,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAM6B,iBAAiB,GAAGA,CAAA,KAAM;IAC9B7B,aAAa,CAAC,KAAK,CAAC;IACpBE,cAAc,CAAC,IAAI,CAAC;IACpBW,aAAa,CAAC,EAAE,CAAC;EACnB,CAAC;EAED,MAAMiB,gBAAgB,GAAGA,CAACC,KAAuB,EAAEL,KAAU,KAAK;IAChEtB,WAAW,CAAC4B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACD,KAAK,GAAGL;IACX,CAAC,CAAC,CAAC;IACHb,aAAa,CAAC,EAAE,CAAC;EACnB,CAAC;EAED,MAAMoB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF;MACA,IAAI,CAAC9B,QAAQ,CAACE,KAAK,EAAE;QACnBQ,aAAa,CAAC,sBAAsB,CAAC;QACrC;MACF;MAEA,IAAI,CAACZ,WAAW,IAAI,CAACE,QAAQ,CAACG,QAAQ,EAAE;QACtCO,aAAa,CAAC,wDAAwD,CAAC;QACvE;MACF;MAEA,IAAIZ,WAAW,EAAE;QACf;QACA,MAAMiC,UAAsB,GAAG;UAC7B7B,KAAK,EAAEF,QAAQ,CAACE,KAAK;UACrBE,UAAU,EAAEJ,QAAQ,CAACI,UAAU,IAAII,SAAS;UAC5CH,SAAS,EAAEL,QAAQ,CAACK,SAAS,IAAIG,SAAS;UAC1CF,SAAS,EAAEN,QAAQ,CAACM,SAAS;UAC7BC,OAAO,EAAEP,QAAQ,CAACO,OAAO,IAAIC;QAC/B,CAAC;QAED,IAAIR,QAAQ,CAACG,QAAQ,EAAE;UACrB4B,UAAU,CAAC5B,QAAQ,GAAGH,QAAQ,CAACG,QAAQ;QACzC;QAEA,MAAM3B,UAAU,CAACwD,UAAU,CAAClC,WAAW,CAACmC,EAAE,EAAEF,UAAU,CAAC;QACvDzD,KAAK,CAAC4D,OAAO,CAAC,sCAAsC,CAAC;MACvD,CAAC,MAAM;QACL;QACA,MAAM1D,UAAU,CAAC2D,UAAU,CAACnC,QAAQ,CAAC;QACrC1B,KAAK,CAAC4D,OAAO,CAAC,gCAAgC,CAAC;MACjD;MAEAR,iBAAiB,CAAC,CAAC;MACnBf,UAAU,CAAC,CAAC;IACd,CAAC,CAAC,OAAOK,KAAU,EAAE;MAAA,IAAAoB,eAAA,EAAAC,oBAAA;MACnB3B,aAAa,CAAC,EAAA0B,eAAA,GAAApB,KAAK,CAACH,QAAQ,cAAAuB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBE,IAAI,cAAAD,oBAAA,uBAApBA,oBAAA,CAAsBE,MAAM,KAAI,0BAA0B,CAAC;IAC3E;EACF,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOC,MAAc,IAAK;IAC7C,IAAIC,MAAM,CAACC,OAAO,CAAC,sDAAsD,CAAC,EAAE;MAC1E,IAAI;QACF,MAAMnE,UAAU,CAACoE,UAAU,CAACH,MAAM,CAAC;QACnCnE,KAAK,CAAC4D,OAAO,CAAC,oCAAoC,CAAC;QACnDvB,UAAU,CAAC,CAAC;MACd,CAAC,CAAC,OAAOK,KAAU,EAAE;QAAA,IAAA6B,gBAAA,EAAAC,qBAAA;QACnBxE,KAAK,CAAC0C,KAAK,CAAC,EAAA6B,gBAAA,GAAA7B,KAAK,CAACH,QAAQ,cAAAgC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBP,IAAI,cAAAQ,qBAAA,uBAApBA,qBAAA,CAAsBP,MAAM,KAAI,gCAAgC,CAAC;MAC/E;IACF;EACF,CAAC;EAED,MAAMQ,OAAqB,GAAG,CAC5B;IACEnB,KAAK,EAAE,IAAI;IACXoB,UAAU,EAAE,IAAI;IAChBC,KAAK,EAAE;EACT,CAAC,EACD;IACErB,KAAK,EAAE,WAAW;IAClBoB,UAAU,EAAE,aAAa;IACzBC,KAAK,EAAE,GAAG;IACVC,UAAU,EAAGC,MAAM,iBACjBzE,OAAA,CAAC/B,GAAG;MAAAyG,QAAA,gBACF1E,OAAA,CAAC7B,UAAU;QAACwG,OAAO,EAAC,OAAO;QAAAD,QAAA,EAAED,MAAM,CAACG,GAAG,CAACC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC/DjF,OAAA,CAAC7B,UAAU;QAACwG,OAAO,EAAC,SAAS;QAACO,KAAK,EAAC,gBAAgB;QAAAR,QAAA,EACjDD,MAAM,CAACG,GAAG,CAACpD;MAAK;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAET,CAAC,EACD;IACE/B,KAAK,EAAE,MAAM;IACboB,UAAU,EAAE,MAAM;IAClBC,KAAK,EAAE,GAAG;IACVC,UAAU,EAAGC,MAAM;MAAA,IAAAU,gBAAA;MAAA,oBACjBnF,OAAA,CAAC1B,IAAI;QACH8G,KAAK,EAAE,EAAAD,gBAAA,GAAAV,MAAM,CAACG,GAAG,CAACS,IAAI,cAAAF,gBAAA,uBAAfA,gBAAA,CAAiBG,IAAI,KAAI,YAAa;QAC7CC,IAAI,EAAC,OAAO;QACZL,KAAK,EAAET,MAAM,CAACG,GAAG,CAACS,IAAI,GAAG,SAAS,GAAG,SAAU;QAC/CV,OAAO,EAAC;MAAU;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAAA;EAEN,CAAC,EACD;IACE/B,KAAK,EAAE,WAAW;IAClBoB,UAAU,EAAE,QAAQ;IACpBC,KAAK,EAAE,GAAG;IACVC,UAAU,EAAGC,MAAM,iBACjBzE,OAAA,CAAC1B,IAAI;MACH8G,KAAK,EAAEX,MAAM,CAACG,GAAG,CAAChD,SAAS,GAAG,OAAO,GAAG,SAAU;MAClD2D,IAAI,EAAC,OAAO;MACZL,KAAK,EAAET,MAAM,CAACG,GAAG,CAAChD,SAAS,GAAG,SAAS,GAAG;IAAU;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrD;EAEL,CAAC,EACD;IACE/B,KAAK,EAAE,YAAY;IACnBoB,UAAU,EAAE,SAAS;IACrBC,KAAK,EAAE,GAAG;IACVC,UAAU,EAAGC,MAAM,IACjB,IAAIe,IAAI,CAACf,MAAM,CAACG,GAAG,CAACa,UAAU,CAAC,CAACC,kBAAkB,CAAC,OAAO;EAE9D,CAAC,EACD;IACExC,KAAK,EAAE,SAAS;IAChByC,IAAI,EAAE,SAAS;IACfrB,UAAU,EAAE,SAAS;IACrBC,KAAK,EAAE,GAAG;IACVqB,UAAU,EAAGnB,MAAM,IAAK;MACtB,MAAMoB,OAAO,GAAG,EAAE;MAElB,IAAI1F,aAAa,CAAC,aAAa,CAAC,EAAE;QAChC0F,OAAO,CAACC,IAAI,cACV9F,OAAA,CAACL,mBAAmB;UAClBoG,IAAI,eAAE/F,OAAA,CAACX,QAAQ;YAAAyF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACnBG,KAAK,EAAC,UAAU;UAChBY,OAAO,EAAEA,CAAA,KAAMlD,gBAAgB,CAAC2B,MAAM,CAACG,GAAG;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CACH,CAAC;MACH;MAEA,IAAI9E,aAAa,CAAC,aAAa,CAAC,EAAE;QAChC0F,OAAO,CAACC,IAAI,cACV9F,OAAA,CAACL,mBAAmB;UAClBoG,IAAI,eAAE/F,OAAA,CAACT,UAAU;YAAAuF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACrBG,KAAK,EAAC,WAAW;UACjBY,OAAO,EAAEA,CAAA,KAAMlC,YAAY,CAACW,MAAM,CAACG,GAAG,CAACrB,EAAE;QAAE;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CACH,CAAC;MACH;MAEA,OAAOY,OAAO;IAChB;EACF,CAAC,CACF;EAED,oBACE7F,OAAA,CAAC/B,GAAG;IAAAyG,QAAA,gBACF1E,OAAA,CAAC/B,GAAG;MAACgI,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,eAAe;MAACC,UAAU,EAAC,QAAQ;MAACC,EAAE,EAAE,CAAE;MAAA1B,QAAA,gBAC3E1E,OAAA,CAAC7B,UAAU;QAACwG,OAAO,EAAC,IAAI;QAAAD,QAAA,EAAC;MAAwB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAE7D9E,aAAa,CAAC,aAAa,CAAC,iBAC3BH,OAAA,CAAC5B,MAAM;QACLuG,OAAO,EAAC,WAAW;QACnB0B,SAAS,eAAErG,OAAA,CAACb,OAAO;UAAA2F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBe,OAAO,EAAEA,CAAA,KAAMlD,gBAAgB,CAAC,CAAE;QAAA4B,QAAA,EACnC;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENjF,OAAA,CAAC9B,KAAK;MAACoI,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEH,EAAE,EAAE;MAAE,CAAE;MAAA1B,QAAA,eACzB1E,OAAA,CAAC3B,SAAS;QACRmI,SAAS;QACTC,WAAW,EAAC,2CAAwC;QACpD5D,KAAK,EAAE7B,MAAO;QACd0F,QAAQ,EAAEhE,YAAa;QACvBiE,UAAU,EAAE;UACVC,cAAc,eAAE5G,OAAA,CAACP,UAAU;YAAC6G,EAAE,EAAE;cAAEO,EAAE,EAAE,CAAC;cAAE3B,KAAK,EAAE;YAAiB;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QACvE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAERjF,OAAA,CAAC9B,KAAK;MAACoI,EAAE,EAAE;QAAEQ,MAAM,EAAE,GAAG;QAAEvC,KAAK,EAAE;MAAO,CAAE;MAAAG,QAAA,eACxC1E,OAAA,CAACN,QAAQ;QACPqH,IAAI,EAAE3G,KAAM;QACZiE,OAAO,EAAEA,OAAQ;QACjB7D,OAAO,EAAEA,OAAQ;QACjBwG,UAAU;QACVC,cAAc,EAAC,QAAQ;QACvBC,QAAQ,EAAExG,KAAM;QAChBE,IAAI,EAAEA,IAAK;QACXE,QAAQ,EAAEA,QAAS;QACnBqG,YAAY,EAAEtG,OAAQ;QACtBuG,gBAAgB,EAAErG,WAAY;QAC9BsG,kBAAkB,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;QACjCC,uBAAuB;MAAA;QAAAxC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGRjF,OAAA,CAACzB,MAAM;MAACgJ,IAAI,EAAErG,UAAW;MAACsG,OAAO,EAAExE,iBAAkB;MAACyE,QAAQ,EAAC,IAAI;MAACjB,SAAS;MAAA9B,QAAA,gBAC3E1E,OAAA,CAACxB,WAAW;QAAAkG,QAAA,EACTtD,WAAW,GAAG,yBAAyB,GAAG;MAAoB;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,eAEdjF,OAAA,CAACvB,aAAa;QAAAiG,QAAA,GACX3C,UAAU,iBACT/B,OAAA,CAACf,KAAK;UAACyI,QAAQ,EAAC,OAAO;UAACpB,EAAE,EAAE;YAAEF,EAAE,EAAE;UAAE,CAAE;UAAA1B,QAAA,EACnC3C;QAAU;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACR,eAEDjF,OAAA,CAAC3B,SAAS;UACRmI,SAAS;UACTpB,KAAK,EAAC,OAAO;UACbO,IAAI,EAAC,OAAO;UACZ9C,KAAK,EAAEvB,QAAQ,CAACE,KAAM;UACtBkF,QAAQ,EAAGiB,CAAC,IAAK1E,gBAAgB,CAAC,OAAO,EAAE0E,CAAC,CAAC/E,MAAM,CAACC,KAAK,CAAE;UAC3D+E,MAAM,EAAC,QAAQ;UACfC,QAAQ;QAAA;UAAA/C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAEFjF,OAAA,CAAC3B,SAAS;UACRmI,SAAS;UACTpB,KAAK,EAAEhE,WAAW,GAAG,kCAAkC,GAAG,cAAe;UACzEuE,IAAI,EAAC,UAAU;UACf9C,KAAK,EAAEvB,QAAQ,CAACG,QAAS;UACzBiF,QAAQ,EAAGiB,CAAC,IAAK1E,gBAAgB,CAAC,UAAU,EAAE0E,CAAC,CAAC/E,MAAM,CAACC,KAAK,CAAE;UAC9D+E,MAAM,EAAC,QAAQ;UACfC,QAAQ,EAAE,CAACzG;QAAY;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eAEFjF,OAAA,CAAC3B,SAAS;UACRmI,SAAS;UACTpB,KAAK,EAAC,WAAQ;UACdvC,KAAK,EAAEvB,QAAQ,CAACI,UAAW;UAC3BgF,QAAQ,EAAGiB,CAAC,IAAK1E,gBAAgB,CAAC,YAAY,EAAE0E,CAAC,CAAC/E,MAAM,CAACC,KAAK,CAAE;UAChE+E,MAAM,EAAC;QAAQ;UAAA9C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eAEFjF,OAAA,CAAC3B,SAAS;UACRmI,SAAS;UACTpB,KAAK,EAAC,gBAAgB;UACtBvC,KAAK,EAAEvB,QAAQ,CAACK,SAAU;UAC1B+E,QAAQ,EAAGiB,CAAC,IAAK1E,gBAAgB,CAAC,WAAW,EAAE0E,CAAC,CAAC/E,MAAM,CAACC,KAAK,CAAE;UAC/D+E,MAAM,EAAC;QAAQ;UAAA9C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eAEFjF,OAAA,CAACrB,WAAW;UAAC6H,SAAS;UAACoB,MAAM,EAAC,QAAQ;UAAAlD,QAAA,gBACpC1E,OAAA,CAACpB,UAAU;YAAA8F,QAAA,EAAC;UAAI;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC7BjF,OAAA,CAACnB,MAAM;YACLgE,KAAK,EAAEvB,QAAQ,CAACO,OAAO,IAAI,EAAG;YAC9B6E,QAAQ,EAAGiB,CAAC,IAAK1E,gBAAgB,CAAC,SAAS,EAAE0E,CAAC,CAAC/E,MAAM,CAACC,KAAK,IAAIf,SAAS,CAAE;YAAA4C,QAAA,gBAE1E1E,OAAA,CAAClB,QAAQ;cAAC+D,KAAK,EAAC,EAAE;cAAA6B,QAAA,EAAC;YAAU;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,EACvC3E,KAAK,CAACwH,GAAG,CAAEzC,IAAI,iBACdrF,OAAA,CAAClB,QAAQ;cAAe+D,KAAK,EAAEwC,IAAI,CAAC9B,EAAG;cAAAmB,QAAA,EACpCW,IAAI,CAACC;YAAI,GADGD,IAAI,CAAC9B,EAAE;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEZ,CACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEdjF,OAAA,CAACjB,gBAAgB;UACfgJ,OAAO,eACL/H,OAAA,CAAChB,MAAM;YACLgJ,OAAO,EAAE1G,QAAQ,CAACM,SAAU;YAC5B8E,QAAQ,EAAGiB,CAAC,IAAK1E,gBAAgB,CAAC,WAAW,EAAE0E,CAAC,CAAC/E,MAAM,CAACoF,OAAO;UAAE;YAAAlD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CACF;UACDG,KAAK,EAAC,mBAAmB;UACzBkB,EAAE,EAAE;YAAE2B,EAAE,EAAE;UAAE;QAAE;UAAAnD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAEhBjF,OAAA,CAACtB,aAAa;QAAAgG,QAAA,gBACZ1E,OAAA,CAAC5B,MAAM;UAAC4H,OAAO,EAAEhD,iBAAkB;UAAA0B,QAAA,EAAC;QAAO;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACpDjF,OAAA,CAAC5B,MAAM;UAAC4H,OAAO,EAAE5C,YAAa;UAACuB,OAAO,EAAC,WAAW;UAAAD,QAAA,EAC/CtD,WAAW,GAAG,eAAe,GAAG;QAAO;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAC/E,EAAA,CAlXID,KAAe;EAAA,QACOJ,OAAO;AAAA;AAAAqI,EAAA,GAD7BjI,KAAe;AAoXrB,eAAeA,KAAK;AAAC,IAAAiI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}