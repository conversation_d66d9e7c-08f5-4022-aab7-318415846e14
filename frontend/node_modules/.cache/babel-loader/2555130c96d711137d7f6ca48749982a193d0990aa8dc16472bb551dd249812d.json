{"ast": null, "code": "import * as React from 'react';\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { useGridConfiguration } from \"../../hooks/utils/useGridConfiguration.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CLASSNAME_PREFIX = 'MuiDataGridVariables';\nconst CSSVariablesContext = /*#__PURE__*/React.createContext({\n  className: 'unset',\n  tag: /*#__PURE__*/_jsx(\"style\", {\n    href: \"/unset\"\n  })\n});\nif (process.env.NODE_ENV !== \"production\") CSSVariablesContext.displayName = \"CSSVariablesContext\";\nexport function useCSSVariablesClass() {\n  return React.useContext(CSSVariablesContext).className;\n}\nexport function useCSSVariablesContext() {\n  return React.useContext(CSSVariablesContext);\n}\nexport function GridPortalWrapper({\n  children\n}) {\n  const className = useCSSVariablesClass();\n  return /*#__PURE__*/_jsx(\"div\", {\n    className: className,\n    children: children\n  });\n}\nexport function GridCSSVariablesContext(props) {\n  const config = useGridConfiguration();\n  const rootProps = useGridRootProps();\n  const description = config.hooks.useCSSVariables();\n  const context = React.useMemo(() => {\n    const className = `${CLASSNAME_PREFIX}-${description.id}`;\n    const cssString = `.${className}{${variablesToString(description.variables)}}`;\n    const tag = /*#__PURE__*/_jsx(\"style\", {\n      href: `/${className}`,\n      nonce: rootProps.nonce,\n      children: cssString\n    });\n    return {\n      className,\n      tag\n    };\n  }, [rootProps.nonce, description]);\n  return /*#__PURE__*/_jsx(CSSVariablesContext.Provider, {\n    value: context,\n    children: props.children\n  });\n}\nfunction variablesToString(variables) {\n  let output = '';\n  for (const key in variables) {\n    if (Object.hasOwn(variables, key) && variables[key] !== undefined) {\n      output += `${key}:${variables[key]};`;\n    }\n  }\n  return output;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}