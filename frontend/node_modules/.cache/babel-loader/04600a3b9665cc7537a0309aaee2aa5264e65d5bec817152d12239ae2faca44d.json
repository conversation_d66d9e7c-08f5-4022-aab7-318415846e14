{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"column\", \"row\", \"rowId\", \"rowNode\", \"align\", \"children\", \"colIndex\", \"width\", \"className\", \"style\", \"colSpan\", \"disableDragEvents\", \"isNotVisible\", \"pinnedOffset\", \"pinnedPosition\", \"showRightBorder\", \"showLeftBorder\", \"onClick\", \"onDoubleClick\", \"onMouseDown\", \"onMouseUp\", \"onMouseOver\", \"onKeyDown\", \"onKeyUp\", \"onDragEnter\", \"onDragOver\"],\n  _excluded2 = [\"changeReason\", \"unstable_updateValueOnRender\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport useForkRef from '@mui/utils/useForkRef';\nimport composeClasses from '@mui/utils/composeClasses';\nimport ownerDocument from '@mui/utils/ownerDocument';\nimport capitalize from '@mui/utils/capitalize';\nimport { fastMemo } from '@mui/x-internals/fastMemo';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { doesSupportPreventScroll } from \"../../utils/doesSupportPreventScroll.js\";\nimport { getDataGridUtilityClass, gridClasses } from \"../../constants/gridClasses.js\";\nimport { GridCellModes } from \"../../models/index.js\";\nimport { useGridSelector } from \"../../hooks/utils/useGridSelector.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { gridFocusCellSelector, gridTabIndexCellSelector } from \"../../hooks/features/focus/gridFocusStateSelector.js\";\nimport { GridPinnedColumnPosition } from \"../../hooks/features/columns/gridColumnsInterfaces.js\";\nimport { PinnedColumnPosition } from \"../../internals/constants.js\";\nimport { gridRowSpanningHiddenCellsSelector, gridRowSpanningSpannedCellsSelector } from \"../../hooks/features/rows/gridRowSpanningSelectors.js\";\nimport { useGridPrivateApiContext } from \"../../hooks/utils/useGridPrivateApiContext.js\";\nimport { gridEditCellStateSelector } from \"../../hooks/features/editing/gridEditingSelectors.js\";\nimport { attachPinnedStyle } from \"../../internals/utils/index.js\";\nimport { useGridConfiguration } from \"../../hooks/utils/useGridConfiguration.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const gridPinnedColumnPositionLookup = {\n  [PinnedColumnPosition.LEFT]: GridPinnedColumnPosition.LEFT,\n  [PinnedColumnPosition.RIGHT]: GridPinnedColumnPosition.RIGHT,\n  [PinnedColumnPosition.NONE]: undefined,\n  [PinnedColumnPosition.VIRTUAL]: undefined\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    align,\n    showLeftBorder,\n    showRightBorder,\n    pinnedPosition,\n    isEditable,\n    isSelected,\n    isSelectionMode,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['cell', `cell--text${capitalize(align)}`, isSelected && 'selected', isEditable && 'cell--editable', showLeftBorder && 'cell--withLeftBorder', showRightBorder && 'cell--withRightBorder', pinnedPosition === PinnedColumnPosition.LEFT && 'cell--pinnedLeft', pinnedPosition === PinnedColumnPosition.RIGHT && 'cell--pinnedRight', isSelectionMode && !isEditable && 'cell--selectionMode']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nlet warnedOnce = false;\n\n// TODO(v7): Removing the wrapper will break the docs performance visualization demo.\n\nconst GridCell = forwardRef(function GridCell(props, ref) {\n  const {\n      column,\n      row,\n      rowId,\n      rowNode,\n      align,\n      colIndex,\n      width,\n      className,\n      style: styleProp,\n      colSpan,\n      disableDragEvents,\n      isNotVisible,\n      pinnedOffset,\n      pinnedPosition,\n      showRightBorder,\n      showLeftBorder,\n      onClick,\n      onDoubleClick,\n      onMouseDown,\n      onMouseUp,\n      onMouseOver,\n      onKeyDown,\n      onKeyUp,\n      onDragEnter,\n      onDragOver\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const apiRef = useGridPrivateApiContext();\n  const rootProps = useGridRootProps();\n  const isRtl = useRtl();\n  const field = column.field;\n  const editCellState = useGridSelector(apiRef, gridEditCellStateSelector, {\n    rowId,\n    field\n  });\n  const config = useGridConfiguration();\n  const cellAggregationResult = config.hooks.useCellAggregationResult(rowId, field);\n  const cellMode = editCellState ? GridCellModes.Edit : GridCellModes.View;\n  const cellParams = apiRef.current.getCellParamsForRow(rowId, field, row, {\n    colDef: column,\n    cellMode,\n    rowNode: rowNode,\n    tabIndex: useGridSelector(apiRef, () => {\n      const cellTabIndex = gridTabIndexCellSelector(apiRef);\n      return cellTabIndex && cellTabIndex.field === field && cellTabIndex.id === rowId ? 0 : -1;\n    }),\n    hasFocus: useGridSelector(apiRef, () => {\n      const focus = gridFocusCellSelector(apiRef);\n      return focus?.id === rowId && focus.field === field;\n    })\n  });\n  cellParams.api = apiRef.current;\n  if (cellAggregationResult) {\n    cellParams.value = cellAggregationResult.value;\n    cellParams.formattedValue = column.valueFormatter ? column.valueFormatter(cellParams.value, row, column, apiRef) : cellParams.value;\n  }\n  const isSelected = useGridSelector(apiRef, () => apiRef.current.unstable_applyPipeProcessors('isCellSelected', false, {\n    id: rowId,\n    field\n  }));\n  const hiddenCells = useGridSelector(apiRef, gridRowSpanningHiddenCellsSelector);\n  const spannedCells = useGridSelector(apiRef, gridRowSpanningSpannedCellsSelector);\n  const {\n    hasFocus,\n    isEditable = false,\n    value\n  } = cellParams;\n  const canManageOwnFocus = column.type === 'actions' && column.getActions?.(apiRef.current.getRowParams(rowId)).some(action => !action.props.disabled);\n  const tabIndex = (cellMode === 'view' || !isEditable) && !canManageOwnFocus ? cellParams.tabIndex : -1;\n  const {\n    classes: rootClasses,\n    getCellClassName\n  } = rootProps;\n\n  // There is a hidden grid state access in `applyPipeProcessor('cellClassName', ...)`\n  const pipesClassName = useGridSelector(apiRef, () => apiRef.current.unstable_applyPipeProcessors('cellClassName', [], {\n    id: rowId,\n    field\n  }).filter(Boolean).join(' '));\n  const classNames = [pipesClassName];\n  if (column.cellClassName) {\n    classNames.push(typeof column.cellClassName === 'function' ? column.cellClassName(cellParams) : column.cellClassName);\n  }\n  if (column.display === 'flex') {\n    classNames.push(gridClasses['cell--flex']);\n  }\n  if (getCellClassName) {\n    classNames.push(getCellClassName(cellParams));\n  }\n  const valueToRender = cellParams.formattedValue ?? value;\n  const cellRef = React.useRef(null);\n  const handleRef = useForkRef(ref, cellRef);\n  const focusElementRef = React.useRef(null);\n  const isSelectionMode = rootProps.cellSelection ?? false;\n  const ownerState = {\n    align,\n    showLeftBorder,\n    showRightBorder,\n    isEditable,\n    classes: rootProps.classes,\n    pinnedPosition,\n    isSelected,\n    isSelectionMode\n  };\n  const classes = useUtilityClasses(ownerState);\n  const publishMouseUp = React.useCallback(eventName => event => {\n    const params = apiRef.current.getCellParams(rowId, field || '');\n    apiRef.current.publishEvent(eventName, params, event);\n    if (onMouseUp) {\n      onMouseUp(event);\n    }\n  }, [apiRef, field, onMouseUp, rowId]);\n  const publishMouseDown = React.useCallback(eventName => event => {\n    const params = apiRef.current.getCellParams(rowId, field || '');\n    apiRef.current.publishEvent(eventName, params, event);\n    if (onMouseDown) {\n      onMouseDown(event);\n    }\n  }, [apiRef, field, onMouseDown, rowId]);\n  const publish = React.useCallback((eventName, propHandler) => event => {\n    // The row might have been deleted during the click\n    if (!apiRef.current.getRow(rowId)) {\n      return;\n    }\n    const params = apiRef.current.getCellParams(rowId, field || '');\n    apiRef.current.publishEvent(eventName, params, event);\n    if (propHandler) {\n      propHandler(event);\n    }\n  }, [apiRef, field, rowId]);\n  const isCellRowSpanned = hiddenCells[rowId]?.[field] ?? false;\n  const rowSpan = spannedCells[rowId]?.[field] ?? 1;\n  const style = React.useMemo(() => {\n    if (isNotVisible) {\n      return {\n        padding: 0,\n        opacity: 0,\n        width: 0,\n        height: 0,\n        border: 0\n      };\n    }\n    const cellStyle = attachPinnedStyle(_extends({\n      '--width': `${width}px`\n    }, styleProp), isRtl, pinnedPosition, pinnedOffset);\n    const isLeftPinned = pinnedPosition === PinnedColumnPosition.LEFT;\n    const isRightPinned = pinnedPosition === PinnedColumnPosition.RIGHT;\n    if (rowSpan > 1) {\n      cellStyle.height = `calc(var(--height) * ${rowSpan})`;\n      cellStyle.zIndex = 10;\n      if (isLeftPinned || isRightPinned) {\n        cellStyle.zIndex = 40;\n      }\n    }\n    return cellStyle;\n  }, [width, isNotVisible, styleProp, pinnedOffset, pinnedPosition, isRtl, rowSpan]);\n  React.useEffect(() => {\n    if (!hasFocus || cellMode === GridCellModes.Edit) {\n      return;\n    }\n    const doc = ownerDocument(apiRef.current.rootElementRef.current);\n    if (cellRef.current && !cellRef.current.contains(doc.activeElement)) {\n      const focusableElement = cellRef.current.querySelector('[tabindex=\"0\"]');\n      const elementToFocus = focusElementRef.current || focusableElement || cellRef.current;\n      if (doesSupportPreventScroll()) {\n        elementToFocus.focus({\n          preventScroll: true\n        });\n      } else {\n        const scrollPosition = apiRef.current.getScrollPosition();\n        elementToFocus.focus();\n        apiRef.current.scroll(scrollPosition);\n      }\n    }\n  }, [hasFocus, cellMode, apiRef]);\n  if (isCellRowSpanned) {\n    return /*#__PURE__*/_jsx(\"div\", {\n      \"data-colindex\": colIndex,\n      role: \"presentation\",\n      style: _extends({\n        width: 'var(--width)'\n      }, style)\n    });\n  }\n  let handleFocus = other.onFocus;\n  if (process.env.NODE_ENV === 'test' && rootProps.experimentalFeatures?.warnIfFocusStateIsNotSynced) {\n    handleFocus = event => {\n      const focusedCell = gridFocusCellSelector(apiRef);\n      if (focusedCell?.id === rowId && focusedCell.field === field) {\n        if (typeof other.onFocus === 'function') {\n          other.onFocus(event);\n        }\n        return;\n      }\n      if (!warnedOnce) {\n        console.warn([`MUI X: The cell with id=${rowId} and field=${field} received focus.`, `According to the state, the focus should be at id=${focusedCell?.id}, field=${focusedCell?.field}.`, \"Not syncing the state may cause unwanted behaviors since the `cellFocusIn` event won't be fired.\", 'Call `fireEvent.mouseUp` before the `fireEvent.click` to sync the focus with the state.'].join('\\n'));\n        warnedOnce = true;\n      }\n    };\n  }\n  let children;\n  let title;\n  if (editCellState === null && column.renderCell) {\n    children = column.renderCell(cellParams);\n  }\n  if (editCellState !== null && column.renderEditCell) {\n    const updatedRow = apiRef.current.getRowWithUpdatedValues(rowId, column.field);\n\n    // eslint-disable-next-line @typescript-eslint/naming-convention\n    const editCellStateRest = _objectWithoutPropertiesLoose(editCellState, _excluded2);\n    const formattedValue = column.valueFormatter ? column.valueFormatter(editCellState.value, updatedRow, column, apiRef) : cellParams.formattedValue;\n    const params = _extends({}, cellParams, {\n      row: updatedRow,\n      formattedValue\n    }, editCellStateRest);\n    children = column.renderEditCell(params);\n    classNames.push(gridClasses['cell--editing']);\n    classNames.push(rootClasses?.['cell--editing']);\n  }\n  if (children === undefined) {\n    const valueString = valueToRender?.toString();\n    children = valueString;\n    title = valueString;\n  }\n  if (/*#__PURE__*/React.isValidElement(children) && canManageOwnFocus) {\n    children = /*#__PURE__*/React.cloneElement(children, {\n      focusElementRef\n    });\n  }\n  const draggableEventHandlers = disableDragEvents ? null : {\n    onDragEnter: publish('cellDragEnter', onDragEnter),\n    onDragOver: publish('cellDragOver', onDragOver)\n  };\n  return /*#__PURE__*/_jsx(\"div\", _extends({\n    className: clsx(classes.root, classNames, className),\n    role: \"gridcell\",\n    \"data-field\": field,\n    \"data-colindex\": colIndex,\n    \"aria-colindex\": colIndex + 1,\n    \"aria-colspan\": colSpan,\n    \"aria-rowspan\": rowSpan,\n    style: style,\n    title: title,\n    tabIndex: tabIndex,\n    onClick: publish('cellClick', onClick),\n    onDoubleClick: publish('cellDoubleClick', onDoubleClick),\n    onMouseOver: publish('cellMouseOver', onMouseOver),\n    onMouseDown: publishMouseDown('cellMouseDown'),\n    onMouseUp: publishMouseUp('cellMouseUp'),\n    onKeyDown: publish('cellKeyDown', onKeyDown),\n    onKeyUp: publish('cellKeyUp', onKeyUp)\n  }, draggableEventHandlers, other, {\n    onFocus: handleFocus,\n    ref: handleRef,\n    children: children\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridCell.displayName = \"GridCell\";\nprocess.env.NODE_ENV !== \"production\" ? GridCell.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  align: PropTypes.oneOf(['center', 'left', 'right']).isRequired,\n  colIndex: PropTypes.number.isRequired,\n  colSpan: PropTypes.number,\n  column: PropTypes.object.isRequired,\n  disableDragEvents: PropTypes.bool,\n  isNotVisible: PropTypes.bool.isRequired,\n  pinnedOffset: PropTypes.number,\n  pinnedPosition: PropTypes.oneOf([0, 1, 2, 3]).isRequired,\n  row: PropTypes.object.isRequired,\n  rowId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,\n  rowNode: PropTypes.object.isRequired,\n  showLeftBorder: PropTypes.bool.isRequired,\n  showRightBorder: PropTypes.bool.isRequired,\n  width: PropTypes.number.isRequired\n} : void 0;\nconst MemoizedGridCell = fastMemo(GridCell);\nexport { MemoizedGridCell as GridCell };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}