{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { GridPanelWrapper } from \"./GridPanelWrapper.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction GridColumnsPanel(props) {\n  const rootProps = useGridRootProps();\n  return /*#__PURE__*/_jsx(GridPanelWrapper, _extends({}, props, {\n    children: /*#__PURE__*/_jsx(rootProps.slots.columnsManagement, _extends({}, rootProps.slotProps?.columnsManagement))\n  }));\n}\nexport { GridColumnsPanel };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}