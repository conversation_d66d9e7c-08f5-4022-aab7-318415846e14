{"ast": null, "code": "import * as React from 'react';\nimport reactMajor from \"../reactMajor/index.js\";\n\n// Compatibility shim that ensures stable props object for forwardRef components\n// Fixes https://github.com/facebook/react/issues/31613\n// We ensure that the ref is always present in the props object (even if that's not the case for older versions of React) to avoid the footgun of spreading props over the ref in the newer versions of React.\n// Footgun: <Component ref={ref} {...props} /> will break past React 19, but the types will now warn us that we should use <Component {...props} ref={ref} /> instead.\nexport const forwardRef = render => {\n  if (reactMajor >= 19) {\n    const Component = props => render(props, props.ref ?? null);\n    Component.displayName = render.displayName ?? render.name;\n    return Component;\n  }\n  return /*#__PURE__*/React.forwardRef(render);\n};\nif (process.env.NODE_ENV !== \"production\") forwardRef.displayName = \"forwardRef\";", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}