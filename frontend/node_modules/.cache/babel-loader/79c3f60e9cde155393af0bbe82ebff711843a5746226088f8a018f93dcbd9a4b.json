{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from '@mui/system';\nimport { fastMemo } from '@mui/x-internals/fastMemo';\nimport { useGridRootProps } from \"../hooks/utils/useGridRootProps.js\";\nimport { getDataGridUtilityClass, gridClasses } from \"../constants/index.js\";\nimport { useGridApiContext } from \"../hooks/utils/useGridApiContext.js\";\nimport { useGridEvent } from \"../hooks/utils/useGridEvent.js\";\nimport { useGridSelector } from \"../hooks/utils/useGridSelector.js\";\nimport { gridDimensionsSelector, gridColumnsTotalWidthSelector } from \"../hooks/features/dimensions/gridDimensionsSelectors.js\";\nimport { gridDensityFactorSelector } from \"../hooks/features/density/densitySelector.js\";\nimport { useTimeout } from \"../hooks/utils/useTimeout.js\";\nimport { getTotalHeaderHeight } from \"../hooks/features/columns/gridColumnsUtils.js\";\nimport { createSelector } from \"../utils/createSelector.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CLIFF = 1;\nconst SLOP = 1.5;\nconst useUtilityClasses = ownerState => {\n  const {\n    scrollDirection,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['scrollArea', `scrollArea--${scrollDirection}`]\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst GridScrollAreaRawRoot = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'ScrollArea',\n  overridesResolver: (props, styles) => [{\n    [`&.${gridClasses['scrollArea--left']}`]: styles['scrollArea--left']\n  }, {\n    [`&.${gridClasses['scrollArea--right']}`]: styles['scrollArea--right']\n  }, styles.scrollArea]\n})(() => ({\n  position: 'absolute',\n  top: 0,\n  zIndex: 101,\n  width: 20,\n  bottom: 0,\n  [`&.${gridClasses['scrollArea--left']}`]: {\n    left: 0\n  },\n  [`&.${gridClasses['scrollArea--right']}`]: {\n    right: 0\n  }\n}));\nconst offsetSelector = createSelector(gridDimensionsSelector, (dimensions, direction) => {\n  if (direction === 'left') {\n    return dimensions.leftPinnedWidth;\n  }\n  if (direction === 'right') {\n    return dimensions.rightPinnedWidth + (dimensions.hasScrollX ? dimensions.scrollbarSize : 0);\n  }\n  return 0;\n});\nfunction GridScrollAreaWrapper(props) {\n  const apiRef = useGridApiContext();\n  const [dragging, setDragging] = React.useState(false);\n  useGridEvent(apiRef, 'columnHeaderDragStart', () => setDragging(true));\n  useGridEvent(apiRef, 'columnHeaderDragEnd', () => setDragging(false));\n  if (!dragging) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(GridScrollAreaContent, _extends({}, props));\n}\nfunction GridScrollAreaContent(props) {\n  const {\n    scrollDirection,\n    scrollPosition\n  } = props;\n  const rootRef = React.useRef(null);\n  const apiRef = useGridApiContext();\n  const timeout = useTimeout();\n  const densityFactor = useGridSelector(apiRef, gridDensityFactorSelector);\n  const columnsTotalWidth = useGridSelector(apiRef, gridColumnsTotalWidthSelector);\n  const sideOffset = useGridSelector(apiRef, offsetSelector, scrollDirection);\n  const getCanScrollMore = () => {\n    const dimensions = gridDimensionsSelector(apiRef);\n    if (scrollDirection === 'left') {\n      // Only render if the user has not reached yet the start of the list\n      return scrollPosition.current.left > 0;\n    }\n    if (scrollDirection === 'right') {\n      // Only render if the user has not reached yet the end of the list\n      const maxScrollLeft = columnsTotalWidth - dimensions.viewportInnerSize.width;\n      return scrollPosition.current.left < maxScrollLeft;\n    }\n    return false;\n  };\n  const [canScrollMore, setCanScrollMore] = React.useState(getCanScrollMore);\n  const rootProps = useGridRootProps();\n  const ownerState = _extends({}, rootProps, {\n    scrollDirection\n  });\n  const classes = useUtilityClasses(ownerState);\n  const totalHeaderHeight = getTotalHeaderHeight(apiRef, rootProps);\n  const headerHeight = Math.floor(rootProps.columnHeaderHeight * densityFactor);\n  const style = {\n    height: headerHeight,\n    top: totalHeaderHeight - headerHeight\n  };\n  if (scrollDirection === 'left') {\n    style.left = sideOffset;\n  } else if (scrollDirection === 'right') {\n    style.right = sideOffset;\n  }\n  const handleScrolling = () => {\n    setCanScrollMore(getCanScrollMore);\n  };\n  const handleDragOver = useEventCallback(event => {\n    let offset;\n\n    // Prevents showing the forbidden cursor\n    event.preventDefault();\n    if (scrollDirection === 'left') {\n      offset = event.clientX - rootRef.current.getBoundingClientRect().right;\n    } else if (scrollDirection === 'right') {\n      offset = Math.max(1, event.clientX - rootRef.current.getBoundingClientRect().left);\n    } else {\n      throw new Error('MUI X: Wrong drag direction');\n    }\n    offset = (offset - CLIFF) * SLOP + CLIFF;\n\n    // Avoid freeze and inertia.\n    timeout.start(0, () => {\n      apiRef.current.scroll({\n        left: scrollPosition.current.left + offset,\n        top: scrollPosition.current.top\n      });\n    });\n  });\n  useGridEvent(apiRef, 'scrollPositionChange', handleScrolling);\n  if (!canScrollMore) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(GridScrollAreaRawRoot, {\n    ref: rootRef,\n    className: classes.root,\n    ownerState: ownerState,\n    onDragOver: handleDragOver,\n    style: style\n  });\n}\nexport const GridScrollArea = fastMemo(GridScrollAreaWrapper);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}