{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport HTMLElementType from '@mui/utils/HTMLElementType';\nimport { useGridApiContext } from \"../../../hooks/utils/useGridApiContext.js\";\nimport { GridMenu } from \"../GridMenu.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction GridColumnHeaderMenu({\n  columnMenuId,\n  columnMenuButtonId,\n  ContentComponent,\n  contentComponentProps,\n  field,\n  open,\n  target,\n  onExited\n}) {\n  const apiRef = useGridApiContext();\n  const colDef = apiRef.current.getColumn(field);\n  const hideMenu = useEventCallback(event => {\n    if (event) {\n      // Prevent triggering the sorting\n      event.stopPropagation();\n      if (target?.contains(event.target)) {\n        return;\n      }\n    }\n    apiRef.current.hideColumnMenu();\n  });\n  if (!target || !colDef) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(GridMenu, {\n    position: `bottom-${colDef.align === 'right' ? 'start' : 'end'}`,\n    open: open,\n    target: target,\n    onClose: hideMenu,\n    onExited: onExited,\n    children: /*#__PURE__*/_jsx(ContentComponent, _extends({\n      colDef: colDef,\n      hideMenu: hideMenu,\n      open: open,\n      id: columnMenuId,\n      labelledby: columnMenuButtonId\n    }, contentComponentProps))\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GridColumnHeaderMenu.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  columnMenuButtonId: PropTypes.string,\n  columnMenuId: PropTypes.string,\n  ContentComponent: PropTypes.elementType.isRequired,\n  contentComponentProps: PropTypes.any,\n  field: PropTypes.string.isRequired,\n  onExited: PropTypes.func,\n  open: PropTypes.bool.isRequired,\n  target: HTMLElementType\n} : void 0;\nexport { GridColumnHeaderMenu };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}