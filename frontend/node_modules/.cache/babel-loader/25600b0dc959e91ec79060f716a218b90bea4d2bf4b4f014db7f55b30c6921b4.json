{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"id\", \"value\", \"formattedValue\", \"api\", \"field\", \"row\", \"rowNode\", \"colDef\", \"cellMode\", \"isEditable\", \"tabIndex\", \"className\", \"hasFocus\", \"isValidating\", \"isProcessingProps\", \"error\", \"onValueChange\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport useId from '@mui/utils/useId';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['editBooleanCell']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nfunction GridEditBooleanCell(props) {\n  const {\n      id: idProp,\n      value,\n      field,\n      className,\n      hasFocus,\n      onValueChange\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const apiRef = useGridApiContext();\n  const inputRef = React.useRef(null);\n  const id = useId();\n  const [valueState, setValueState] = React.useState(value);\n  const rootProps = useGridRootProps();\n  const ownerState = {\n    classes: rootProps.classes\n  };\n  const classes = useUtilityClasses(ownerState);\n  const handleChange = React.useCallback(async event => {\n    const newValue = event.target.checked;\n    if (onValueChange) {\n      await onValueChange(event, newValue);\n    }\n    setValueState(newValue);\n    await apiRef.current.setEditCellValue({\n      id: idProp,\n      field,\n      value: newValue\n    }, event);\n  }, [apiRef, field, idProp, onValueChange]);\n  React.useEffect(() => {\n    setValueState(value);\n  }, [value]);\n  useEnhancedEffect(() => {\n    if (hasFocus) {\n      inputRef.current.focus();\n    }\n  }, [hasFocus]);\n  return /*#__PURE__*/_jsx(\"label\", _extends({\n    htmlFor: id,\n    className: clsx(classes.root, className)\n  }, other, {\n    children: /*#__PURE__*/_jsx(rootProps.slots.baseCheckbox, _extends({\n      id: id,\n      inputRef: inputRef,\n      checked: Boolean(valueState),\n      onChange: handleChange,\n      size: \"small\"\n    }, rootProps.slotProps?.baseCheckbox))\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? GridEditBooleanCell.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * GridApi that let you manipulate the grid.\n   */\n  api: PropTypes.object.isRequired,\n  /**\n   * The mode of the cell.\n   */\n  cellMode: PropTypes.oneOf(['edit', 'view']).isRequired,\n  changeReason: PropTypes.oneOf(['debouncedSetEditCellValue', 'setEditCellValue']),\n  /**\n   * The column of the row that the current cell belongs to.\n   */\n  colDef: PropTypes.object.isRequired,\n  /**\n   * The column field of the cell that triggered the event.\n   */\n  field: PropTypes.string.isRequired,\n  /**\n   * The cell value formatted with the column valueFormatter.\n   */\n  formattedValue: PropTypes.any,\n  /**\n   * If true, the cell is the active element.\n   */\n  hasFocus: PropTypes.bool.isRequired,\n  /**\n   * The grid row id.\n   */\n  id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,\n  /**\n   * If true, the cell is editable.\n   */\n  isEditable: PropTypes.bool,\n  isProcessingProps: PropTypes.bool,\n  isValidating: PropTypes.bool,\n  /**\n   * Callback called when the value is changed by the user.\n   * @param {React.ChangeEvent<HTMLInputElement>} event The event source of the callback.\n   * @param {boolean} newValue The value that is going to be passed to `apiRef.current.setEditCellValue`.\n   * @returns {Promise<void> | void} A promise to be awaited before calling `apiRef.current.setEditCellValue`\n   */\n  onValueChange: PropTypes.func,\n  /**\n   * The row model of the row that the current cell belongs to.\n   */\n  row: PropTypes.any.isRequired,\n  /**\n   * The node of the row that the current cell belongs to.\n   */\n  rowNode: PropTypes.object.isRequired,\n  /**\n   * the tabIndex value.\n   */\n  tabIndex: PropTypes.oneOf([-1, 0]).isRequired,\n  /**\n   * The cell value.\n   * If the column has `valueGetter`, use `params.row` to directly access the fields.\n   */\n  value: PropTypes.any\n} : void 0;\nexport { GridEditBooleanCell };\nexport const renderEditBooleanCell = params => /*#__PURE__*/_jsx(GridEditBooleanCell, _extends({}, params));\nif (process.env.NODE_ENV !== \"production\") renderEditBooleanCell.displayName = \"renderEditBooleanCell\";", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}