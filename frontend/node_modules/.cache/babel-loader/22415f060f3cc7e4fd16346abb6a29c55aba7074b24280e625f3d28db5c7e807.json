{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { styled } from '@mui/material/styles';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { vars } from \"../../constants/cssVariables.js\";\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['panelFooter']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst GridPanelFooterRoot = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'PanelFooter'\n})({\n  padding: vars.spacing(1),\n  display: 'flex',\n  justifyContent: 'space-between',\n  borderTop: `1px solid ${vars.colors.border.base}`\n});\nfunction GridPanelFooter(props) {\n  const {\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const rootProps = useGridRootProps();\n  const classes = useUtilityClasses(rootProps);\n  return /*#__PURE__*/_jsx(GridPanelFooterRoot, _extends({\n    className: clsx(classes.root, className),\n    ownerState: rootProps\n  }, other));\n}\nprocess.env.NODE_ENV !== \"production\" ? GridPanelFooter.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport { GridPanelFooter };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}