{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"item\", \"applyValue\", \"type\", \"apiRef\", \"focusElementRef\", \"tabIndex\", \"disabled\", \"isFilterActive\", \"slotProps\", \"clearButton\", \"headerFilterMenu\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport useId from '@mui/utils/useId';\nimport { useTimeout } from \"../../../hooks/utils/useTimeout.js\";\nimport { useGridRootProps } from \"../../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction GridFilterInputValue(props) {\n  const {\n      item,\n      applyValue,\n      type,\n      apiRef,\n      focusElementRef,\n      tabIndex,\n      disabled,\n      slotProps,\n      clearButton,\n      headerFilterMenu\n    } = props,\n    others = _objectWithoutPropertiesLoose(props, _excluded);\n  const textFieldProps = slotProps?.root;\n  const filterTimeout = useTimeout();\n  const [filterValueState, setFilterValueState] = React.useState(sanitizeFilterItemValue(item.value));\n  const [applying, setIsApplying] = React.useState(false);\n  const id = useId();\n  const rootProps = useGridRootProps();\n  const onFilterChange = React.useCallback(event => {\n    const value = sanitizeFilterItemValue(event.target.value);\n    setFilterValueState(value);\n    setIsApplying(true);\n    filterTimeout.start(rootProps.filterDebounceMs, () => {\n      const newItem = _extends({}, item, {\n        value: type === 'number' && !Number.isNaN(Number(value)) ? Number(value) : value,\n        fromInput: id\n      });\n      applyValue(newItem);\n      setIsApplying(false);\n    });\n  }, [filterTimeout, rootProps.filterDebounceMs, item, type, id, applyValue]);\n  React.useEffect(() => {\n    const itemPlusTag = item;\n    if (itemPlusTag.fromInput !== id || item.value == null) {\n      setFilterValueState(sanitizeFilterItemValue(item.value));\n    }\n  }, [id, item]);\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(rootProps.slots.baseTextField, _extends({\n      id: id,\n      label: apiRef.current.getLocaleText('filterPanelInputLabel'),\n      placeholder: apiRef.current.getLocaleText('filterPanelInputPlaceholder'),\n      value: filterValueState ?? '',\n      onChange: onFilterChange,\n      type: type || 'text',\n      disabled: disabled,\n      slotProps: _extends({}, textFieldProps?.slotProps, {\n        input: _extends({\n          endAdornment: applying ? /*#__PURE__*/_jsx(rootProps.slots.loadIcon, {\n            fontSize: \"small\",\n            color: \"action\"\n          }) : null\n        }, textFieldProps?.slotProps?.input),\n        htmlInput: _extends({\n          tabIndex\n        }, textFieldProps?.slotProps?.htmlInput)\n      }),\n      inputRef: focusElementRef\n    }, rootProps.slotProps?.baseTextField, others, textFieldProps)), headerFilterMenu, clearButton]\n  });\n}\nfunction sanitizeFilterItemValue(value) {\n  if (value == null || value === '') {\n    return undefined;\n  }\n  return String(value);\n}\nprocess.env.NODE_ENV !== \"production\" ? GridFilterInputValue.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  apiRef: PropTypes.shape({\n    current: PropTypes.object.isRequired\n  }).isRequired,\n  applyValue: PropTypes.func.isRequired,\n  className: PropTypes.string,\n  clearButton: PropTypes.node,\n  disabled: PropTypes.bool,\n  focusElementRef: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.func, PropTypes.object]),\n  headerFilterMenu: PropTypes.node,\n  inputRef: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n    current: (props, propName) => {\n      if (props[propName] == null) {\n        return null;\n      }\n      if (typeof props[propName] !== 'object' || props[propName].nodeType !== 1) {\n        return new Error(`Expected prop '${propName}' to be of type Element`);\n      }\n      return null;\n    }\n  })]),\n  /**\n   * It is `true` if the filter either has a value or an operator with no value\n   * required is selected (for example `isEmpty`)\n   */\n  isFilterActive: PropTypes.bool,\n  item: PropTypes.shape({\n    field: PropTypes.string.isRequired,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    operator: PropTypes.string.isRequired,\n    value: PropTypes.any\n  }).isRequired,\n  onBlur: PropTypes.func,\n  onFocus: PropTypes.func,\n  slotProps: PropTypes.object,\n  tabIndex: PropTypes.number,\n  type: PropTypes.oneOf(['date', 'datetime-local', 'number', 'text'])\n} : void 0;\nexport { GridFilterInputValue };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}