{"ast": null, "code": "import * as React from 'react';\nexport const GridPrivateApiContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== \"production\") GridPrivateApiContext.displayName = \"GridPrivateApiContext\";\nexport function useGridPrivateApiContext() {\n  const privateApiRef = React.useContext(GridPrivateApiContext);\n  if (privateApiRef === undefined) {\n    throw new Error(['MUI X: Could not find the Data Grid private context.', 'It looks like you rendered your component outside of a DataGrid, DataGridPro or DataGridPremium parent component.', 'This can also happen if you are bundling multiple versions of the Data Grid.'].join('\\n'));\n  }\n  return privateApiRef;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}