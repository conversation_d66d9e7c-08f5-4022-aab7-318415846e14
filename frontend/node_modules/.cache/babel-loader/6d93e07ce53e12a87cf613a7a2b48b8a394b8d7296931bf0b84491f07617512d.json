{"ast": null, "code": "import { useGridRefs } from \"./useGridRefs.js\";\nimport { useGridIsRtl } from \"./useGridIsRtl.js\";\nimport { useGridLoggerFactory } from \"./useGridLoggerFactory.js\";\nimport { useGridLocaleText } from \"./useGridLocaleText.js\";\nimport { useGridPipeProcessing } from \"./pipeProcessing/index.js\";\nimport { useGridStrategyProcessing } from \"./strategyProcessing/index.js\";\nimport { useGridStateInitialization } from \"./useGridStateInitialization.js\";\nimport { useGridProps } from \"./useGridProps.js\";\n\n/**\n * Initialize the technical pieces of the DataGrid (logger, state, ...) that any DataGrid implementation needs\n */\nexport const useGridInitialization = (privateApiRef, props) => {\n  useGridRefs(privateApiRef);\n  useGridProps(privateApiRef, props);\n  useGridIsRtl(privateApiRef);\n  useGridLoggerFactory(privateApiRef, props);\n  useGridStateInitialization(privateApiRef);\n  useGridPipeProcessing(privateApiRef);\n  useGridStrategyProcessing(privateApiRef);\n  useGridLocaleText(privateApiRef, props);\n  privateApiRef.current.register('private', {\n    rootProps: props\n  });\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}