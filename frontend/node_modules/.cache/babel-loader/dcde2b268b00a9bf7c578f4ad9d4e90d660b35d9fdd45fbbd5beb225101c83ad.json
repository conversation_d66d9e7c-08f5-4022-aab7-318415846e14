{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"field\", \"type\", \"align\", \"width\", \"height\", \"empty\", \"style\", \"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport capitalize from '@mui/utils/capitalize';\nimport { fastMemo } from '@mui/x-internals/fastMemo';\nimport { createRandomNumberGenerator } from \"../../utils/utils.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CIRCULAR_CONTENT_SIZE = '1.3em';\nconst CONTENT_HEIGHT = '1.2em';\nconst DEFAULT_CONTENT_WIDTH_RANGE = [40, 80];\nconst CONTENT_WIDTH_RANGE_BY_TYPE = {\n  number: [40, 60],\n  string: [40, 80],\n  date: [40, 60],\n  dateTime: [60, 80],\n  singleSelect: [40, 80]\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    align,\n    classes,\n    empty\n  } = ownerState;\n  const slots = {\n    root: ['cell', 'cellSkeleton', `cell--text${align ? capitalize(align) : 'Left'}`, empty && 'cellEmpty']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst randomNumberGenerator = createRandomNumberGenerator(12345);\nfunction GridSkeletonCell(props) {\n  const {\n      field,\n      type,\n      align,\n      width,\n      height,\n      empty = false,\n      style,\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const rootProps = useGridRootProps();\n  const ownerState = {\n    classes: rootProps.classes,\n    align,\n    empty\n  };\n  const classes = useUtilityClasses(ownerState);\n\n  // Memo prevents the non-circular skeleton widths changing to random widths on every render\n  const skeletonProps = React.useMemo(() => {\n    const isCircularContent = type === 'boolean' || type === 'actions';\n    if (isCircularContent) {\n      return {\n        variant: 'circular',\n        width: CIRCULAR_CONTENT_SIZE,\n        height: CIRCULAR_CONTENT_SIZE\n      };\n    }\n\n    // The width of the skeleton is a random number between the min and max values\n    // The min and max values are determined by the type of the column\n    const [min, max] = type ? CONTENT_WIDTH_RANGE_BY_TYPE[type] ?? DEFAULT_CONTENT_WIDTH_RANGE : DEFAULT_CONTENT_WIDTH_RANGE;\n    return {\n      variant: 'text',\n      width: `${Math.round(randomNumberGenerator(min, max))}%`,\n      height: CONTENT_HEIGHT\n    };\n  }, [type]);\n  return /*#__PURE__*/_jsx(\"div\", _extends({\n    \"data-field\": field,\n    className: clsx(classes.root, className),\n    style: _extends({\n      height,\n      maxWidth: width,\n      minWidth: width\n    }, style)\n  }, other, {\n    children: !empty && /*#__PURE__*/_jsx(rootProps.slots.baseSkeleton, _extends({}, skeletonProps))\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? GridSkeletonCell.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  align: PropTypes.string,\n  /**\n   * If `true`, the cell will not display the skeleton but still reserve the cell space.\n   * @default false\n   */\n  empty: PropTypes.bool,\n  field: PropTypes.string,\n  height: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.number]),\n  type: PropTypes.oneOf(['actions', 'boolean', 'custom', 'date', 'dateTime', 'number', 'singleSelect', 'string']),\n  width: PropTypes.oneOfType([PropTypes.number, PropTypes.string])\n} : void 0;\nconst Memoized = fastMemo(GridSkeletonCell);\nexport { Memoized as GridSkeletonCell };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}