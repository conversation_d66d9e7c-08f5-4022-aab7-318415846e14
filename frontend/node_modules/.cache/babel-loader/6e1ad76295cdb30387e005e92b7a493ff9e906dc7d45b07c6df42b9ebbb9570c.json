{"ast": null, "code": "import * as React from 'react';\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridRegisterStrategyProcessor } from \"../../core/strategyProcessing/useGridRegisterStrategyProcessor.js\";\nimport { useGridEvent as addEventHandler } from \"../../utils/useGridEvent.js\";\nimport { useGridDataSourceBase } from \"./useGridDataSourceBase.js\";\n/**\n * Community version of the data source hook. Contains implementation of the `useGridDataSourceBase` hook.\n */\nexport const useGridDataSource = (apiRef, props) => {\n  const {\n    api,\n    strategyProcessor,\n    events,\n    setStrategyAvailability\n  } = useGridDataSourceBase(apiRef, props);\n  useGridApiMethod(apiRef, api.public, 'public');\n  useGridRegisterStrategyProcessor(apiRef, strategyProcessor.strategyName, strategyProcessor.group, strategyProcessor.processor);\n  Object.entries(events).forEach(([event, handler]) => {\n    addEventHandler(apiRef, event, handler);\n  });\n  React.useEffect(() => {\n    setStrategyAvailability();\n  }, [setStrategyAvailability]);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}