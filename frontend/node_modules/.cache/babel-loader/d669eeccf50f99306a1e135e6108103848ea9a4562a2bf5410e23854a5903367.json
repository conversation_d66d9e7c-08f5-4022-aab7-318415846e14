{"ast": null, "code": "import { GRID_ID_AUTOGENERATED } from \"../features/rows/gridRowsUtils.js\";\nimport { createRootSelector } from \"../../utils/createSelector.js\";\n\n/**\n * Get the row id for a given row\n * @param apiRef - The grid api reference\n * @param {GridRowModel} row - The row to get the id for\n * @returns {GridRowId} The row id\n */\nexport const gridRowIdSelector = createRootSelector((state, row) => {\n  if (GRID_ID_AUTOGENERATED in row) {\n    return row[GRID_ID_AUTOGENERATED];\n  }\n  return state.props.getRowId ? state.props.getRowId(row) : row.id;\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}