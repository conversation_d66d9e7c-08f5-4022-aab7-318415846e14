{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/components/ModernLayout.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Outlet, useNavigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { Avatar, Dropdown, Button } from 'flowbite-react';\nimport { HiChart<PERSON><PERSON>, HiUsers, HiShieldCheck, HiKey, HiLogout, HiMenu, HiX } from 'react-icons/hi';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ModernLayout = () => {\n  _s();\n  var _filteredMenuItems$fi;\n  const {\n    user,\n    logout,\n    hasPermission\n  } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const handleLogout = () => {\n    logout();\n    navigate('/login');\n  };\n  const menuItems = [{\n    title: 'Dashboard',\n    path: '/dashboard',\n    icon: HiChart<PERSON>ie,\n    permission: 'dashboard:access'\n  }, {\n    title: 'Utilisateurs',\n    path: '/users',\n    icon: HiUsers,\n    permission: 'user:read'\n  }, {\n    title: 'Rôles',\n    path: '/roles',\n    icon: HiShieldCheck,\n    permission: 'role:read'\n  }, {\n    title: 'Permissions',\n    path: '/permissions',\n    icon: HiKey,\n    permission: 'permission:read'\n  }];\n  const filteredMenuItems = menuItems.filter(item => hasPermission(item.permission));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"lg:hidden\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between p-4 bg-white border-b border-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-xl font-semibold text-gray-900\",\n          children: \"Gestion Utilisateurs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          color: \"gray\",\n          size: \"sm\",\n          onClick: () => setSidebarOpen(!sidebarOpen),\n          className: \"lg:hidden\",\n          children: sidebarOpen ? /*#__PURE__*/_jsxDEV(HiX, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 28\n          }, this) : /*#__PURE__*/_jsxDEV(HiMenu, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 58\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `\n          fixed inset-y-0 left-0 z-50 w-64 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0\n          ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}\n        `,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col h-full\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center h-16 px-4 bg-primary-600\",\n            children: /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-xl font-bold text-white\",\n              children: \"Admin Panel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n            className: \"flex-1 px-4 py-6 space-y-2\",\n            children: filteredMenuItems.map(item => {\n              const Icon = item.icon;\n              const isActive = location.pathname === item.path;\n              return /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  navigate(item.path);\n                  setSidebarOpen(false);\n                },\n                className: `\n                      w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200\n                      ${isActive ? 'bg-primary-100 text-primary-700 border-r-2 border-primary-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'}\n                    `,\n                children: [/*#__PURE__*/_jsxDEV(Icon, {\n                  className: \"w-5 h-5 mr-3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 117,\n                  columnNumber: 21\n                }, this), item.title]\n              }, item.path, true, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 border-t border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                img: `https://ui-avatars.com/api/?name=${encodeURIComponent((user === null || user === void 0 ? void 0 : user.full_name) || 'User')}&background=3b82f6&color=fff`,\n                rounded: true,\n                size: \"sm\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1 min-w-0\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-900 truncate\",\n                  children: user === null || user === void 0 ? void 0 : user.full_name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500 truncate\",\n                  children: user === null || user === void 0 ? void 0 : user.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 136,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n                arrowIcon: false,\n                inline: true,\n                label: /*#__PURE__*/_jsxDEV(Button, {\n                  color: \"gray\",\n                  size: \"xs\",\n                  className: \"p-1\",\n                  children: /*#__PURE__*/_jsxDEV(HiLogout, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 145,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 21\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(Dropdown.Item, {\n                  onClick: handleLogout,\n                  children: [/*#__PURE__*/_jsxDEV(HiLogout, {\n                    className: \"w-4 h-4 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 150,\n                    columnNumber: 21\n                  }, this), \"D\\xE9connexion\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 149,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), sidebarOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\",\n        onClick: () => setSidebarOpen(false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 lg:ml-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden lg:block\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white border-b border-gray-200 px-6 py-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-2xl font-semibold text-gray-900\",\n                  children: ((_filteredMenuItems$fi = filteredMenuItems.find(item => item.path === location.pathname)) === null || _filteredMenuItems$fi === void 0 ? void 0 : _filteredMenuItems$fi.title) || 'Dashboard'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-500 mt-1\",\n                  children: [\"Bienvenue, \", user === null || user === void 0 ? void 0 : user.full_name]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                  img: `https://ui-avatars.com/api/?name=${encodeURIComponent((user === null || user === void 0 ? void 0 : user.full_name) || 'User')}&background=3b82f6&color=fff`,\n                  rounded: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n                  arrowIcon: false,\n                  inline: true,\n                  label: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-700 hover:text-gray-900 cursor-pointer\",\n                    children: user === null || user === void 0 ? void 0 : user.full_name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 191,\n                    columnNumber: 23\n                  }, this),\n                  children: [/*#__PURE__*/_jsxDEV(Dropdown.Header, {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"block text-sm\",\n                      children: user === null || user === void 0 ? void 0 : user.full_name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 197,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"block truncate text-sm font-medium\",\n                      children: user === null || user === void 0 ? void 0 : user.email\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 198,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 196,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Dropdown.Item, {\n                    onClick: handleLogout,\n                    children: [/*#__PURE__*/_jsxDEV(HiLogout, {\n                      className: \"w-4 h-4 mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 201,\n                      columnNumber: 23\n                    }, this), \"D\\xE9connexion\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 200,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n          className: \"p-6\",\n          children: /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 64,\n    columnNumber: 5\n  }, this);\n};\n_s(ModernLayout, \"/wLYle2orWEQahPnKwc4pu7MfsM=\", false, function () {\n  return [useAuth, useNavigate, useLocation];\n});\n_c = ModernLayout;\nexport default ModernLayout;\nvar _c;\n$RefreshReg$(_c, \"ModernLayout\");", "map": {"version": 3, "names": ["React", "useState", "Outlet", "useNavigate", "useLocation", "useAuth", "Avatar", "Dropdown", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "HiUsers", "HiShieldCheck", "<PERSON><PERSON><PERSON>", "HiLogout", "HiMenu", "HiX", "jsxDEV", "_jsxDEV", "ModernLayout", "_s", "_filteredMenuItems$fi", "user", "logout", "hasPermission", "navigate", "location", "sidebarOpen", "setSidebarOpen", "handleLogout", "menuItems", "title", "path", "icon", "permission", "filteredMenuItems", "filter", "item", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "size", "onClick", "map", "Icon", "isActive", "pathname", "img", "encodeURIComponent", "full_name", "rounded", "email", "arrowIcon", "inline", "label", "<PERSON><PERSON>", "find", "Header", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/components/ModernLayout.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Outlet, useNavigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport {\n  Sidebar,\n  Navbar,\n  Avatar,\n  Dropdown,\n  Button,\n} from 'flowbite-react';\nimport {\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  HiShieldCheck,\n  HiKey,\n  HiLogout,\n  HiMenu,\n  HiX,\n} from 'react-icons/hi';\n\nconst ModernLayout: React.FC = () => {\n  const { user, logout, hasPermission } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  const handleLogout = () => {\n    logout();\n    navigate('/login');\n  };\n\n  const menuItems = [\n    {\n      title: 'Dashboard',\n      path: '/dashboard',\n      icon: HiChartPie,\n      permission: 'dashboard:access',\n    },\n    {\n      title: 'Utilisateurs',\n      path: '/users',\n      icon: HiUsers,\n      permission: 'user:read',\n    },\n    {\n      title: 'R<PERSON><PERSON>',\n      path: '/roles',\n      icon: HiShi<PERSON><PERSON><PERSON><PERSON>,\n      permission: 'role:read',\n    },\n    {\n      title: 'Permissions',\n      path: '/permissions',\n      icon: <PERSON><PERSON><PERSON>,\n      permission: 'permission:read',\n    },\n  ];\n\n  const filteredMenuItems = menuItems.filter(item => \n    hasPermission(item.permission)\n  );\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Mobile menu button */}\n      <div className=\"lg:hidden\">\n        <div className=\"flex items-center justify-between p-4 bg-white border-b border-gray-200\">\n          <h1 className=\"text-xl font-semibold text-gray-900\">\n            Gestion Utilisateurs\n          </h1>\n          <Button\n            color=\"gray\"\n            size=\"sm\"\n            onClick={() => setSidebarOpen(!sidebarOpen)}\n            className=\"lg:hidden\"\n          >\n            {sidebarOpen ? <HiX className=\"w-5 h-5\" /> : <HiMenu className=\"w-5 h-5\" />}\n          </Button>\n        </div>\n      </div>\n\n      <div className=\"flex\">\n        {/* Sidebar */}\n        <div className={`\n          fixed inset-y-0 left-0 z-50 w-64 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0\n          ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}\n        `}>\n          <div className=\"flex flex-col h-full\">\n            {/* Logo */}\n            <div className=\"flex items-center justify-center h-16 px-4 bg-primary-600\">\n              <h1 className=\"text-xl font-bold text-white\">\n                Admin Panel\n              </h1>\n            </div>\n\n            {/* Navigation */}\n            <nav className=\"flex-1 px-4 py-6 space-y-2\">\n              {filteredMenuItems.map((item) => {\n                const Icon = item.icon;\n                const isActive = location.pathname === item.path;\n                \n                return (\n                  <button\n                    key={item.path}\n                    onClick={() => {\n                      navigate(item.path);\n                      setSidebarOpen(false);\n                    }}\n                    className={`\n                      w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200\n                      ${isActive \n                        ? 'bg-primary-100 text-primary-700 border-r-2 border-primary-600' \n                        : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'\n                      }\n                    `}\n                  >\n                    <Icon className=\"w-5 h-5 mr-3\" />\n                    {item.title}\n                  </button>\n                );\n              })}\n            </nav>\n\n            {/* User section */}\n            <div className=\"p-4 border-t border-gray-200\">\n              <div className=\"flex items-center space-x-3\">\n                <Avatar\n                  img={`https://ui-avatars.com/api/?name=${encodeURIComponent(user?.full_name || 'User')}&background=3b82f6&color=fff`}\n                  rounded\n                  size=\"sm\"\n                />\n                <div className=\"flex-1 min-w-0\">\n                  <p className=\"text-sm font-medium text-gray-900 truncate\">\n                    {user?.full_name}\n                  </p>\n                  <p className=\"text-xs text-gray-500 truncate\">\n                    {user?.email}\n                  </p>\n                </div>\n                <Dropdown\n                  arrowIcon={false}\n                  inline\n                  label={\n                    <Button color=\"gray\" size=\"xs\" className=\"p-1\">\n                      <HiLogout className=\"w-4 h-4\" />\n                    </Button>\n                  }\n                >\n                  <Dropdown.Item onClick={handleLogout}>\n                    <HiLogout className=\"w-4 h-4 mr-2\" />\n                    Déconnexion\n                  </Dropdown.Item>\n                </Dropdown>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Overlay for mobile */}\n        {sidebarOpen && (\n          <div\n            className=\"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\"\n            onClick={() => setSidebarOpen(false)}\n          />\n        )}\n\n        {/* Main content */}\n        <div className=\"flex-1 lg:ml-0\">\n          {/* Top bar for desktop */}\n          <div className=\"hidden lg:block\">\n            <div className=\"bg-white border-b border-gray-200 px-6 py-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <h1 className=\"text-2xl font-semibold text-gray-900\">\n                    {filteredMenuItems.find(item => item.path === location.pathname)?.title || 'Dashboard'}\n                  </h1>\n                  <p className=\"text-sm text-gray-500 mt-1\">\n                    Bienvenue, {user?.full_name}\n                  </p>\n                </div>\n                \n                <div className=\"flex items-center space-x-4\">\n                  <Avatar\n                    img={`https://ui-avatars.com/api/?name=${encodeURIComponent(user?.full_name || 'User')}&background=3b82f6&color=fff`}\n                    rounded\n                  />\n                  <Dropdown\n                    arrowIcon={false}\n                    inline\n                    label={\n                      <span className=\"text-sm font-medium text-gray-700 hover:text-gray-900 cursor-pointer\">\n                        {user?.full_name}\n                      </span>\n                    }\n                  >\n                    <Dropdown.Header>\n                      <span className=\"block text-sm\">{user?.full_name}</span>\n                      <span className=\"block truncate text-sm font-medium\">{user?.email}</span>\n                    </Dropdown.Header>\n                    <Dropdown.Item onClick={handleLogout}>\n                      <HiLogout className=\"w-4 h-4 mr-2\" />\n                      Déconnexion\n                    </Dropdown.Item>\n                  </Dropdown>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Page content */}\n          <main className=\"p-6\">\n            <Outlet />\n          </main>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ModernLayout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACnE,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAGEC,MAAM,EACNC,QAAQ,EACRC,MAAM,QACD,gBAAgB;AACvB,SACEC,UAAU,EACVC,OAAO,EACPC,aAAa,EACbC,KAAK,EACLC,QAAQ,EACRC,MAAM,EACNC,GAAG,QACE,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACnC,MAAM;IAAEC,IAAI;IAAEC,MAAM;IAAEC;EAAc,CAAC,GAAGlB,OAAO,CAAC,CAAC;EACjD,MAAMmB,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAC9B,MAAMsB,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACsB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAM2B,YAAY,GAAGA,CAAA,KAAM;IACzBN,MAAM,CAAC,CAAC;IACRE,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAMK,SAAS,GAAG,CAChB;IACEC,KAAK,EAAE,WAAW;IAClBC,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAEvB,UAAU;IAChBwB,UAAU,EAAE;EACd,CAAC,EACD;IACEH,KAAK,EAAE,cAAc;IACrBC,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAEtB,OAAO;IACbuB,UAAU,EAAE;EACd,CAAC,EACD;IACEH,KAAK,EAAE,OAAO;IACdC,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAErB,aAAa;IACnBsB,UAAU,EAAE;EACd,CAAC,EACD;IACEH,KAAK,EAAE,aAAa;IACpBC,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAEpB,KAAK;IACXqB,UAAU,EAAE;EACd,CAAC,CACF;EAED,MAAMC,iBAAiB,GAAGL,SAAS,CAACM,MAAM,CAACC,IAAI,IAC7Cb,aAAa,CAACa,IAAI,CAACH,UAAU,CAC/B,CAAC;EAED,oBACEhB,OAAA;IAAKoB,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBAEtCrB,OAAA;MAAKoB,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBrB,OAAA;QAAKoB,SAAS,EAAC,yEAAyE;QAAAC,QAAA,gBACtFrB,OAAA;UAAIoB,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAEpD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLzB,OAAA,CAACT,MAAM;UACLmC,KAAK,EAAC,MAAM;UACZC,IAAI,EAAC,IAAI;UACTC,OAAO,EAAEA,CAAA,KAAMlB,cAAc,CAAC,CAACD,WAAW,CAAE;UAC5CW,SAAS,EAAC,WAAW;UAAAC,QAAA,EAEpBZ,WAAW,gBAAGT,OAAA,CAACF,GAAG;YAACsB,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGzB,OAAA,CAACH,MAAM;YAACuB,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENzB,OAAA;MAAKoB,SAAS,EAAC,MAAM;MAAAC,QAAA,gBAEnBrB,OAAA;QAAKoB,SAAS,EAAE;AACxB;AACA,YAAYX,WAAW,GAAG,eAAe,GAAG,mBAAmB;AAC/D,SAAU;QAAAY,QAAA,eACArB,OAAA;UAAKoB,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBAEnCrB,OAAA;YAAKoB,SAAS,EAAC,2DAA2D;YAAAC,QAAA,eACxErB,OAAA;cAAIoB,SAAS,EAAC,8BAA8B;cAAAC,QAAA,EAAC;YAE7C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGNzB,OAAA;YAAKoB,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EACxCJ,iBAAiB,CAACY,GAAG,CAAEV,IAAI,IAAK;cAC/B,MAAMW,IAAI,GAAGX,IAAI,CAACJ,IAAI;cACtB,MAAMgB,QAAQ,GAAGvB,QAAQ,CAACwB,QAAQ,KAAKb,IAAI,CAACL,IAAI;cAEhD,oBACEd,OAAA;gBAEE4B,OAAO,EAAEA,CAAA,KAAM;kBACbrB,QAAQ,CAACY,IAAI,CAACL,IAAI,CAAC;kBACnBJ,cAAc,CAAC,KAAK,CAAC;gBACvB,CAAE;gBACFU,SAAS,EAAE;AAC/B;AACA,wBAAwBW,QAAQ,GACN,+DAA+D,GAC/D,qDAAqD;AAC/E,qBACsB;gBAAAV,QAAA,gBAEFrB,OAAA,CAAC8B,IAAI;kBAACV,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAChCN,IAAI,CAACN,KAAK;cAAA,GAdNM,IAAI,CAACL,IAAI;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAeR,CAAC;YAEb,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNzB,OAAA;YAAKoB,SAAS,EAAC,8BAA8B;YAAAC,QAAA,eAC3CrB,OAAA;cAAKoB,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CrB,OAAA,CAACX,MAAM;gBACL4C,GAAG,EAAE,oCAAoCC,kBAAkB,CAAC,CAAA9B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+B,SAAS,KAAI,MAAM,CAAC,8BAA+B;gBACrHC,OAAO;gBACPT,IAAI,EAAC;cAAI;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACFzB,OAAA;gBAAKoB,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BrB,OAAA;kBAAGoB,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EACtDjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+B;gBAAS;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eACJzB,OAAA;kBAAGoB,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAC1CjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiC;gBAAK;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNzB,OAAA,CAACV,QAAQ;gBACPgD,SAAS,EAAE,KAAM;gBACjBC,MAAM;gBACNC,KAAK,eACHxC,OAAA,CAACT,MAAM;kBAACmC,KAAK,EAAC,MAAM;kBAACC,IAAI,EAAC,IAAI;kBAACP,SAAS,EAAC,KAAK;kBAAAC,QAAA,eAC5CrB,OAAA,CAACJ,QAAQ;oBAACwB,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CACT;gBAAAJ,QAAA,eAEDrB,OAAA,CAACV,QAAQ,CAACmD,IAAI;kBAACb,OAAO,EAAEjB,YAAa;kBAAAU,QAAA,gBACnCrB,OAAA,CAACJ,QAAQ;oBAACwB,SAAS,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,kBAEvC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLhB,WAAW,iBACVT,OAAA;QACEoB,SAAS,EAAC,qDAAqD;QAC/DQ,OAAO,EAAEA,CAAA,KAAMlB,cAAc,CAAC,KAAK;MAAE;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CACF,eAGDzB,OAAA;QAAKoB,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAE7BrB,OAAA;UAAKoB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BrB,OAAA;YAAKoB,SAAS,EAAC,6CAA6C;YAAAC,QAAA,eAC1DrB,OAAA;cAAKoB,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDrB,OAAA;gBAAAqB,QAAA,gBACErB,OAAA;kBAAIoB,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,EACjD,EAAAlB,qBAAA,GAAAc,iBAAiB,CAACyB,IAAI,CAACvB,IAAI,IAAIA,IAAI,CAACL,IAAI,KAAKN,QAAQ,CAACwB,QAAQ,CAAC,cAAA7B,qBAAA,uBAA/DA,qBAAA,CAAiEU,KAAK,KAAI;gBAAW;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF,CAAC,eACLzB,OAAA;kBAAGoB,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,GAAC,aAC7B,EAACjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+B,SAAS;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAENzB,OAAA;gBAAKoB,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CrB,OAAA,CAACX,MAAM;kBACL4C,GAAG,EAAE,oCAAoCC,kBAAkB,CAAC,CAAA9B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+B,SAAS,KAAI,MAAM,CAAC,8BAA+B;kBACrHC,OAAO;gBAAA;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACFzB,OAAA,CAACV,QAAQ;kBACPgD,SAAS,EAAE,KAAM;kBACjBC,MAAM;kBACNC,KAAK,eACHxC,OAAA;oBAAMoB,SAAS,EAAC,sEAAsE;oBAAAC,QAAA,EACnFjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+B;kBAAS;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CACP;kBAAAJ,QAAA,gBAEDrB,OAAA,CAACV,QAAQ,CAACqD,MAAM;oBAAAtB,QAAA,gBACdrB,OAAA;sBAAMoB,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAEjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+B;oBAAS;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACxDzB,OAAA;sBAAMoB,SAAS,EAAC,oCAAoC;sBAAAC,QAAA,EAAEjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiC;oBAAK;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1D,CAAC,eAClBzB,OAAA,CAACV,QAAQ,CAACmD,IAAI;oBAACb,OAAO,EAAEjB,YAAa;oBAAAU,QAAA,gBACnCrB,OAAA,CAACJ,QAAQ;sBAACwB,SAAS,EAAC;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,kBAEvC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAe,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNzB,OAAA;UAAMoB,SAAS,EAAC,KAAK;UAAAC,QAAA,eACnBrB,OAAA,CAACf,MAAM;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvB,EAAA,CArMID,YAAsB;EAAA,QACcb,OAAO,EAC9BF,WAAW,EACXC,WAAW;AAAA;AAAAyD,EAAA,GAHxB3C,YAAsB;AAuM5B,eAAeA,YAAY;AAAC,IAAA2C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}