{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport { styled } from '@mui/material/styles';\nimport { useGridSelector } from \"../../utils/index.js\";\nimport { useGridRootProps } from \"../../utils/useGridRootProps.js\";\nimport { useGridPrivateApiContext } from \"../../utils/useGridPrivateApiContext.js\";\nimport { useGridEvent } from \"../../utils/useGridEvent.js\";\nimport { GridColumnHeaderItem } from \"../../../components/columnHeaders/GridColumnHeaderItem.js\";\nimport { gridColumnsTotalWidthSelector, gridGroupHeaderHeightSelector, gridHasFillerSelector, gridHeaderHeightSelector, gridVerticalScrollbarWidthSelector } from \"../dimensions/gridDimensionsSelectors.js\";\nimport { gridRenderContextColumnsSelector } from \"../virtualization/index.js\";\nimport { computeOffsetLeft } from \"../virtualization/useGridVirtualScroller.js\";\nimport { GridColumnGroupHeader } from \"../../../components/columnHeaders/GridColumnGroupHeader.js\";\nimport { gridColumnPositionsSelector, gridVisiblePinnedColumnDefinitionsSelector, gridColumnLookupSelector } from \"../columns/index.js\";\nimport { gridColumnGroupsUnwrappedModelSelector } from \"../columnGrouping/gridColumnGroupsSelector.js\";\nimport { GridScrollbarFillerCell as ScrollbarFiller } from \"../../../components/GridScrollbarFillerCell.js\";\nimport { getPinnedCellOffset } from \"../../../internals/utils/getPinnedCellOffset.js\";\nimport { GridColumnHeaderSeparatorSides } from \"../../../components/columnHeaders/GridColumnHeaderSeparator.js\";\nimport { gridClasses } from \"../../../constants/gridClasses.js\";\nimport { shouldCellShowLeftBorder, shouldCellShowRightBorder } from \"../../../utils/cellBorderUtils.js\";\nimport { PinnedColumnPosition } from \"../../../internals/constants.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport const GridColumnHeaderRow = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'ColumnHeaderRow'\n})({\n  display: 'flex'\n});\nexport const useGridColumnHeaders = props => {\n  const {\n    visibleColumns,\n    sortColumnLookup,\n    filterColumnLookup,\n    columnHeaderTabIndexState,\n    columnGroupHeaderTabIndexState,\n    columnHeaderFocus,\n    columnGroupHeaderFocus,\n    headerGroupingMaxDepth,\n    columnMenuState,\n    columnVisibility,\n    columnGroupsHeaderStructure,\n    hasOtherElementInTabSequence\n  } = props;\n  const [dragCol, setDragCol] = React.useState('');\n  const [resizeCol, setResizeCol] = React.useState('');\n  const apiRef = useGridPrivateApiContext();\n  const rootProps = useGridRootProps();\n  const columnGroupsModel = useGridSelector(apiRef, gridColumnGroupsUnwrappedModelSelector);\n  const columnPositions = useGridSelector(apiRef, gridColumnPositionsSelector);\n  const renderContext = useGridSelector(apiRef, gridRenderContextColumnsSelector);\n  const pinnedColumns = useGridSelector(apiRef, gridVisiblePinnedColumnDefinitionsSelector);\n  const columnsLookup = useGridSelector(apiRef, gridColumnLookupSelector);\n  const offsetLeft = computeOffsetLeft(columnPositions, renderContext, pinnedColumns.left.length);\n  const columnsTotalWidth = useGridSelector(apiRef, gridColumnsTotalWidthSelector);\n  const gridHasFiller = useGridSelector(apiRef, gridHasFillerSelector);\n  const headerHeight = useGridSelector(apiRef, gridHeaderHeightSelector);\n  const groupHeaderHeight = useGridSelector(apiRef, gridGroupHeaderHeightSelector);\n  const scrollbarWidth = useGridSelector(apiRef, gridVerticalScrollbarWidthSelector);\n  const handleColumnResizeStart = React.useCallback(params => setResizeCol(params.field), []);\n  const handleColumnResizeStop = React.useCallback(() => setResizeCol(''), []);\n  const handleColumnReorderStart = React.useCallback(params => setDragCol(params.field), []);\n  const handleColumnReorderStop = React.useCallback(() => setDragCol(''), []);\n  const leftRenderContext = React.useMemo(() => {\n    return pinnedColumns.left.length ? {\n      firstColumnIndex: 0,\n      lastColumnIndex: pinnedColumns.left.length\n    } : null;\n  }, [pinnedColumns.left.length]);\n  const rightRenderContext = React.useMemo(() => {\n    return pinnedColumns.right.length ? {\n      firstColumnIndex: visibleColumns.length - pinnedColumns.right.length,\n      lastColumnIndex: visibleColumns.length\n    } : null;\n  }, [pinnedColumns.right.length, visibleColumns.length]);\n  useGridEvent(apiRef, 'columnResizeStart', handleColumnResizeStart);\n  useGridEvent(apiRef, 'columnResizeStop', handleColumnResizeStop);\n  useGridEvent(apiRef, 'columnHeaderDragStart', handleColumnReorderStart);\n  useGridEvent(apiRef, 'columnHeaderDragEndNative', handleColumnReorderStop);\n\n  // Helper for computation common between getColumnHeaders and getColumnGroupHeaders\n  const getColumnsToRender = params => {\n    const {\n      renderContext: currentContext = renderContext\n    } = params || {};\n    const firstColumnToRender = currentContext.firstColumnIndex;\n    const lastColumnToRender = currentContext.lastColumnIndex;\n    const renderedColumns = visibleColumns.slice(firstColumnToRender, lastColumnToRender);\n    return {\n      renderedColumns,\n      firstColumnToRender,\n      lastColumnToRender\n    };\n  };\n  const getFillers = (params, children, leftOverflow, borderBottom = false) => {\n    const isPinnedRight = params?.position === PinnedColumnPosition.RIGHT;\n    const isNotPinned = params?.position === undefined;\n    const hasScrollbarFiller = pinnedColumns.right.length > 0 && isPinnedRight || pinnedColumns.right.length === 0 && isNotPinned;\n    const leftOffsetWidth = offsetLeft - leftOverflow;\n    return /*#__PURE__*/_jsxs(React.Fragment, {\n      children: [isNotPinned && /*#__PURE__*/_jsx(\"div\", {\n        role: \"presentation\",\n        style: {\n          width: leftOffsetWidth\n        }\n      }), children, isNotPinned && /*#__PURE__*/_jsx(\"div\", {\n        role: \"presentation\",\n        className: clsx(gridClasses.filler, borderBottom && gridClasses['filler--borderBottom'])\n      }), hasScrollbarFiller && /*#__PURE__*/_jsx(ScrollbarFiller, {\n        header: true,\n        pinnedRight: isPinnedRight,\n        borderBottom: borderBottom,\n        borderTop: false\n      })]\n    });\n  };\n  if (process.env.NODE_ENV !== \"production\") getFillers.displayName = \"getFillers\";\n  const getColumnHeaders = (params, other = {}) => {\n    const {\n      renderedColumns,\n      firstColumnToRender\n    } = getColumnsToRender(params);\n    const columns = [];\n    for (let i = 0; i < renderedColumns.length; i += 1) {\n      const colDef = renderedColumns[i];\n      const columnIndex = firstColumnToRender + i;\n      const isFirstColumn = columnIndex === 0;\n      const tabIndex = columnHeaderTabIndexState !== null && columnHeaderTabIndexState.field === colDef.field || isFirstColumn && !hasOtherElementInTabSequence ? 0 : -1;\n      const hasFocus = columnHeaderFocus !== null && columnHeaderFocus.field === colDef.field;\n      const open = columnMenuState.open && columnMenuState.field === colDef.field;\n      const pinnedPosition = params?.position;\n      const pinnedOffset = getPinnedCellOffset(pinnedPosition, colDef.computedWidth, columnIndex, columnPositions, columnsTotalWidth, scrollbarWidth);\n      const siblingWithBorderingSeparator = pinnedPosition === PinnedColumnPosition.RIGHT ? renderedColumns[i - 1] : renderedColumns[i + 1];\n      const isSiblingFocused = siblingWithBorderingSeparator ? columnHeaderFocus !== null && columnHeaderFocus.field === siblingWithBorderingSeparator.field : false;\n      const isLastUnpinned = columnIndex + 1 === columnPositions.length - pinnedColumns.right.length;\n      const indexInSection = i;\n      const sectionLength = renderedColumns.length;\n      const showLeftBorder = shouldCellShowLeftBorder(pinnedPosition, indexInSection);\n      const showRightBorder = shouldCellShowRightBorder(pinnedPosition, indexInSection, sectionLength, rootProps.showColumnVerticalBorder, gridHasFiller);\n      columns.push(/*#__PURE__*/_jsx(GridColumnHeaderItem, _extends({}, sortColumnLookup[colDef.field], {\n        columnMenuOpen: open,\n        filterItemsCounter: filterColumnLookup[colDef.field] && filterColumnLookup[colDef.field].length,\n        headerHeight: headerHeight,\n        isDragging: colDef.field === dragCol,\n        colDef: colDef,\n        colIndex: columnIndex,\n        isResizing: resizeCol === colDef.field,\n        isLast: columnIndex === columnPositions.length - 1,\n        hasFocus: hasFocus,\n        tabIndex: tabIndex,\n        pinnedPosition: pinnedPosition,\n        pinnedOffset: pinnedOffset,\n        isLastUnpinned: isLastUnpinned,\n        isSiblingFocused: isSiblingFocused,\n        showLeftBorder: showLeftBorder,\n        showRightBorder: showRightBorder\n      }, other), colDef.field));\n    }\n    return getFillers(params, columns, 0);\n  };\n  const getColumnHeadersRow = () => {\n    return /*#__PURE__*/_jsxs(GridColumnHeaderRow, {\n      role: \"row\",\n      \"aria-rowindex\": headerGroupingMaxDepth + 1,\n      ownerState: rootProps,\n      className: gridClasses['row--borderBottom'],\n      style: {\n        height: headerHeight\n      },\n      children: [leftRenderContext && getColumnHeaders({\n        position: PinnedColumnPosition.LEFT,\n        renderContext: leftRenderContext\n      }, {\n        disableReorder: true\n      }), getColumnHeaders({\n        renderContext\n      }), rightRenderContext && getColumnHeaders({\n        position: PinnedColumnPosition.RIGHT,\n        renderContext: rightRenderContext\n      }, {\n        disableReorder: true,\n        separatorSide: GridColumnHeaderSeparatorSides.Left\n      })]\n    });\n  };\n  if (process.env.NODE_ENV !== \"production\") getColumnHeadersRow.displayName = \"getColumnHeadersRow\";\n  const getColumnGroupHeaders = ({\n    depth,\n    params\n  }) => {\n    const columnsToRender = getColumnsToRender(params);\n    if (columnsToRender.renderedColumns.length === 0) {\n      return null;\n    }\n    const {\n      firstColumnToRender,\n      lastColumnToRender\n    } = columnsToRender;\n    const rowStructure = columnGroupsHeaderStructure[depth];\n    const firstColumnFieldToRender = visibleColumns[firstColumnToRender].field;\n    const firstGroupToRender = columnGroupsModel[firstColumnFieldToRender]?.[depth] ?? null;\n    const firstGroupIndex = rowStructure.findIndex(({\n      groupId,\n      columnFields\n    }) => groupId === firstGroupToRender && columnFields.includes(firstColumnFieldToRender));\n    const lastColumnFieldToRender = visibleColumns[lastColumnToRender - 1].field;\n    const lastGroupToRender = columnGroupsModel[lastColumnFieldToRender]?.[depth] ?? null;\n    const lastGroupIndex = rowStructure.findIndex(({\n      groupId,\n      columnFields\n    }) => groupId === lastGroupToRender && columnFields.includes(lastColumnFieldToRender));\n    const visibleColumnGroupHeader = rowStructure.slice(firstGroupIndex, lastGroupIndex + 1).map(groupStructure => {\n      return _extends({}, groupStructure, {\n        columnFields: groupStructure.columnFields.filter(field => columnVisibility[field] !== false)\n      });\n    }).filter(groupStructure => groupStructure.columnFields.length > 0);\n    const firstVisibleColumnIndex = visibleColumnGroupHeader[0].columnFields.indexOf(firstColumnFieldToRender);\n    const hiddenGroupColumns = visibleColumnGroupHeader[0].columnFields.slice(0, firstVisibleColumnIndex);\n    const leftOverflow = hiddenGroupColumns.reduce((acc, field) => {\n      const column = columnsLookup[field];\n      return acc + (column.computedWidth ?? 0);\n    }, 0);\n    let columnIndex = firstColumnToRender;\n    const children = visibleColumnGroupHeader.map(({\n      groupId,\n      columnFields\n    }, index) => {\n      const hasFocus = columnGroupHeaderFocus !== null && columnGroupHeaderFocus.depth === depth && columnFields.includes(columnGroupHeaderFocus.field);\n      const tabIndex = columnGroupHeaderTabIndexState !== null && columnGroupHeaderTabIndexState.depth === depth && columnFields.includes(columnGroupHeaderTabIndexState.field) ? 0 : -1;\n      const headerInfo = {\n        groupId,\n        width: columnFields.reduce((acc, field) => acc + columnsLookup[field].computedWidth, 0),\n        fields: columnFields,\n        colIndex: columnIndex,\n        hasFocus,\n        tabIndex\n      };\n      const pinnedPosition = params.position;\n      const pinnedOffset = getPinnedCellOffset(pinnedPosition, headerInfo.width, columnIndex, columnPositions, columnsTotalWidth, scrollbarWidth);\n      columnIndex += columnFields.length;\n      let indexInSection = index;\n      if (pinnedPosition === PinnedColumnPosition.LEFT) {\n        // Group headers can expand to multiple columns, we need to adjust the index\n        indexInSection = columnIndex - 1;\n      }\n      return /*#__PURE__*/_jsx(GridColumnGroupHeader, {\n        groupId: groupId,\n        width: headerInfo.width,\n        fields: headerInfo.fields,\n        colIndex: headerInfo.colIndex,\n        depth: depth,\n        isLastColumn: index === visibleColumnGroupHeader.length - 1,\n        maxDepth: headerGroupingMaxDepth,\n        height: groupHeaderHeight,\n        hasFocus: hasFocus,\n        tabIndex: tabIndex,\n        pinnedPosition: pinnedPosition,\n        pinnedOffset: pinnedOffset,\n        showLeftBorder: shouldCellShowLeftBorder(pinnedPosition, indexInSection),\n        showRightBorder: shouldCellShowRightBorder(pinnedPosition, indexInSection, visibleColumnGroupHeader.length, rootProps.showColumnVerticalBorder, gridHasFiller)\n      }, index);\n    });\n    return getFillers(params, children, leftOverflow);\n  };\n  const getColumnGroupHeadersRows = () => {\n    if (headerGroupingMaxDepth === 0) {\n      return null;\n    }\n    const headerRows = [];\n    for (let depth = 0; depth < headerGroupingMaxDepth; depth += 1) {\n      headerRows.push(/*#__PURE__*/_jsxs(GridColumnHeaderRow, {\n        role: \"row\",\n        \"aria-rowindex\": depth + 1,\n        ownerState: rootProps,\n        style: {\n          height: groupHeaderHeight\n        },\n        children: [leftRenderContext && getColumnGroupHeaders({\n          depth,\n          params: {\n            position: PinnedColumnPosition.LEFT,\n            renderContext: leftRenderContext,\n            maxLastColumn: leftRenderContext.lastColumnIndex\n          }\n        }), getColumnGroupHeaders({\n          depth,\n          params: {\n            renderContext\n          }\n        }), rightRenderContext && getColumnGroupHeaders({\n          depth,\n          params: {\n            position: PinnedColumnPosition.RIGHT,\n            renderContext: rightRenderContext,\n            maxLastColumn: rightRenderContext.lastColumnIndex\n          }\n        })]\n      }, depth));\n    }\n    return headerRows;\n  };\n  return {\n    renderContext,\n    leftRenderContext,\n    rightRenderContext,\n    pinnedColumns,\n    visibleColumns,\n    columnPositions,\n    getFillers,\n    getColumnHeadersRow,\n    getColumnsToRender,\n    getColumnGroupHeadersRows,\n    getPinnedCellOffset,\n    isDragging: !!dragCol,\n    getInnerProps: () => ({\n      role: 'rowgroup'\n    })\n  };\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}