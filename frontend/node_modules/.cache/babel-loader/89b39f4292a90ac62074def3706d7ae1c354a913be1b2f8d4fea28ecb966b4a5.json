{"ast": null, "code": "import { GRID_ROW_GROUPING_SINGLE_GROUPING_FIELD } from \"../constants.js\";\nexport const getRowGroupingCriteriaFromGroupingField = groupingColDefField => {\n  const match = groupingColDefField.match(/^__row_group_by_columns_group_(.*)__$/);\n  if (!match) {\n    return null;\n  }\n  return match[1];\n};\nexport const isGroupingColumn = field => field === GRID_ROW_GROUPING_SINGLE_GROUPING_FIELD || getRowGroupingCriteriaFromGroupingField(field) !== null;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}