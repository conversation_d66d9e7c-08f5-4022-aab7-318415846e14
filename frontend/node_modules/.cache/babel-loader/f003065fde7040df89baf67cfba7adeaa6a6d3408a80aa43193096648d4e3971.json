{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { GridCellCheckboxRenderer } from \"../components/columnSelection/GridCellCheckboxRenderer.js\";\nimport { GridHeaderCheckbox } from \"../components/columnSelection/GridHeaderCheckbox.js\";\nimport { GRID_BOOLEAN_COL_DEF } from \"./gridBooleanColDef.js\";\nimport { gridRowIdSelector } from \"../hooks/core/gridPropsSelectors.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const GRID_CHECKBOX_SELECTION_FIELD = '__check__';\nexport const GRID_CHECKBOX_SELECTION_COL_DEF = _extends({}, GRID_BOOLEAN_COL_DEF, {\n  type: 'custom',\n  field: GRID_CHECKBOX_SELECTION_FIELD,\n  width: 50,\n  resizable: false,\n  sortable: false,\n  filterable: false,\n  // @ts-ignore\n  aggregable: false,\n  disableColumnMenu: true,\n  disableReorder: true,\n  disableExport: true,\n  getApplyQuickFilterFn: () => null,\n  display: 'flex',\n  valueGetter: (value, row, column, apiRef) => {\n    const rowId = gridRowIdSelector(apiRef, row);\n    return apiRef.current.isRowSelected(rowId);\n  },\n  renderHeader: params => /*#__PURE__*/_jsx(GridHeaderCheckbox, _extends({}, params)),\n  renderCell: params => /*#__PURE__*/_jsx(GridCellCheckboxRenderer, _extends({}, params))\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}