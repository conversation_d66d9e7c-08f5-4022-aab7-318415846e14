{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { useGridApiContext } from \"../hooks/utils/useGridApiContext.js\";\nimport { GridOverlay } from \"./containers/GridOverlay.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst GridNoRowsOverlay = forwardRef(function GridNoRowsOverlay(props, ref) {\n  const apiRef = useGridApiContext();\n  const noRowsLabel = apiRef.current.getLocaleText('noRowsLabel');\n  return /*#__PURE__*/_jsx(GridOverlay, _extends({}, props, {\n    ref: ref,\n    children: noRowsLabel\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridNoRowsOverlay.displayName = \"GridNoRowsOverlay\";\nprocess.env.NODE_ENV !== \"production\" ? GridNoRowsOverlay.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport { GridNoRowsOverlay };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}