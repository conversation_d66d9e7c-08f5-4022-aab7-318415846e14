{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { GridIconButtonContainer } from \"./GridIconButtonContainer.js\";\nimport { GridColumnSortButton } from \"../GridColumnSortButton.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction GridColumnHeaderSortIconRaw(props) {\n  return /*#__PURE__*/_jsx(GridIconButtonContainer, {\n    children: /*#__PURE__*/_jsx(GridColumnSortButton, _extends({}, props, {\n      tabIndex: -1\n    }))\n  });\n}\nconst GridColumnHeaderSortIcon = /*#__PURE__*/React.memo(GridColumnHeaderSortIconRaw);\nif (process.env.NODE_ENV !== \"production\") GridColumnHeaderSortIcon.displayName = \"GridColumnHeaderSortIcon\";\nprocess.env.NODE_ENV !== \"production\" ? GridColumnHeaderSortIconRaw.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  className: PropTypes.string,\n  color: PropTypes.oneOf(['default', 'inherit', 'primary']),\n  direction: PropTypes.oneOf(['asc', 'desc']),\n  disabled: PropTypes.bool,\n  edge: PropTypes.oneOf(['end', 'start', false]),\n  field: PropTypes.string.isRequired,\n  id: PropTypes.string,\n  index: PropTypes.number,\n  label: PropTypes.string,\n  role: PropTypes.string,\n  size: PropTypes.oneOf(['large', 'medium', 'small']),\n  sortingOrder: PropTypes.arrayOf(PropTypes.oneOf(['asc', 'desc'])).isRequired,\n  style: PropTypes.object,\n  tabIndex: PropTypes.number,\n  title: PropTypes.string,\n  touchRippleRef: PropTypes.any\n} : void 0;\nexport { GridColumnHeaderSortIcon };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}