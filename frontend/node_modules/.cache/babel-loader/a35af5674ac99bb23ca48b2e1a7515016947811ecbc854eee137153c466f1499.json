{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// NOTE: Breakpoints can't come from the theme because we need access to them at\n// initialization time and media-queries can't use CSS variables. For users with\n// custom breakpoints, we might want to provide a way to configure them globally\n// instead of through the theme.\nconst breakpoints = {\n  values: {\n    xs: 0,\n    // phone\n    sm: 600,\n    // tablet\n    md: 900,\n    // small laptop\n    lg: 1200,\n    // desktop\n    xl: 1536 // large screen\n  },\n  up: key => {\n    const values = breakpoints.values;\n    const value = typeof values[key] === 'number' ? values[key] : key;\n    return `@media (min-width:${value}px)`;\n  }\n};\nconst keys = {\n  spacingUnit: '--DataGrid-t-spacing-unit',\n  /* Variables */\n  colors: {\n    border: {\n      base: '--DataGrid-t-color-border-base'\n    },\n    foreground: {\n      base: '--DataGrid-t-color-foreground-base',\n      muted: '--DataGrid-t-color-foreground-muted',\n      accent: '--DataGrid-t-color-foreground-accent',\n      disabled: '--DataGrid-t-color-foreground-disabled',\n      error: '--DataGrid-t-color-foreground-error'\n    },\n    background: {\n      base: '--DataGrid-t-color-background-base',\n      overlay: '--DataGrid-t-color-background-overlay',\n      backdrop: '--DataGrid-t-color-background-backdrop'\n    },\n    interactive: {\n      hover: '--DataGrid-t-color-interactive-hover',\n      hoverOpacity: '--DataGrid-t-color-interactive-hover-opacity',\n      focus: '--DataGrid-t-color-interactive-focus',\n      focusOpacity: '--DataGrid-t-color-interactive-focus-opacity',\n      disabled: '--DataGrid-t-color-interactive-disabled',\n      disabledOpacity: '--DataGrid-t-color-interactive-disabled-opacity',\n      selected: '--DataGrid-t-color-interactive-selected',\n      selectedOpacity: '--DataGrid-t-color-interactive-selected-opacity'\n    }\n  },\n  header: {\n    background: {\n      base: '--DataGrid-t-header-background-base'\n    }\n  },\n  cell: {\n    background: {\n      pinned: '--DataGrid-t-cell-background-pinned'\n    }\n  },\n  radius: {\n    base: '--DataGrid-t-radius-base'\n  },\n  typography: {\n    font: {\n      body: '--DataGrid-t-typography-font-body',\n      small: '--DataGrid-t-typography-font-small',\n      large: '--DataGrid-t-typography-font-large'\n    },\n    fontFamily: {\n      base: '--DataGrid-t-typography-font-family-base'\n    },\n    fontWeight: {\n      light: '--DataGrid-t-typography-font-weight-light',\n      regular: '--DataGrid-t-typography-font-weight-regular',\n      medium: '--DataGrid-t-typography-font-weight-medium',\n      bold: '--DataGrid-t-typography-font-weight-bold'\n    }\n  },\n  transitions: {\n    easing: {\n      easeIn: '--DataGrid-t-transition-easing-ease-in',\n      easeOut: '--DataGrid-t-transition-easing-ease-out',\n      easeInOut: '--DataGrid-t-transition-easing-ease-in-out'\n    },\n    duration: {\n      short: '--DataGrid-t-transition-duration-short',\n      base: '--DataGrid-t-transition-duration-base',\n      long: '--DataGrid-t-transition-duration-long'\n    }\n  },\n  shadows: {\n    base: '--DataGrid-t-shadow-base',\n    overlay: '--DataGrid-t-shadow-overlay'\n  },\n  zIndex: {\n    panel: '--DataGrid-t-z-index-panel',\n    menu: '--DataGrid-t-z-index-menu'\n  }\n};\nconst values = wrap(keys);\nexport const vars = _extends({\n  breakpoints,\n  spacing,\n  transition,\n  keys\n}, values);\nfunction spacing(a, b, c, d) {\n  /* eslint-disable prefer-template */\n  if (a === undefined) {\n    return spacingString(1);\n  }\n  if (b === undefined) {\n    return spacingString(a);\n  }\n  if (c === undefined) {\n    return spacingString(a) + ' ' + spacingString(b);\n  }\n  if (d === undefined) {\n    return spacingString(a) + ' ' + spacingString(b) + ' ' + spacingString(c);\n  }\n  return spacingString(a) + ' ' + spacingString(b) + ' ' + spacingString(c) + ' ' + spacingString(d);\n  /* eslint-enable prefer-template */\n}\nfunction spacingString(value) {\n  if (value === 0) {\n    return '0';\n  }\n  return `calc(var(--DataGrid-t-spacing-unit) * ${value})`;\n}\nfunction transition(props, options) {\n  const {\n    duration = vars.transitions.duration.base,\n    easing = vars.transitions.easing.easeInOut,\n    delay = 0\n  } = options ?? {};\n  return props.map(prop => `${prop} ${duration} ${easing} ${delay}ms`).join(', ');\n}\nfunction wrap(input) {\n  if (typeof input === 'string') {\n    return `var(${input})`;\n  }\n  const result = {};\n  for (const key in input) {\n    if (Object.hasOwn(input, key)) {\n      result[key] = wrap(input[key]);\n    }\n  }\n  return result;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}