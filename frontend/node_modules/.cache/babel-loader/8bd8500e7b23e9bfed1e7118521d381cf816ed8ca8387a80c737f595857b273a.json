{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nexport let DataSourceRowsUpdateStrategy = /*#__PURE__*/function (DataSourceRowsUpdateStrategy) {\n  DataSourceRowsUpdateStrategy[\"Default\"] = \"set-new-rows\";\n  DataSourceRowsUpdateStrategy[\"LazyLoading\"] = \"replace-row-range\";\n  return DataSourceRowsUpdateStrategy;\n}({});\n\n/**\n * Provides better cache hit rate by:\n * 1. Splitting the data into smaller chunks to be stored in the cache (cache `set`)\n * 2. Merging multiple cache entries into a single response to get the required chunk (cache `get`)\n */\nexport class CacheChunkManager {\n  /**\n   * @param chunkSize The number of rows to store in each cache entry.\n   * If not set, the whole array will be stored in a single cache entry.\n   * Setting this value to smallest page size will result in better cache hit rate.\n   * Has no effect if cursor pagination is used.\n   */\n  constructor(chunkSize) {\n    this.chunkSize = void 0;\n    this.getCacheKeys = key => {\n      if (this.chunkSize < 1 || typeof key.start !== 'number') {\n        return [key];\n      }\n\n      // split the range into chunks\n      const chunkedKeys = [];\n      for (let i = key.start; i <= key.end; i += this.chunkSize) {\n        const end = Math.min(i + this.chunkSize - 1, key.end);\n        chunkedKeys.push(_extends({}, key, {\n          start: i,\n          end\n        }));\n      }\n      return chunkedKeys;\n    };\n    this.splitResponse = (key, response) => {\n      const cacheKeys = this.getCacheKeys(key);\n      const responses = new Map();\n      cacheKeys.forEach(chunkKey => {\n        const isLastChunk = chunkKey.end === key.end;\n        const responseSlice = _extends({}, response, {\n          pageInfo: _extends({}, response.pageInfo, {\n            // If the original response had page info, update that information for all but last chunk and keep the original value for the last chunk\n            hasNextPage: response.pageInfo?.hasNextPage !== undefined && !isLastChunk ? true : response.pageInfo?.hasNextPage,\n            nextCursor: response.pageInfo?.nextCursor !== undefined && !isLastChunk ? response.rows[chunkKey.end + 1].id : response.pageInfo?.nextCursor\n          }),\n          rows: typeof chunkKey.start !== 'number' || typeof key.start !== 'number' ? response.rows : response.rows.slice(chunkKey.start - key.start, chunkKey.end - key.start + 1)\n        });\n        responses.set(chunkKey, responseSlice);\n      });\n      return responses;\n    };\n    this.chunkSize = chunkSize;\n  }\n}\nCacheChunkManager.mergeResponses = responses => {\n  if (responses.length === 1) {\n    return responses[0];\n  }\n  return responses.reduce((acc, response) => ({\n    rows: [...acc.rows, ...response.rows],\n    rowCount: response.rowCount,\n    pageInfo: response.pageInfo\n  }), {\n    rows: [],\n    rowCount: 0,\n    pageInfo: {}\n  });\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}