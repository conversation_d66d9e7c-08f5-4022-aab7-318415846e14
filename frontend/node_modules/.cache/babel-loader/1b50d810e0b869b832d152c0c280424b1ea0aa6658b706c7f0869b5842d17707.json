{"ast": null, "code": "var GridEditModes = /*#__PURE__*/function (GridEditModes) {\n  GridEditModes[\"Cell\"] = \"cell\";\n  GridEditModes[\"Row\"] = \"row\";\n  return GridEditModes;\n}(GridEditModes || {});\nvar GridCellModes = /*#__PURE__*/function (GridCellModes) {\n  GridCellModes[\"Edit\"] = \"edit\";\n  GridCellModes[\"View\"] = \"view\";\n  return GridCellModes;\n}(GridCellModes || {});\nvar GridRowModes = /*#__PURE__*/function (GridRowModes) {\n  GridRowModes[\"Edit\"] = \"edit\";\n  GridRowModes[\"View\"] = \"view\";\n  return GridRowModes;\n}(GridRowModes || {});\nexport { GridEditModes, GridCellModes, GridRowModes };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}