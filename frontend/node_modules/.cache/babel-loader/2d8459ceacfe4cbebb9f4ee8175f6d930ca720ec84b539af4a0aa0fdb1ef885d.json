{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridLogger, useGridSelector, useGridApiMethod } from \"../../utils/index.js\";\nimport { useGridRegisterPipeProcessor } from \"../../core/pipeProcessing/index.js\";\nimport { gridPaginationMetaSelector } from \"./gridPaginationSelector.js\";\nexport const useGridPaginationMeta = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridPaginationMeta');\n  const paginationMeta = useGridSelector(apiRef, gridPaginationMetaSelector);\n  apiRef.current.registerControlState({\n    stateId: 'paginationMeta',\n    propModel: props.paginationMeta,\n    propOnChange: props.onPaginationMetaChange,\n    stateSelector: gridPaginationMetaSelector,\n    changeEvent: 'paginationMetaChange'\n  });\n\n  /**\n   * API METHODS\n   */\n  const setPaginationMeta = React.useCallback(newPaginationMeta => {\n    if (paginationMeta === newPaginationMeta) {\n      return;\n    }\n    logger.debug(\"Setting 'paginationMeta' to\", newPaginationMeta);\n    apiRef.current.setState(state => _extends({}, state, {\n      pagination: _extends({}, state.pagination, {\n        meta: newPaginationMeta\n      })\n    }));\n  }, [apiRef, logger, paginationMeta]);\n  const paginationMetaApi = {\n    setPaginationMeta\n  };\n  useGridApiMethod(apiRef, paginationMetaApi, 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    const exportedPaginationMeta = gridPaginationMetaSelector(apiRef);\n    const shouldExportRowCount =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the `paginationMeta` is controlled\n    props.paginationMeta != null ||\n    // Always export if the `paginationMeta` has been initialized\n    props.initialState?.pagination?.meta != null;\n    if (!shouldExportRowCount) {\n      return prevState;\n    }\n    return _extends({}, prevState, {\n      pagination: _extends({}, prevState.pagination, {\n        meta: exportedPaginationMeta\n      })\n    });\n  }, [apiRef, props.paginationMeta, props.initialState?.pagination?.meta]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    const restoredPaginationMeta = context.stateToRestore.pagination?.meta ? context.stateToRestore.pagination.meta : gridPaginationMetaSelector(apiRef);\n    apiRef.current.setState(state => _extends({}, state, {\n      pagination: _extends({}, state.pagination, {\n        meta: restoredPaginationMeta\n      })\n    }));\n    return params;\n  }, [apiRef]);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n\n  /**\n   * EFFECTS\n   */\n  React.useEffect(() => {\n    if (props.paginationMeta) {\n      apiRef.current.setPaginationMeta(props.paginationMeta);\n    }\n  }, [apiRef, props.paginationMeta]);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}