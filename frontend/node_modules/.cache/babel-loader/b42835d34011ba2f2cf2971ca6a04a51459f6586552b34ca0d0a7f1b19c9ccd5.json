{"ast": null, "code": "import { createRootSelector, createSelector, createSelectorMemoized } from \"../../../utils/createSelector.js\";\nimport { gridRowTreeSelector, gridRowsLookupSelector } from \"../rows/gridRowsSelector.js\";\nimport { GRID_ID_AUTOGENERATED, isAutogeneratedRowNode } from \"../rows/gridRowsUtils.js\";\n/**\n * @category Sorting\n * @ignore - do not document.\n */\nconst gridSortingStateSelector = createRootSelector(state => state.sorting);\n\n/**\n * Get the id of the rows after the sorting process.\n * @category Sorting\n */\nexport const gridSortedRowIdsSelector = createSelector(gridSortingStateSelector, sortingState => sortingState.sortedRows);\n\n/**\n * Get the id and the model of the rows after the sorting process.\n * @category Sorting\n */\nexport const gridSortedRowEntriesSelector = createSelectorMemoized(gridSortedRowIdsSelector, gridRowsLookupSelector, gridRowTreeSelector, (sortedIds, idRowsLookup, rowTree) => sortedIds.reduce((acc, id) => {\n  const model = idRowsLookup[id];\n  if (model) {\n    acc.push({\n      id,\n      model\n    });\n  } else {\n    const rowNode = rowTree[id];\n    if (rowNode && isAutogeneratedRowNode(rowNode)) {\n      acc.push({\n        id,\n        model: {\n          [GRID_ID_AUTOGENERATED]: id\n        }\n      });\n    }\n  }\n  return acc;\n}, []));\n\n/**\n * Get the current sorting model.\n * @category Sorting\n */\nexport const gridSortModelSelector = createSelector(gridSortingStateSelector, sorting => sorting.sortModel);\n/**\n * @category Sorting\n * @ignore - do not document.\n */\nexport const gridSortColumnLookupSelector = createSelectorMemoized(gridSortModelSelector, sortModel => {\n  const result = sortModel.reduce((res, sortItem, index) => {\n    res[sortItem.field] = {\n      sortDirection: sortItem.sort,\n      sortIndex: sortModel.length > 1 ? index + 1 : undefined\n    };\n    return res;\n  }, {});\n  return result;\n});\n\n/**\n * @category Sorting\n * @ignore - do not document.\n */\nexport const gridSortedRowIndexLookupSelector = createSelectorMemoized(gridSortedRowIdsSelector, sortedIds => {\n  return sortedIds.reduce((acc, id, index) => {\n    acc[id] = index;\n    return acc;\n  }, Object.create(null));\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}