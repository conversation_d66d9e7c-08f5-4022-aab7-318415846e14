{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { fastMemo } from '@mui/x-internals/fastMemo';\nimport { useGridPrivateApiContext } from \"../hooks/utils/useGridPrivateApiContext.js\";\nimport { useGridSelector } from \"../hooks/utils/useGridSelector.js\";\nimport { useGridRootProps } from \"../hooks/utils/useGridRootProps.js\";\nimport { gridColumnVisibilityModelSelector, gridVisibleColumnDefinitionsSelector } from \"../hooks/features/columns/gridColumnsSelector.js\";\nimport { gridFilterActiveItemsLookupSelector } from \"../hooks/features/filter/gridFilterSelector.js\";\nimport { gridSortColumnLookupSelector } from \"../hooks/features/sorting/gridSortingSelector.js\";\nimport { gridTabIndexColumnHeaderSelector, gridTabIndexCellSelector, gridFocusColumnHeaderSelector, gridTabIndexColumnGroupHeaderSelector, gridFocusColumnGroupHeaderSelector } from \"../hooks/features/focus/gridFocusStateSelector.js\";\nimport { gridColumnGroupsHeaderMaxDepthSelector, gridColumnGroupsHeaderStructureSelector } from \"../hooks/features/columnGrouping/gridColumnGroupsSelector.js\";\nimport { gridColumnMenuSelector } from \"../hooks/features/columnMenu/columnMenuSelector.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction GridHeaders() {\n  const apiRef = useGridPrivateApiContext();\n  const rootProps = useGridRootProps();\n  const visibleColumns = useGridSelector(apiRef, gridVisibleColumnDefinitionsSelector);\n  const filterColumnLookup = useGridSelector(apiRef, gridFilterActiveItemsLookupSelector);\n  const sortColumnLookup = useGridSelector(apiRef, gridSortColumnLookupSelector);\n  const columnHeaderTabIndexState = useGridSelector(apiRef, gridTabIndexColumnHeaderSelector);\n  const hasNoCellTabIndexState = useGridSelector(apiRef, () => gridTabIndexCellSelector(apiRef) === null);\n  const columnGroupHeaderTabIndexState = useGridSelector(apiRef, gridTabIndexColumnGroupHeaderSelector);\n  const columnHeaderFocus = useGridSelector(apiRef, gridFocusColumnHeaderSelector);\n  const columnGroupHeaderFocus = useGridSelector(apiRef, gridFocusColumnGroupHeaderSelector);\n  const headerGroupingMaxDepth = useGridSelector(apiRef, gridColumnGroupsHeaderMaxDepthSelector);\n  const columnMenuState = useGridSelector(apiRef, gridColumnMenuSelector);\n  const columnVisibility = useGridSelector(apiRef, gridColumnVisibilityModelSelector);\n  const columnGroupsHeaderStructure = useGridSelector(apiRef, gridColumnGroupsHeaderStructureSelector);\n  const hasOtherElementInTabSequence = !(columnGroupHeaderTabIndexState === null && columnHeaderTabIndexState === null && hasNoCellTabIndexState);\n  const columnsContainerRef = apiRef.current.columnHeadersContainerRef;\n  return /*#__PURE__*/_jsx(rootProps.slots.columnHeaders, _extends({\n    ref: columnsContainerRef,\n    visibleColumns: visibleColumns,\n    filterColumnLookup: filterColumnLookup,\n    sortColumnLookup: sortColumnLookup,\n    columnHeaderTabIndexState: columnHeaderTabIndexState,\n    columnGroupHeaderTabIndexState: columnGroupHeaderTabIndexState,\n    columnHeaderFocus: columnHeaderFocus,\n    columnGroupHeaderFocus: columnGroupHeaderFocus,\n    headerGroupingMaxDepth: headerGroupingMaxDepth,\n    columnMenuState: columnMenuState,\n    columnVisibility: columnVisibility,\n    columnGroupsHeaderStructure: columnGroupsHeaderStructure,\n    hasOtherElementInTabSequence: hasOtherElementInTabSequence\n  }, rootProps.slotProps?.columnHeaders));\n}\nconst MemoizedGridHeaders = fastMemo(GridHeaders);\nexport { MemoizedGridHeaders as GridHeaders };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}