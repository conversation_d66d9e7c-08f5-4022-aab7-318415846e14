{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { gridDensitySelector } from \"./densitySelector.js\";\nimport { useGridRegisterPipeProcessor } from \"../../core/pipeProcessing/index.js\";\nexport const densityStateInitializer = (state, props) => _extends({}, state, {\n  density: props.initialState?.density ?? props.density ?? 'standard'\n});\nexport const useGridDensity = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useDensity');\n  apiRef.current.registerControlState({\n    stateId: 'density',\n    propModel: props.density,\n    propOnChange: props.onDensityChange,\n    stateSelector: gridDensitySelector,\n    changeEvent: 'densityChange'\n  });\n  const setDensity = useEventCallback(newDensity => {\n    const currentDensity = gridDensitySelector(apiRef);\n    if (currentDensity === newDensity) {\n      return;\n    }\n    logger.debug(`Set grid density to ${newDensity}`);\n    apiRef.current.setState(state => _extends({}, state, {\n      density: newDensity\n    }));\n  });\n  const densityApi = {\n    setDensity\n  };\n  useGridApiMethod(apiRef, densityApi, 'public');\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    const exportedDensity = gridDensitySelector(apiRef);\n    const shouldExportRowCount =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the `density` is controlled\n    props.density != null ||\n    // Always export if the `density` has been initialized\n    props.initialState?.density != null;\n    if (!shouldExportRowCount) {\n      return prevState;\n    }\n    return _extends({}, prevState, {\n      density: exportedDensity\n    });\n  }, [apiRef, props.density, props.initialState?.density]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    const restoredDensity = context.stateToRestore?.density ? context.stateToRestore.density : gridDensitySelector(apiRef);\n    apiRef.current.setState(state => _extends({}, state, {\n      density: restoredDensity\n    }));\n    return params;\n  }, [apiRef]);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n  React.useEffect(() => {\n    if (props.density) {\n      apiRef.current.setDensity(props.density);\n    }\n  }, [apiRef, props.density]);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}