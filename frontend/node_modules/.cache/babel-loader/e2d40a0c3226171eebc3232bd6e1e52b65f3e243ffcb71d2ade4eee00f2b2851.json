{"ast": null, "code": "import { createRootSelector, createSelector } from \"../../../utils/createSelector.js\";\nexport const gridColumnResizeSelector = createRootSelector(state => state.columnResize);\nexport const gridResizingColumnFieldSelector = createSelector(gridColumnResizeSelector, columnResize => columnResize.resizingColumnField);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}