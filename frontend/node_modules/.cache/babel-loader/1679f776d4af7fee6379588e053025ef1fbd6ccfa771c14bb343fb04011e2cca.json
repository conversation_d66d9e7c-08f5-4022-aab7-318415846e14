{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { useGridRootProps } from \"../hooks/utils/useGridRootProps.js\";\nimport { useGridApiContext } from \"../hooks/utils/useGridApiContext.js\";\nimport { GridOverlay } from \"./containers/GridOverlay.js\";\nimport { GridPreferencePanelsValue } from \"../hooks/features/preferencesPanel/gridPreferencePanelsValue.js\";\nimport { gridColumnFieldsSelector, useGridSelector } from \"../hooks/index.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst GridNoColumnsOverlay = forwardRef(function GridNoColumnsOverlay(props, ref) {\n  const rootProps = useGridRootProps();\n  const apiRef = useGridApiContext();\n  const columns = useGridSelector(apiRef, gridColumnFieldsSelector);\n  const handleOpenManageColumns = () => {\n    apiRef.current.showPreferences(GridPreferencePanelsValue.columns);\n  };\n  const showManageColumnsButton = !rootProps.disableColumnSelector && columns.length > 0;\n  return /*#__PURE__*/_jsxs(GridOverlay, _extends({}, props, {\n    ref: ref,\n    children: [apiRef.current.getLocaleText('noColumnsOverlayLabel'), showManageColumnsButton && /*#__PURE__*/_jsx(rootProps.slots.baseButton, _extends({\n      size: \"small\"\n    }, rootProps.slotProps?.baseButton, {\n      onClick: handleOpenManageColumns,\n      children: apiRef.current.getLocaleText('noColumnsOverlayManageColumns')\n    }))]\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridNoColumnsOverlay.displayName = \"GridNoColumnsOverlay\";\nprocess.env.NODE_ENV !== \"production\" ? GridNoColumnsOverlay.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport { GridNoColumnsOverlay };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}