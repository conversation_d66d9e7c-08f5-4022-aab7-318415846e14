{"ast": null, "code": "import { createSelector, createSelectorMemoized, createRootSelector } from \"../../../utils/createSelector.js\";\nimport { EMPTY_PINNED_COLUMN_FIELDS } from \"./gridColumnsInterfaces.js\";\nimport { gridIsRtlSelector } from \"../../core/gridCoreSelector.js\";\n\n/**\n * Get the columns state\n * @category Columns\n */\nexport const gridColumnsStateSelector = createRootSelector(state => state.columns);\n\n/**\n * Get an array of column fields in the order rendered on screen.\n * @category Columns\n */\nexport const gridColumnFieldsSelector = createSelector(gridColumnsStateSelector, columnsState => columnsState.orderedFields);\n\n/**\n * Get the columns as a lookup (an object containing the field for keys and the definition for values).\n * @category Columns\n */\nexport const gridColumnLookupSelector = createSelector(gridColumnsStateSelector, columnsState => columnsState.lookup);\n\n/**\n * Get an array of column definitions in the order rendered on screen..\n * @category Columns\n */\nexport const gridColumnDefinitionsSelector = createSelectorMemoized(gridColumnFieldsSelector, gridColumnLookupSelector, (allFields, lookup) => allFields.map(field => lookup[field]));\n\n/**\n * Get the column visibility model, containing the visibility status of each column.\n * If a column is not registered in the model, it is visible.\n * @category Visible Columns\n */\nexport const gridColumnVisibilityModelSelector = createSelector(gridColumnsStateSelector, columnsState => columnsState.columnVisibilityModel);\n\n/**\n * Get the \"initial\" column visibility model, containing the visibility status of each column.\n * It is updated when the `columns` prop is updated or when `updateColumns` API method is called.\n * If a column is not registered in the model, it is visible.\n * @category Visible Columns\n */\nexport const gridInitialColumnVisibilityModelSelector = createSelector(gridColumnsStateSelector, columnsState => columnsState.initialColumnVisibilityModel);\n\n/**\n * Get the visible columns as a lookup (an object containing the field for keys and the definition for values).\n * @category Visible Columns\n */\nexport const gridVisibleColumnDefinitionsSelector = createSelectorMemoized(gridColumnDefinitionsSelector, gridColumnVisibilityModelSelector, (columns, columnVisibilityModel) => columns.filter(column => columnVisibilityModel[column.field] !== false));\n\n/**\n * Get the field of each visible column.\n * @category Visible Columns\n */\nexport const gridVisibleColumnFieldsSelector = createSelectorMemoized(gridVisibleColumnDefinitionsSelector, visibleColumns => visibleColumns.map(column => column.field));\n\n/**\n * Get the visible pinned columns model.\n * @category Visible Columns\n */\nexport const gridPinnedColumnsSelector = createRootSelector(state => state.pinnedColumns);\n\n/**\n * Get all existing pinned columns. Place the columns on the side that depends on the rtl state.\n * @category Pinned Columns\n * @ignore - Do not document\n */\nexport const gridExistingPinnedColumnSelector = createSelectorMemoized(gridPinnedColumnsSelector, gridColumnFieldsSelector, gridIsRtlSelector, (model, orderedFields, isRtl) => filterMissingColumns(model, orderedFields, isRtl));\n\n/**\n * Get the visible pinned columns.\n * @category Visible Columns\n */\nexport const gridVisiblePinnedColumnDefinitionsSelector = createSelectorMemoized(gridColumnsStateSelector, gridPinnedColumnsSelector, gridVisibleColumnFieldsSelector, gridIsRtlSelector, (columnsState, model, visibleColumnFields, isRtl) => {\n  const visiblePinnedFields = filterMissingColumns(model, visibleColumnFields, isRtl);\n  const visiblePinnedColumns = {\n    left: visiblePinnedFields.left.map(field => columnsState.lookup[field]),\n    right: visiblePinnedFields.right.map(field => columnsState.lookup[field])\n  };\n  return visiblePinnedColumns;\n});\nfunction filterMissingColumns(pinnedColumns, columns, invert) {\n  if (!Array.isArray(pinnedColumns.left) && !Array.isArray(pinnedColumns.right)) {\n    return EMPTY_PINNED_COLUMN_FIELDS;\n  }\n  if (pinnedColumns.left?.length === 0 && pinnedColumns.right?.length === 0) {\n    return EMPTY_PINNED_COLUMN_FIELDS;\n  }\n  const filter = (newPinnedColumns, remainingColumns) => {\n    if (!Array.isArray(newPinnedColumns)) {\n      return [];\n    }\n    return newPinnedColumns.filter(field => remainingColumns.includes(field));\n  };\n  const leftPinnedColumns = filter(pinnedColumns.left, columns);\n  const columnsWithoutLeftPinnedColumns = columns.filter(\n  // Filter out from the remaining columns those columns already pinned to the left\n  field => !leftPinnedColumns.includes(field));\n  const rightPinnedColumns = filter(pinnedColumns.right, columnsWithoutLeftPinnedColumns);\n  if (invert) {\n    return {\n      left: rightPinnedColumns,\n      right: leftPinnedColumns\n    };\n  }\n  return {\n    left: leftPinnedColumns,\n    right: rightPinnedColumns\n  };\n}\n\n/**\n * Get the left position in pixel of each visible columns relative to the left of the first column.\n * @category Visible Columns\n */\nexport const gridColumnPositionsSelector = createSelectorMemoized(gridVisibleColumnDefinitionsSelector, visibleColumns => {\n  const positions = [];\n  let currentPosition = 0;\n  for (let i = 0; i < visibleColumns.length; i += 1) {\n    positions.push(currentPosition);\n    currentPosition += visibleColumns[i].computedWidth;\n  }\n  return positions;\n});\n\n/**\n * Get the filterable columns as an array.\n * @category Columns\n */\nexport const gridFilterableColumnDefinitionsSelector = createSelectorMemoized(gridColumnDefinitionsSelector, columns => columns.filter(col => col.filterable));\n\n/**\n * Get the filterable columns as a lookup (an object containing the field for keys and the definition for values).\n * @category Columns\n */\nexport const gridFilterableColumnLookupSelector = createSelectorMemoized(gridColumnDefinitionsSelector, columns => columns.reduce((acc, col) => {\n  if (col.filterable) {\n    acc[col.field] = col;\n  }\n  return acc;\n}, {}));\n\n/**\n * Checks if some column has a colSpan field.\n * @category Columns\n * @ignore - Do not document\n */\nexport const gridHasColSpanSelector = createSelectorMemoized(gridColumnDefinitionsSelector, columns => columns.some(column => column.colSpan !== undefined));", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}