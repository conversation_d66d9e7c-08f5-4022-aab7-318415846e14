{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { gridNumberComparator } from \"../hooks/features/sorting/gridSortingUtils.js\";\nimport { isNumber } from \"../utils/utils.js\";\nimport { getGridNumericOperators, getGridNumericQuickFilterFn } from \"./gridNumericOperators.js\";\nimport { GRID_STRING_COL_DEF } from \"./gridStringColDef.js\";\nexport const GRID_NUMERIC_COL_DEF = _extends({}, GRID_STRING_COL_DEF, {\n  type: 'number',\n  align: 'right',\n  headerAlign: 'right',\n  sortComparator: gridNumberComparator,\n  valueParser: value => value === '' ? null : Number(value),\n  valueFormatter: value => isNumber(value) ? value.toLocaleString() : value || '',\n  filterOperators: getGridNumericOperators(),\n  getApplyQuickFilterFn: getGridNumericQuickFilterFn\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}