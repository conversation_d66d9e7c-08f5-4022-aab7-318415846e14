{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useLazyRef from '@mui/utils/useLazyRef';\nimport debounce from '@mui/utils/debounce';\nimport { warnOnce } from '@mui/x-internals/warning';\nimport { isDeepEqual } from '@mui/x-internals/isDeepEqual';\nimport { GRID_ROOT_GROUP_ID } from \"../rows/gridRowsUtils.js\";\nimport { runIf } from \"../../../utils/utils.js\";\nimport { GridStrategyGroup } from \"../../core/strategyProcessing/index.js\";\nimport { useGridSelector } from \"../../utils/useGridSelector.js\";\nimport { gridPaginationModelSelector } from \"../pagination/gridPaginationSelector.js\";\nimport { gridGetRowsParamsSelector } from \"./gridDataSourceSelector.js\";\nimport { CacheChunkManager, DataSourceRowsUpdateStrategy } from \"./utils.js\";\nimport { GridDataSourceCacheDefault } from \"./cache.js\";\nimport { GridGetRowsError, GridUpdateRowError } from \"./gridDataSourceError.js\";\nconst noopCache = {\n  clear: () => {},\n  get: () => undefined,\n  set: () => {}\n};\nfunction getCache(cacheProp, options = {}) {\n  if (cacheProp === null) {\n    return noopCache;\n  }\n  return cacheProp ?? new GridDataSourceCacheDefault(options);\n}\nexport const useGridDataSourceBase = (apiRef, props, options = {}) => {\n  const setStrategyAvailability = React.useCallback(() => {\n    apiRef.current.setStrategyAvailability(GridStrategyGroup.DataSource, DataSourceRowsUpdateStrategy.Default, props.dataSource ? () => true : () => false);\n  }, [apiRef, props.dataSource]);\n  const [defaultRowsUpdateStrategyActive, setDefaultRowsUpdateStrategyActive] = React.useState(false);\n  const paginationModel = useGridSelector(apiRef, gridPaginationModelSelector);\n  const lastRequestId = React.useRef(0);\n  const onDataSourceErrorProp = props.onDataSourceError;\n  const cacheChunkManager = useLazyRef(() => {\n    const sortedPageSizeOptions = props.pageSizeOptions.map(option => typeof option === 'number' ? option : option.value).sort((a, b) => a - b);\n    const cacheChunkSize = Math.min(paginationModel.pageSize, sortedPageSizeOptions[0]);\n    return new CacheChunkManager(cacheChunkSize);\n  }).current;\n  const [cache, setCache] = React.useState(() => getCache(props.dataSourceCache, options.cacheOptions));\n  const fetchRows = React.useCallback(async (parentId, params) => {\n    const getRows = props.dataSource?.getRows;\n    if (!getRows) {\n      return;\n    }\n    if (parentId && parentId !== GRID_ROOT_GROUP_ID && props.signature !== 'DataGrid') {\n      options.fetchRowChildren?.([parentId]);\n      return;\n    }\n    options.clearDataSourceState?.();\n    const fetchParams = _extends({}, gridGetRowsParamsSelector(apiRef), apiRef.current.unstable_applyPipeProcessors('getRowsParams', {}), params);\n    const cacheKeys = cacheChunkManager.getCacheKeys(fetchParams);\n    const responses = cacheKeys.map(cacheKey => cache.get(cacheKey));\n    if (responses.every(response => response !== undefined)) {\n      apiRef.current.applyStrategyProcessor('dataSourceRowsUpdate', {\n        response: CacheChunkManager.mergeResponses(responses),\n        fetchParams\n      });\n      return;\n    }\n\n    // Manage loading state only for the default strategy\n    if (defaultRowsUpdateStrategyActive || apiRef.current.getRowsCount() === 0) {\n      apiRef.current.setLoading(true);\n    }\n    const requestId = lastRequestId.current + 1;\n    lastRequestId.current = requestId;\n    try {\n      const getRowsResponse = await getRows(fetchParams);\n      const cacheResponses = cacheChunkManager.splitResponse(fetchParams, getRowsResponse);\n      cacheResponses.forEach((response, key) => cache.set(key, response));\n      if (lastRequestId.current === requestId) {\n        apiRef.current.applyStrategyProcessor('dataSourceRowsUpdate', {\n          response: getRowsResponse,\n          fetchParams\n        });\n      }\n    } catch (originalError) {\n      if (lastRequestId.current === requestId) {\n        apiRef.current.applyStrategyProcessor('dataSourceRowsUpdate', {\n          error: originalError,\n          fetchParams\n        });\n        if (typeof onDataSourceErrorProp === 'function') {\n          onDataSourceErrorProp(new GridGetRowsError({\n            message: originalError?.message,\n            params: fetchParams,\n            cause: originalError\n          }));\n        } else if (process.env.NODE_ENV !== 'production') {\n          warnOnce(['MUI X: A call to `dataSource.getRows()` threw an error which was not handled because `onDataSourceError()` is missing.', 'To handle the error pass a callback to the `onDataSourceError` prop, for example `<DataGrid onDataSourceError={(error) => ...} />`.', 'For more detail, see https://mui.com/x/react-data-grid/server-side-data/#error-handling.'], 'error');\n        }\n      }\n    } finally {\n      if (defaultRowsUpdateStrategyActive && lastRequestId.current === requestId) {\n        apiRef.current.setLoading(false);\n      }\n    }\n  }, [cacheChunkManager, cache, apiRef, defaultRowsUpdateStrategyActive, props.dataSource?.getRows, onDataSourceErrorProp, options, props.signature]);\n  const handleStrategyActivityChange = React.useCallback(() => {\n    setDefaultRowsUpdateStrategyActive(apiRef.current.getActiveStrategy(GridStrategyGroup.DataSource) === DataSourceRowsUpdateStrategy.Default);\n  }, [apiRef]);\n  const handleDataUpdate = React.useCallback(params => {\n    if ('error' in params) {\n      apiRef.current.setRows([]);\n      return;\n    }\n    const {\n      response\n    } = params;\n    if (response.rowCount !== undefined) {\n      apiRef.current.setRowCount(response.rowCount);\n    }\n    apiRef.current.setRows(response.rows);\n    apiRef.current.unstable_applyPipeProcessors('processDataSourceRows', {\n      params: params.fetchParams,\n      response\n    }, true);\n  }, [apiRef]);\n  const dataSourceUpdateRow = props.dataSource?.updateRow;\n  const handleEditRowOption = options.handleEditRow;\n  const editRow = React.useCallback(async params => {\n    if (!dataSourceUpdateRow) {\n      return undefined;\n    }\n    try {\n      const finalRowUpdate = await dataSourceUpdateRow(params);\n      if (typeof handleEditRowOption === 'function') {\n        handleEditRowOption(params, finalRowUpdate);\n        return finalRowUpdate;\n      }\n      apiRef.current.updateNestedRows([finalRowUpdate], []);\n      if (finalRowUpdate && !isDeepEqual(finalRowUpdate, params.previousRow)) {\n        // Reset the outdated cache, only if the row is _actually_ updated\n        apiRef.current.dataSource.cache.clear();\n      }\n      return finalRowUpdate;\n    } catch (errorThrown) {\n      if (typeof onDataSourceErrorProp === 'function') {\n        onDataSourceErrorProp(new GridUpdateRowError({\n          message: errorThrown?.message,\n          params,\n          cause: errorThrown\n        }));\n      } else if (process.env.NODE_ENV !== 'production') {\n        warnOnce(['MUI X: A call to `dataSource.updateRow()` threw an error which was not handled because `onDataSourceError()` is missing.', 'To handle the error pass a callback to the `onDataSourceError` prop, for example `<DataGrid onDataSourceError={(error) => ...} />`.', 'For more detail, see https://mui.com/x/react-data-grid/server-side-data/#error-handling.'], 'error');\n      }\n      throw errorThrown; // Let the caller handle the error further\n    }\n  }, [apiRef, dataSourceUpdateRow, onDataSourceErrorProp, handleEditRowOption]);\n  const dataSourceApi = {\n    dataSource: {\n      fetchRows,\n      cache,\n      editRow\n    }\n  };\n  const debouncedFetchRows = React.useMemo(() => debounce(fetchRows, 0), [fetchRows]);\n  const isFirstRender = React.useRef(true);\n  React.useEffect(() => {\n    if (isFirstRender.current) {\n      isFirstRender.current = false;\n      return;\n    }\n    if (props.dataSourceCache === undefined) {\n      return;\n    }\n    const newCache = getCache(props.dataSourceCache, options.cacheOptions);\n    setCache(prevCache => prevCache !== newCache ? newCache : prevCache);\n  }, [props.dataSourceCache, options.cacheOptions]);\n  React.useEffect(() => {\n    if (props.dataSource) {\n      apiRef.current.dataSource.cache.clear();\n      apiRef.current.dataSource.fetchRows();\n    }\n    return () => {\n      // ignore the current request on unmount\n      lastRequestId.current += 1;\n    };\n  }, [apiRef, props.dataSource]);\n  return {\n    api: {\n      public: dataSourceApi\n    },\n    debouncedFetchRows,\n    strategyProcessor: {\n      strategyName: DataSourceRowsUpdateStrategy.Default,\n      group: 'dataSourceRowsUpdate',\n      processor: handleDataUpdate\n    },\n    setStrategyAvailability,\n    cacheChunkManager,\n    cache,\n    events: {\n      strategyAvailabilityChange: handleStrategyActivityChange,\n      sortModelChange: runIf(defaultRowsUpdateStrategyActive, () => debouncedFetchRows()),\n      filterModelChange: runIf(defaultRowsUpdateStrategyActive, () => debouncedFetchRows()),\n      paginationModelChange: runIf(defaultRowsUpdateStrategyActive, () => debouncedFetchRows())\n    }\n  };\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}