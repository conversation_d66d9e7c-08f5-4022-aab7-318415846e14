{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport { styled } from '@mui/system';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { useGridSelector } from \"../../hooks/utils/useGridSelector.js\";\nimport { gridRowsMetaSelector } from \"../../hooks/features/rows/index.js\";\nimport { gridRenderContextSelector } from \"../../hooks/features/virtualization/index.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['virtualScrollerRenderZone']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst VirtualScrollerRenderZoneRoot = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'VirtualScrollerRenderZone'\n})({\n  position: 'absolute',\n  display: 'flex',\n  // Prevents margin collapsing when using `getRowSpacing`\n  flexDirection: 'column'\n});\nconst GridVirtualScrollerRenderZone = forwardRef(function GridVirtualScrollerRenderZone(props, ref) {\n  const {\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const classes = useUtilityClasses(rootProps);\n  const offsetTop = useGridSelector(apiRef, () => {\n    const renderContext = gridRenderContextSelector(apiRef);\n    const rowsMeta = gridRowsMetaSelector(apiRef);\n    return rowsMeta.positions[renderContext.firstRowIndex] ?? 0;\n  });\n  return /*#__PURE__*/_jsx(VirtualScrollerRenderZoneRoot, _extends({\n    className: clsx(classes.root, className),\n    ownerState: rootProps,\n    style: {\n      transform: `translate3d(0, ${offsetTop}px, 0)`\n    }\n  }, other, {\n    ref: ref\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridVirtualScrollerRenderZone.displayName = \"GridVirtualScrollerRenderZone\";\nexport { GridVirtualScrollerRenderZone };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}