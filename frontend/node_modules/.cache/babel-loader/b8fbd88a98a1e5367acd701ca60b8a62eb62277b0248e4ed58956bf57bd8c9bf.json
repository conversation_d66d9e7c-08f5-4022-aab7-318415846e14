{"ast": null, "code": "import * as React from 'react';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nconst noop = () => {};\n\n/**\n * Runs an effect once, when `condition` is true.\n */\nexport const useRunOnce = (condition, effect) => {\n  const didRun = React.useRef(false);\n  useEnhancedEffect(() => {\n    if (didRun.current || !condition) {\n      return noop;\n    }\n    didRun.current = true;\n    return effect();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [didRun.current || condition]);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}