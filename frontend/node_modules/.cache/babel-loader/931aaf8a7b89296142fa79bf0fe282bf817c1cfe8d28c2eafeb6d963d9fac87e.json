{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { gridColumnFieldsSelector, gridColumnDefinitionsSelector, gridColumnLookupSelector, gridColumnsStateSelector, gridColumnVisibilityModelSelector, gridVisibleColumnDefinitionsSelector, gridColumnPositionsSelector } from \"./gridColumnsSelector.js\";\nimport { GridSignature } from \"../../../constants/signature.js\";\nimport { useGridEvent } from \"../../utils/useGridEvent.js\";\nimport { useGridRegisterPipeProcessor, useGridRegisterPipeApplier } from \"../../core/pipeProcessing/index.js\";\nimport { EMPTY_PINNED_COLUMN_FIELDS } from \"./gridColumnsInterfaces.js\";\nimport { hydrateColumnsWidth, createColumnsState, COLUMNS_DIMENSION_PROPERTIES } from \"./gridColumnsUtils.js\";\nimport { GridPreferencePanelsValue } from \"../preferencesPanel/index.js\";\nimport { gridPivotActiveSelector } from \"../pivoting/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const columnsStateInitializer = (state, props, apiRef) => {\n  const columnsState = createColumnsState({\n    apiRef,\n    columnsToUpsert: props.columns,\n    initialState: props.initialState?.columns,\n    columnVisibilityModel: props.columnVisibilityModel ?? props.initialState?.columns?.columnVisibilityModel ?? {},\n    keepOnlyColumnsToUpsert: true\n  });\n  return _extends({}, state, {\n    columns: columnsState,\n    // In pro/premium, this part of the state is defined. We give it an empty but defined value\n    // for the community version.\n    pinnedColumns: state.pinnedColumns ?? EMPTY_PINNED_COLUMN_FIELDS\n  });\n};\n\n/**\n * @requires useGridParamsApi (method)\n * @requires useGridDimensions (method, event) - can be after\n * TODO: Impossible priority - useGridParamsApi also needs to be after useGridColumns\n */\nexport function useGridColumns(apiRef, props) {\n  const logger = useGridLogger(apiRef, 'useGridColumns');\n  const previousColumnsProp = React.useRef(props.columns);\n  apiRef.current.registerControlState({\n    stateId: 'visibleColumns',\n    propModel: props.columnVisibilityModel,\n    propOnChange: props.onColumnVisibilityModelChange,\n    stateSelector: gridColumnVisibilityModelSelector,\n    changeEvent: 'columnVisibilityModelChange'\n  });\n  const setGridColumnsState = React.useCallback(columnsState => {\n    logger.debug('Updating columns state.');\n    apiRef.current.setState(mergeColumnsState(columnsState));\n    apiRef.current.publishEvent('columnsChange', columnsState.orderedFields);\n  }, [logger, apiRef]);\n\n  /**\n   * API METHODS\n   */\n  const getColumn = React.useCallback(field => gridColumnLookupSelector(apiRef)[field], [apiRef]);\n  const getAllColumns = React.useCallback(() => gridColumnDefinitionsSelector(apiRef), [apiRef]);\n  const getVisibleColumns = React.useCallback(() => gridVisibleColumnDefinitionsSelector(apiRef), [apiRef]);\n  const getColumnIndex = React.useCallback((field, useVisibleColumns = true) => {\n    const columns = useVisibleColumns ? gridVisibleColumnDefinitionsSelector(apiRef) : gridColumnDefinitionsSelector(apiRef);\n    return columns.findIndex(col => col.field === field);\n  }, [apiRef]);\n  const getColumnPosition = React.useCallback(field => {\n    const index = getColumnIndex(field);\n    return gridColumnPositionsSelector(apiRef)[index];\n  }, [apiRef, getColumnIndex]);\n  const setColumnVisibilityModel = React.useCallback(model => {\n    const currentModel = gridColumnVisibilityModelSelector(apiRef);\n    if (currentModel !== model) {\n      apiRef.current.setState(state => _extends({}, state, {\n        columns: createColumnsState({\n          apiRef,\n          columnsToUpsert: [],\n          initialState: undefined,\n          columnVisibilityModel: model,\n          keepOnlyColumnsToUpsert: false\n        })\n      }));\n      apiRef.current.updateRenderContext?.();\n    }\n  }, [apiRef]);\n  const updateColumns = React.useCallback(columns => {\n    if (gridPivotActiveSelector(apiRef)) {\n      apiRef.current.updateNonPivotColumns(columns);\n      return;\n    }\n    const columnsState = createColumnsState({\n      apiRef,\n      columnsToUpsert: columns,\n      initialState: undefined,\n      keepOnlyColumnsToUpsert: false,\n      updateInitialVisibilityModel: true\n    });\n    setGridColumnsState(columnsState);\n  }, [apiRef, setGridColumnsState]);\n  const setColumnVisibility = React.useCallback((field, isVisible) => {\n    const columnVisibilityModel = gridColumnVisibilityModelSelector(apiRef);\n    const isCurrentlyVisible = columnVisibilityModel[field] ?? true;\n    if (isVisible !== isCurrentlyVisible) {\n      const newModel = _extends({}, columnVisibilityModel, {\n        [field]: isVisible\n      });\n      apiRef.current.setColumnVisibilityModel(newModel);\n    }\n  }, [apiRef]);\n  const getColumnIndexRelativeToVisibleColumns = React.useCallback(field => {\n    const allColumns = gridColumnFieldsSelector(apiRef);\n    return allColumns.findIndex(col => col === field);\n  }, [apiRef]);\n  const setColumnIndex = React.useCallback((field, targetIndexPosition) => {\n    const allColumns = gridColumnFieldsSelector(apiRef);\n    const oldIndexPosition = getColumnIndexRelativeToVisibleColumns(field);\n    if (oldIndexPosition === targetIndexPosition) {\n      return;\n    }\n    logger.debug(`Moving column ${field} to index ${targetIndexPosition}`);\n    const updatedColumns = [...allColumns];\n    const fieldRemoved = updatedColumns.splice(oldIndexPosition, 1)[0];\n    updatedColumns.splice(targetIndexPosition, 0, fieldRemoved);\n    setGridColumnsState(_extends({}, gridColumnsStateSelector(apiRef), {\n      orderedFields: updatedColumns\n    }));\n    const params = {\n      column: apiRef.current.getColumn(field),\n      targetIndex: apiRef.current.getColumnIndexRelativeToVisibleColumns(field),\n      oldIndex: oldIndexPosition\n    };\n    apiRef.current.publishEvent('columnIndexChange', params);\n  }, [apiRef, logger, setGridColumnsState, getColumnIndexRelativeToVisibleColumns]);\n  const setColumnWidth = React.useCallback((field, width) => {\n    logger.debug(`Updating column ${field} width to ${width}`);\n    const columnsState = gridColumnsStateSelector(apiRef);\n    const column = columnsState.lookup[field];\n    const newColumn = _extends({}, column, {\n      width,\n      hasBeenResized: true\n    });\n    setGridColumnsState(hydrateColumnsWidth(_extends({}, columnsState, {\n      lookup: _extends({}, columnsState.lookup, {\n        [field]: newColumn\n      })\n    }), apiRef.current.getRootDimensions()));\n    apiRef.current.publishEvent('columnWidthChange', {\n      element: apiRef.current.getColumnHeaderElement(field),\n      colDef: newColumn,\n      width\n    });\n  }, [apiRef, logger, setGridColumnsState]);\n  const columnApi = {\n    getColumn,\n    getAllColumns,\n    getColumnIndex,\n    getColumnPosition,\n    getVisibleColumns,\n    getColumnIndexRelativeToVisibleColumns,\n    updateColumns,\n    setColumnVisibilityModel,\n    setColumnVisibility,\n    setColumnWidth\n  };\n  const columnReorderApi = {\n    setColumnIndex\n  };\n  useGridApiMethod(apiRef, columnApi, 'public');\n  useGridApiMethod(apiRef, columnReorderApi, props.signature === GridSignature.DataGrid ? 'private' : 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    const columnsStateToExport = {};\n    const columnVisibilityModelToExport = gridColumnVisibilityModelSelector(apiRef);\n    const shouldExportColumnVisibilityModel =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the model is controlled\n    props.columnVisibilityModel != null ||\n    // Always export if the model has been initialized\n    // TODO v6 Do a nullish check instead to export even if the initial model equals \"{}\"\n    Object.keys(props.initialState?.columns?.columnVisibilityModel ?? {}).length > 0 ||\n    // Always export if the model is not empty\n    Object.keys(columnVisibilityModelToExport).length > 0;\n    if (shouldExportColumnVisibilityModel) {\n      columnsStateToExport.columnVisibilityModel = columnVisibilityModelToExport;\n    }\n    columnsStateToExport.orderedFields = gridColumnFieldsSelector(apiRef);\n    const columns = gridColumnDefinitionsSelector(apiRef);\n    const dimensions = {};\n    columns.forEach(colDef => {\n      if (colDef.hasBeenResized) {\n        const colDefDimensions = {};\n        COLUMNS_DIMENSION_PROPERTIES.forEach(propertyName => {\n          let propertyValue = colDef[propertyName];\n          if (propertyValue === Infinity) {\n            propertyValue = -1;\n          }\n          colDefDimensions[propertyName] = propertyValue;\n        });\n        dimensions[colDef.field] = colDefDimensions;\n      }\n    });\n    if (Object.keys(dimensions).length > 0) {\n      columnsStateToExport.dimensions = dimensions;\n    }\n    return _extends({}, prevState, {\n      columns: columnsStateToExport\n    });\n  }, [apiRef, props.columnVisibilityModel, props.initialState?.columns]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    const columnVisibilityModelToImport = context.stateToRestore.columns?.columnVisibilityModel;\n    const initialState = context.stateToRestore.columns;\n    if (columnVisibilityModelToImport == null && initialState == null) {\n      return params;\n    }\n    const columnsState = createColumnsState({\n      apiRef,\n      columnsToUpsert: [],\n      initialState,\n      columnVisibilityModel: columnVisibilityModelToImport,\n      keepOnlyColumnsToUpsert: false\n    });\n    apiRef.current.setState(mergeColumnsState(columnsState));\n    if (initialState != null) {\n      apiRef.current.publishEvent('columnsChange', columnsState.orderedFields);\n    }\n    return params;\n  }, [apiRef]);\n  const preferencePanelPreProcessing = React.useCallback((initialValue, value) => {\n    if (value === GridPreferencePanelsValue.columns) {\n      const ColumnsPanel = props.slots.columnsPanel;\n      return /*#__PURE__*/_jsx(ColumnsPanel, _extends({}, props.slotProps?.columnsPanel));\n    }\n    return initialValue;\n  }, [props.slots.columnsPanel, props.slotProps?.columnsPanel]);\n  const addColumnMenuItems = React.useCallback(columnMenuItems => {\n    const isPivotActive = gridPivotActiveSelector(apiRef);\n    if (props.disableColumnSelector || isPivotActive) {\n      return columnMenuItems;\n    }\n    return [...columnMenuItems, 'columnMenuColumnsItem'];\n  }, [props.disableColumnSelector, apiRef]);\n  useGridRegisterPipeProcessor(apiRef, 'columnMenu', addColumnMenuItems);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'preferencePanel', preferencePanelPreProcessing);\n\n  /*\n   * EVENTS\n   */\n\n  const prevInnerWidth = React.useRef(null);\n  const handleGridSizeChange = size => {\n    if (prevInnerWidth.current !== size.width) {\n      prevInnerWidth.current = size.width;\n      const hasFlexColumns = gridVisibleColumnDefinitionsSelector(apiRef).some(col => col.flex && col.flex > 0);\n      if (!hasFlexColumns) {\n        return;\n      }\n      setGridColumnsState(hydrateColumnsWidth(gridColumnsStateSelector(apiRef), apiRef.current.getRootDimensions()));\n    }\n  };\n  useGridEvent(apiRef, 'viewportInnerSizeChange', handleGridSizeChange);\n\n  /**\n   * APPLIERS\n   */\n  const hydrateColumns = React.useCallback(() => {\n    logger.info(`Columns pipe processing have changed, regenerating the columns`);\n    const columnsState = createColumnsState({\n      apiRef,\n      columnsToUpsert: [],\n      initialState: undefined,\n      keepOnlyColumnsToUpsert: false\n    });\n    setGridColumnsState(columnsState);\n  }, [apiRef, logger, setGridColumnsState]);\n  useGridRegisterPipeApplier(apiRef, 'hydrateColumns', hydrateColumns);\n\n  /*\n   * EFFECTS\n   */\n  // The effect do not track any value defined synchronously during the 1st render by hooks called after `useGridColumns`\n  // As a consequence, the state generated by the 1st run of this useEffect will always be equal to the initialization one\n  const isFirstRender = React.useRef(true);\n  React.useEffect(() => {\n    if (isFirstRender.current) {\n      isFirstRender.current = false;\n      return;\n    }\n    logger.info(`GridColumns have changed, new length ${props.columns.length}`);\n    if (previousColumnsProp.current === props.columns) {\n      return;\n    }\n    const columnsState = createColumnsState({\n      apiRef,\n      initialState: undefined,\n      // If the user provides a model, we don't want to set it in the state here because it has it's dedicated `useEffect` which calls `setColumnVisibilityModel`\n      columnsToUpsert: props.columns,\n      keepOnlyColumnsToUpsert: true,\n      updateInitialVisibilityModel: true\n    });\n    previousColumnsProp.current = props.columns;\n    setGridColumnsState(columnsState);\n  }, [logger, apiRef, setGridColumnsState, props.columns]);\n  React.useEffect(() => {\n    if (props.columnVisibilityModel !== undefined) {\n      apiRef.current.setColumnVisibilityModel(props.columnVisibilityModel);\n    }\n  }, [apiRef, logger, props.columnVisibilityModel]);\n}\nfunction mergeColumnsState(columnsState) {\n  return state => _extends({}, state, {\n    columns: columnsState\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}