{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { GRID_STRING_COL_DEF } from \"./gridStringColDef.js\";\nimport { renderBooleanCell } from \"../components/cell/GridBooleanCell.js\";\nimport { renderEditBooleanCell } from \"../components/cell/GridEditBooleanCell.js\";\nimport { gridNumberComparator } from \"../hooks/features/sorting/gridSortingUtils.js\";\nimport { getGridBooleanOperators } from \"./gridBooleanOperators.js\";\nconst gridBooleanFormatter = (value, row, column, apiRef) => {\n  return value ? apiRef.current.getLocaleText('booleanCellTrueLabel') : apiRef.current.getLocaleText('booleanCellFalseLabel');\n};\nconst stringToBoolean = value => {\n  switch (value.toLowerCase().trim()) {\n    case 'true':\n    case 'yes':\n    case '1':\n      return true;\n    case 'false':\n    case 'no':\n    case '0':\n    case 'null':\n    case 'undefined':\n      return false;\n    default:\n      return undefined;\n  }\n};\nexport const GRID_BOOLEAN_COL_DEF = _extends({}, GRID_STRING_COL_DEF, {\n  type: 'boolean',\n  display: 'flex',\n  align: 'center',\n  headerAlign: 'center',\n  renderCell: renderBooleanCell,\n  renderEditCell: renderEditBooleanCell,\n  sortComparator: gridNumberComparator,\n  valueFormatter: gridBooleanFormatter,\n  filterOperators: getGridBooleanOperators(),\n  getApplyQuickFilterFn: () => null,\n  // @ts-ignore\n  aggregable: false,\n  // @ts-ignore\n  pastedValueParser: value => stringToBoolean(value)\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}