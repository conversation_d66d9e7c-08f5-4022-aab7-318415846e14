{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"groupId\", \"children\"];\nimport * as React from 'react';\nimport { isLeaf } from \"../../../models/gridColumnGrouping.js\";\nimport { gridColumnGroupsLookupSelector, gridColumnGroupsUnwrappedModelSelector } from \"./gridColumnGroupsSelector.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { getColumnGroupsHeaderStructure, unwrapGroupingColumnModel } from \"./gridColumnGroupsUtils.js\";\nimport { useGridEvent } from \"../../utils/useGridEvent.js\";\nimport { gridColumnFieldsSelector, gridVisibleColumnFieldsSelector } from \"../columns/index.js\";\nconst createGroupLookup = columnGroupingModel => {\n  let groupLookup = {};\n  columnGroupingModel.forEach(node => {\n    if (isLeaf(node)) {\n      return;\n    }\n    const {\n        groupId,\n        children\n      } = node,\n      other = _objectWithoutPropertiesLoose(node, _excluded);\n    if (!groupId) {\n      throw new Error('MUI X: An element of the columnGroupingModel does not have either `field` or `groupId`.');\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (!children) {\n        console.warn(`MUI X: group groupId=${groupId} has no children.`);\n      }\n    }\n    const groupParam = _extends({}, other, {\n      groupId\n    });\n    const subTreeLookup = createGroupLookup(children);\n    if (subTreeLookup[groupId] !== undefined || groupLookup[groupId] !== undefined) {\n      throw new Error(`MUI X: The groupId ${groupId} is used multiple times in the columnGroupingModel.`);\n    }\n    groupLookup = _extends({}, groupLookup, subTreeLookup, {\n      [groupId]: groupParam\n    });\n  });\n  return _extends({}, groupLookup);\n};\nexport const columnGroupsStateInitializer = (state, props, apiRef) => {\n  if (!props.columnGroupingModel) {\n    return state;\n  }\n  const columnFields = gridColumnFieldsSelector(apiRef);\n  const visibleColumnFields = gridVisibleColumnFieldsSelector(apiRef);\n  const groupLookup = createGroupLookup(props.columnGroupingModel ?? []);\n  const unwrappedGroupingModel = unwrapGroupingColumnModel(props.columnGroupingModel ?? []);\n  const columnGroupsHeaderStructure = getColumnGroupsHeaderStructure(columnFields, unwrappedGroupingModel, apiRef.current.state.pinnedColumns ?? {});\n  const maxDepth = visibleColumnFields.length === 0 ? 0 : Math.max(...visibleColumnFields.map(field => unwrappedGroupingModel[field]?.length ?? 0));\n  return _extends({}, state, {\n    columnGrouping: {\n      lookup: groupLookup,\n      unwrappedGroupingModel,\n      headerStructure: columnGroupsHeaderStructure,\n      maxDepth\n    }\n  });\n};\n\n/**\n * @requires useGridColumns (method, event)\n * @requires useGridParamsApi (method)\n */\nexport const useGridColumnGrouping = (apiRef, props) => {\n  /**\n   * API METHODS\n   */\n  const getColumnGroupPath = React.useCallback(field => {\n    const unwrappedGroupingModel = gridColumnGroupsUnwrappedModelSelector(apiRef);\n    return unwrappedGroupingModel[field] ?? [];\n  }, [apiRef]);\n  const getAllGroupDetails = React.useCallback(() => {\n    const columnGroupLookup = gridColumnGroupsLookupSelector(apiRef);\n    return columnGroupLookup;\n  }, [apiRef]);\n  const columnGroupingApi = {\n    getColumnGroupPath,\n    getAllGroupDetails\n  };\n  useGridApiMethod(apiRef, columnGroupingApi, 'public');\n  const handleColumnIndexChange = React.useCallback(() => {\n    const unwrappedGroupingModel = unwrapGroupingColumnModel(props.columnGroupingModel ?? []);\n    apiRef.current.setState(state => {\n      const orderedFields = state.columns?.orderedFields ?? [];\n      const pinnedColumns = state.pinnedColumns ?? {};\n      const columnGroupsHeaderStructure = getColumnGroupsHeaderStructure(orderedFields, unwrappedGroupingModel, pinnedColumns);\n      return _extends({}, state, {\n        columnGrouping: _extends({}, state.columnGrouping, {\n          headerStructure: columnGroupsHeaderStructure\n        })\n      });\n    });\n  }, [apiRef, props.columnGroupingModel]);\n  const updateColumnGroupingState = React.useCallback(columnGroupingModel => {\n    // @ts-expect-error Move this logic to `Pro` package\n    const pinnedColumns = apiRef.current.getPinnedColumns?.() ?? {};\n    const columnFields = gridColumnFieldsSelector(apiRef);\n    const visibleColumnFields = gridVisibleColumnFieldsSelector(apiRef);\n    const groupLookup = createGroupLookup(columnGroupingModel ?? []);\n    const unwrappedGroupingModel = unwrapGroupingColumnModel(columnGroupingModel ?? []);\n    const columnGroupsHeaderStructure = getColumnGroupsHeaderStructure(columnFields, unwrappedGroupingModel, pinnedColumns);\n    const maxDepth = visibleColumnFields.length === 0 ? 0 : Math.max(...visibleColumnFields.map(field => unwrappedGroupingModel[field]?.length ?? 0));\n    apiRef.current.setState(state => {\n      return _extends({}, state, {\n        columnGrouping: {\n          lookup: groupLookup,\n          unwrappedGroupingModel,\n          headerStructure: columnGroupsHeaderStructure,\n          maxDepth\n        }\n      });\n    });\n  }, [apiRef]);\n  useGridEvent(apiRef, 'columnIndexChange', handleColumnIndexChange);\n  useGridEvent(apiRef, 'columnsChange', () => {\n    updateColumnGroupingState(props.columnGroupingModel);\n  });\n  useGridEvent(apiRef, 'columnVisibilityModelChange', () => {\n    updateColumnGroupingState(props.columnGroupingModel);\n  });\n\n  /**\n   * EFFECTS\n   */\n  React.useEffect(() => {\n    updateColumnGroupingState(props.columnGroupingModel);\n  }, [updateColumnGroupingState, props.columnGroupingModel]);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}