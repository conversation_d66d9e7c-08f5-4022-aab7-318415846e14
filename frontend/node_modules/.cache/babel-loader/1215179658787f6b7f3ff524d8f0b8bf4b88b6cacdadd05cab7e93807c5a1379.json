{"ast": null, "code": "import _toPropertyKey from \"@babel/runtime/helpers/esm/toPropertyKey\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"id\"],\n  _excluded2 = [\"id\"];\nimport * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport { warnOnce } from '@mui/x-internals/warning';\nimport { isDeepEqual } from '@mui/x-internals/isDeepEqual';\nimport { useGridEvent, useGridEventPriority } from \"../../utils/useGridEvent.js\";\nimport { GridEditModes, GridRowModes } from \"../../../models/gridEditRowModel.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { gridEditRowsStateSelector, gridRowIsEditingSelector } from \"./gridEditingSelectors.js\";\nimport { isPrintableKey, isPasteShortcut } from \"../../../utils/keyboardUtils.js\";\nimport { gridColumnDefinitionsSelector, gridVisibleColumnFieldsSelector } from \"../columns/gridColumnsSelector.js\";\nimport { gridRowsLookupSelector } from \"../rows/gridRowsSelector.js\";\nimport { deepClone } from \"../../../utils/utils.js\";\nimport { GridRowEditStopReasons, GridRowEditStartReasons } from \"../../../models/params/gridRowParams.js\";\nimport { GRID_ACTIONS_COLUMN_TYPE } from \"../../../colDef/index.js\";\nimport { getDefaultCellValue } from \"./utils.js\";\nexport const useGridRowEditing = (apiRef, props) => {\n  const [rowModesModel, setRowModesModel] = React.useState({});\n  const rowModesModelRef = React.useRef(rowModesModel);\n  const prevRowModesModel = React.useRef({});\n  const prevRowValuesLookup = React.useRef({});\n  const focusTimeout = React.useRef(undefined);\n  const nextFocusedCell = React.useRef(null);\n  const {\n    processRowUpdate,\n    onProcessRowUpdateError,\n    rowModesModel: rowModesModelProp,\n    onRowModesModelChange\n  } = props;\n  const runIfEditModeIsRow = callback => (...args) => {\n    if (props.editMode === GridEditModes.Row) {\n      callback(...args);\n    }\n  };\n  const throwIfNotEditable = React.useCallback((id, field) => {\n    const params = apiRef.current.getCellParams(id, field);\n    if (!apiRef.current.isCellEditable(params)) {\n      throw new Error(`MUI X: The cell with id=${id} and field=${field} is not editable.`);\n    }\n  }, [apiRef]);\n  const throwIfNotInMode = React.useCallback((id, mode) => {\n    if (apiRef.current.getRowMode(id) !== mode) {\n      throw new Error(`MUI X: The row with id=${id} is not in ${mode} mode.`);\n    }\n  }, [apiRef]);\n  const hasFieldsWithErrors = React.useCallback(rowId => {\n    const editingState = gridEditRowsStateSelector(apiRef);\n    return Object.values(editingState[rowId]).some(fieldProps => fieldProps.error);\n  }, [apiRef]);\n  const handleCellDoubleClick = React.useCallback((params, event) => {\n    if (!params.isEditable) {\n      return;\n    }\n    if (apiRef.current.getRowMode(params.id) === GridRowModes.Edit) {\n      return;\n    }\n    const rowParams = apiRef.current.getRowParams(params.id);\n    const newParams = _extends({}, rowParams, {\n      field: params.field,\n      reason: GridRowEditStartReasons.cellDoubleClick\n    });\n    apiRef.current.publishEvent('rowEditStart', newParams, event);\n  }, [apiRef]);\n  const handleCellFocusIn = React.useCallback(params => {\n    nextFocusedCell.current = params;\n  }, []);\n  const handleCellFocusOut = React.useCallback((params, event) => {\n    if (!params.isEditable) {\n      return;\n    }\n    if (apiRef.current.getRowMode(params.id) === GridRowModes.View) {\n      return;\n    }\n    // The mechanism to detect if we can stop editing a row is different from\n    // the cell editing. Instead of triggering it when clicking outside a cell,\n    // we must check if another cell in the same row was not clicked. To achieve\n    // that, first we keep track of all cells that gained focus. When a cell loses\n    // focus we check if the next cell that received focus is from a different row.\n    nextFocusedCell.current = null;\n    focusTimeout.current = setTimeout(() => {\n      if (nextFocusedCell.current?.id !== params.id) {\n        // The row might have been deleted during the click\n        if (!apiRef.current.getRow(params.id)) {\n          return;\n        }\n\n        // The row may already changed its mode\n        if (apiRef.current.getRowMode(params.id) === GridRowModes.View) {\n          return;\n        }\n        if (hasFieldsWithErrors(params.id)) {\n          return;\n        }\n        const rowParams = apiRef.current.getRowParams(params.id);\n        const newParams = _extends({}, rowParams, {\n          field: params.field,\n          reason: GridRowEditStopReasons.rowFocusOut\n        });\n        apiRef.current.publishEvent('rowEditStop', newParams, event);\n      }\n    });\n  }, [apiRef, hasFieldsWithErrors]);\n  React.useEffect(() => {\n    return () => {\n      clearTimeout(focusTimeout.current);\n    };\n  }, []);\n  const handleCellKeyDown = React.useCallback((params, event) => {\n    if (params.cellMode === GridRowModes.Edit) {\n      // Wait until IME is settled for Asian languages like Japanese and Chinese\n      // TODO: to replace at one point. See https://github.com/mui/material-ui/pull/39713#discussion_r1381678957.\n      if (event.which === 229) {\n        return;\n      }\n      let reason;\n      if (event.key === 'Escape') {\n        reason = GridRowEditStopReasons.escapeKeyDown;\n      } else if (event.key === 'Enter') {\n        reason = GridRowEditStopReasons.enterKeyDown;\n      } else if (event.key === 'Tab') {\n        const columnFields = gridVisibleColumnFieldsSelector(apiRef).filter(field => {\n          const column = apiRef.current.getColumn(field);\n          if (column.type === GRID_ACTIONS_COLUMN_TYPE) {\n            return true;\n          }\n          return apiRef.current.isCellEditable(apiRef.current.getCellParams(params.id, field));\n        });\n        if (event.shiftKey) {\n          if (params.field === columnFields[0]) {\n            // Exit if user pressed Shift+Tab on the first field\n            reason = GridRowEditStopReasons.shiftTabKeyDown;\n          }\n        } else if (params.field === columnFields[columnFields.length - 1]) {\n          // Exit if user pressed Tab on the last field\n          reason = GridRowEditStopReasons.tabKeyDown;\n        }\n\n        // Always prevent going to the next element in the tab sequence because the focus is\n        // handled manually to support edit components rendered inside Portals\n        event.preventDefault();\n        if (!reason) {\n          const index = columnFields.findIndex(field => field === params.field);\n          const nextFieldToFocus = columnFields[event.shiftKey ? index - 1 : index + 1];\n          apiRef.current.setCellFocus(params.id, nextFieldToFocus);\n        }\n      }\n      if (reason) {\n        if (reason !== GridRowEditStopReasons.escapeKeyDown && hasFieldsWithErrors(params.id)) {\n          return;\n        }\n        const newParams = _extends({}, apiRef.current.getRowParams(params.id), {\n          reason,\n          field: params.field\n        });\n        apiRef.current.publishEvent('rowEditStop', newParams, event);\n      }\n    } else if (params.isEditable) {\n      let reason;\n      const canStartEditing = apiRef.current.unstable_applyPipeProcessors('canStartEditing', true, {\n        event,\n        cellParams: params,\n        editMode: 'row'\n      });\n      if (!canStartEditing) {\n        return;\n      }\n      if (isPrintableKey(event)) {\n        reason = GridRowEditStartReasons.printableKeyDown;\n      } else if (isPasteShortcut(event)) {\n        reason = GridRowEditStartReasons.printableKeyDown;\n      } else if (event.key === 'Enter') {\n        reason = GridRowEditStartReasons.enterKeyDown;\n      } else if (event.key === 'Backspace' || event.key === 'Delete') {\n        reason = GridRowEditStartReasons.deleteKeyDown;\n      }\n      if (reason) {\n        const rowParams = apiRef.current.getRowParams(params.id);\n        const newParams = _extends({}, rowParams, {\n          field: params.field,\n          reason\n        });\n        apiRef.current.publishEvent('rowEditStart', newParams, event);\n      }\n    }\n  }, [apiRef, hasFieldsWithErrors]);\n  const handleRowEditStart = React.useCallback(params => {\n    const {\n      id,\n      field,\n      reason\n    } = params;\n    const startRowEditModeParams = {\n      id,\n      fieldToFocus: field\n    };\n    if (reason === GridRowEditStartReasons.printableKeyDown || reason === GridRowEditStartReasons.deleteKeyDown) {\n      startRowEditModeParams.deleteValue = !!field;\n    }\n    apiRef.current.startRowEditMode(startRowEditModeParams);\n  }, [apiRef]);\n  const handleRowEditStop = React.useCallback(params => {\n    const {\n      id,\n      reason,\n      field\n    } = params;\n    apiRef.current.runPendingEditCellValueMutation(id);\n    let cellToFocusAfter;\n    if (reason === GridRowEditStopReasons.enterKeyDown) {\n      cellToFocusAfter = 'below';\n    } else if (reason === GridRowEditStopReasons.tabKeyDown) {\n      cellToFocusAfter = 'right';\n    } else if (reason === GridRowEditStopReasons.shiftTabKeyDown) {\n      cellToFocusAfter = 'left';\n    }\n    const ignoreModifications = reason === 'escapeKeyDown';\n    apiRef.current.stopRowEditMode({\n      id,\n      ignoreModifications,\n      field,\n      cellToFocusAfter\n    });\n  }, [apiRef]);\n  useGridEvent(apiRef, 'cellDoubleClick', runIfEditModeIsRow(handleCellDoubleClick));\n  useGridEvent(apiRef, 'cellFocusIn', runIfEditModeIsRow(handleCellFocusIn));\n  useGridEvent(apiRef, 'cellFocusOut', runIfEditModeIsRow(handleCellFocusOut));\n  useGridEvent(apiRef, 'cellKeyDown', runIfEditModeIsRow(handleCellKeyDown));\n  useGridEvent(apiRef, 'rowEditStart', runIfEditModeIsRow(handleRowEditStart));\n  useGridEvent(apiRef, 'rowEditStop', runIfEditModeIsRow(handleRowEditStop));\n  useGridEventPriority(apiRef, 'rowEditStart', props.onRowEditStart);\n  useGridEventPriority(apiRef, 'rowEditStop', props.onRowEditStop);\n  const getRowMode = React.useCallback(id => {\n    const isEditing = gridRowIsEditingSelector(apiRef, {\n      rowId: id,\n      editMode: props.editMode\n    });\n    return isEditing ? GridRowModes.Edit : GridRowModes.View;\n  }, [apiRef, props.editMode]);\n  const updateRowModesModel = useEventCallback(newModel => {\n    const isNewModelDifferentFromProp = newModel !== props.rowModesModel;\n    if (onRowModesModelChange && isNewModelDifferentFromProp) {\n      onRowModesModelChange(newModel, {\n        api: apiRef.current\n      });\n    }\n    if (props.rowModesModel && isNewModelDifferentFromProp) {\n      return; // The prop always win\n    }\n    setRowModesModel(newModel);\n    rowModesModelRef.current = newModel;\n    apiRef.current.publishEvent('rowModesModelChange', newModel);\n  });\n  const updateRowInRowModesModel = React.useCallback((id, newProps) => {\n    const newModel = _extends({}, rowModesModelRef.current);\n    if (newProps !== null) {\n      newModel[id] = _extends({}, newProps);\n    } else {\n      delete newModel[id];\n    }\n    updateRowModesModel(newModel);\n  }, [updateRowModesModel]);\n  const updateOrDeleteRowState = React.useCallback((id, newProps) => {\n    apiRef.current.setState(state => {\n      const newEditingState = _extends({}, state.editRows);\n      if (newProps !== null) {\n        newEditingState[id] = newProps;\n      } else {\n        delete newEditingState[id];\n      }\n      return _extends({}, state, {\n        editRows: newEditingState\n      });\n    });\n  }, [apiRef]);\n  const updateOrDeleteFieldState = React.useCallback((id, field, newProps) => {\n    apiRef.current.setState(state => {\n      const newEditingState = _extends({}, state.editRows);\n      if (newProps !== null) {\n        newEditingState[id] = _extends({}, newEditingState[id], {\n          [field]: _extends({}, newProps)\n        });\n      } else {\n        delete newEditingState[id][field];\n        if (Object.keys(newEditingState[id]).length === 0) {\n          delete newEditingState[id];\n        }\n      }\n      return _extends({}, state, {\n        editRows: newEditingState\n      });\n    });\n  }, [apiRef]);\n  const startRowEditMode = React.useCallback(params => {\n    const {\n        id\n      } = params,\n      other = _objectWithoutPropertiesLoose(params, _excluded);\n    throwIfNotInMode(id, GridRowModes.View);\n    updateRowInRowModesModel(id, _extends({\n      mode: GridRowModes.Edit\n    }, other));\n  }, [throwIfNotInMode, updateRowInRowModesModel]);\n  const updateStateToStartRowEditMode = useEventCallback(params => {\n    const {\n      id,\n      fieldToFocus,\n      deleteValue,\n      initialValue\n    } = params;\n    const row = apiRef.current.getRow(id);\n    const columns = gridColumnDefinitionsSelector(apiRef);\n    const newProps = columns.reduce((acc, col) => {\n      const field = col.field;\n      const cellParams = apiRef.current.getCellParams(id, field);\n      if (!cellParams.isEditable) {\n        return acc;\n      }\n      const column = apiRef.current.getColumn(field);\n      let newValue = apiRef.current.getCellValue(id, field);\n      if (fieldToFocus === field && (deleteValue || initialValue)) {\n        if (deleteValue) {\n          newValue = getDefaultCellValue(column);\n        } else if (initialValue) {\n          newValue = initialValue;\n        }\n      }\n      acc[field] = {\n        value: newValue,\n        error: false,\n        isProcessingProps: column.editable && !!column.preProcessEditCellProps && deleteValue\n      };\n      return acc;\n    }, {});\n    prevRowValuesLookup.current[id] = row;\n    updateOrDeleteRowState(id, newProps);\n    if (fieldToFocus) {\n      apiRef.current.setCellFocus(id, fieldToFocus);\n    }\n    columns.filter(column => column.editable && !!column.preProcessEditCellProps && deleteValue).forEach(column => {\n      const field = column.field;\n      const value = apiRef.current.getCellValue(id, field);\n      const newValue = deleteValue ? getDefaultCellValue(column) : initialValue ?? value;\n      Promise.resolve(column.preProcessEditCellProps({\n        id,\n        row,\n        props: newProps[field],\n        hasChanged: newValue !== value\n      })).then(processedProps => {\n        // Check if still in edit mode before updating\n        if (apiRef.current.getRowMode(id) === GridRowModes.Edit) {\n          const editingState = gridEditRowsStateSelector(apiRef);\n          updateOrDeleteFieldState(id, field, _extends({}, processedProps, {\n            value: editingState[id][field].value,\n            isProcessingProps: false\n          }));\n        }\n      });\n    });\n  });\n  const stopRowEditMode = React.useCallback(params => {\n    const {\n        id\n      } = params,\n      other = _objectWithoutPropertiesLoose(params, _excluded2);\n    throwIfNotInMode(id, GridRowModes.Edit);\n    updateRowInRowModesModel(id, _extends({\n      mode: GridRowModes.View\n    }, other));\n  }, [throwIfNotInMode, updateRowInRowModesModel]);\n  const updateStateToStopRowEditMode = useEventCallback(async params => {\n    const {\n      id,\n      ignoreModifications,\n      field: focusedField,\n      cellToFocusAfter = 'none'\n    } = params;\n    apiRef.current.runPendingEditCellValueMutation(id);\n    const finishRowEditMode = () => {\n      if (cellToFocusAfter !== 'none' && focusedField) {\n        apiRef.current.moveFocusToRelativeCell(id, focusedField, cellToFocusAfter);\n      }\n      updateOrDeleteRowState(id, null);\n      updateRowInRowModesModel(id, null);\n      delete prevRowValuesLookup.current[id];\n    };\n    if (ignoreModifications) {\n      finishRowEditMode();\n      return;\n    }\n    const editingState = gridEditRowsStateSelector(apiRef);\n    const row = prevRowValuesLookup.current[id];\n    const isSomeFieldProcessingProps = Object.values(editingState[id]).some(fieldProps => fieldProps.isProcessingProps);\n    if (isSomeFieldProcessingProps) {\n      prevRowModesModel.current[id].mode = GridRowModes.Edit;\n      return;\n    }\n    if (hasFieldsWithErrors(id)) {\n      prevRowModesModel.current[id].mode = GridRowModes.Edit;\n      // Revert the mode in the rowModesModel prop back to \"edit\"\n      updateRowInRowModesModel(id, {\n        mode: GridRowModes.Edit\n      });\n      return;\n    }\n    const rowUpdate = apiRef.current.getRowWithUpdatedValuesFromRowEditing(id);\n    if (props.dataSource?.updateRow) {\n      if (isDeepEqual(row, rowUpdate)) {\n        finishRowEditMode();\n        return;\n      }\n      const handleError = () => {\n        prevRowModesModel.current[id].mode = GridRowModes.Edit;\n        // Revert the mode in the rowModesModel prop back to \"edit\"\n        updateRowInRowModesModel(id, {\n          mode: GridRowModes.Edit\n        });\n      };\n      const updateRowParams = {\n        rowId: id,\n        updatedRow: rowUpdate,\n        previousRow: row\n      };\n      try {\n        await apiRef.current.dataSource.editRow(updateRowParams);\n        finishRowEditMode();\n      } catch {\n        handleError();\n      }\n    } else if (processRowUpdate) {\n      const handleError = errorThrown => {\n        // The row might have been deleted\n        if (prevRowModesModel.current[id]) {\n          prevRowModesModel.current[id].mode = GridRowModes.Edit;\n          // Revert the mode in the rowModesModel prop back to \"edit\"\n          updateRowInRowModesModel(id, {\n            mode: GridRowModes.Edit\n          });\n        }\n        if (onProcessRowUpdateError) {\n          onProcessRowUpdateError(errorThrown);\n        } else if (process.env.NODE_ENV !== 'production') {\n          warnOnce(['MUI X: A call to `processRowUpdate` threw an error which was not handled because `onProcessRowUpdateError` is missing.', 'To handle the error pass a callback to the `onProcessRowUpdateError` prop, for example `<DataGrid onProcessRowUpdateError={(error) => ...} />`.', 'For more detail, see https://mui.com/x/react-data-grid/editing/persistence/.'], 'error');\n        }\n      };\n      try {\n        Promise.resolve(processRowUpdate(rowUpdate, row, {\n          rowId: id\n        })).then(finalRowUpdate => {\n          apiRef.current.updateRows([finalRowUpdate]);\n          finishRowEditMode();\n        }).catch(handleError);\n      } catch (errorThrown) {\n        handleError(errorThrown);\n      }\n    } else {\n      apiRef.current.updateRows([rowUpdate]);\n      finishRowEditMode();\n    }\n  });\n  const setRowEditingEditCellValue = React.useCallback(params => {\n    const {\n      id,\n      field,\n      value,\n      debounceMs,\n      unstable_skipValueParser: skipValueParser\n    } = params;\n    throwIfNotEditable(id, field);\n    const column = apiRef.current.getColumn(field);\n    const row = apiRef.current.getRow(id);\n    let parsedValue = value;\n    if (column.valueParser && !skipValueParser) {\n      parsedValue = column.valueParser(value, row, column, apiRef);\n    }\n    let editingState = gridEditRowsStateSelector(apiRef);\n    let newProps = _extends({}, editingState[id][field], {\n      value: parsedValue,\n      changeReason: debounceMs ? 'debouncedSetEditCellValue' : 'setEditCellValue'\n    });\n    if (!column.preProcessEditCellProps) {\n      updateOrDeleteFieldState(id, field, newProps);\n    }\n    return new Promise(resolve => {\n      const promises = [];\n      if (column.preProcessEditCellProps) {\n        const hasChanged = newProps.value !== editingState[id][field].value;\n        newProps = _extends({}, newProps, {\n          isProcessingProps: true\n        });\n        updateOrDeleteFieldState(id, field, newProps);\n        const _editingState$id = editingState[id],\n          otherFieldsProps = _objectWithoutPropertiesLoose(_editingState$id, [field].map(_toPropertyKey));\n        const promise = Promise.resolve(column.preProcessEditCellProps({\n          id,\n          row,\n          props: newProps,\n          hasChanged,\n          otherFieldsProps\n        })).then(processedProps => {\n          // Check again if the row is in edit mode because the user may have\n          // discarded the changes while the props were being processed.\n          if (apiRef.current.getRowMode(id) === GridRowModes.View) {\n            resolve(false);\n            return;\n          }\n          editingState = gridEditRowsStateSelector(apiRef);\n          processedProps = _extends({}, processedProps, {\n            isProcessingProps: false\n          });\n          // We don't reuse the value from the props pre-processing because when the\n          // promise resolves it may be already outdated. The only exception to this rule\n          // is when there's no pre-processing.\n          processedProps.value = column.preProcessEditCellProps ? editingState[id][field].value : parsedValue;\n          updateOrDeleteFieldState(id, field, processedProps);\n        });\n        promises.push(promise);\n      }\n      Object.entries(editingState[id]).forEach(([thisField, fieldProps]) => {\n        if (thisField === field) {\n          return;\n        }\n        const fieldColumn = apiRef.current.getColumn(thisField);\n        if (!fieldColumn.preProcessEditCellProps) {\n          return;\n        }\n        fieldProps = _extends({}, fieldProps, {\n          isProcessingProps: true\n        });\n        updateOrDeleteFieldState(id, thisField, fieldProps);\n        editingState = gridEditRowsStateSelector(apiRef);\n        const _editingState$id2 = editingState[id],\n          otherFieldsProps = _objectWithoutPropertiesLoose(_editingState$id2, [thisField].map(_toPropertyKey));\n        const promise = Promise.resolve(fieldColumn.preProcessEditCellProps({\n          id,\n          row,\n          props: fieldProps,\n          hasChanged: false,\n          otherFieldsProps\n        })).then(processedProps => {\n          // Check again if the row is in edit mode because the user may have\n          // discarded the changes while the props were being processed.\n          if (apiRef.current.getRowMode(id) === GridRowModes.View) {\n            resolve(false);\n            return;\n          }\n          processedProps = _extends({}, processedProps, {\n            isProcessingProps: false\n          });\n          updateOrDeleteFieldState(id, thisField, processedProps);\n        });\n        promises.push(promise);\n      });\n      Promise.all(promises).then(() => {\n        if (apiRef.current.getRowMode(id) === GridRowModes.Edit) {\n          editingState = gridEditRowsStateSelector(apiRef);\n          resolve(!editingState[id][field].error);\n        } else {\n          resolve(false);\n        }\n      });\n    });\n  }, [apiRef, throwIfNotEditable, updateOrDeleteFieldState]);\n  const getRowWithUpdatedValuesFromRowEditing = React.useCallback(id => {\n    const editingState = gridEditRowsStateSelector(apiRef);\n    const row = apiRef.current.getRow(id);\n    if (!editingState[id]) {\n      return apiRef.current.getRow(id);\n    }\n    let rowUpdate = _extends({}, prevRowValuesLookup.current[id], row);\n    Object.entries(editingState[id]).forEach(([field, fieldProps]) => {\n      const column = apiRef.current.getColumn(field);\n      // Column might have been removed\n      // see https://github.com/mui/mui-x/pull/16888\n      if (column?.valueSetter) {\n        rowUpdate = column.valueSetter(fieldProps.value, rowUpdate, column, apiRef);\n      } else {\n        rowUpdate[field] = fieldProps.value;\n      }\n    });\n    return rowUpdate;\n  }, [apiRef]);\n  const editingApi = {\n    getRowMode,\n    startRowEditMode,\n    stopRowEditMode\n  };\n  const editingPrivateApi = {\n    setRowEditingEditCellValue,\n    getRowWithUpdatedValuesFromRowEditing\n  };\n  useGridApiMethod(apiRef, editingApi, 'public');\n  useGridApiMethod(apiRef, editingPrivateApi, 'private');\n  React.useEffect(() => {\n    if (rowModesModelProp) {\n      updateRowModesModel(rowModesModelProp);\n    }\n  }, [rowModesModelProp, updateRowModesModel]);\n\n  // Run this effect synchronously so that the keyboard event can impact the yet-to-be-rendered input.\n  useEnhancedEffect(() => {\n    const rowsLookup = gridRowsLookupSelector(apiRef);\n\n    // Update the ref here because updateStateToStopRowEditMode may change it later\n    const copyOfPrevRowModesModel = prevRowModesModel.current;\n    prevRowModesModel.current = deepClone(rowModesModel); // Do a deep-clone because the attributes might be changed later\n\n    const ids = new Set([...Object.keys(rowModesModel), ...Object.keys(copyOfPrevRowModesModel)]);\n    Array.from(ids).forEach(id => {\n      const params = rowModesModel[id] ?? {\n        mode: GridRowModes.View\n      };\n      const prevMode = copyOfPrevRowModesModel[id]?.mode || GridRowModes.View;\n      const originalId = rowsLookup[id] ? apiRef.current.getRowId(rowsLookup[id]) : id;\n      if (params.mode === GridRowModes.Edit && prevMode === GridRowModes.View) {\n        updateStateToStartRowEditMode(_extends({\n          id: originalId\n        }, params));\n      } else if (params.mode === GridRowModes.View && prevMode === GridRowModes.Edit) {\n        updateStateToStopRowEditMode(_extends({\n          id: originalId\n        }, params));\n      }\n    });\n  }, [apiRef, rowModesModel, updateStateToStartRowEditMode, updateStateToStopRowEditMode]);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}