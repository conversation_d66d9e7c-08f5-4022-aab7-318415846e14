{"ast": null, "code": "import { gridVisibleColumnDefinitionsSelector } from \"../features/columns/gridColumnsSelector.js\";\nimport { useGridSelector } from \"./useGridSelector.js\";\nimport { useGridRootProps } from \"./useGridRootProps.js\";\nimport { gridColumnGroupsHeaderMaxDepthSelector } from \"../features/columnGrouping/gridColumnGroupsSelector.js\";\nimport { gridPinnedRowsCountSelector } from \"../features/rows/gridRowsSelector.js\";\nimport { useGridPrivateApiContext } from \"./useGridPrivateApiContext.js\";\nimport { isMultipleRowSelectionEnabled } from \"../features/rowSelection/utils.js\";\nimport { gridExpandedRowCountSelector } from \"../features/filter/gridFilterSelector.js\";\nexport const useGridAriaAttributes = () => {\n  const apiRef = useGridPrivateApiContext();\n  const rootProps = useGridRootProps();\n  const visibleColumns = useGridSelector(apiRef, gridVisibleColumnDefinitionsSelector);\n  const accessibleRowCount = useGridSelector(apiRef, gridExpandedRowCountSelector);\n  const headerGroupingMaxDepth = useGridSelector(apiRef, gridColumnGroupsHeaderMaxDepthSelector);\n  const pinnedRowsCount = useGridSelector(apiRef, gridPinnedRowsCountSelector);\n  const ariaLabel = rootProps['aria-label'];\n  const ariaLabelledby = rootProps['aria-labelledby'];\n  // `aria-label` and `aria-labelledby` should take precedence over `label`\n  const shouldUseLabelAsAriaLabel = !ariaLabel && !ariaLabelledby && rootProps.label;\n  return {\n    role: 'grid',\n    'aria-label': shouldUseLabelAsAriaLabel ? rootProps.label : ariaLabel,\n    'aria-labelledby': ariaLabelledby,\n    'aria-colcount': visibleColumns.length,\n    'aria-rowcount': headerGroupingMaxDepth + 1 + pinnedRowsCount + accessibleRowCount,\n    'aria-multiselectable': isMultipleRowSelectionEnabled(rootProps)\n  };\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}