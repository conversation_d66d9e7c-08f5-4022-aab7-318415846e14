{"ast": null, "code": "import * as React from 'react';\nimport { localStorageAvailable } from \"../../utils/utils.js\";\nimport { useGridApiMethod } from \"../utils/index.js\";\nconst forceDebug = localStorageAvailable() && window.localStorage.getItem('DEBUG') != null;\nconst noop = () => {};\nconst noopLogger = {\n  debug: noop,\n  info: noop,\n  warn: noop,\n  error: noop\n};\nconst LOG_LEVELS = ['debug', 'info', 'warn', 'error'];\nfunction getAppender(name, logLevel, appender = console) {\n  const minLogLevelIdx = LOG_LEVELS.indexOf(logLevel);\n  if (minLogLevelIdx === -1) {\n    throw new Error(`MUI X: Log level ${logLevel} not recognized.`);\n  }\n  const logger = LOG_LEVELS.reduce((loggerObj, method, idx) => {\n    if (idx >= minLogLevelIdx) {\n      loggerObj[method] = (...args) => {\n        const [message, ...other] = args;\n        appender[method](`MUI X: ${name} - ${message}`, ...other);\n      };\n    } else {\n      loggerObj[method] = noop;\n    }\n    return loggerObj;\n  }, {});\n  return logger;\n}\nexport const useGridLoggerFactory = (apiRef, props) => {\n  const getLogger = React.useCallback(name => {\n    if (forceDebug) {\n      return getAppender(name, 'debug', props.logger);\n    }\n    if (!props.logLevel) {\n      return noopLogger;\n    }\n    return getAppender(name, props.logLevel.toString(), props.logger);\n  }, [props.logLevel, props.logger]);\n  useGridApiMethod(apiRef, {\n    getLogger\n  }, 'private');\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}