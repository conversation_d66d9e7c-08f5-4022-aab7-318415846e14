{"ast": null, "code": "/* eslint-disable no-bitwise */\nexport function sortByDocumentPosition(a, b) {\n  if (!a.ref.current || !b.ref.current) {\n    return 0;\n  }\n  const position = a.ref.current.compareDocumentPosition(b.ref.current);\n  if (!position) {\n    return 0;\n  }\n  if (position & Node.DOCUMENT_POSITION_FOLLOWING || position & Node.DOCUMENT_POSITION_CONTAINED_BY) {\n    return -1;\n  }\n  if (position & Node.DOCUMENT_POSITION_PRECEDING || position & Node.DOCUMENT_POSITION_CONTAINS) {\n    return 1;\n  }\n  return 0;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}