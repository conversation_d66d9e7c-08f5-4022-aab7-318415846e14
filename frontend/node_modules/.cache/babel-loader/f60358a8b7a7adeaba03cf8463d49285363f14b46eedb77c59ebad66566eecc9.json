{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"api\", \"colDef\", \"id\", \"hasFocus\", \"isEditable\", \"field\", \"value\", \"formattedValue\", \"row\", \"rowNode\", \"cellMode\", \"tabIndex\", \"position\", \"focusElementRef\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport useId from '@mui/utils/useId';\nimport { gridClasses } from \"../../constants/gridClasses.js\";\nimport { GridMenu } from \"../menu/GridMenu.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst hasActions = colDef => typeof colDef.getActions === 'function';\nfunction GridActionsCell(props) {\n  const {\n      colDef,\n      id,\n      hasFocus,\n      tabIndex,\n      position = 'bottom-end',\n      focusElementRef\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const [focusedButtonIndex, setFocusedButtonIndex] = React.useState(-1);\n  const [open, setOpen] = React.useState(false);\n  const apiRef = useGridApiContext();\n  const rootRef = React.useRef(null);\n  const buttonRef = React.useRef(null);\n  const ignoreCallToFocus = React.useRef(false);\n  const touchRippleRefs = React.useRef({});\n  const isRtl = useRtl();\n  const menuId = useId();\n  const buttonId = useId();\n  const rootProps = useGridRootProps();\n  if (!hasActions(colDef)) {\n    throw new Error('MUI X: Missing the `getActions` property in the `GridColDef`.');\n  }\n  const options = colDef.getActions(apiRef.current.getRowParams(id));\n  const iconButtons = options.filter(option => !option.props.showInMenu);\n  const menuButtons = options.filter(option => option.props.showInMenu);\n  const numberOfButtons = iconButtons.length + (menuButtons.length ? 1 : 0);\n  React.useLayoutEffect(() => {\n    if (!hasFocus) {\n      Object.entries(touchRippleRefs.current).forEach(([index, ref]) => {\n        ref?.stop({}, () => {\n          delete touchRippleRefs.current[index];\n        });\n      });\n    }\n  }, [hasFocus]);\n  React.useEffect(() => {\n    if (focusedButtonIndex < 0 || !rootRef.current) {\n      return;\n    }\n    if (focusedButtonIndex >= rootRef.current.children.length) {\n      return;\n    }\n    const child = rootRef.current.children[focusedButtonIndex];\n    child.focus({\n      preventScroll: true\n    });\n  }, [focusedButtonIndex]);\n  React.useEffect(() => {\n    if (!hasFocus) {\n      setFocusedButtonIndex(-1);\n      ignoreCallToFocus.current = false;\n    }\n  }, [hasFocus]);\n  React.useImperativeHandle(focusElementRef, () => ({\n    focus() {\n      // If ignoreCallToFocus is true, then one of the buttons was clicked and the focus is already set\n      if (!ignoreCallToFocus.current) {\n        // find the first focusable button and pass the index to the state\n        const focusableButtonIndex = options.findIndex(o => !o.props.disabled);\n        setFocusedButtonIndex(focusableButtonIndex);\n      }\n    }\n  }), [options]);\n  React.useEffect(() => {\n    if (focusedButtonIndex >= numberOfButtons) {\n      setFocusedButtonIndex(numberOfButtons - 1);\n    }\n  }, [focusedButtonIndex, numberOfButtons]);\n  const showMenu = () => {\n    setOpen(true);\n    setFocusedButtonIndex(numberOfButtons - 1);\n    ignoreCallToFocus.current = true;\n  };\n  const hideMenu = () => {\n    setOpen(false);\n  };\n  const toggleMenu = event => {\n    event.stopPropagation();\n    event.preventDefault();\n    if (open) {\n      hideMenu();\n    } else {\n      showMenu();\n    }\n  };\n  const handleTouchRippleRef = index => instance => {\n    touchRippleRefs.current[index] = instance;\n  };\n  const handleButtonClick = (index, onClick) => event => {\n    setFocusedButtonIndex(index);\n    ignoreCallToFocus.current = true;\n    if (onClick) {\n      onClick(event);\n    }\n  };\n  const handleRootKeyDown = event => {\n    if (numberOfButtons <= 1) {\n      return;\n    }\n    const getNewIndex = (index, direction) => {\n      if (index < 0 || index > options.length) {\n        return index;\n      }\n\n      // for rtl mode we need to reverse the direction\n      const rtlMod = isRtl ? -1 : 1;\n      const indexMod = (direction === 'left' ? -1 : 1) * rtlMod;\n\n      // if the button that should receive focus is disabled go one more step\n      return options[index + indexMod]?.props.disabled ? getNewIndex(index + indexMod, direction) : index + indexMod;\n    };\n    let newIndex = focusedButtonIndex;\n    if (event.key === 'ArrowRight') {\n      newIndex = getNewIndex(focusedButtonIndex, 'right');\n    } else if (event.key === 'ArrowLeft') {\n      newIndex = getNewIndex(focusedButtonIndex, 'left');\n    }\n    if (newIndex < 0 || newIndex >= numberOfButtons) {\n      return; // We're already in the first or last item = do nothing and let the grid listen the event\n    }\n    if (newIndex !== focusedButtonIndex) {\n      event.preventDefault(); // Prevent scrolling\n      event.stopPropagation(); // Don't stop propagation for other keys, for example ArrowUp\n      setFocusedButtonIndex(newIndex);\n    }\n  };\n  return /*#__PURE__*/_jsxs(\"div\", _extends({\n    role: \"menu\",\n    ref: rootRef,\n    tabIndex: -1,\n    className: gridClasses.actionsCell,\n    onKeyDown: handleRootKeyDown\n  }, other, {\n    children: [iconButtons.map((button, index) => /*#__PURE__*/React.cloneElement(button, {\n      key: index,\n      touchRippleRef: handleTouchRippleRef(index),\n      onClick: handleButtonClick(index, button.props.onClick),\n      tabIndex: focusedButtonIndex === index ? tabIndex : -1\n    })), menuButtons.length > 0 && buttonId && /*#__PURE__*/_jsx(rootProps.slots.baseIconButton, _extends({\n      ref: buttonRef,\n      id: buttonId,\n      \"aria-label\": apiRef.current.getLocaleText('actionsCellMore'),\n      \"aria-haspopup\": \"menu\",\n      \"aria-expanded\": open,\n      \"aria-controls\": open ? menuId : undefined,\n      role: \"menuitem\",\n      size: \"small\",\n      onClick: toggleMenu,\n      touchRippleRef: handleTouchRippleRef(buttonId),\n      tabIndex: focusedButtonIndex === iconButtons.length ? tabIndex : -1\n    }, rootProps.slotProps?.baseIconButton, {\n      children: /*#__PURE__*/_jsx(rootProps.slots.moreActionsIcon, {\n        fontSize: \"small\"\n      })\n    })), menuButtons.length > 0 && /*#__PURE__*/_jsx(GridMenu, {\n      open: open,\n      target: buttonRef.current,\n      position: position,\n      onClose: hideMenu,\n      children: /*#__PURE__*/_jsx(rootProps.slots.baseMenuList, {\n        id: menuId,\n        className: gridClasses.menuList,\n        \"aria-labelledby\": buttonId,\n        autoFocusItem: true,\n        children: menuButtons.map((button, index) => /*#__PURE__*/React.cloneElement(button, {\n          key: index,\n          closeMenu: hideMenu\n        }))\n      })\n    })]\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? GridActionsCell.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  api: PropTypes.object,\n  /**\n   * The mode of the cell.\n   */\n  cellMode: PropTypes.oneOf(['edit', 'view']).isRequired,\n  /**\n   * The column of the row that the current cell belongs to.\n   */\n  colDef: PropTypes.object.isRequired,\n  /**\n   * The column field of the cell that triggered the event.\n   */\n  field: PropTypes.string.isRequired,\n  /**\n   * A ref allowing to set imperative focus.\n   * It can be passed to the element that should receive focus.\n   * @ignore - do not document.\n   */\n  focusElementRef: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n    current: PropTypes.shape({\n      focus: PropTypes.func.isRequired\n    })\n  })]),\n  /**\n   * The cell value formatted with the column valueFormatter.\n   */\n  formattedValue: PropTypes.any,\n  /**\n   * If true, the cell is the active element.\n   */\n  hasFocus: PropTypes.bool.isRequired,\n  /**\n   * The grid row id.\n   */\n  id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,\n  /**\n   * If true, the cell is editable.\n   */\n  isEditable: PropTypes.bool,\n  position: PropTypes.oneOf(['bottom-end', 'bottom-start', 'bottom', 'left-end', 'left-start', 'left', 'right-end', 'right-start', 'right', 'top-end', 'top-start', 'top']),\n  /**\n   * The row model of the row that the current cell belongs to.\n   */\n  row: PropTypes.any.isRequired,\n  /**\n   * The node of the row that the current cell belongs to.\n   */\n  rowNode: PropTypes.object.isRequired,\n  /**\n   * the tabIndex value.\n   */\n  tabIndex: PropTypes.oneOf([-1, 0]).isRequired,\n  /**\n   * The cell value.\n   * If the column has `valueGetter`, use `params.row` to directly access the fields.\n   */\n  value: PropTypes.any\n} : void 0;\nexport { GridActionsCell };\nexport const renderActionsCell = params => /*#__PURE__*/_jsx(GridActionsCell, _extends({}, params));\nif (process.env.NODE_ENV !== \"production\") renderActionsCell.displayName = \"renderActionsCell\";", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}