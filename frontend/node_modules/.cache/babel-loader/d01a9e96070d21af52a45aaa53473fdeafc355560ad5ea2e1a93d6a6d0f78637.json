{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/Dashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Paper, Typography, Card, CardContent, Avatar, List, ListItem, ListItemAvatar, ListItemText, Chip } from '@mui/material';\nimport { People as PeopleIcon, Security as SecurityIcon, VpnKey as VpnKeyIcon, TrendingUp as TrendingUpIcon } from '@mui/icons-material';\nimport { useAuth } from '../contexts/AuthContext';\nimport apiService from '../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  var _user$role;\n  const {\n    user\n  } = useAuth();\n  const [stats, setStats] = useState({\n    totalUsers: 0,\n    totalRoles: 0,\n    totalPermissions: 0,\n    recentUsers: []\n  });\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    const fetchDashboardData = async () => {\n      try {\n        setLoading(true);\n\n        // Récupérer les statistiques en parallèle\n        const [usersResponse, rolesResponse, permissionsResponse] = await Promise.all([apiService.getUsers(1, 5),\n        // Récupérer les 5 premiers utilisateurs\n        apiService.getAllRoles(), apiService.getAllPermissions()]);\n        setStats({\n          totalUsers: usersResponse.total,\n          totalRoles: rolesResponse.length,\n          totalPermissions: permissionsResponse.length,\n          recentUsers: usersResponse.items\n        });\n      } catch (error) {\n        console.error('Erreur lors du chargement des données du dashboard:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchDashboardData();\n  }, []);\n  const StatCard = ({\n    title,\n    value,\n    icon,\n    color\n  }) => /*#__PURE__*/_jsxDEV(Card, {\n    children: /*#__PURE__*/_jsxDEV(CardContent, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        alignItems: \"center\",\n        justifyContent: \"space-between\",\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            color: \"textSecondary\",\n            gutterBottom: true,\n            variant: \"body2\",\n            children: title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            component: \"h2\",\n            children: loading ? '...' : value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n          sx: {\n            bgcolor: color,\n            width: 56,\n            height: 56\n          },\n          children: icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 77,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: \"Tableau de bord\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      color: \"text.secondary\",\n      sx: {\n        mb: 3\n      },\n      children: [\"Bienvenue \", user === null || user === void 0 ? void 0 : user.full_name, \" ! Voici un aper\\xE7u de votre syst\\xE8me de gestion des utilisateurs.\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        flexWrap: 'wrap',\n        gap: 3,\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          flex: '1 1 250px'\n        },\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Utilisateurs\",\n          value: stats.totalUsers,\n          icon: /*#__PURE__*/_jsxDEV(PeopleIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 19\n          }, this),\n          color: \"#1976d2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          flex: '1 1 250px'\n        },\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"R\\xF4les\",\n          value: stats.totalRoles,\n          icon: /*#__PURE__*/_jsxDEV(SecurityIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 19\n          }, this),\n          color: \"#388e3c\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          flex: '1 1 250px'\n        },\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Permissions\",\n          value: stats.totalPermissions,\n          icon: /*#__PURE__*/_jsxDEV(VpnKeyIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 19\n          }, this),\n          color: \"#f57c00\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          flex: '1 1 250px'\n        },\n        children: /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Activit\\xE9\",\n          value: 100,\n          icon: /*#__PURE__*/_jsxDEV(TrendingUpIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 19\n          }, this),\n          color: \"#7b1fa2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        flexWrap: 'wrap',\n        gap: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          flex: '1 1 400px'\n        },\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Utilisateurs r\\xE9cents\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this), loading ? /*#__PURE__*/_jsxDEV(Typography, {\n            children: \"Chargement...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(List, {\n            children: stats.recentUsers.map(recentUser => {\n              var _recentUser$role;\n              return /*#__PURE__*/_jsxDEV(ListItem, {\n                children: [/*#__PURE__*/_jsxDEV(ListItemAvatar, {\n                  children: /*#__PURE__*/_jsxDEV(Avatar, {\n                    children: recentUser.full_name.charAt(0).toUpperCase()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 160,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: recentUser.full_name,\n                  secondary: /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: recentUser.email\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 168,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        mt: 0.5\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Chip, {\n                        label: ((_recentUser$role = recentUser.role) === null || _recentUser$role === void 0 ? void 0 : _recentUser$role.name) || 'Aucun rôle',\n                        size: \"small\",\n                        color: recentUser.is_active ? 'success' : 'default',\n                        variant: \"outlined\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 172,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 171,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 167,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 21\n                }, this)]\n              }, recentUser.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          flex: '1 1 400px'\n        },\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Votre profil\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Nom complet\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              sx: {\n                mb: 2\n              },\n              children: user === null || user === void 0 ? void 0 : user.full_name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              sx: {\n                mb: 2\n              },\n              children: user === null || user === void 0 ? void 0 : user.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"R\\xF4le\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              label: (user === null || user === void 0 ? void 0 : (_user$role = user.role) === null || _user$role === void 0 ? void 0 : _user$role.name) || 'Aucun rôle',\n              color: \"primary\",\n              sx: {\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Statut\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              label: user !== null && user !== void 0 && user.is_active ? 'Actif' : 'Inactif',\n              color: user !== null && user !== void 0 && user.is_active ? 'success' : 'default',\n              sx: {\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this), (user === null || user === void 0 ? void 0 : user.is_superuser) && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Privil\\xE8ges\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: \"Super Administrateur\",\n                color: \"error\",\n                sx: {\n                  mb: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Permissions (\", (user === null || user === void 0 ? void 0 : user.permissions.length) || 0, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mt: 1\n              },\n              children: [user === null || user === void 0 ? void 0 : user.permissions.slice(0, 5).map(permission => /*#__PURE__*/_jsxDEV(Chip, {\n                label: permission,\n                size: \"small\",\n                variant: \"outlined\",\n                sx: {\n                  mr: 0.5,\n                  mb: 0.5\n                }\n              }, permission, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 19\n              }, this)), user && user.permissions.length > 5 && /*#__PURE__*/_jsxDEV(Chip, {\n                label: `+${user.permissions.length - 5} autres`,\n                size: \"small\",\n                variant: \"outlined\",\n                sx: {\n                  mr: 0.5,\n                  mb: 0.5\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 97,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"E4RQA3+bx4jHwwberxsyZBMsAxE=\", false, function () {\n  return [useAuth];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Paper", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Avatar", "List", "ListItem", "ListItemAvatar", "ListItemText", "Chip", "People", "PeopleIcon", "Security", "SecurityIcon", "VpnKey", "VpnKeyIcon", "TrendingUp", "TrendingUpIcon", "useAuth", "apiService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Dashboard", "_s", "_user$role", "user", "stats", "setStats", "totalUsers", "totalRoles", "totalPermissions", "recentUsers", "loading", "setLoading", "fetchDashboardData", "usersResponse", "rolesResponse", "permissionsResponse", "Promise", "all", "getUsers", "getAllRoles", "getAllPermissions", "total", "length", "items", "error", "console", "StatCard", "title", "value", "icon", "color", "children", "display", "alignItems", "justifyContent", "gutterBottom", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "component", "sx", "bgcolor", "width", "height", "mb", "full_name", "flexWrap", "gap", "flex", "p", "map", "recentUser", "_recentUser$role", "char<PERSON>t", "toUpperCase", "primary", "secondary", "email", "mt", "label", "role", "name", "size", "is_active", "id", "is_superuser", "permissions", "slice", "permission", "mr", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/Dashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Grid,\n  Paper,\n  Typography,\n  Card,\n  CardContent,\n  Avatar,\n  List,\n  ListItem,\n  ListItemAvatar,\n  ListItemText,\n  Chip,\n} from '@mui/material';\nimport {\n  People as PeopleIcon,\n  Security as SecurityIcon,\n  VpnKey as VpnKeyIcon,\n  TrendingUp as TrendingUpIcon,\n} from '@mui/icons-material';\nimport { useAuth } from '../contexts/AuthContext';\nimport apiService from '../services/api';\nimport { User } from '../types';\n\ninterface DashboardStats {\n  totalUsers: number;\n  totalRoles: number;\n  totalPermissions: number;\n  recentUsers: User[];\n}\n\nconst Dashboard: React.FC = () => {\n  const { user } = useAuth();\n  const [stats, setStats] = useState<DashboardStats>({\n    totalUsers: 0,\n    totalRoles: 0,\n    totalPermissions: 0,\n    recentUsers: [],\n  });\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchDashboardData = async () => {\n      try {\n        setLoading(true);\n        \n        // Récupérer les statistiques en parallèle\n        const [usersResponse, rolesResponse, permissionsResponse] = await Promise.all([\n          apiService.getUsers(1, 5), // Récupérer les 5 premiers utilisateurs\n          apiService.getAllRoles(),\n          apiService.getAllPermissions(),\n        ]);\n\n        setStats({\n          totalUsers: usersResponse.total,\n          totalRoles: rolesResponse.length,\n          totalPermissions: permissionsResponse.length,\n          recentUsers: usersResponse.items,\n        });\n      } catch (error) {\n        console.error('Erreur lors du chargement des données du dashboard:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchDashboardData();\n  }, []);\n\n  const StatCard: React.FC<{\n    title: string;\n    value: number;\n    icon: React.ReactElement;\n    color: string;\n  }> = ({ title, value, icon, color }) => (\n    <Card>\n      <CardContent>\n        <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n          <Box>\n            <Typography color=\"textSecondary\" gutterBottom variant=\"body2\">\n              {title}\n            </Typography>\n            <Typography variant=\"h4\" component=\"h2\">\n              {loading ? '...' : value}\n            </Typography>\n          </Box>\n          <Avatar sx={{ bgcolor: color, width: 56, height: 56 }}>\n            {icon}\n          </Avatar>\n        </Box>\n      </CardContent>\n    </Card>\n  );\n\n  return (\n    <Box>\n      <Typography variant=\"h4\" gutterBottom>\n        Tableau de bord\n      </Typography>\n      \n      <Typography variant=\"body1\" color=\"text.secondary\" sx={{ mb: 3 }}>\n        Bienvenue {user?.full_name} ! Voici un aperçu de votre système de gestion des utilisateurs.\n      </Typography>\n\n      {/* Statistiques */}\n      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3, mb: 4 }}>\n        <Box sx={{ flex: '1 1 250px' }}>\n          <StatCard\n            title=\"Utilisateurs\"\n            value={stats.totalUsers}\n            icon={<PeopleIcon />}\n            color=\"#1976d2\"\n          />\n        </Box>\n\n        <Box sx={{ flex: '1 1 250px' }}>\n          <StatCard\n            title=\"Rôles\"\n            value={stats.totalRoles}\n            icon={<SecurityIcon />}\n            color=\"#388e3c\"\n          />\n        </Box>\n\n        <Box sx={{ flex: '1 1 250px' }}>\n          <StatCard\n            title=\"Permissions\"\n            value={stats.totalPermissions}\n            icon={<VpnKeyIcon />}\n            color=\"#f57c00\"\n          />\n        </Box>\n\n        <Box sx={{ flex: '1 1 250px' }}>\n          <StatCard\n            title=\"Activité\"\n            value={100}\n            icon={<TrendingUpIcon />}\n            color=\"#7b1fa2\"\n          />\n        </Box>\n      </Box>\n\n      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>\n        {/* Utilisateurs récents */}\n        <Box sx={{ flex: '1 1 400px' }}>\n          <Paper sx={{ p: 2 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Utilisateurs récents\n            </Typography>\n            \n            {loading ? (\n              <Typography>Chargement...</Typography>\n            ) : (\n              <List>\n                {stats.recentUsers.map((recentUser) => (\n                  <ListItem key={recentUser.id}>\n                    <ListItemAvatar>\n                      <Avatar>\n                        {recentUser.full_name.charAt(0).toUpperCase()}\n                      </Avatar>\n                    </ListItemAvatar>\n                    <ListItemText\n                      primary={recentUser.full_name}\n                      secondary={\n                        <Box>\n                          <Typography variant=\"body2\" color=\"text.secondary\">\n                            {recentUser.email}\n                          </Typography>\n                          <Box sx={{ mt: 0.5 }}>\n                            <Chip\n                              label={recentUser.role?.name || 'Aucun rôle'}\n                              size=\"small\"\n                              color={recentUser.is_active ? 'success' : 'default'}\n                              variant=\"outlined\"\n                            />\n                          </Box>\n                        </Box>\n                      }\n                    />\n                  </ListItem>\n                ))}\n              </List>\n            )}\n          </Paper>\n        </Box>\n\n        {/* Informations utilisateur */}\n        <Box sx={{ flex: '1 1 400px' }}>\n          <Paper sx={{ p: 2 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Votre profil\n            </Typography>\n            \n            <Box sx={{ mt: 2 }}>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Nom complet\n              </Typography>\n              <Typography variant=\"body1\" sx={{ mb: 2 }}>\n                {user?.full_name}\n              </Typography>\n\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Email\n              </Typography>\n              <Typography variant=\"body1\" sx={{ mb: 2 }}>\n                {user?.email}\n              </Typography>\n\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Rôle\n              </Typography>\n              <Chip\n                label={user?.role?.name || 'Aucun rôle'}\n                color=\"primary\"\n                sx={{ mb: 2 }}\n              />\n\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Statut\n              </Typography>\n              <Chip\n                label={user?.is_active ? 'Actif' : 'Inactif'}\n                color={user?.is_active ? 'success' : 'default'}\n                sx={{ mb: 2 }}\n              />\n\n              {user?.is_superuser && (\n                <>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Privilèges\n                  </Typography>\n                  <Chip\n                    label=\"Super Administrateur\"\n                    color=\"error\"\n                    sx={{ mb: 2 }}\n                  />\n                </>\n              )}\n\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Permissions ({user?.permissions.length || 0})\n              </Typography>\n              <Box sx={{ mt: 1 }}>\n                {user?.permissions.slice(0, 5).map((permission) => (\n                  <Chip\n                    key={permission}\n                    label={permission}\n                    size=\"small\"\n                    variant=\"outlined\"\n                    sx={{ mr: 0.5, mb: 0.5 }}\n                  />\n                ))}\n                {user && user.permissions.length > 5 && (\n                  <Chip\n                    label={`+${user.permissions.length - 5} autres`}\n                    size=\"small\"\n                    variant=\"outlined\"\n                    sx={{ mr: 0.5, mb: 0.5 }}\n                  />\n                )}\n              </Box>\n            </Box>\n          </Paper>\n        </Box>\n      </Box>\n    </Box>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EAEHC,KAAK,EACLC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,IAAI,QACC,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,EACxBC,MAAM,IAAIC,UAAU,EACpBC,UAAU,IAAIC,cAAc,QACvB,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,UAAU,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAUzC,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,UAAA;EAChC,MAAM;IAAEC;EAAK,CAAC,GAAGT,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACU,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAAiB;IACjDiC,UAAU,EAAE,CAAC;IACbC,UAAU,EAAE,CAAC;IACbC,gBAAgB,EAAE,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd,MAAMsC,kBAAkB,GAAG,MAAAA,CAAA,KAAY;MACrC,IAAI;QACFD,UAAU,CAAC,IAAI,CAAC;;QAEhB;QACA,MAAM,CAACE,aAAa,EAAEC,aAAa,EAAEC,mBAAmB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC5EtB,UAAU,CAACuB,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;QAAE;QAC3BvB,UAAU,CAACwB,WAAW,CAAC,CAAC,EACxBxB,UAAU,CAACyB,iBAAiB,CAAC,CAAC,CAC/B,CAAC;QAEFf,QAAQ,CAAC;UACPC,UAAU,EAAEO,aAAa,CAACQ,KAAK;UAC/Bd,UAAU,EAAEO,aAAa,CAACQ,MAAM;UAChCd,gBAAgB,EAAEO,mBAAmB,CAACO,MAAM;UAC5Cb,WAAW,EAAEI,aAAa,CAACU;QAC7B,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,qDAAqD,EAAEA,KAAK,CAAC;MAC7E,CAAC,SAAS;QACRb,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDC,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMc,QAKJ,GAAGA,CAAC;IAAEC,KAAK;IAAEC,KAAK;IAAEC,IAAI;IAAEC;EAAM,CAAC,kBACjCjC,OAAA,CAACnB,IAAI;IAAAqD,QAAA,eACHlC,OAAA,CAAClB,WAAW;MAAAoD,QAAA,eACVlC,OAAA,CAACtB,GAAG;QAACyD,OAAO,EAAC,MAAM;QAACC,UAAU,EAAC,QAAQ;QAACC,cAAc,EAAC,eAAe;QAAAH,QAAA,gBACpElC,OAAA,CAACtB,GAAG;UAAAwD,QAAA,gBACFlC,OAAA,CAACpB,UAAU;YAACqD,KAAK,EAAC,eAAe;YAACK,YAAY;YAACC,OAAO,EAAC,OAAO;YAAAL,QAAA,EAC3DJ;UAAK;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eACb3C,OAAA,CAACpB,UAAU;YAAC2D,OAAO,EAAC,IAAI;YAACK,SAAS,EAAC,IAAI;YAAAV,QAAA,EACpCrB,OAAO,GAAG,KAAK,GAAGkB;UAAK;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACN3C,OAAA,CAACjB,MAAM;UAAC8D,EAAE,EAAE;YAAEC,OAAO,EAAEb,KAAK;YAAEc,KAAK,EAAE,EAAE;YAAEC,MAAM,EAAE;UAAG,CAAE;UAAAd,QAAA,EACnDF;QAAI;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACP;EAED,oBACE3C,OAAA,CAACtB,GAAG;IAAAwD,QAAA,gBACFlC,OAAA,CAACpB,UAAU;MAAC2D,OAAO,EAAC,IAAI;MAACD,YAAY;MAAAJ,QAAA,EAAC;IAEtC;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEb3C,OAAA,CAACpB,UAAU;MAAC2D,OAAO,EAAC,OAAO;MAACN,KAAK,EAAC,gBAAgB;MAACY,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAf,QAAA,GAAC,YACtD,EAAC5B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4C,SAAS,EAAC,wEAC7B;IAAA;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAGb3C,OAAA,CAACtB,GAAG;MAACmE,EAAE,EAAE;QAAEV,OAAO,EAAE,MAAM;QAAEgB,QAAQ,EAAE,MAAM;QAAEC,GAAG,EAAE,CAAC;QAAEH,EAAE,EAAE;MAAE,CAAE;MAAAf,QAAA,gBAC5DlC,OAAA,CAACtB,GAAG;QAACmE,EAAE,EAAE;UAAEQ,IAAI,EAAE;QAAY,CAAE;QAAAnB,QAAA,eAC7BlC,OAAA,CAAC6B,QAAQ;UACPC,KAAK,EAAC,cAAc;UACpBC,KAAK,EAAExB,KAAK,CAACE,UAAW;UACxBuB,IAAI,eAAEhC,OAAA,CAACV,UAAU;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACrBV,KAAK,EAAC;QAAS;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN3C,OAAA,CAACtB,GAAG;QAACmE,EAAE,EAAE;UAAEQ,IAAI,EAAE;QAAY,CAAE;QAAAnB,QAAA,eAC7BlC,OAAA,CAAC6B,QAAQ;UACPC,KAAK,EAAC,UAAO;UACbC,KAAK,EAAExB,KAAK,CAACG,UAAW;UACxBsB,IAAI,eAAEhC,OAAA,CAACR,YAAY;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBV,KAAK,EAAC;QAAS;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN3C,OAAA,CAACtB,GAAG;QAACmE,EAAE,EAAE;UAAEQ,IAAI,EAAE;QAAY,CAAE;QAAAnB,QAAA,eAC7BlC,OAAA,CAAC6B,QAAQ;UACPC,KAAK,EAAC,aAAa;UACnBC,KAAK,EAAExB,KAAK,CAACI,gBAAiB;UAC9BqB,IAAI,eAAEhC,OAAA,CAACN,UAAU;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACrBV,KAAK,EAAC;QAAS;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN3C,OAAA,CAACtB,GAAG;QAACmE,EAAE,EAAE;UAAEQ,IAAI,EAAE;QAAY,CAAE;QAAAnB,QAAA,eAC7BlC,OAAA,CAAC6B,QAAQ;UACPC,KAAK,EAAC,aAAU;UAChBC,KAAK,EAAE,GAAI;UACXC,IAAI,eAAEhC,OAAA,CAACJ,cAAc;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBV,KAAK,EAAC;QAAS;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN3C,OAAA,CAACtB,GAAG;MAACmE,EAAE,EAAE;QAAEV,OAAO,EAAE,MAAM;QAAEgB,QAAQ,EAAE,MAAM;QAAEC,GAAG,EAAE;MAAE,CAAE;MAAAlB,QAAA,gBAErDlC,OAAA,CAACtB,GAAG;QAACmE,EAAE,EAAE;UAAEQ,IAAI,EAAE;QAAY,CAAE;QAAAnB,QAAA,eAC7BlC,OAAA,CAACrB,KAAK;UAACkE,EAAE,EAAE;YAAES,CAAC,EAAE;UAAE,CAAE;UAAApB,QAAA,gBAClBlC,OAAA,CAACpB,UAAU;YAAC2D,OAAO,EAAC,IAAI;YAACD,YAAY;YAAAJ,QAAA,EAAC;UAEtC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EAEZ9B,OAAO,gBACNb,OAAA,CAACpB,UAAU;YAAAsD,QAAA,EAAC;UAAa;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,gBAEtC3C,OAAA,CAAChB,IAAI;YAAAkD,QAAA,EACF3B,KAAK,CAACK,WAAW,CAAC2C,GAAG,CAAEC,UAAU;cAAA,IAAAC,gBAAA;cAAA,oBAChCzD,OAAA,CAACf,QAAQ;gBAAAiD,QAAA,gBACPlC,OAAA,CAACd,cAAc;kBAAAgD,QAAA,eACblC,OAAA,CAACjB,MAAM;oBAAAmD,QAAA,EACJsB,UAAU,CAACN,SAAS,CAACQ,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;kBAAC;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,eACjB3C,OAAA,CAACb,YAAY;kBACXyE,OAAO,EAAEJ,UAAU,CAACN,SAAU;kBAC9BW,SAAS,eACP7D,OAAA,CAACtB,GAAG;oBAAAwD,QAAA,gBACFlC,OAAA,CAACpB,UAAU;sBAAC2D,OAAO,EAAC,OAAO;sBAACN,KAAK,EAAC,gBAAgB;sBAAAC,QAAA,EAC/CsB,UAAU,CAACM;oBAAK;sBAAAtB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACP,CAAC,eACb3C,OAAA,CAACtB,GAAG;sBAACmE,EAAE,EAAE;wBAAEkB,EAAE,EAAE;sBAAI,CAAE;sBAAA7B,QAAA,eACnBlC,OAAA,CAACZ,IAAI;wBACH4E,KAAK,EAAE,EAAAP,gBAAA,GAAAD,UAAU,CAACS,IAAI,cAAAR,gBAAA,uBAAfA,gBAAA,CAAiBS,IAAI,KAAI,YAAa;wBAC7CC,IAAI,EAAC,OAAO;wBACZlC,KAAK,EAAEuB,UAAU,CAACY,SAAS,GAAG,SAAS,GAAG,SAAU;wBACpD7B,OAAO,EAAC;sBAAU;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA,GAvBWa,UAAU,CAACa,EAAE;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAwBlB,CAAC;YAAA,CACZ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGN3C,OAAA,CAACtB,GAAG;QAACmE,EAAE,EAAE;UAAEQ,IAAI,EAAE;QAAY,CAAE;QAAAnB,QAAA,eAC7BlC,OAAA,CAACrB,KAAK;UAACkE,EAAE,EAAE;YAAES,CAAC,EAAE;UAAE,CAAE;UAAApB,QAAA,gBAClBlC,OAAA,CAACpB,UAAU;YAAC2D,OAAO,EAAC,IAAI;YAACD,YAAY;YAAAJ,QAAA,EAAC;UAEtC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEb3C,OAAA,CAACtB,GAAG;YAACmE,EAAE,EAAE;cAAEkB,EAAE,EAAE;YAAE,CAAE;YAAA7B,QAAA,gBACjBlC,OAAA,CAACpB,UAAU;cAAC2D,OAAO,EAAC,OAAO;cAACN,KAAK,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAEnD;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb3C,OAAA,CAACpB,UAAU;cAAC2D,OAAO,EAAC,OAAO;cAACM,EAAE,EAAE;gBAAEI,EAAE,EAAE;cAAE,CAAE;cAAAf,QAAA,EACvC5B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4C;YAAS;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEb3C,OAAA,CAACpB,UAAU;cAAC2D,OAAO,EAAC,OAAO;cAACN,KAAK,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAEnD;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb3C,OAAA,CAACpB,UAAU;cAAC2D,OAAO,EAAC,OAAO;cAACM,EAAE,EAAE;gBAAEI,EAAE,EAAE;cAAE,CAAE;cAAAf,QAAA,EACvC5B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwD;YAAK;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEb3C,OAAA,CAACpB,UAAU;cAAC2D,OAAO,EAAC,OAAO;cAACN,KAAK,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAEnD;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb3C,OAAA,CAACZ,IAAI;cACH4E,KAAK,EAAE,CAAA1D,IAAI,aAAJA,IAAI,wBAAAD,UAAA,GAAJC,IAAI,CAAE2D,IAAI,cAAA5D,UAAA,uBAAVA,UAAA,CAAY6D,IAAI,KAAI,YAAa;cACxCjC,KAAK,EAAC,SAAS;cACfY,EAAE,EAAE;gBAAEI,EAAE,EAAE;cAAE;YAAE;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eAEF3C,OAAA,CAACpB,UAAU;cAAC2D,OAAO,EAAC,OAAO;cAACN,KAAK,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAEnD;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb3C,OAAA,CAACZ,IAAI;cACH4E,KAAK,EAAE1D,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8D,SAAS,GAAG,OAAO,GAAG,SAAU;cAC7CnC,KAAK,EAAE3B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8D,SAAS,GAAG,SAAS,GAAG,SAAU;cAC/CvB,EAAE,EAAE;gBAAEI,EAAE,EAAE;cAAE;YAAE;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,EAED,CAAArC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgE,YAAY,kBACjBtE,OAAA,CAAAE,SAAA;cAAAgC,QAAA,gBACElC,OAAA,CAACpB,UAAU;gBAAC2D,OAAO,EAAC,OAAO;gBAACN,KAAK,EAAC,gBAAgB;gBAAAC,QAAA,EAAC;cAEnD;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb3C,OAAA,CAACZ,IAAI;gBACH4E,KAAK,EAAC,sBAAsB;gBAC5B/B,KAAK,EAAC,OAAO;gBACbY,EAAE,EAAE;kBAAEI,EAAE,EAAE;gBAAE;cAAE;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA,eACF,CACH,eAED3C,OAAA,CAACpB,UAAU;cAAC2D,OAAO,EAAC,OAAO;cAACN,KAAK,EAAC,gBAAgB;cAAAC,QAAA,GAAC,eACpC,EAAC,CAAA5B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiE,WAAW,CAAC9C,MAAM,KAAI,CAAC,EAAC,GAC9C;YAAA;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb3C,OAAA,CAACtB,GAAG;cAACmE,EAAE,EAAE;gBAAEkB,EAAE,EAAE;cAAE,CAAE;cAAA7B,QAAA,GAChB5B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiE,WAAW,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACjB,GAAG,CAAEkB,UAAU,iBAC5CzE,OAAA,CAACZ,IAAI;gBAEH4E,KAAK,EAAES,UAAW;gBAClBN,IAAI,EAAC,OAAO;gBACZ5B,OAAO,EAAC,UAAU;gBAClBM,EAAE,EAAE;kBAAE6B,EAAE,EAAE,GAAG;kBAAEzB,EAAE,EAAE;gBAAI;cAAE,GAJpBwB,UAAU;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKhB,CACF,CAAC,EACDrC,IAAI,IAAIA,IAAI,CAACiE,WAAW,CAAC9C,MAAM,GAAG,CAAC,iBAClCzB,OAAA,CAACZ,IAAI;gBACH4E,KAAK,EAAE,IAAI1D,IAAI,CAACiE,WAAW,CAAC9C,MAAM,GAAG,CAAC,SAAU;gBAChD0C,IAAI,EAAC,OAAO;gBACZ5B,OAAO,EAAC,UAAU;gBAClBM,EAAE,EAAE;kBAAE6B,EAAE,EAAE,GAAG;kBAAEzB,EAAE,EAAE;gBAAI;cAAE;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvC,EAAA,CA7OID,SAAmB;EAAA,QACNN,OAAO;AAAA;AAAA8E,EAAA,GADpBxE,SAAmB;AA+OzB,eAAeA,SAAS;AAAC,IAAAwE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}