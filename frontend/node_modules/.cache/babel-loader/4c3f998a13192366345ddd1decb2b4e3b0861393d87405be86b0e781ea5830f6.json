{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\", \"csvOptions\", \"printOptions\", \"excelOptions\", \"showQuickFilter\", \"quickFilterProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { GridToolbarContainer } from \"../containers/GridToolbarContainer.js\";\nimport { GridToolbarColumnsButton } from \"./GridToolbarColumnsButton.js\";\nimport { GridToolbarDensitySelector } from \"./GridToolbarDensitySelector.js\";\nimport { GridToolbarFilterButton } from \"./GridToolbarFilterButton.js\";\nimport { GridToolbarExport } from \"./GridToolbarExport.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { GridToolbarQuickFilter } from \"./GridToolbarQuickFilter.js\";\nimport { GridToolbarLabel } from \"../toolbarV8/GridToolbar.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\n/**\n * @deprecated Use the `showToolbar` prop to show the default toolbar instead. This component will be removed in a future major release.\n */\nconst GridToolbar = forwardRef(function GridToolbar(props, ref) {\n  // TODO v7: think about where export option should be passed.\n  // from slotProps={{ toolbarExport: { ...exportOption } }} seems to be more appropriate\n  const _ref = props,\n    {\n      csvOptions,\n      printOptions,\n      excelOptions,\n      showQuickFilter = true,\n      quickFilterProps = {}\n    } = _ref,\n    other = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const rootProps = useGridRootProps();\n  if (rootProps.disableColumnFilter && rootProps.disableColumnSelector && rootProps.disableDensitySelector && !showQuickFilter) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxs(GridToolbarContainer, _extends({}, other, {\n    ref: ref,\n    children: [rootProps.label && /*#__PURE__*/_jsx(GridToolbarLabel, {\n      children: rootProps.label\n    }), /*#__PURE__*/_jsx(GridToolbarColumnsButton, {}), /*#__PURE__*/_jsx(GridToolbarFilterButton, {}), /*#__PURE__*/_jsx(GridToolbarDensitySelector, {}), /*#__PURE__*/_jsx(GridToolbarExport, {\n      csvOptions: csvOptions,\n      printOptions: printOptions\n      // @ts-ignore\n      ,\n\n      excelOptions: excelOptions\n    }), /*#__PURE__*/_jsx(\"div\", {\n      style: {\n        flex: 1\n      }\n    }), showQuickFilter && /*#__PURE__*/_jsx(GridToolbarQuickFilter, _extends({}, quickFilterProps))]\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridToolbar.displayName = \"GridToolbar\";\nprocess.env.NODE_ENV !== \"production\" ? GridToolbar.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  csvOptions: PropTypes.object,\n  printOptions: PropTypes.object,\n  /**\n   * Props passed to the quick filter component.\n   */\n  quickFilterProps: PropTypes.shape({\n    className: PropTypes.string,\n    debounceMs: PropTypes.number,\n    quickFilterFormatter: PropTypes.func,\n    quickFilterParser: PropTypes.func,\n    slotProps: PropTypes.object\n  }),\n  /**\n   * Show the quick filter component.\n   * @default true\n   */\n  showQuickFilter: PropTypes.bool,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport { GridToolbar };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}