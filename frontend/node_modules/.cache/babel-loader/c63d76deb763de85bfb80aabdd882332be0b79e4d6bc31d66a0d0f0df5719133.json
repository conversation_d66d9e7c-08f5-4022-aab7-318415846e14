{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from '@mui/system';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['columnHeaders']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst GridColumnHeadersRoot = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'ColumnHeaders'\n})({\n  display: 'flex',\n  flexDirection: 'column',\n  borderTopLeftRadius: 'var(--unstable_DataGrid-radius)',\n  borderTopRightRadius: 'var(--unstable_DataGrid-radius)'\n});\nexport const GridBaseColumnHeaders = forwardRef(function GridColumnHeaders(props, ref) {\n  const {\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const rootProps = useGridRootProps();\n  const classes = useUtilityClasses(rootProps);\n  return /*#__PURE__*/_jsx(GridColumnHeadersRoot, _extends({\n    className: clsx(classes.root, className),\n    ownerState: rootProps\n  }, other, {\n    role: \"presentation\",\n    ref: ref\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridBaseColumnHeaders.displayName = \"GridBaseColumnHeaders\";", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}