{"ast": null, "code": "/**\n * Loads all stylesheets from the given root element into the document.\n * @returns an array of promises that resolve when each stylesheet is loaded\n * @param document Document to load stylesheets into\n * @param root Document or ShadowRoot to load stylesheets from\n */\nexport function loadStyleSheets(document, root) {\n  const stylesheetLoadPromises = [];\n  const headStyleElements = root.querySelectorAll(\"style, link[rel='stylesheet']\");\n  for (let i = 0; i < headStyleElements.length; i += 1) {\n    const node = headStyleElements[i];\n    if (node.tagName === 'STYLE') {\n      const newHeadStyleElements = document.createElement(node.tagName);\n      const sheet = node.sheet;\n      if (sheet) {\n        let styleCSS = '';\n        for (let j = 0; j < sheet.cssRules.length; j += 1) {\n          if (typeof sheet.cssRules[j].cssText === 'string') {\n            styleCSS += `${sheet.cssRules[j].cssText}\\r\\n`;\n          }\n        }\n        newHeadStyleElements.appendChild(document.createTextNode(styleCSS));\n        document.head.appendChild(newHeadStyleElements);\n      }\n    } else if (node.getAttribute('href')) {\n      // If `href` tag is empty, avoid loading these links\n\n      const newHeadStyleElements = document.createElement(node.tagName);\n      for (let j = 0; j < node.attributes.length; j += 1) {\n        const attr = node.attributes[j];\n        if (attr) {\n          newHeadStyleElements.setAttribute(attr.nodeName, attr.nodeValue || '');\n        }\n      }\n      stylesheetLoadPromises.push(new Promise(resolve => {\n        newHeadStyleElements.addEventListener('load', () => resolve());\n      }));\n      document.head.appendChild(newHeadStyleElements);\n    }\n  }\n  return stylesheetLoadPromises;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}