{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"hideMenu\", \"colDef\", \"id\", \"labelledby\", \"className\", \"children\", \"open\"];\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport * as React from 'react';\nimport { styled } from '@mui/material/styles';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { isHideMenuKey } from \"../../../utils/keyboardUtils.js\";\nimport { NotRendered } from \"../../../utils/assert.js\";\nimport { gridClasses } from \"../../../constants/gridClasses.js\";\nimport { useGridRootProps } from \"../../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst StyledMenuList = styled(NotRendered)(() => ({\n  minWidth: 248\n}));\nfunction handleMenuScrollCapture(event) {\n  if (!event.currentTarget.contains(event.target)) {\n    return;\n  }\n  event.stopPropagation();\n}\nconst GridColumnMenuContainer = forwardRef(function GridColumnMenuContainer(props, ref) {\n  const {\n      hideMenu,\n      id,\n      labelledby,\n      className,\n      children,\n      open\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const rootProps = useGridRootProps();\n  const handleListKeyDown = React.useCallback(event => {\n    if (event.key === 'Tab') {\n      event.preventDefault();\n    }\n    if (isHideMenuKey(event.key)) {\n      hideMenu(event);\n    }\n  }, [hideMenu]);\n  return /*#__PURE__*/_jsx(StyledMenuList, _extends({\n    as: rootProps.slots.baseMenuList,\n    id: id,\n    className: clsx(gridClasses.menuList, className),\n    \"aria-labelledby\": labelledby,\n    onKeyDown: handleListKeyDown,\n    onWheel: handleMenuScrollCapture,\n    onTouchMove: handleMenuScrollCapture,\n    autoFocus: open\n  }, other, {\n    ref: ref,\n    children: children\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridColumnMenuContainer.displayName = \"GridColumnMenuContainer\";\nprocess.env.NODE_ENV !== \"production\" ? GridColumnMenuContainer.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  colDef: PropTypes.object.isRequired,\n  hideMenu: PropTypes.func.isRequired,\n  id: PropTypes.string,\n  labelledby: PropTypes.string,\n  open: PropTypes.bool.isRequired\n} : void 0;\nexport { GridColumnMenuContainer };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}