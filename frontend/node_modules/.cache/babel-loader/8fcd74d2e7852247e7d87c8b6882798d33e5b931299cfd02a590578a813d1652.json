{"ast": null, "code": "import * as React from 'react';\nimport { GridApiContext } from \"../components/GridApiContext.js\";\nimport { GridPrivateApiContext } from \"../hooks/utils/useGridPrivateApiContext.js\";\nimport { GridRootPropsContext } from \"./GridRootPropsContext.js\";\nimport { GridConfigurationContext } from \"../components/GridConfigurationContext.js\";\nimport { GridPanelContextProvider } from \"../components/panel/GridPanelContext.js\";\nimport { GridCSSVariablesContext } from \"../utils/css/context.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function GridContextProvider({\n  privateApiRef,\n  configuration,\n  props,\n  children\n}) {\n  const apiRef = React.useRef(privateApiRef.current.getPublicApi());\n  return /*#__PURE__*/_jsx(GridConfigurationContext.Provider, {\n    value: configuration,\n    children: /*#__PURE__*/_jsx(GridRootPropsContext.Provider, {\n      value: props,\n      children: /*#__PURE__*/_jsx(GridPrivateApiContext.Provider, {\n        value: privateApiRef,\n        children: /*#__PURE__*/_jsx(GridApiContext.Provider, {\n          value: apiRef,\n          children: /*#__PURE__*/_jsx(GridPanelContextProvider, {\n            children: /*#__PURE__*/_jsx(GridCSSVariablesContext, {\n              children: children\n            })\n          })\n        })\n      })\n    })\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}