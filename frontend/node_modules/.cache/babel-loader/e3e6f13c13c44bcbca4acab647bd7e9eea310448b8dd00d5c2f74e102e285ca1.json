{"ast": null, "code": "import * as React from 'react';\nexport const QuickFilterContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== \"production\") QuickFilterContext.displayName = \"QuickFilterContext\";\nexport function useQuickFilterContext() {\n  const context = React.useContext(QuickFilterContext);\n  if (context === undefined) {\n    throw new Error('MUI X: Missing context. Quick Filter subcomponents must be placed within a <QuickFilter /> component.');\n  }\n  return context;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}