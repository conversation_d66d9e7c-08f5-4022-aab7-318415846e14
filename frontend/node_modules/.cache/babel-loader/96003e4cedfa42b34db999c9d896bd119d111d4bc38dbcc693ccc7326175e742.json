{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\", \"visibleColumns\", \"sortColumnLookup\", \"filterColumnLookup\", \"columnHeaderTabIndexState\", \"columnGroupHeaderTabIndexState\", \"columnHeaderFocus\", \"columnGroupHeaderFocus\", \"headerGroupingMaxDepth\", \"columnMenuState\", \"columnVisibility\", \"columnGroupsHeaderStructure\", \"hasOtherElementInTabSequence\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { fastMemo } from '@mui/x-internals/fastMemo';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { useGridColumnHeaders } from \"../hooks/features/columnHeaders/useGridColumnHeaders.js\";\nimport { GridBaseColumnHeaders } from \"./columnHeaders/GridBaseColumnHeaders.js\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst GridColumnHeaders = forwardRef(function GridColumnHeaders(props, ref) {\n  const {\n      visibleColumns,\n      sortColumnLookup,\n      filterColumnLookup,\n      columnHeaderTabIndexState,\n      columnGroupHeaderTabIndexState,\n      columnHeaderFocus,\n      columnGroupHeaderFocus,\n      headerGroupingMaxDepth,\n      columnMenuState,\n      columnVisibility,\n      columnGroupsHeaderStructure,\n      hasOtherElementInTabSequence\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    getInnerProps,\n    getColumnHeadersRow,\n    getColumnGroupHeadersRows\n  } = useGridColumnHeaders({\n    visibleColumns,\n    sortColumnLookup,\n    filterColumnLookup,\n    columnHeaderTabIndexState,\n    columnGroupHeaderTabIndexState,\n    columnHeaderFocus,\n    columnGroupHeaderFocus,\n    headerGroupingMaxDepth,\n    columnMenuState,\n    columnVisibility,\n    columnGroupsHeaderStructure,\n    hasOtherElementInTabSequence\n  });\n  return /*#__PURE__*/_jsxs(GridBaseColumnHeaders, _extends({}, other, getInnerProps(), {\n    ref: ref,\n    children: [getColumnGroupHeadersRows(), getColumnHeadersRow()]\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridColumnHeaders.displayName = \"GridColumnHeaders\";\nprocess.env.NODE_ENV !== \"production\" ? GridColumnHeaders.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  columnGroupHeaderFocus: PropTypes.shape({\n    depth: PropTypes.number.isRequired,\n    field: PropTypes.string.isRequired\n  }),\n  columnGroupHeaderTabIndexState: PropTypes.shape({\n    depth: PropTypes.number.isRequired,\n    field: PropTypes.string.isRequired\n  }),\n  columnGroupsHeaderStructure: PropTypes.arrayOf(PropTypes.arrayOf(PropTypes.shape({\n    columnFields: PropTypes.arrayOf(PropTypes.string).isRequired,\n    groupId: PropTypes.string\n  }))).isRequired,\n  columnHeaderFocus: PropTypes.shape({\n    field: PropTypes.string.isRequired\n  }),\n  columnHeaderTabIndexState: PropTypes.shape({\n    field: PropTypes.string.isRequired\n  }),\n  columnMenuState: PropTypes.shape({\n    field: PropTypes.string,\n    open: PropTypes.bool.isRequired\n  }).isRequired,\n  columnVisibility: PropTypes.object.isRequired,\n  filterColumnLookup: PropTypes.object.isRequired,\n  hasOtherElementInTabSequence: PropTypes.bool.isRequired,\n  headerGroupingMaxDepth: PropTypes.number.isRequired,\n  sortColumnLookup: PropTypes.object.isRequired,\n  visibleColumns: PropTypes.arrayOf(PropTypes.object).isRequired\n} : void 0;\nconst MemoizedGridColumnHeaders = fastMemo(GridColumnHeaders);\nexport { MemoizedGridColumnHeaders as GridColumnHeaders };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}