{"ast": null, "code": "import { warnOnce } from '@mui/x-internals/warning';\nimport { GRID_CHECKBOX_SELECTION_COL_DEF } from \"../../../../colDef/index.js\";\nfunction sanitizeCellValue(value, csvOptions) {\n  if (value === null || value === undefined) {\n    return '';\n  }\n  const valueStr = typeof value === 'string' ? value : `${value}`;\n  if (csvOptions.shouldAppendQuotes || csvOptions.escapeFormulas) {\n    const escapedValue = valueStr.replace(/\"/g, '\"\"');\n    if (csvOptions.escapeFormulas) {\n      // See https://owasp.org/www-community/attacks/CSV_Injection\n      if (['=', '+', '-', '@', '\\t', '\\r'].includes(escapedValue[0])) {\n        return `\"'${escapedValue}\"`;\n      }\n    }\n    // Make sure value containing delimiter or line break won't be split into multiple cells\n    if ([csvOptions.delimiter, '\\n', '\\r', '\"'].some(delimiter => valueStr.includes(delimiter))) {\n      return `\"${escapedValue}\"`;\n    }\n    return escapedValue;\n  }\n  return valueStr;\n}\nexport const serializeCellValue = (cellParams, options) => {\n  const {\n    csvOptions,\n    ignoreValueFormatter\n  } = options;\n  let value;\n  if (ignoreValueFormatter) {\n    const columnType = cellParams.colDef.type;\n    if (columnType === 'number') {\n      value = String(cellParams.value);\n    } else if (columnType === 'date' || columnType === 'dateTime') {\n      value = cellParams.value?.toISOString();\n    } else if (typeof cellParams.value?.toString === 'function') {\n      value = cellParams.value.toString();\n    } else {\n      value = cellParams.value;\n    }\n  } else {\n    value = cellParams.formattedValue;\n  }\n  return sanitizeCellValue(value, csvOptions);\n};\nclass CSVRow {\n  constructor(options) {\n    this.options = void 0;\n    this.rowString = '';\n    this.isEmpty = true;\n    this.options = options;\n  }\n  addValue(value) {\n    if (!this.isEmpty) {\n      this.rowString += this.options.csvOptions.delimiter;\n    }\n    if (typeof this.options.sanitizeCellValue === 'function') {\n      this.rowString += this.options.sanitizeCellValue(value, this.options.csvOptions);\n    } else {\n      this.rowString += value;\n    }\n    this.isEmpty = false;\n  }\n  getRowString() {\n    return this.rowString;\n  }\n}\nconst serializeRow = ({\n  id,\n  columns,\n  getCellParams,\n  csvOptions,\n  ignoreValueFormatter\n}) => {\n  const row = new CSVRow({\n    csvOptions\n  });\n  columns.forEach(column => {\n    const cellParams = getCellParams(id, column.field);\n    if (process.env.NODE_ENV !== 'production') {\n      if (String(cellParams.formattedValue) === '[object Object]') {\n        warnOnce(['MUI X: When the value of a field is an object or a `renderCell` is provided, the CSV export might not display the value correctly.', 'You can provide a `valueFormatter` with a string representation to be used.']);\n      }\n    }\n    row.addValue(serializeCellValue(cellParams, {\n      ignoreValueFormatter,\n      csvOptions\n    }));\n  });\n  return row.getRowString();\n};\nexport function buildCSV(options) {\n  const {\n    columns,\n    rowIds,\n    csvOptions,\n    ignoreValueFormatter,\n    apiRef\n  } = options;\n  const CSVBody = rowIds.reduce((acc, id) => `${acc}${serializeRow({\n    id,\n    columns,\n    getCellParams: apiRef.current.getCellParams,\n    ignoreValueFormatter,\n    csvOptions\n  })}\\r\\n`, '').trim();\n  if (!csvOptions.includeHeaders) {\n    return CSVBody;\n  }\n  const filteredColumns = columns.filter(column => column.field !== GRID_CHECKBOX_SELECTION_COL_DEF.field);\n  const headerRows = [];\n  if (csvOptions.includeColumnGroupsHeaders) {\n    const columnGroupLookup = apiRef.current.getAllGroupDetails();\n    let maxColumnGroupsDepth = 0;\n    const columnGroupPathsLookup = filteredColumns.reduce((acc, column) => {\n      const columnGroupPath = apiRef.current.getColumnGroupPath(column.field);\n      acc[column.field] = columnGroupPath;\n      maxColumnGroupsDepth = Math.max(maxColumnGroupsDepth, columnGroupPath.length);\n      return acc;\n    }, {});\n    for (let i = 0; i < maxColumnGroupsDepth; i += 1) {\n      const headerGroupRow = new CSVRow({\n        csvOptions,\n        sanitizeCellValue\n      });\n      headerRows.push(headerGroupRow);\n      filteredColumns.forEach(column => {\n        const columnGroupId = (columnGroupPathsLookup[column.field] || [])[i];\n        const columnGroup = columnGroupLookup[columnGroupId];\n        headerGroupRow.addValue(columnGroup ? columnGroup.headerName || columnGroup.groupId : '');\n      });\n    }\n  }\n  const mainHeaderRow = new CSVRow({\n    csvOptions,\n    sanitizeCellValue\n  });\n  filteredColumns.forEach(column => {\n    mainHeaderRow.addValue(column.headerName || column.field);\n  });\n  headerRows.push(mainHeaderRow);\n  const CSVHead = `${headerRows.map(row => row.getRowString()).join('\\r\\n')}\\r\\n`;\n  return `${CSVHead}${CSVBody}`.trim();\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}