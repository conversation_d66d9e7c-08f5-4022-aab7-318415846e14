{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"render\", \"className\", \"parser\", \"formatter\", \"debounceMs\", \"defaultExpanded\", \"expanded\", \"onExpandedChange\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport debounce from '@mui/utils/debounce';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport useId from '@mui/utils/useId';\nimport { isDeepEqual } from '@mui/x-internals/isDeepEqual';\nimport { useComponentRenderer } from '@mui/x-internals/useComponentRenderer';\nimport { QuickFilterContext } from \"./QuickFilterContext.js\";\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { useGridSelector } from \"../../hooks/utils/useGridSelector.js\";\nimport { gridQuickFilterValuesSelector } from \"../../hooks/features/filter/index.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DEFAULT_PARSER = searchText => searchText.split(' ').filter(word => word !== '');\nconst DEFAULT_FORMATTER = values => values.join(' ');\n\n/**\n * The top level Quick Filter component that provides context to child components.\n * It renders a `<div />` element.\n *\n * Demos:\n *\n * - [Quick Filter](https://mui.com/x/react-data-grid/components/quick-filter/)\n *\n * API:\n *\n * - [QuickFilter API](https://mui.com/x/api/data-grid/quick-filter/)\n */\nfunction QuickFilter(props) {\n  const rootProps = useGridRootProps();\n  const {\n      render,\n      className,\n      parser = DEFAULT_PARSER,\n      formatter = DEFAULT_FORMATTER,\n      debounceMs = rootProps.filterDebounceMs,\n      defaultExpanded,\n      expanded,\n      onExpandedChange\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const apiRef = useGridApiContext();\n  const controlRef = React.useRef(null);\n  const triggerRef = React.useRef(null);\n  const quickFilterValues = useGridSelector(apiRef, gridQuickFilterValuesSelector);\n  const [value, setValue] = React.useState(formatter(quickFilterValues ?? []));\n  const [internalExpanded, setInternalExpanded] = React.useState(defaultExpanded ?? value.length > 0);\n  const expandedValue = expanded ?? internalExpanded;\n  const state = React.useMemo(() => ({\n    value,\n    expanded: expandedValue\n  }), [value, expandedValue]);\n  const resolvedClassName = typeof className === 'function' ? className(state) : className;\n  const ref = React.useRef(null);\n  const controlId = useId();\n  const handleExpandedChange = React.useCallback(newExpanded => {\n    if (onExpandedChange) {\n      onExpandedChange(newExpanded);\n    }\n    if (expanded === undefined) {\n      setInternalExpanded(newExpanded);\n    }\n  }, [onExpandedChange, expanded]);\n  const prevQuickFilterValuesRef = React.useRef(quickFilterValues);\n  React.useEffect(() => {\n    if (!isDeepEqual(prevQuickFilterValuesRef.current, quickFilterValues)) {\n      // The model of quick filter value has been updated\n      prevQuickFilterValuesRef.current = quickFilterValues;\n\n      // Update the input value if needed to match the new model\n      setValue(prevSearchValue => isDeepEqual(parser(prevSearchValue), quickFilterValues) ? prevSearchValue : formatter(quickFilterValues ?? []));\n    }\n  }, [quickFilterValues, formatter, parser]);\n  const isFirstRender = React.useRef(true);\n  const previousExpandedValue = React.useRef(expandedValue);\n  React.useEffect(() => {\n    if (isFirstRender.current) {\n      isFirstRender.current = false;\n      return;\n    }\n\n    // Ensure the expanded state has actually changed before focusing\n    if (previousExpandedValue.current !== expandedValue) {\n      if (expandedValue) {\n        // Ensures the focus does not interupt CSS transitions and animations on the control\n        requestAnimationFrame(() => {\n          controlRef.current?.focus({\n            preventScroll: true\n          });\n        });\n      } else {\n        triggerRef.current?.focus({\n          preventScroll: true\n        });\n      }\n      previousExpandedValue.current = expandedValue;\n    }\n  }, [expandedValue]);\n  const setQuickFilterValueDebounced = React.useMemo(() => debounce(newValue => {\n    const newQuickFilterValues = parser(newValue);\n    prevQuickFilterValuesRef.current = newQuickFilterValues;\n    apiRef.current.setQuickFilterValues(newQuickFilterValues);\n  }, debounceMs), [apiRef, debounceMs, parser]);\n  React.useEffect(() => setQuickFilterValueDebounced.clear, [setQuickFilterValueDebounced]);\n  const handleValueChange = React.useCallback(event => {\n    const newValue = event.target.value;\n    setValue(newValue);\n    setQuickFilterValueDebounced(newValue);\n  }, [setQuickFilterValueDebounced]);\n  const handleClearValue = React.useCallback(() => {\n    setValue('');\n    apiRef.current.setQuickFilterValues([]);\n    controlRef.current?.focus();\n  }, [apiRef, controlRef]);\n  const contextValue = React.useMemo(() => ({\n    controlRef,\n    triggerRef,\n    state,\n    controlId,\n    clearValue: handleClearValue,\n    onValueChange: handleValueChange,\n    onExpandedChange: handleExpandedChange\n  }), [controlId, state, handleValueChange, handleClearValue, handleExpandedChange]);\n  useEnhancedEffect(() => {\n    if (ref.current && triggerRef.current) {\n      ref.current.style.setProperty('--trigger-width', `${triggerRef.current?.offsetWidth}px`);\n    }\n  }, []);\n  const element = useComponentRenderer('div', render, _extends({\n    className: resolvedClassName\n  }, other, {\n    ref\n  }), state);\n  return /*#__PURE__*/_jsx(QuickFilterContext.Provider, {\n    value: contextValue,\n    children: element\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? QuickFilter.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  className: PropTypes.oneOfType([PropTypes.func, PropTypes.string]),\n  /**\n   * The debounce time in milliseconds.\n   * @default 150\n   */\n  debounceMs: PropTypes.number,\n  /**\n   * The default expanded state of the quick filter control.\n   * @default false\n   */\n  defaultExpanded: PropTypes.bool,\n  /**\n   * The expanded state of the quick filter control.\n   */\n  expanded: PropTypes.bool,\n  /**\n   * Function responsible for formatting values of quick filter in a string when the model is modified\n   * @param {any[]} values The new values passed to the quick filter model\n   * @returns {string} The string to display in the text field\n   * @default (values: string[]) => values.join(' ')\n   */\n  formatter: PropTypes.func,\n  /**\n   * Callback function that is called when the quick filter input is expanded or collapsed.\n   * @param {boolean} expanded The new expanded state of the quick filter control\n   */\n  onExpandedChange: PropTypes.func,\n  /**\n   * Function responsible for parsing text input in an array of independent values for quick filtering.\n   * @param {string} input The value entered by the user\n   * @returns {any[]} The array of value on which quick filter is applied\n   * @default (searchText: string) => searchText.split(' ').filter((word) => word !== '')\n   */\n  parser: PropTypes.func,\n  /**\n   * A function to customize rendering of the component.\n   */\n  render: PropTypes.oneOfType([PropTypes.element, PropTypes.func])\n} : void 0;\nexport { QuickFilter };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}