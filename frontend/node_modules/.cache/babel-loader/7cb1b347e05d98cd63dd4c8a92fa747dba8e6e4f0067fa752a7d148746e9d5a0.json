{"ast": null, "code": "import * as React from 'react';\nimport { getGridCellElement, getGridColumnHeaderElement, getGridRowElement } from \"../../../utils/domUtils.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { gridFocusCellSelector, gridTabIndexCellSelector } from \"../focus/gridFocusStateSelector.js\";\nimport { gridListColumnSelector } from \"../listView/gridListViewSelectors.js\";\nimport { gridRowNodeSelector } from \"./gridRowsSelector.js\";\nimport { getRowValue as getRowValueFn } from \"./gridRowsUtils.js\";\nexport class MissingRowIdError extends Error {}\n\n/**\n * @requires useGridColumns (method)\n * @requires useGridRows (method)\n * @requires useGridFocus (state)\n * @requires useGridEditing (method)\n * TODO: Impossible priority - useGridEditing also needs to be after useGridParamsApi\n * TODO: Impossible priority - useGridFocus also needs to be after useGridParamsApi\n */\nexport function useGridParamsApi(apiRef, props) {\n  const getColumnHeaderParams = React.useCallback(field => ({\n    field,\n    colDef: apiRef.current.getColumn(field)\n  }), [apiRef]);\n  const getRowParams = React.useCallback(id => {\n    const row = apiRef.current.getRow(id);\n    if (!row) {\n      throw new MissingRowIdError(`No row with id #${id} found`);\n    }\n    const params = {\n      id,\n      columns: apiRef.current.getAllColumns(),\n      row\n    };\n    return params;\n  }, [apiRef]);\n  const getCellParamsForRow = React.useCallback((id, field, row, {\n    cellMode,\n    colDef,\n    hasFocus,\n    rowNode,\n    tabIndex\n  }) => {\n    const rawValue = row[field];\n    const value = colDef?.valueGetter ? colDef.valueGetter(rawValue, row, colDef, apiRef) : rawValue;\n    const params = {\n      id,\n      field,\n      row,\n      rowNode,\n      colDef,\n      cellMode,\n      hasFocus,\n      tabIndex,\n      value,\n      formattedValue: value,\n      isEditable: false,\n      api: apiRef.current\n    };\n    if (colDef && colDef.valueFormatter) {\n      params.formattedValue = colDef.valueFormatter(value, row, colDef, apiRef);\n    }\n    params.isEditable = colDef && apiRef.current.isCellEditable(params);\n    return params;\n  }, [apiRef]);\n  const getCellParams = React.useCallback((id, field) => {\n    const row = apiRef.current.getRow(id);\n    const rowNode = gridRowNodeSelector(apiRef, id);\n    if (!row || !rowNode) {\n      throw new MissingRowIdError(`No row with id #${id} found`);\n    }\n    const cellFocus = gridFocusCellSelector(apiRef);\n    const cellTabIndex = gridTabIndexCellSelector(apiRef);\n    const cellMode = apiRef.current.getCellMode(id, field);\n    return apiRef.current.getCellParamsForRow(id, field, row, {\n      colDef: props.listView && props.listViewColumn?.field === field ? gridListColumnSelector(apiRef) : apiRef.current.getColumn(field),\n      rowNode,\n      hasFocus: cellFocus !== null && cellFocus.field === field && cellFocus.id === id,\n      tabIndex: cellTabIndex && cellTabIndex.field === field && cellTabIndex.id === id ? 0 : -1,\n      cellMode\n    });\n  }, [apiRef, props.listView, props.listViewColumn?.field]);\n  const getCellValue = React.useCallback((id, field) => {\n    const colDef = apiRef.current.getColumn(field);\n    const row = apiRef.current.getRow(id);\n    if (!row) {\n      throw new MissingRowIdError(`No row with id #${id} found`);\n    }\n    if (!colDef || !colDef.valueGetter) {\n      return row[field];\n    }\n    return colDef.valueGetter(row[colDef.field], row, colDef, apiRef);\n  }, [apiRef]);\n  const getRowValue = React.useCallback((row, colDef) => getRowValueFn(row, colDef, apiRef), [apiRef]);\n  const getRowFormattedValue = React.useCallback((row, colDef) => {\n    const value = getRowValue(row, colDef);\n    if (!colDef || !colDef.valueFormatter) {\n      return value;\n    }\n    return colDef.valueFormatter(value, row, colDef, apiRef);\n  }, [apiRef, getRowValue]);\n  const getColumnHeaderElement = React.useCallback(field => {\n    if (!apiRef.current.rootElementRef.current) {\n      return null;\n    }\n    return getGridColumnHeaderElement(apiRef.current.rootElementRef.current, field);\n  }, [apiRef]);\n  const getRowElement = React.useCallback(id => {\n    if (!apiRef.current.rootElementRef.current) {\n      return null;\n    }\n    return getGridRowElement(apiRef.current.rootElementRef.current, id);\n  }, [apiRef]);\n  const getCellElement = React.useCallback((id, field) => {\n    if (!apiRef.current.rootElementRef.current) {\n      return null;\n    }\n    return getGridCellElement(apiRef.current.rootElementRef.current, {\n      id,\n      field\n    });\n  }, [apiRef]);\n  const paramsApi = {\n    getCellValue,\n    getCellParams,\n    getCellElement,\n    getRowValue,\n    getRowFormattedValue,\n    getRowParams,\n    getRowElement,\n    getColumnHeaderParams,\n    getColumnHeaderElement\n  };\n  const paramsPrivateApi = {\n    getCellParamsForRow\n  };\n  useGridApiMethod(apiRef, paramsApi, 'public');\n  useGridApiMethod(apiRef, paramsPrivateApi, 'private');\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}