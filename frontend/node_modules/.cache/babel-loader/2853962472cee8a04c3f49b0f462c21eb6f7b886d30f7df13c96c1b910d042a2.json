{"ast": null, "code": "export const GRID_DEFAULT_LOCALE_TEXT = {\n  // Root\n  noRowsLabel: 'No rows',\n  noResultsOverlayLabel: 'No results found.',\n  noColumnsOverlayLabel: 'No columns',\n  noColumnsOverlayManageColumns: 'Manage columns',\n  emptyPivotOverlayLabel: 'Add fields to rows, columns, and values to create a pivot table',\n  // Density selector toolbar button text\n  toolbarDensity: 'Density',\n  toolbarDensityLabel: 'Density',\n  toolbarDensityCompact: 'Compact',\n  toolbarDensityStandard: 'Standard',\n  toolbarDensityComfortable: 'Comfortable',\n  // Columns selector toolbar button text\n  toolbarColumns: 'Columns',\n  toolbarColumnsLabel: 'Select columns',\n  // Filters toolbar button text\n  toolbarFilters: 'Filters',\n  toolbarFiltersLabel: 'Show filters',\n  toolbarFiltersTooltipHide: 'Hide filters',\n  toolbarFiltersTooltipShow: 'Show filters',\n  toolbarFiltersTooltipActive: count => count !== 1 ? `${count} active filters` : `${count} active filter`,\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: 'Search…',\n  toolbarQuickFilterLabel: 'Search',\n  toolbarQuickFilterDeleteIconLabel: 'Clear',\n  // Export selector toolbar button text\n  toolbarExport: 'Export',\n  toolbarExportLabel: 'Export',\n  toolbarExportCSV: 'Download as CSV',\n  toolbarExportPrint: 'Print',\n  toolbarExportExcel: 'Download as Excel',\n  // Toolbar pivot button\n  toolbarPivot: 'Pivot',\n  // Toolbar AI Assistant button\n  toolbarAssistant: 'AI Assistant',\n  // Columns management text\n  columnsManagementSearchTitle: 'Search',\n  columnsManagementNoColumns: 'No columns',\n  columnsManagementShowHideAllText: 'Show/Hide All',\n  columnsManagementReset: 'Reset',\n  columnsManagementDeleteIconLabel: 'Clear',\n  // Filter panel text\n  filterPanelAddFilter: 'Add filter',\n  filterPanelRemoveAll: 'Remove all',\n  filterPanelDeleteIconLabel: 'Delete',\n  filterPanelLogicOperator: 'Logic operator',\n  filterPanelOperator: 'Operator',\n  filterPanelOperatorAnd: 'And',\n  filterPanelOperatorOr: 'Or',\n  filterPanelColumns: 'Columns',\n  filterPanelInputLabel: 'Value',\n  filterPanelInputPlaceholder: 'Filter value',\n  // Filter operators text\n  filterOperatorContains: 'contains',\n  filterOperatorDoesNotContain: 'does not contain',\n  filterOperatorEquals: 'equals',\n  filterOperatorDoesNotEqual: 'does not equal',\n  filterOperatorStartsWith: 'starts with',\n  filterOperatorEndsWith: 'ends with',\n  filterOperatorIs: 'is',\n  filterOperatorNot: 'is not',\n  filterOperatorAfter: 'is after',\n  filterOperatorOnOrAfter: 'is on or after',\n  filterOperatorBefore: 'is before',\n  filterOperatorOnOrBefore: 'is on or before',\n  filterOperatorIsEmpty: 'is empty',\n  filterOperatorIsNotEmpty: 'is not empty',\n  filterOperatorIsAnyOf: 'is any of',\n  'filterOperator=': '=',\n  'filterOperator!=': '!=',\n  'filterOperator>': '>',\n  'filterOperator>=': '>=',\n  'filterOperator<': '<',\n  'filterOperator<=': '<=',\n  // Header filter operators text\n  headerFilterOperatorContains: 'Contains',\n  headerFilterOperatorDoesNotContain: 'Does not contain',\n  headerFilterOperatorEquals: 'Equals',\n  headerFilterOperatorDoesNotEqual: 'Does not equal',\n  headerFilterOperatorStartsWith: 'Starts with',\n  headerFilterOperatorEndsWith: 'Ends with',\n  headerFilterOperatorIs: 'Is',\n  headerFilterOperatorNot: 'Is not',\n  headerFilterOperatorAfter: 'Is after',\n  headerFilterOperatorOnOrAfter: 'Is on or after',\n  headerFilterOperatorBefore: 'Is before',\n  headerFilterOperatorOnOrBefore: 'Is on or before',\n  headerFilterOperatorIsEmpty: 'Is empty',\n  headerFilterOperatorIsNotEmpty: 'Is not empty',\n  headerFilterOperatorIsAnyOf: 'Is any of',\n  'headerFilterOperator=': 'Equals',\n  'headerFilterOperator!=': 'Not equals',\n  'headerFilterOperator>': 'Greater than',\n  'headerFilterOperator>=': 'Greater than or equal to',\n  'headerFilterOperator<': 'Less than',\n  'headerFilterOperator<=': 'Less than or equal to',\n  headerFilterClear: 'Clear filter',\n  // Filter values text\n  filterValueAny: 'any',\n  filterValueTrue: 'true',\n  filterValueFalse: 'false',\n  // Column menu text\n  columnMenuLabel: 'Menu',\n  columnMenuAriaLabel: columnName => `${columnName} column menu`,\n  columnMenuShowColumns: 'Show columns',\n  columnMenuManageColumns: 'Manage columns',\n  columnMenuFilter: 'Filter',\n  columnMenuHideColumn: 'Hide column',\n  columnMenuUnsort: 'Unsort',\n  columnMenuSortAsc: 'Sort by ASC',\n  columnMenuSortDesc: 'Sort by DESC',\n  columnMenuManagePivot: 'Manage pivot',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => count !== 1 ? `${count} active filters` : `${count} active filter`,\n  columnHeaderFiltersLabel: 'Show filters',\n  columnHeaderSortIconLabel: 'Sort',\n  // Rows selected footer text\n  footerRowSelected: count => count !== 1 ? `${count.toLocaleString()} rows selected` : `${count.toLocaleString()} row selected`,\n  // Total row amount footer text\n  footerTotalRows: 'Total Rows:',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} of ${totalCount.toLocaleString()}`,\n  // Checkbox selection text\n  checkboxSelectionHeaderName: 'Checkbox selection',\n  checkboxSelectionSelectAllRows: 'Select all rows',\n  checkboxSelectionUnselectAllRows: 'Unselect all rows',\n  checkboxSelectionSelectRow: 'Select row',\n  checkboxSelectionUnselectRow: 'Unselect row',\n  // Boolean cell text\n  booleanCellTrueLabel: 'yes',\n  booleanCellFalseLabel: 'no',\n  // Actions cell more text\n  actionsCellMore: 'more',\n  // Column pinning text\n  pinToLeft: 'Pin to left',\n  pinToRight: 'Pin to right',\n  unpin: 'Unpin',\n  // Tree Data\n  treeDataGroupingHeaderName: 'Group',\n  treeDataExpand: 'see children',\n  treeDataCollapse: 'hide children',\n  // Grouping columns\n  groupingColumnHeaderName: 'Group',\n  groupColumn: name => `Group by ${name}`,\n  unGroupColumn: name => `Stop grouping by ${name}`,\n  // Master/detail\n  detailPanelToggle: 'Detail panel toggle',\n  expandDetailPanel: 'Expand',\n  collapseDetailPanel: 'Collapse',\n  // Pagination\n  paginationRowsPerPage: 'Rows per page:',\n  paginationDisplayedRows: ({\n    from,\n    to,\n    count,\n    estimated\n  }) => {\n    if (!estimated) {\n      return `${from}–${to} of ${count !== -1 ? count : `more than ${to}`}`;\n    }\n    const estimatedLabel = estimated && estimated > to ? `around ${estimated}` : `more than ${to}`;\n    return `${from}–${to} of ${count !== -1 ? count : estimatedLabel}`;\n  },\n  paginationItemAriaLabel: type => {\n    if (type === 'first') {\n      return 'Go to first page';\n    }\n    if (type === 'last') {\n      return 'Go to last page';\n    }\n    if (type === 'next') {\n      return 'Go to next page';\n    }\n    // if (type === 'previous') {\n    return 'Go to previous page';\n  },\n  // Row reordering text\n  rowReorderingHeaderName: 'Row reordering',\n  // Aggregation\n  aggregationMenuItemHeader: 'Aggregation',\n  aggregationFunctionLabelSum: 'sum',\n  aggregationFunctionLabelAvg: 'avg',\n  aggregationFunctionLabelMin: 'min',\n  aggregationFunctionLabelMax: 'max',\n  aggregationFunctionLabelSize: 'size',\n  // Pivot panel\n  pivotToggleLabel: 'Pivot',\n  pivotRows: 'Rows',\n  pivotColumns: 'Columns',\n  pivotValues: 'Values',\n  pivotCloseButton: 'Close pivot settings',\n  pivotSearchButton: 'Search fields',\n  pivotSearchControlPlaceholder: 'Search fields',\n  pivotSearchControlLabel: 'Search fields',\n  pivotSearchControlClear: 'Clear search',\n  pivotNoFields: 'No fields',\n  pivotMenuMoveUp: 'Move up',\n  pivotMenuMoveDown: 'Move down',\n  pivotMenuMoveToTop: 'Move to top',\n  pivotMenuMoveToBottom: 'Move to bottom',\n  pivotMenuRows: 'Rows',\n  pivotMenuColumns: 'Columns',\n  pivotMenuValues: 'Values',\n  pivotMenuOptions: 'Field options',\n  pivotMenuAddToRows: 'Add to Rows',\n  pivotMenuAddToColumns: 'Add to Columns',\n  pivotMenuAddToValues: 'Add to Values',\n  pivotMenuRemove: 'Remove',\n  pivotDragToRows: 'Drag here to create rows',\n  pivotDragToColumns: 'Drag here to create columns',\n  pivotDragToValues: 'Drag here to create values',\n  pivotYearColumnHeaderName: '(Year)',\n  pivotQuarterColumnHeaderName: '(Quarter)',\n  // AI Assistant panel\n  aiAssistantPanelTitle: 'AI Assistant',\n  aiAssistantPanelClose: 'Close AI Assistant',\n  aiAssistantPanelNewConversation: 'New conversation',\n  aiAssistantPanelConversationHistory: 'Conversation history',\n  aiAssistantPanelEmptyConversation: 'No prompt history',\n  aiAssistantSuggestions: 'Suggestions',\n  // Prompt field\n  promptFieldLabel: 'Prompt',\n  promptFieldPlaceholder: 'Type a prompt…',\n  promptFieldPlaceholderWithRecording: 'Type or record a prompt…',\n  promptFieldPlaceholderListening: 'Listening for prompt…',\n  promptFieldSpeechRecognitionNotSupported: 'Speech recognition is not supported in this browser',\n  promptFieldSend: 'Send',\n  promptFieldRecord: 'Record',\n  promptFieldStopRecording: 'Stop recording',\n  // Prompt\n  promptRerun: 'Run again',\n  promptProcessing: 'Processing…',\n  promptAppliedChanges: 'Applied changes',\n  // Prompt changes\n  promptChangeGroupDescription: column => `Group by ${column}`,\n  promptChangeAggregationLabel: (column, aggregation) => `${column} (${aggregation})`,\n  promptChangeAggregationDescription: (column, aggregation) => `Aggregate ${column} (${aggregation})`,\n  promptChangeFilterLabel: (column, operator, value) => {\n    if (operator === 'is any of') {\n      return `${column} is any of: ${value}`;\n    }\n    return `${column} ${operator} ${value}`;\n  },\n  promptChangeFilterDescription: (column, operator, value) => {\n    if (operator === 'is any of') {\n      return `Filter where ${column} is any of: ${value}`;\n    }\n    return `Filter where ${column} ${operator} ${value}`;\n  },\n  promptChangeSortDescription: (column, direction) => `Sort by ${column} (${direction})`,\n  promptChangePivotEnableLabel: 'Pivot',\n  promptChangePivotEnableDescription: 'Enable pivot',\n  promptChangePivotColumnsLabel: count => `Columns (${count})`,\n  promptChangePivotColumnsDescription: (column, direction) => `${column}${direction ? ` (${direction})` : ''}`,\n  promptChangePivotRowsLabel: count => `Rows (${count})`,\n  promptChangePivotValuesLabel: count => `Values (${count})`,\n  promptChangePivotValuesDescription: (column, aggregation) => `${column} (${aggregation})`\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}