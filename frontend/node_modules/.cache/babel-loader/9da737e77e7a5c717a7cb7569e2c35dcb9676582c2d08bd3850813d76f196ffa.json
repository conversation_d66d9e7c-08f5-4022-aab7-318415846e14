{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/App.tsx\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { ToastContainer } from 'react-toastify';\nimport 'react-toastify/dist/ReactToastify.css';\nimport { AuthProvider } from './contexts/AuthContext';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport ModernLayout from './components/ModernLayout';\nimport ModernLogin from './pages/ModernLogin';\nimport ModernDashboard from './pages/ModernDashboard';\nimport Users from './pages/Users';\nimport Roles from './pages/Roles';\nimport Permissions from './pages/Permissions';\n\n// Thème Material-UI\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst theme = createTheme({\n  palette: {\n    primary: {\n      main: '#1976d2'\n    },\n    secondary: {\n      main: '#dc004e'\n    }\n  }\n});\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    theme: theme,\n    children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AuthProvider, {\n      children: [/*#__PURE__*/_jsxDEV(Router, {\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/login\",\n            element: /*#__PURE__*/_jsxDEV(ModernLogin, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 38,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(ModernLayout, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 45,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 17\n            }, this),\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              index: true,\n              element: /*#__PURE__*/_jsxDEV(Navigate, {\n                to: \"/dashboard\",\n                replace: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 49,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"dashboard\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                requiredPermissions: ['dashboard:access'],\n                children: /*#__PURE__*/_jsxDEV(ModernDashboard, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 54,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 53,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"users\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                requiredPermissions: ['user:read'],\n                children: /*#__PURE__*/_jsxDEV(Users, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 62,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"roles\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                requiredPermissions: ['role:read'],\n                children: /*#__PURE__*/_jsxDEV(Roles, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 70,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 69,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"permissions\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                requiredPermissions: ['permission:read'],\n                children: /*#__PURE__*/_jsxDEV(Permissions, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 78,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"*\",\n            element: /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/dashboard\",\n              replace: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ToastContainer, {\n        position: \"top-right\",\n        autoClose: 5000,\n        hideProgressBar: false,\n        newestOnTop: false,\n        closeOnClick: true,\n        rtl: false,\n        pauseOnFocusLoss: true,\n        draggable: true,\n        pauseOnHover: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "ToastContainer", "<PERSON>th<PERSON><PERSON><PERSON>", "ProtectedRoute", "ModernLayout", "ModernLogin", "ModernDashboard", "Users", "Roles", "Permissions", "jsxDEV", "_jsxDEV", "theme", "createTheme", "palette", "primary", "main", "secondary", "App", "ThemeProvider", "children", "CssBaseline", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "index", "to", "replace", "requiredPermissions", "position", "autoClose", "hideProgressBar", "newestOnTop", "closeOnClick", "rtl", "pauseOnFocusLoss", "draggable", "pauseOnHover", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { ToastContainer } from 'react-toastify';\nimport 'react-toastify/dist/ReactToastify.css';\n\nimport { AuthProvider } from './contexts/AuthContext';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport Layout from './components/Layout';\nimport ModernLayout from './components/ModernLayout';\nimport Login from './pages/Login';\nimport ModernLogin from './pages/ModernLogin';\nimport Dashboard from './pages/Dashboard';\nimport ModernDashboard from './pages/ModernDashboard';\nimport Users from './pages/Users';\nimport Roles from './pages/Roles';\nimport Permissions from './pages/Permissions';\n\n// Thème Material-UI\nconst theme = createTheme({\n  palette: {\n    primary: {\n      main: '#1976d2',\n    },\n    secondary: {\n      main: '#dc004e',\n    },\n  },\n});\n\nfunction App() {\n  return (\n    <ThemeProvider theme={theme}>\n      <CssBaseline />\n      <AuthProvider>\n        <Router>\n          <Routes>\n            {/* Route de connexion */}\n            <Route path=\"/login\" element={<ModernLogin />} />\n\n            {/* Routes protégées */}\n            <Route\n              path=\"/\"\n              element={\n                <ProtectedRoute>\n                  <ModernLayout />\n                </ProtectedRoute>\n              }\n            >\n              <Route index element={<Navigate to=\"/dashboard\" replace />} />\n              <Route\n                path=\"dashboard\"\n                element={\n                  <ProtectedRoute requiredPermissions={['dashboard:access']}>\n                    <ModernDashboard />\n                  </ProtectedRoute>\n                }\n              />\n              <Route\n                path=\"users\"\n                element={\n                  <ProtectedRoute requiredPermissions={['user:read']}>\n                    <Users />\n                  </ProtectedRoute>\n                }\n              />\n              <Route\n                path=\"roles\"\n                element={\n                  <ProtectedRoute requiredPermissions={['role:read']}>\n                    <Roles />\n                  </ProtectedRoute>\n                }\n              />\n              <Route\n                path=\"permissions\"\n                element={\n                  <ProtectedRoute requiredPermissions={['permission:read']}>\n                    <Permissions />\n                  </ProtectedRoute>\n                }\n              />\n            </Route>\n\n            {/* Redirection par défaut */}\n            <Route path=\"*\" element={<Navigate to=\"/dashboard\" replace />} />\n          </Routes>\n        </Router>\n\n        {/* Notifications toast */}\n        <ToastContainer\n          position=\"top-right\"\n          autoClose={5000}\n          hideProgressBar={false}\n          newestOnTop={false}\n          closeOnClick\n          rtl={false}\n          pauseOnFocusLoss\n          draggable\n          pauseOnHover\n        />\n      </AuthProvider>\n    </ThemeProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,OAAO,uCAAuC;AAE9C,SAASC,YAAY,QAAQ,wBAAwB;AACrD,OAAOC,cAAc,MAAM,6BAA6B;AAExD,OAAOC,YAAY,MAAM,2BAA2B;AAEpD,OAAOC,WAAW,MAAM,qBAAqB;AAE7C,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,WAAW,MAAM,qBAAqB;;AAE7C;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,KAAK,GAAGC,WAAW,CAAC;EACxBC,OAAO,EAAE;IACPC,OAAO,EAAE;MACPC,IAAI,EAAE;IACR,CAAC;IACDC,SAAS,EAAE;MACTD,IAAI,EAAE;IACR;EACF;AACF,CAAC,CAAC;AAEF,SAASE,GAAGA,CAAA,EAAG;EACb,oBACEP,OAAA,CAACQ,aAAa;IAACP,KAAK,EAAEA,KAAM;IAAAQ,QAAA,gBAC1BT,OAAA,CAACU,WAAW;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACfd,OAAA,CAACT,YAAY;MAAAkB,QAAA,gBACXT,OAAA,CAACd,MAAM;QAAAuB,QAAA,eACLT,OAAA,CAACb,MAAM;UAAAsB,QAAA,gBAELT,OAAA,CAACZ,KAAK;YAAC2B,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAEhB,OAAA,CAACN,WAAW;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGjDd,OAAA,CAACZ,KAAK;YACJ2B,IAAI,EAAC,GAAG;YACRC,OAAO,eACLhB,OAAA,CAACR,cAAc;cAAAiB,QAAA,eACbT,OAAA,CAACP,YAAY;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CACjB;YAAAL,QAAA,gBAEDT,OAAA,CAACZ,KAAK;cAAC6B,KAAK;cAACD,OAAO,eAAEhB,OAAA,CAACX,QAAQ;gBAAC6B,EAAE,EAAC,YAAY;gBAACC,OAAO;cAAA;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9Dd,OAAA,CAACZ,KAAK;cACJ2B,IAAI,EAAC,WAAW;cAChBC,OAAO,eACLhB,OAAA,CAACR,cAAc;gBAAC4B,mBAAmB,EAAE,CAAC,kBAAkB,CAAE;gBAAAX,QAAA,eACxDT,OAAA,CAACL,eAAe;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACFd,OAAA,CAACZ,KAAK;cACJ2B,IAAI,EAAC,OAAO;cACZC,OAAO,eACLhB,OAAA,CAACR,cAAc;gBAAC4B,mBAAmB,EAAE,CAAC,WAAW,CAAE;gBAAAX,QAAA,eACjDT,OAAA,CAACJ,KAAK;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACFd,OAAA,CAACZ,KAAK;cACJ2B,IAAI,EAAC,OAAO;cACZC,OAAO,eACLhB,OAAA,CAACR,cAAc;gBAAC4B,mBAAmB,EAAE,CAAC,WAAW,CAAE;gBAAAX,QAAA,eACjDT,OAAA,CAACH,KAAK;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACFd,OAAA,CAACZ,KAAK;cACJ2B,IAAI,EAAC,aAAa;cAClBC,OAAO,eACLhB,OAAA,CAACR,cAAc;gBAAC4B,mBAAmB,EAAE,CAAC,iBAAiB,CAAE;gBAAAX,QAAA,eACvDT,OAAA,CAACF,WAAW;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAGRd,OAAA,CAACZ,KAAK;YAAC2B,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEhB,OAAA,CAACX,QAAQ;cAAC6B,EAAE,EAAC,YAAY;cAACC,OAAO;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGTd,OAAA,CAACV,cAAc;QACb+B,QAAQ,EAAC,WAAW;QACpBC,SAAS,EAAE,IAAK;QAChBC,eAAe,EAAE,KAAM;QACvBC,WAAW,EAAE,KAAM;QACnBC,YAAY;QACZC,GAAG,EAAE,KAAM;QACXC,gBAAgB;QAChBC,SAAS;QACTC,YAAY;MAAA;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACU,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEpB;AAACgB,EAAA,GA1EQvB,GAAG;AA4EZ,eAAeA,GAAG;AAAC,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}