{"ast": null, "code": "export function getUnprocessedRange(testRange, processedRange) {\n  if (testRange.firstRowIndex >= processedRange.firstRowIndex && testRange.lastRowIndex <= processedRange.lastRowIndex) {\n    return null;\n  }\n  // Overflowing at the end\n  // Example: testRange={ firstRowIndex: 10, lastRowIndex: 20 }, processedRange={ firstRowIndex: 0, lastRowIndex: 15 }\n  // Unprocessed Range={ firstRowIndex: 16, lastRowIndex: 20 }\n  if (testRange.firstRowIndex >= processedRange.firstRowIndex && testRange.lastRowIndex > processedRange.lastRowIndex) {\n    return {\n      firstRowIndex: processedRange.lastRowIndex,\n      lastRowIndex: testRange.lastRowIndex\n    };\n  }\n  // Overflowing at the beginning\n  // Example: testRange={ firstRowIndex: 0, lastRowIndex: 20 }, processedRange={ firstRowIndex: 16, lastRowIndex: 30 }\n  // Unprocessed Range={ firstRowIndex: 0, lastRowIndex: 15 }\n  if (testRange.firstRowIndex < processedRange.firstRowIndex && testRange.lastRowIndex <= processedRange.lastRowIndex) {\n    return {\n      firstRowIndex: testRange.firstRowIndex,\n      lastRowIndex: processedRange.firstRowIndex - 1\n    };\n  }\n  // TODO: Should return two ranges handle overflowing at both ends ?\n  return testRange;\n}\nexport function isRowContextInitialized(renderContext) {\n  return renderContext.firstRowIndex !== 0 || renderContext.lastRowIndex !== 0;\n}\nexport function isRowRangeUpdated(range1, range2) {\n  return range1.firstRowIndex !== range2.firstRowIndex || range1.lastRowIndex !== range2.lastRowIndex;\n}\nexport const getCellValue = (row, colDef, apiRef) => {\n  if (!row) {\n    return null;\n  }\n  let cellValue = row[colDef.field];\n  const valueGetter = colDef.rowSpanValueGetter ?? colDef.valueGetter;\n  if (valueGetter) {\n    cellValue = valueGetter(cellValue, row, colDef, apiRef);\n  }\n  return cellValue;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}