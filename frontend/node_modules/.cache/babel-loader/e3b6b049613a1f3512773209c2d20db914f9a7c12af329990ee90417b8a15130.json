{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"field\", \"colDef\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { isMultipleRowSelectionEnabled } from \"../../hooks/features/rowSelection/utils.js\";\nimport { useGridSelector } from \"../../hooks/utils/useGridSelector.js\";\nimport { gridTabIndexColumnHeaderSelector } from \"../../hooks/features/focus/gridFocusStateSelector.js\";\nimport { gridRowSelectionStateSelector } from \"../../hooks/features/rowSelection/gridRowSelectionSelector.js\";\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { gridExpandedSortedRowIdsSelector } from \"../../hooks/features/filter/gridFilterSelector.js\";\nimport { gridPaginatedVisibleSortedGridRowIdsSelector } from \"../../hooks/features/pagination/gridPaginationSelector.js\";\nimport { createRowSelectionManager } from \"../../models/gridRowSelectionManager.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['checkboxInput']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst GridHeaderCheckbox = forwardRef(function GridHeaderCheckbox(props, ref) {\n  const other = _objectWithoutPropertiesLoose(props, _excluded);\n  const [, forceUpdate] = React.useState(false);\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const ownerState = {\n    classes: rootProps.classes\n  };\n  const classes = useUtilityClasses(ownerState);\n  const tabIndexState = useGridSelector(apiRef, gridTabIndexColumnHeaderSelector);\n  const selection = useGridSelector(apiRef, gridRowSelectionStateSelector);\n  const visibleRowIds = useGridSelector(apiRef, gridExpandedSortedRowIdsSelector);\n  const paginatedVisibleRowIds = useGridSelector(apiRef, gridPaginatedVisibleSortedGridRowIdsSelector);\n  const filteredSelection = React.useMemo(() => {\n    const isRowSelectable = rootProps.isRowSelectable;\n    if (typeof isRowSelectable !== 'function') {\n      return selection;\n    }\n    if (selection.type === 'exclude') {\n      return selection;\n    }\n\n    // selection.type === 'include'\n    const selectionModel = {\n      type: 'include',\n      ids: new Set()\n    };\n    for (const id of selection.ids) {\n      if (rootProps.keepNonExistentRowsSelected) {\n        selectionModel.ids.add(id);\n      }\n      // The row might have been deleted\n      if (!apiRef.current.getRow(id)) {\n        continue;\n      }\n      if (isRowSelectable(apiRef.current.getRowParams(id))) {\n        selectionModel.ids.add(id);\n      }\n    }\n    return selectionModel;\n  }, [apiRef, rootProps.isRowSelectable, rootProps.keepNonExistentRowsSelected, selection]);\n\n  // All the rows that could be selected / unselected by toggling this checkbox\n  const selectionCandidates = React.useMemo(() => {\n    const rowIds = !rootProps.pagination || !rootProps.checkboxSelectionVisibleOnly || rootProps.paginationMode === 'server' ? visibleRowIds : paginatedVisibleRowIds;\n\n    // Convert to a Set to make O(1) checking if a row exists or not\n    const candidates = new Set();\n    for (let i = 0; i < rowIds.length; i += 1) {\n      const id = rowIds[i];\n      if (!apiRef.current.getRow(id)) {\n        // The row could have been removed\n        continue;\n      }\n      if (apiRef.current.isRowSelectable(id)) {\n        candidates.add(id);\n      }\n    }\n    return candidates;\n  }, [apiRef, rootProps.pagination, rootProps.paginationMode, rootProps.checkboxSelectionVisibleOnly, paginatedVisibleRowIds, visibleRowIds]);\n\n  // Amount of rows selected and that are visible in the current page\n  const currentSelectionSize = React.useMemo(() => {\n    const selectionManager = createRowSelectionManager(filteredSelection);\n    let size = 0;\n    for (const id of selectionCandidates) {\n      if (selectionManager.has(id)) {\n        size += 1;\n      }\n    }\n    return size;\n  }, [filteredSelection, selectionCandidates]);\n  const isIndeterminate = React.useMemo(() => {\n    if (filteredSelection.ids.size === 0) {\n      return false;\n    }\n    const selectionManager = createRowSelectionManager(filteredSelection);\n    for (const rowId of selectionCandidates) {\n      if (!selectionManager.has(rowId)) {\n        return true;\n      }\n    }\n    return false;\n  }, [filteredSelection, selectionCandidates]);\n  const isChecked = currentSelectionSize > 0;\n  const handleChange = event => {\n    const params = {\n      value: event.target.checked\n    };\n    apiRef.current.publishEvent('headerSelectionCheckboxChange', params);\n  };\n  const tabIndex = tabIndexState !== null && tabIndexState.field === props.field ? 0 : -1;\n  React.useLayoutEffect(() => {\n    const element = apiRef.current.getColumnHeaderElement(props.field);\n    if (tabIndex === 0 && element) {\n      element.tabIndex = -1;\n    }\n  }, [tabIndex, apiRef, props.field]);\n  const handleKeyDown = React.useCallback(event => {\n    if (event.key === ' ') {\n      // imperative toggle the checkbox because Space is disable by some preventDefault\n      apiRef.current.publishEvent('headerSelectionCheckboxChange', {\n        value: !isChecked\n      });\n    }\n  }, [apiRef, isChecked]);\n  const handleSelectionChange = React.useCallback(() => {\n    forceUpdate(p => !p);\n  }, []);\n  React.useEffect(() => {\n    return apiRef.current.subscribeEvent('rowSelectionChange', handleSelectionChange);\n  }, [apiRef, handleSelectionChange]);\n  const label = apiRef.current.getLocaleText(isChecked && !isIndeterminate ? 'checkboxSelectionUnselectAllRows' : 'checkboxSelectionSelectAllRows');\n  return /*#__PURE__*/_jsx(rootProps.slots.baseCheckbox, _extends({\n    indeterminate: isIndeterminate,\n    checked: isChecked && !isIndeterminate,\n    onChange: handleChange,\n    className: classes.root,\n    slotProps: {\n      htmlInput: {\n        'aria-label': label,\n        name: 'select_all_rows'\n      }\n    },\n    tabIndex: tabIndex,\n    onKeyDown: handleKeyDown,\n    disabled: !isMultipleRowSelectionEnabled(rootProps)\n  }, rootProps.slotProps?.baseCheckbox, other, {\n    ref: ref\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridHeaderCheckbox.displayName = \"GridHeaderCheckbox\";\nprocess.env.NODE_ENV !== \"production\" ? GridHeaderCheckbox.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * The column of the current header component.\n   */\n  colDef: PropTypes.object.isRequired,\n  /**\n   * The column field of the column that triggered the event\n   */\n  field: PropTypes.string.isRequired\n} : void 0;\nexport { GridHeaderCheckbox };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}