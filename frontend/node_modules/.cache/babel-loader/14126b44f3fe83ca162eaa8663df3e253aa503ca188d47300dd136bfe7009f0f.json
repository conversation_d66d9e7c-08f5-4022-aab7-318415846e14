{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { isDeepEqual } from '@mui/x-internals/isDeepEqual';\nimport { gridFilterModelSelector, gridFilterActiveItemsSelector } from \"../filter/gridFilterSelector.js\";\nimport { gridDensityFactorSelector } from \"../density/index.js\";\nimport { useGridLogger, useGridSelector, useGridApiMethod, useGridEvent } from \"../../utils/index.js\";\nimport { useGridRegisterPipeProcessor } from \"../../core/pipeProcessing/index.js\";\nimport { gridPageCountSelector, gridPaginationModelSelector } from \"./gridPaginationSelector.js\";\nimport { getPageCount, defaultPageSize, throwIfPageSizeExceedsTheLimit, getDefaultGridPaginationModel, getValidPage } from \"./gridPaginationUtils.js\";\nexport const getDerivedPaginationModel = (paginationState, signature, paginationModelProp) => {\n  let paginationModel = paginationState.paginationModel;\n  const rowCount = paginationState.rowCount;\n  const pageSize = paginationModelProp?.pageSize ?? paginationModel.pageSize;\n  const page = paginationModelProp?.page ?? paginationModel.page;\n  const pageCount = getPageCount(rowCount, pageSize, page);\n  if (paginationModelProp && (paginationModelProp?.page !== paginationModel.page || paginationModelProp?.pageSize !== paginationModel.pageSize)) {\n    paginationModel = paginationModelProp;\n  }\n  const validPage = pageSize === -1 ? 0 : getValidPage(paginationModel.page, pageCount);\n  if (validPage !== paginationModel.page) {\n    paginationModel = _extends({}, paginationModel, {\n      page: validPage\n    });\n  }\n  throwIfPageSizeExceedsTheLimit(paginationModel.pageSize, signature);\n  return paginationModel;\n};\n\n/**\n * @requires useGridFilter (state)\n * @requires useGridDimensions (event) - can be after\n */\nexport const useGridPaginationModel = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridPaginationModel');\n  const densityFactor = useGridSelector(apiRef, gridDensityFactorSelector);\n  const previousFilterModel = React.useRef(gridFilterModelSelector(apiRef));\n  const rowHeight = Math.floor(props.rowHeight * densityFactor);\n  apiRef.current.registerControlState({\n    stateId: 'paginationModel',\n    propModel: props.paginationModel,\n    propOnChange: props.onPaginationModelChange,\n    stateSelector: gridPaginationModelSelector,\n    changeEvent: 'paginationModelChange'\n  });\n\n  /**\n   * API METHODS\n   */\n  const setPage = React.useCallback(page => {\n    const currentModel = gridPaginationModelSelector(apiRef);\n    if (page === currentModel.page) {\n      return;\n    }\n    logger.debug(`Setting page to ${page}`);\n    apiRef.current.setPaginationModel({\n      page,\n      pageSize: currentModel.pageSize\n    });\n  }, [apiRef, logger]);\n  const setPageSize = React.useCallback(pageSize => {\n    const currentModel = gridPaginationModelSelector(apiRef);\n    if (pageSize === currentModel.pageSize) {\n      return;\n    }\n    logger.debug(`Setting page size to ${pageSize}`);\n    apiRef.current.setPaginationModel({\n      pageSize,\n      page: currentModel.page\n    });\n  }, [apiRef, logger]);\n  const setPaginationModel = React.useCallback(paginationModel => {\n    const currentModel = gridPaginationModelSelector(apiRef);\n    if (paginationModel === currentModel) {\n      return;\n    }\n    logger.debug(\"Setting 'paginationModel' to\", paginationModel);\n    apiRef.current.setState(state => _extends({}, state, {\n      pagination: _extends({}, state.pagination, {\n        paginationModel: getDerivedPaginationModel(state.pagination, props.signature, paginationModel)\n      })\n    }), 'setPaginationModel');\n  }, [apiRef, logger, props.signature]);\n  const paginationModelApi = {\n    setPage,\n    setPageSize,\n    setPaginationModel\n  };\n  useGridApiMethod(apiRef, paginationModelApi, 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    const paginationModel = gridPaginationModelSelector(apiRef);\n    const shouldExportPaginationModel =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the `paginationModel` is controlled\n    props.paginationModel != null ||\n    // Always export if the `paginationModel` has been initialized\n    props.initialState?.pagination?.paginationModel != null ||\n    // Export if `page` or `pageSize` is not equal to the default value\n    paginationModel.page !== 0 && paginationModel.pageSize !== defaultPageSize(props.autoPageSize);\n    if (!shouldExportPaginationModel) {\n      return prevState;\n    }\n    return _extends({}, prevState, {\n      pagination: _extends({}, prevState.pagination, {\n        paginationModel\n      })\n    });\n  }, [apiRef, props.paginationModel, props.initialState?.pagination?.paginationModel, props.autoPageSize]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    const paginationModel = context.stateToRestore.pagination?.paginationModel ? _extends({}, getDefaultGridPaginationModel(props.autoPageSize), context.stateToRestore.pagination?.paginationModel) : gridPaginationModelSelector(apiRef);\n    apiRef.current.setState(state => _extends({}, state, {\n      pagination: _extends({}, state.pagination, {\n        paginationModel: getDerivedPaginationModel(state.pagination, props.signature, paginationModel)\n      })\n    }), 'stateRestorePreProcessing');\n    return params;\n  }, [apiRef, props.autoPageSize, props.signature]);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n\n  /**\n   * EVENTS\n   */\n  const handlePaginationModelChange = () => {\n    const paginationModel = gridPaginationModelSelector(apiRef);\n    if (apiRef.current.virtualScrollerRef?.current) {\n      apiRef.current.scrollToIndexes({\n        rowIndex: paginationModel.page * paginationModel.pageSize\n      });\n    }\n  };\n  const handleUpdateAutoPageSize = React.useCallback(() => {\n    if (!props.autoPageSize) {\n      return;\n    }\n    const dimensions = apiRef.current.getRootDimensions();\n    const maximumPageSizeWithoutScrollBar = Math.max(1, Math.floor(dimensions.viewportInnerSize.height / rowHeight));\n    apiRef.current.setPageSize(maximumPageSizeWithoutScrollBar);\n  }, [apiRef, props.autoPageSize, rowHeight]);\n  const handleRowCountChange = React.useCallback(newRowCount => {\n    if (newRowCount == null) {\n      return;\n    }\n    const paginationModel = gridPaginationModelSelector(apiRef);\n    if (paginationModel.page === 0) {\n      return;\n    }\n    const pageCount = gridPageCountSelector(apiRef);\n    if (paginationModel.page > pageCount - 1) {\n      apiRef.current.setPage(Math.max(0, pageCount - 1));\n    }\n  }, [apiRef]);\n\n  /**\n   * Goes to the first row of the grid\n   */\n  const navigateToStart = React.useCallback(() => {\n    const paginationModel = gridPaginationModelSelector(apiRef);\n    if (paginationModel.page !== 0) {\n      apiRef.current.setPage(0);\n    }\n\n    // If the page was not changed it might be needed to scroll to the top\n    const scrollPosition = apiRef.current.getScrollPosition();\n    if (scrollPosition.top !== 0) {\n      apiRef.current.scroll({\n        top: 0\n      });\n    }\n  }, [apiRef]);\n\n  /**\n   * Resets the page only if the active items or quick filter has changed from the last time.\n   * This is to avoid resetting the page when the filter model is changed\n   * because of and update of the operator from an item that does not have the value\n   * or reseting when the filter panel is just opened\n   */\n  const handleFilterModelChange = React.useCallback(filterModel => {\n    const currentActiveFilters = _extends({}, filterModel, {\n      // replace items with the active items\n      items: gridFilterActiveItemsSelector(apiRef)\n    });\n    if (isDeepEqual(currentActiveFilters, previousFilterModel.current)) {\n      return;\n    }\n    previousFilterModel.current = currentActiveFilters;\n    navigateToStart();\n  }, [apiRef, navigateToStart]);\n  useGridEvent(apiRef, 'viewportInnerSizeChange', handleUpdateAutoPageSize);\n  useGridEvent(apiRef, 'paginationModelChange', handlePaginationModelChange);\n  useGridEvent(apiRef, 'rowCountChange', handleRowCountChange);\n  useGridEvent(apiRef, 'sortModelChange', navigateToStart);\n  useGridEvent(apiRef, 'filterModelChange', handleFilterModelChange);\n\n  /**\n   * EFFECTS\n   */\n  const isFirstRender = React.useRef(true);\n  React.useEffect(() => {\n    if (isFirstRender.current) {\n      isFirstRender.current = false;\n      return;\n    }\n    if (!props.pagination) {\n      return;\n    }\n    apiRef.current.setState(state => _extends({}, state, {\n      pagination: _extends({}, state.pagination, {\n        paginationModel: getDerivedPaginationModel(state.pagination, props.signature, props.paginationModel)\n      })\n    }));\n  }, [apiRef, props.paginationModel, props.signature, props.pagination]);\n  React.useEffect(() => {\n    apiRef.current.setState(state => {\n      const isEnabled = props.pagination === true;\n      if (state.pagination.paginationMode === props.paginationMode || state.pagination.enabled === isEnabled) {\n        return state;\n      }\n      return _extends({}, state, {\n        pagination: _extends({}, state.pagination, {\n          paginationMode: props.paginationMode,\n          enabled: props.pagination === true\n        })\n      });\n    });\n  }, [apiRef, props.paginationMode, props.pagination]);\n  React.useEffect(handleUpdateAutoPageSize, [handleUpdateAutoPageSize]);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}