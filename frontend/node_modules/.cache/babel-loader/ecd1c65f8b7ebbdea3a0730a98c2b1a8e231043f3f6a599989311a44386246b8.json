{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport ownerDocument from '@mui/utils/ownerDocument';\nimport { throttle } from '@mui/x-internals/throttle';\nimport { isDeepEqual } from '@mui/x-internals/isDeepEqual';\nimport { useGridEventPriority } from \"../../utils/useGridEvent.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { createSelector } from \"../../../utils/createSelector.js\";\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { gridColumnPositionsSelector, gridVisibleColumnDefinitionsSelector, gridVisiblePinnedColumnDefinitionsSelector } from \"../columns/index.js\";\nimport { gridDimensionsSelector } from \"./gridDimensionsSelectors.js\";\nimport { gridDensityFactorSelector } from \"../density/index.js\";\nimport { gridRenderContextSelector } from \"../virtualization/index.js\";\nimport { useGridSelector } from \"../../utils/index.js\";\nimport { getVisibleRows } from \"../../utils/useGridVisibleRows.js\";\nimport { gridRowsMetaSelector } from \"../rows/gridRowsMetaSelector.js\";\nimport { getValidRowHeight, rowHeightWarning } from \"../rows/gridRowsUtils.js\";\nimport { getTotalHeaderHeight } from \"../columns/gridColumnsUtils.js\";\nimport { DATA_GRID_PROPS_DEFAULT_VALUES } from \"../../../constants/dataGridPropsDefaultValues.js\";\nimport { roundToDecimalPlaces } from \"../../../utils/roundToDecimalPlaces.js\";\nimport { isJSDOM } from \"../../../utils/isJSDOM.js\";\nconst EMPTY_SIZE = {\n  width: 0,\n  height: 0\n};\nconst EMPTY_DIMENSIONS = {\n  isReady: false,\n  root: EMPTY_SIZE,\n  viewportOuterSize: EMPTY_SIZE,\n  viewportInnerSize: EMPTY_SIZE,\n  contentSize: EMPTY_SIZE,\n  minimumSize: EMPTY_SIZE,\n  hasScrollX: false,\n  hasScrollY: false,\n  scrollbarSize: 0,\n  headerHeight: 0,\n  groupHeaderHeight: 0,\n  headerFilterHeight: 0,\n  rowWidth: 0,\n  rowHeight: 0,\n  columnsTotalWidth: 0,\n  leftPinnedWidth: 0,\n  rightPinnedWidth: 0,\n  headersTotalHeight: 0,\n  topContainerHeight: 0,\n  bottomContainerHeight: 0\n};\nexport const dimensionsStateInitializer = (state, props, apiRef) => {\n  const dimensions = EMPTY_DIMENSIONS;\n  const density = gridDensityFactorSelector(apiRef);\n  return _extends({}, state, {\n    dimensions: _extends({}, dimensions, getStaticDimensions(props, apiRef, density, gridVisiblePinnedColumnDefinitionsSelector(apiRef)))\n  });\n};\nconst columnsTotalWidthSelector = createSelector(gridVisibleColumnDefinitionsSelector, gridColumnPositionsSelector, (visibleColumns, positions) => {\n  const colCount = visibleColumns.length;\n  if (colCount === 0) {\n    return 0;\n  }\n  return roundToDecimalPlaces(positions[colCount - 1] + visibleColumns[colCount - 1].computedWidth, 1);\n});\nexport function useGridDimensions(apiRef, props) {\n  const logger = useGridLogger(apiRef, 'useResizeContainer');\n  const errorShown = React.useRef(false);\n  const rootDimensionsRef = React.useRef(EMPTY_SIZE);\n  const pinnedColumns = useGridSelector(apiRef, gridVisiblePinnedColumnDefinitionsSelector);\n  const densityFactor = useGridSelector(apiRef, gridDensityFactorSelector);\n  const columnsTotalWidth = useGridSelector(apiRef, columnsTotalWidthSelector);\n  const isFirstSizing = React.useRef(true);\n  const {\n    rowHeight,\n    headerHeight,\n    groupHeaderHeight,\n    headerFilterHeight,\n    headersTotalHeight,\n    leftPinnedWidth,\n    rightPinnedWidth\n  } = getStaticDimensions(props, apiRef, densityFactor, pinnedColumns);\n  const getRootDimensions = React.useCallback(() => gridDimensionsSelector(apiRef), [apiRef]);\n  const setDimensions = React.useCallback(dimensions => {\n    apiRef.current.setState(state => _extends({}, state, {\n      dimensions\n    }));\n    if (apiRef.current.rootElementRef.current) {\n      setCSSVariables(apiRef.current.rootElementRef.current, gridDimensionsSelector(apiRef));\n    }\n  }, [apiRef]);\n  const getViewportPageSize = React.useCallback(() => {\n    const dimensions = gridDimensionsSelector(apiRef);\n    if (!dimensions.isReady) {\n      return 0;\n    }\n    const currentPage = getVisibleRows(apiRef);\n\n    // TODO: Use a combination of scrollTop, dimensions.viewportInnerSize.height and rowsMeta.possitions\n    // to find out the maximum number of rows that can fit in the visible part of the grid\n    if (props.getRowHeight) {\n      const renderContext = gridRenderContextSelector(apiRef);\n      const viewportPageSize = renderContext.lastRowIndex - renderContext.firstRowIndex;\n      return Math.min(viewportPageSize - 1, currentPage.rows.length);\n    }\n    const maximumPageSizeWithoutScrollBar = Math.floor(dimensions.viewportInnerSize.height / rowHeight);\n    return Math.min(maximumPageSizeWithoutScrollBar, currentPage.rows.length);\n  }, [apiRef, props.getRowHeight, rowHeight]);\n  const updateDimensions = React.useCallback(() => {\n    if (isFirstSizing.current) {\n      return;\n    }\n    // All the floating point dimensions should be rounded to .1 decimal places to avoid subpixel rendering issues\n    // https://github.com/mui/mui-x/issues/9550#issuecomment-1619020477\n    // https://github.com/mui/mui-x/issues/15721\n    const scrollbarSize = measureScrollbarSize(apiRef.current.mainElementRef.current, props.scrollbarSize);\n    const rowsMeta = gridRowsMetaSelector(apiRef);\n    const topContainerHeight = headersTotalHeight + rowsMeta.pinnedTopRowsTotalHeight;\n    const bottomContainerHeight = rowsMeta.pinnedBottomRowsTotalHeight;\n    const contentSize = {\n      width: columnsTotalWidth,\n      height: roundToDecimalPlaces(rowsMeta.currentPageTotalHeight, 1)\n    };\n    let viewportOuterSize;\n    let viewportInnerSize;\n    let hasScrollX = false;\n    let hasScrollY = false;\n    if (props.autoHeight) {\n      hasScrollY = false;\n      hasScrollX = Math.round(columnsTotalWidth) > Math.round(rootDimensionsRef.current.width);\n      viewportOuterSize = {\n        width: rootDimensionsRef.current.width,\n        height: topContainerHeight + bottomContainerHeight + contentSize.height\n      };\n      viewportInnerSize = {\n        width: Math.max(0, viewportOuterSize.width - (hasScrollY ? scrollbarSize : 0)),\n        height: Math.max(0, viewportOuterSize.height - (hasScrollX ? scrollbarSize : 0))\n      };\n    } else {\n      viewportOuterSize = {\n        width: rootDimensionsRef.current.width,\n        height: rootDimensionsRef.current.height\n      };\n      viewportInnerSize = {\n        width: Math.max(0, viewportOuterSize.width),\n        height: Math.max(0, viewportOuterSize.height - topContainerHeight - bottomContainerHeight)\n      };\n      const content = contentSize;\n      const container = viewportInnerSize;\n      const hasScrollXIfNoYScrollBar = content.width > container.width;\n      const hasScrollYIfNoXScrollBar = content.height > container.height;\n      if (hasScrollXIfNoYScrollBar || hasScrollYIfNoXScrollBar) {\n        hasScrollY = hasScrollYIfNoXScrollBar;\n        hasScrollX = content.width + (hasScrollY ? scrollbarSize : 0) > container.width;\n\n        // We recalculate the scroll y to consider the size of the x scrollbar.\n        if (hasScrollX) {\n          hasScrollY = content.height + scrollbarSize > container.height;\n        }\n      }\n      if (hasScrollY) {\n        viewportInnerSize.width -= scrollbarSize;\n      }\n      if (hasScrollX) {\n        viewportInnerSize.height -= scrollbarSize;\n      }\n    }\n    const rowWidth = Math.max(viewportOuterSize.width, columnsTotalWidth + (hasScrollY ? scrollbarSize : 0));\n    const minimumSize = {\n      width: columnsTotalWidth,\n      height: topContainerHeight + contentSize.height + bottomContainerHeight\n    };\n    const newDimensions = {\n      isReady: true,\n      root: rootDimensionsRef.current,\n      viewportOuterSize,\n      viewportInnerSize,\n      contentSize,\n      minimumSize,\n      hasScrollX,\n      hasScrollY,\n      scrollbarSize,\n      headerHeight,\n      groupHeaderHeight,\n      headerFilterHeight,\n      rowWidth,\n      rowHeight,\n      columnsTotalWidth,\n      leftPinnedWidth,\n      rightPinnedWidth,\n      headersTotalHeight,\n      topContainerHeight,\n      bottomContainerHeight\n    };\n    const prevDimensions = apiRef.current.state.dimensions;\n    if (isDeepEqual(prevDimensions, newDimensions)) {\n      return;\n    }\n    setDimensions(newDimensions);\n    if (!areElementSizesEqual(newDimensions.viewportInnerSize, prevDimensions.viewportInnerSize)) {\n      apiRef.current.publishEvent('viewportInnerSizeChange', newDimensions.viewportInnerSize);\n    }\n    apiRef.current.updateRenderContext?.();\n  }, [apiRef, setDimensions, props.scrollbarSize, props.autoHeight, rowHeight, headerHeight, groupHeaderHeight, headerFilterHeight, columnsTotalWidth, headersTotalHeight, leftPinnedWidth, rightPinnedWidth]);\n  const updateDimensionCallback = useEventCallback(updateDimensions);\n  const debouncedUpdateDimensions = React.useMemo(() => props.resizeThrottleMs > 0 ? throttle(() => {\n    updateDimensionCallback();\n    apiRef.current.publishEvent('debouncedResize', rootDimensionsRef.current);\n  }, props.resizeThrottleMs) : undefined, [apiRef, props.resizeThrottleMs, updateDimensionCallback]);\n  React.useEffect(() => debouncedUpdateDimensions?.clear, [debouncedUpdateDimensions]);\n  const apiPublic = {\n    getRootDimensions\n  };\n  const apiPrivate = {\n    updateDimensions,\n    getViewportPageSize\n  };\n  useEnhancedEffect(updateDimensions, [updateDimensions]);\n  useGridApiMethod(apiRef, apiPublic, 'public');\n  useGridApiMethod(apiRef, apiPrivate, 'private');\n  const handleRootMount = React.useCallback(root => {\n    setCSSVariables(root, gridDimensionsSelector(apiRef));\n  }, [apiRef]);\n  const handleResize = React.useCallback(size => {\n    rootDimensionsRef.current = size;\n    if (size.height === 0 && !errorShown.current && !props.autoHeight && !isJSDOM) {\n      logger.error(['The parent DOM element of the Data Grid has an empty height.', 'Please make sure that this element has an intrinsic height.', 'The grid displays with a height of 0px.', '', 'More details: https://mui.com/r/x-data-grid-no-dimensions.'].join('\\n'));\n      errorShown.current = true;\n    }\n    if (size.width === 0 && !errorShown.current && !isJSDOM) {\n      logger.error(['The parent DOM element of the Data Grid has an empty width.', 'Please make sure that this element has an intrinsic width.', 'The grid displays with a width of 0px.', '', 'More details: https://mui.com/r/x-data-grid-no-dimensions.'].join('\\n'));\n      errorShown.current = true;\n    }\n    if (isFirstSizing.current || !debouncedUpdateDimensions) {\n      // We want to initialize the grid dimensions as soon as possible to avoid flickering\n      isFirstSizing.current = false;\n      updateDimensions();\n      return;\n    }\n    debouncedUpdateDimensions();\n  }, [updateDimensions, props.autoHeight, debouncedUpdateDimensions, logger]);\n  useGridEventPriority(apiRef, 'rootMount', handleRootMount);\n  useGridEventPriority(apiRef, 'resize', handleResize);\n  useGridEventPriority(apiRef, 'debouncedResize', props.onResize);\n}\nfunction setCSSVariables(root, dimensions) {\n  const set = (k, v) => root.style.setProperty(k, v);\n  set('--DataGrid-hasScrollX', `${Number(dimensions.hasScrollX)}`);\n  set('--DataGrid-hasScrollY', `${Number(dimensions.hasScrollY)}`);\n  set('--DataGrid-scrollbarSize', `${dimensions.scrollbarSize}px`);\n  set('--DataGrid-rowWidth', `${dimensions.rowWidth}px`);\n  set('--DataGrid-columnsTotalWidth', `${dimensions.columnsTotalWidth}px`);\n  set('--DataGrid-leftPinnedWidth', `${dimensions.leftPinnedWidth}px`);\n  set('--DataGrid-rightPinnedWidth', `${dimensions.rightPinnedWidth}px`);\n  set('--DataGrid-headerHeight', `${dimensions.headerHeight}px`);\n  set('--DataGrid-headersTotalHeight', `${dimensions.headersTotalHeight}px`);\n  set('--DataGrid-topContainerHeight', `${dimensions.topContainerHeight}px`);\n  set('--DataGrid-bottomContainerHeight', `${dimensions.bottomContainerHeight}px`);\n  set('--height', `${dimensions.rowHeight}px`);\n}\nfunction getStaticDimensions(props, apiRef, density, pinnedColumnns) {\n  const validRowHeight = getValidRowHeight(props.rowHeight, DATA_GRID_PROPS_DEFAULT_VALUES.rowHeight, rowHeightWarning);\n  return {\n    rowHeight: Math.floor(validRowHeight * density),\n    headerHeight: Math.floor(props.columnHeaderHeight * density),\n    groupHeaderHeight: Math.floor((props.columnGroupHeaderHeight ?? props.columnHeaderHeight) * density),\n    headerFilterHeight: Math.floor((props.headerFilterHeight ?? props.columnHeaderHeight) * density),\n    columnsTotalWidth: columnsTotalWidthSelector(apiRef),\n    headersTotalHeight: getTotalHeaderHeight(apiRef, props),\n    leftPinnedWidth: pinnedColumnns.left.reduce((w, col) => w + col.computedWidth, 0),\n    rightPinnedWidth: pinnedColumnns.right.reduce((w, col) => w + col.computedWidth, 0)\n  };\n}\nconst scrollbarSizeCache = new WeakMap();\nfunction measureScrollbarSize(element, scrollbarSize) {\n  if (scrollbarSize !== undefined) {\n    return scrollbarSize;\n  }\n  if (element === null) {\n    return 0;\n  }\n  const cachedSize = scrollbarSizeCache.get(element);\n  if (cachedSize !== undefined) {\n    return cachedSize;\n  }\n  const doc = ownerDocument(element);\n  const scrollDiv = doc.createElement('div');\n  scrollDiv.style.width = '99px';\n  scrollDiv.style.height = '99px';\n  scrollDiv.style.position = 'absolute';\n  scrollDiv.style.overflow = 'scroll';\n  scrollDiv.className = 'scrollDiv';\n  element.appendChild(scrollDiv);\n  const size = scrollDiv.offsetWidth - scrollDiv.clientWidth;\n  element.removeChild(scrollDiv);\n  scrollbarSizeCache.set(element, size);\n  return size;\n}\nfunction areElementSizesEqual(a, b) {\n  return a.width === b.width && a.height === b.height;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}