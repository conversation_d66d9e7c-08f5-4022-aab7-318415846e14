{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nexport class Store {\n  static create(state) {\n    return new Store(state);\n  }\n  constructor(state) {\n    this.state = void 0;\n    this.listeners = void 0;\n    this.subscribe = fn => {\n      this.listeners.add(fn);\n      return () => {\n        this.listeners.delete(fn);\n      };\n    };\n    this.getSnapshot = () => {\n      return this.state;\n    };\n    this.update = newState => {\n      if (this.state !== newState) {\n        this.state = newState;\n        this.listeners.forEach(l => l(newState));\n      }\n    };\n    this.state = state;\n    this.listeners = new Set();\n  }\n  apply(changes) {\n    for (const key in changes) {\n      if (!Object.is(this.state[key], changes[key])) {\n        this.update(_extends({}, this.state, changes));\n        return;\n      }\n    }\n  }\n  set(key, value) {\n    if (!Object.is(this.state[key], value)) {\n      this.update(_extends({}, this.state, {\n        [key]: value\n      }));\n    }\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}