{"ast": null, "code": "const encoder = new TextEncoder();\n\n// bufferLength must be a multiple of 4 to satisfy Int32Array constraints\nlet bufferLength = 2 * 1024;\nlet buffer = new ArrayBuffer(bufferLength);\nlet uint8View = new Uint8Array(buffer);\nlet int32View = new Int32Array(buffer);\nexport const hash = xxh;\n\n/**\n * Returns an xxh hash of `input` formatted as a decimal string.\n */\n// prettier-ignore\nfunction xxh(input) {\n  /* eslint-disable no-bitwise */\n\n  // Worst-case scenario: full string of 2-byte characters\n  const requiredLength = input.length * 2;\n  if (requiredLength > bufferLength) {\n    // buffer.resize() is only available in recent browsers, so we re-allocate\n    // a new and views\n    bufferLength = requiredLength + (4 - requiredLength % 4);\n    buffer = new ArrayBuffer(bufferLength);\n    uint8View = new Uint8Array(buffer);\n    int32View = new Int32Array(buffer);\n  }\n  const length8 = encoder.encodeInto(input, uint8View).written;\n  const seed = 0;\n  const len = length8 | 0;\n  let i = 0;\n  let h = (seed + len | 0) + 0x165667B1 | 0;\n  if (len < 16) {\n    for (; (i + 3 | 0) < len; i = i + 4 | 0) {\n      h = Math.imul(rotl32(h + Math.imul(int32View[i] | 0, 0xC2B2AE3D) | 0, 17) | 0, 0x27D4EB2F);\n    }\n  } else {\n    let v0 = seed + 0x24234428 | 0;\n    let v1 = seed + 0x85EBCA77 | 0;\n    let v2 = seed;\n    let v3 = seed - 0x9E3779B1 | 0;\n    for (; (i + 15 | 0) < len; i = i + 16 | 0) {\n      v0 = Math.imul(rotl32(v0 + Math.imul(int32View[i + 0 | 0] | 0, 0x85EBCA77) | 0, 13) | 0, 0x9E3779B1);\n      v1 = Math.imul(rotl32(v1 + Math.imul(int32View[i + 4 | 0] | 0, 0x85EBCA77) | 0, 13) | 0, 0x9E3779B1);\n      v2 = Math.imul(rotl32(v2 + Math.imul(int32View[i + 8 | 0] | 0, 0x85EBCA77) | 0, 13) | 0, 0x9E3779B1);\n      v3 = Math.imul(rotl32(v3 + Math.imul(int32View[i + 12 | 0] | 0, 0x85EBCA77) | 0, 13) | 0, 0x9E3779B1);\n    }\n    h = (((rotl32(v0, 1) | 0 + rotl32(v1, 7) | 0) + rotl32(v2, 12) | 0) + rotl32(v3, 18) | 0) + len | 0;\n    for (; (i + 3 | 0) < len; i = i + 4 | 0) {\n      h = Math.imul(rotl32(h + Math.imul(int32View[i] | 0, 0xC2B2AE3D) | 0, 17) | 0, 0x27D4EB2F);\n    }\n  }\n  for (; i < len; i = i + 1 | 0) {\n    h = Math.imul(rotl32(h + Math.imul(uint8View[i] | 0, 0x165667B1) | 0, 11) | 0, 0x9E3779B1);\n  }\n  h = Math.imul(h ^ h >>> 15, 0x85EBCA77);\n  h = Math.imul(h ^ h >>> 13, 0xC2B2AE3D);\n  return ((h ^ h >>> 16) >>> 0).toString();\n}\nfunction rotl32(x, r) {\n  return x << r | x >>> 32 - r;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}