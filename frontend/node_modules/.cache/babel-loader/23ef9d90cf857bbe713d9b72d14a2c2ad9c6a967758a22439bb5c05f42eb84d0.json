{"ast": null, "code": "import { isObjectEmpty } from '@mui/x-internals/isObjectEmpty';\nimport { createSelector, createRootSelector, createSelectorMemoized } from \"../../../utils/createSelector.js\";\nimport { gridSortedRowEntriesSelector } from \"../sorting/gridSortingSelector.js\";\nimport { gridColumnLookupSelector } from \"../columns/gridColumnsSelector.js\";\nimport { gridRowMaximumTreeDepthSelector, gridRowTreeSelector } from \"../rows/gridRowsSelector.js\";\n\n/**\n * @category Filtering\n */\nconst gridFilterStateSelector = createRootSelector(state => state.filter);\n\n/**\n * Get the current filter model.\n * @category Filtering\n */\nexport const gridFilterModelSelector = createSelector(gridFilterStateSelector, filterState => filterState.filterModel);\n\n/**\n * Get the current quick filter values.\n * @category Filtering\n */\nexport const gridQuickFilterValuesSelector = createSelector(gridFilterModelSelector, filterModel => filterModel.quickFilterValues);\n\n/**\n * @category Visible rows\n * @ignore - do not document.\n */\nexport const gridVisibleRowsLookupSelector = createRootSelector(state => state.visibleRowsLookup);\n\n/**\n * @category Filtering\n * @ignore - do not document.\n */\nexport const gridFilteredRowsLookupSelector = createSelector(gridFilterStateSelector, filterState => filterState.filteredRowsLookup);\n\n/**\n * @category Filtering\n * @ignore - do not document.\n */\nexport const gridFilteredChildrenCountLookupSelector = createSelector(gridFilterStateSelector, filterState => filterState.filteredChildrenCountLookup);\n\n/**\n * @category Filtering\n * @ignore - do not document.\n */\nexport const gridFilteredDescendantCountLookupSelector = createSelector(gridFilterStateSelector, filterState => filterState.filteredDescendantCountLookup);\n\n/**\n * Get the id and the model of the rows accessible after the filtering process.\n * Does not contain the collapsed children.\n * @category Filtering\n */\nexport const gridExpandedSortedRowEntriesSelector = createSelectorMemoized(gridVisibleRowsLookupSelector, gridSortedRowEntriesSelector, (visibleRowsLookup, sortedRows) => {\n  if (isObjectEmpty(visibleRowsLookup)) {\n    return sortedRows;\n  }\n  return sortedRows.filter(row => visibleRowsLookup[row.id] !== false);\n});\n\n/**\n * Get the id of the rows accessible after the filtering process.\n * Does not contain the collapsed children.\n * @category Filtering\n */\nexport const gridExpandedSortedRowIdsSelector = createSelectorMemoized(gridExpandedSortedRowEntriesSelector, visibleSortedRowEntries => visibleSortedRowEntries.map(row => row.id));\n\n/**\n * Get the id and the model of the rows accessible after the filtering process.\n * Contains the collapsed children.\n * @category Filtering\n */\nexport const gridFilteredSortedRowEntriesSelector = createSelectorMemoized(gridFilteredRowsLookupSelector, gridSortedRowEntriesSelector, (filteredRowsLookup, sortedRows) => isObjectEmpty(filteredRowsLookup) ? sortedRows : sortedRows.filter(row => filteredRowsLookup[row.id] !== false));\n\n/**\n * Get the id of the rows accessible after the filtering process.\n * Contains the collapsed children.\n * @category Filtering\n */\nexport const gridFilteredSortedRowIdsSelector = createSelectorMemoized(gridFilteredSortedRowEntriesSelector, filteredSortedRowEntries => filteredSortedRowEntries.map(row => row.id));\n\n/**\n * Get the ids to position in the current tree level lookup of the rows accessible after the filtering process.\n * Does not contain the collapsed children.\n * @category Filtering\n * @ignore - do not document.\n */\nexport const gridExpandedSortedRowTreeLevelPositionLookupSelector = createSelectorMemoized(gridExpandedSortedRowIdsSelector, gridRowTreeSelector, (visibleSortedRowIds, rowTree) => {\n  const depthPositionCounter = {};\n  let lastDepth = 0;\n  return visibleSortedRowIds.reduce((acc, rowId) => {\n    const rowNode = rowTree[rowId];\n    if (!depthPositionCounter[rowNode.depth]) {\n      depthPositionCounter[rowNode.depth] = 0;\n    }\n\n    // going deeper in the tree should reset the counter\n    // since it might have been used in some other branch at the same level, up in the tree\n    // going back up should keep the counter and continue where it left off\n    if (rowNode.depth > lastDepth) {\n      depthPositionCounter[rowNode.depth] = 0;\n    }\n    lastDepth = rowNode.depth;\n    depthPositionCounter[rowNode.depth] += 1;\n    acc[rowId] = depthPositionCounter[rowNode.depth];\n    return acc;\n  }, {});\n});\n\n/**\n * Get the id and the model of the top level rows accessible after the filtering process.\n * @category Filtering\n */\nexport const gridFilteredSortedTopLevelRowEntriesSelector = createSelectorMemoized(gridExpandedSortedRowEntriesSelector, gridRowTreeSelector, gridRowMaximumTreeDepthSelector, (visibleSortedRows, rowTree, rowTreeDepth) => {\n  if (rowTreeDepth < 2) {\n    return visibleSortedRows;\n  }\n  return visibleSortedRows.filter(row => rowTree[row.id]?.depth === 0);\n});\n\n/**\n * Get the amount of rows accessible after the filtering process.\n * @category Filtering\n */\nexport const gridExpandedRowCountSelector = createSelector(gridExpandedSortedRowEntriesSelector, visibleSortedRows => visibleSortedRows.length);\n\n/**\n * Get the amount of top level rows accessible after the filtering process.\n * @category Filtering\n */\nexport const gridFilteredTopLevelRowCountSelector = createSelector(gridFilteredSortedTopLevelRowEntriesSelector, visibleSortedTopLevelRows => visibleSortedTopLevelRows.length);\n\n/**\n * Get the amount of rows accessible after the filtering process.\n * Includes top level and descendant rows.\n * @category Filtering\n */\nexport const gridFilteredRowCountSelector = createSelector(gridFilteredSortedRowEntriesSelector, filteredSortedRowEntries => filteredSortedRowEntries.length);\n\n/**\n * Get the amount of descendant rows accessible after the filtering process.\n * @category Filtering\n */\nexport const gridFilteredDescendantRowCountSelector = createSelector(gridFilteredRowCountSelector, gridFilteredTopLevelRowCountSelector, (totalRowCount, topLevelRowCount) => totalRowCount - topLevelRowCount);\n\n/**\n * @category Filtering\n * @ignore - do not document.\n */\nexport const gridFilterActiveItemsSelector = createSelectorMemoized(gridFilterModelSelector, gridColumnLookupSelector, (filterModel, columnLookup) => filterModel.items?.filter(item => {\n  if (!item.field) {\n    return false;\n  }\n  const column = columnLookup[item.field];\n  if (!column?.filterOperators || column?.filterOperators?.length === 0) {\n    return false;\n  }\n  const filterOperator = column.filterOperators.find(operator => operator.value === item.operator);\n  if (!filterOperator) {\n    return false;\n  }\n  return !filterOperator.InputComponent || item.value != null && item.value?.toString() !== '';\n}));\n/**\n * @category Filtering\n * @ignore - do not document.\n */\nexport const gridFilterActiveItemsLookupSelector = createSelectorMemoized(gridFilterActiveItemsSelector, activeFilters => {\n  const result = activeFilters.reduce((res, filterItem) => {\n    if (!res[filterItem.field]) {\n      res[filterItem.field] = [filterItem];\n    } else {\n      res[filterItem.field].push(filterItem);\n    }\n    return res;\n  }, {});\n  return result;\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}