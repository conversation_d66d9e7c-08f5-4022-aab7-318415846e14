{"ast": null, "code": "import * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { useGridApiContext } from \"../../../../hooks/utils/useGridApiContext.js\";\nimport { useGridRootProps } from \"../../../../hooks/utils/useGridRootProps.js\";\nimport { gridVisibleColumnDefinitionsSelector } from \"../../../../hooks/features/columns/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction GridColumnMenuHideItem(props) {\n  const {\n    colDef,\n    onClick\n  } = props;\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const visibleColumns = gridVisibleColumnDefinitionsSelector(apiRef);\n  const columnsWithMenu = visibleColumns.filter(col => col.disableColumnMenu !== true);\n  // do not allow to hide the last column with menu\n  const disabled = columnsWithMenu.length === 1;\n  const toggleColumn = React.useCallback(event => {\n    /**\n     * Disabled `MenuItem` would trigger `click` event\n     * after imperative `.click()` call on HTML element.\n     * Also, click is triggered in testing environment as well.\n     */\n    if (disabled) {\n      return;\n    }\n    apiRef.current.setColumnVisibility(colDef.field, false);\n    onClick(event);\n  }, [apiRef, colDef.field, onClick, disabled]);\n  if (rootProps.disableColumnSelector) {\n    return null;\n  }\n  if (colDef.hideable === false) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(rootProps.slots.baseMenuItem, {\n    onClick: toggleColumn,\n    disabled: disabled,\n    iconStart: /*#__PURE__*/_jsx(rootProps.slots.columnMenuHideIcon, {\n      fontSize: \"small\"\n    }),\n    children: apiRef.current.getLocaleText('columnMenuHideColumn')\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GridColumnMenuHideItem.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  colDef: PropTypes.object.isRequired,\n  onClick: PropTypes.func.isRequired\n} : void 0;\nexport { GridColumnMenuHideItem };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}