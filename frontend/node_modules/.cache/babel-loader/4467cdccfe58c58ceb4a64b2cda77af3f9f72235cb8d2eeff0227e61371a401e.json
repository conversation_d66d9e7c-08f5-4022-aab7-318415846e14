{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport useId from '@mui/utils/useId';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport useForkRef from '@mui/utils/useForkRef';\nimport { useGridSelector } from \"../../hooks/utils/useGridSelector.js\";\nimport { gridPreferencePanelStateSelector } from \"../../hooks/features/preferencesPanel/gridPreferencePanelSelector.js\";\nimport { GridPreferencePanelsValue } from \"../../hooks/features/preferencesPanel/gridPreferencePanelsValue.js\";\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { useGridPanelContext } from \"../panel/GridPanelContext.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * @deprecated Use the {@link https://mui.com/x/react-data-grid/components/columns-panel/ Columns Panel Trigger} component instead. This component will be removed in a future major release.\n */\nconst GridToolbarColumnsButton = forwardRef(function GridToolbarColumnsButton(props, ref) {\n  const {\n    slotProps = {}\n  } = props;\n  const buttonProps = slotProps.button || {};\n  const tooltipProps = slotProps.tooltip || {};\n  const columnButtonId = useId();\n  const columnPanelId = useId();\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const {\n    columnsPanelTriggerRef\n  } = useGridPanelContext();\n  const preferencePanel = useGridSelector(apiRef, gridPreferencePanelStateSelector);\n  const handleRef = useForkRef(ref, columnsPanelTriggerRef);\n  const showColumns = event => {\n    if (preferencePanel.open && preferencePanel.openedPanelValue === GridPreferencePanelsValue.columns) {\n      apiRef.current.hidePreferences();\n    } else {\n      apiRef.current.showPreferences(GridPreferencePanelsValue.columns, columnPanelId, columnButtonId);\n    }\n    buttonProps.onClick?.(event);\n  };\n\n  // Disable the button if the corresponding is disabled\n  if (rootProps.disableColumnSelector) {\n    return null;\n  }\n  const isOpen = preferencePanel.open && preferencePanel.panelId === columnPanelId;\n  return /*#__PURE__*/_jsx(rootProps.slots.baseTooltip, _extends({\n    title: apiRef.current.getLocaleText('toolbarColumnsLabel'),\n    enterDelay: 1000\n  }, rootProps.slotProps?.baseTooltip, tooltipProps, {\n    children: /*#__PURE__*/_jsx(rootProps.slots.baseButton, _extends({\n      id: columnButtonId,\n      size: \"small\",\n      \"aria-label\": apiRef.current.getLocaleText('toolbarColumnsLabel'),\n      \"aria-haspopup\": \"menu\",\n      \"aria-expanded\": isOpen,\n      \"aria-controls\": isOpen ? columnPanelId : undefined,\n      startIcon: /*#__PURE__*/_jsx(rootProps.slots.columnSelectorIcon, {})\n    }, rootProps.slotProps?.baseButton, buttonProps, {\n      onPointerUp: event => {\n        if (preferencePanel.open) {\n          event.stopPropagation();\n        }\n        buttonProps.onPointerUp?.(event);\n      },\n      onClick: showColumns,\n      ref: handleRef,\n      children: apiRef.current.getLocaleText('toolbarColumns')\n    }))\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridToolbarColumnsButton.displayName = \"GridToolbarColumnsButton\";\nprocess.env.NODE_ENV !== \"production\" ? GridToolbarColumnsButton.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.object\n} : void 0;\nexport { GridToolbarColumnsButton };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}