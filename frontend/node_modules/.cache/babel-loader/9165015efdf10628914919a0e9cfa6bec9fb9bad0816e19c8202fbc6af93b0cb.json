{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"item\", \"applyValue\", \"type\", \"apiRef\", \"focusElementRef\", \"slotProps\", \"isFilterActive\", \"headerFilterMenu\", \"clearButton\", \"tabIndex\", \"disabled\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport useId from '@mui/utils/useId';\nimport { useTimeout } from \"../../../hooks/utils/useTimeout.js\";\nimport { useGridRootProps } from \"../../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction convertFilterItemValueToInputValue(itemValue, inputType) {\n  if (itemValue == null) {\n    return '';\n  }\n  const dateCopy = new Date(itemValue);\n  if (Number.isNaN(dateCopy.getTime())) {\n    return '';\n  }\n  if (inputType === 'date') {\n    return dateCopy.toISOString().substring(0, 10);\n  }\n  if (inputType === 'datetime-local') {\n    // The date picker expects the date to be in the local timezone.\n    // But .toISOString() converts it to UTC with zero offset.\n    // So we need to subtract the timezone offset.\n    dateCopy.setMinutes(dateCopy.getMinutes() - dateCopy.getTimezoneOffset());\n    return dateCopy.toISOString().substring(0, 19);\n  }\n  return dateCopy.toISOString().substring(0, 10);\n}\nfunction GridFilterInputDate(props) {\n  const {\n      item,\n      applyValue,\n      type,\n      apiRef,\n      focusElementRef,\n      slotProps,\n      headerFilterMenu,\n      clearButton,\n      tabIndex,\n      disabled\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const rootSlotProps = slotProps?.root.slotProps;\n  const filterTimeout = useTimeout();\n  const [filterValueState, setFilterValueState] = React.useState(() => convertFilterItemValueToInputValue(item.value, type));\n  const [applying, setIsApplying] = React.useState(false);\n  const id = useId();\n  const rootProps = useGridRootProps();\n  const onFilterChange = React.useCallback(event => {\n    filterTimeout.clear();\n    const value = event.target.value;\n    setFilterValueState(value);\n    setIsApplying(true);\n    filterTimeout.start(rootProps.filterDebounceMs, () => {\n      const date = new Date(value);\n      applyValue(_extends({}, item, {\n        value: Number.isNaN(date.getTime()) ? undefined : date\n      }));\n      setIsApplying(false);\n    });\n  }, [applyValue, item, rootProps.filterDebounceMs, filterTimeout]);\n  React.useEffect(() => {\n    const value = convertFilterItemValueToInputValue(item.value, type);\n    setFilterValueState(value);\n  }, [item.value, type]);\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(rootProps.slots.baseTextField, _extends({\n      fullWidth: true,\n      id: id,\n      label: apiRef.current.getLocaleText('filterPanelInputLabel'),\n      placeholder: apiRef.current.getLocaleText('filterPanelInputPlaceholder'),\n      value: filterValueState,\n      onChange: onFilterChange,\n      type: type || 'text',\n      disabled: disabled,\n      inputRef: focusElementRef,\n      slotProps: _extends({}, rootSlotProps, {\n        input: _extends({\n          endAdornment: applying ? /*#__PURE__*/_jsx(rootProps.slots.loadIcon, {\n            fontSize: \"small\",\n            color: \"action\"\n          }) : null\n        }, rootSlotProps?.input),\n        htmlInput: _extends({\n          max: type === 'datetime-local' ? '9999-12-31T23:59' : '9999-12-31',\n          tabIndex\n        }, rootSlotProps?.htmlInput)\n      })\n    }, rootProps.slotProps?.baseTextField, other, slotProps?.root)), headerFilterMenu, clearButton]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GridFilterInputDate.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  apiRef: PropTypes.shape({\n    current: PropTypes.object.isRequired\n  }).isRequired,\n  applyValue: PropTypes.func.isRequired,\n  className: PropTypes.string,\n  clearButton: PropTypes.node,\n  disabled: PropTypes.bool,\n  focusElementRef: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.func, PropTypes.object]),\n  headerFilterMenu: PropTypes.node,\n  inputRef: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n    current: (props, propName) => {\n      if (props[propName] == null) {\n        return null;\n      }\n      if (typeof props[propName] !== 'object' || props[propName].nodeType !== 1) {\n        return new Error(`Expected prop '${propName}' to be of type Element`);\n      }\n      return null;\n    }\n  })]),\n  /**\n   * It is `true` if the filter either has a value or an operator with no value\n   * required is selected (for example `isEmpty`)\n   */\n  isFilterActive: PropTypes.bool,\n  item: PropTypes.shape({\n    field: PropTypes.string.isRequired,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    operator: PropTypes.string.isRequired,\n    value: PropTypes.any\n  }).isRequired,\n  onBlur: PropTypes.func,\n  onFocus: PropTypes.func,\n  slotProps: PropTypes.object,\n  tabIndex: PropTypes.number,\n  type: PropTypes.oneOf(['date', 'datetime-local'])\n} : void 0;\nexport { GridFilterInputDate };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}