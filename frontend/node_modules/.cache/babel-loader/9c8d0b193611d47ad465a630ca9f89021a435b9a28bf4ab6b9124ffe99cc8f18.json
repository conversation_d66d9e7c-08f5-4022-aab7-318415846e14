{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport useId from '@mui/utils/useId';\nimport useForkRef from '@mui/utils/useForkRef';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { gridDensitySelector } from \"../../hooks/features/density/densitySelector.js\";\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { useGridSelector } from \"../../hooks/utils/useGridSelector.js\";\nimport { GridMenu } from \"../menu/GridMenu.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { gridClasses } from \"../../constants/gridClasses.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\n/**\n * @deprecated See {@link https://mui.com/x/react-data-grid/accessibility/#set-the-density-programmatically Accessibility—Set the density programmatically} for an example of adding a density selector to the toolbar. This component will be removed in a future major release.\n */\nconst GridToolbarDensitySelector = forwardRef(function GridToolbarDensitySelector(props, ref) {\n  const {\n    slotProps = {}\n  } = props;\n  const buttonProps = slotProps.button || {};\n  const tooltipProps = slotProps.tooltip || {};\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const density = useGridSelector(apiRef, gridDensitySelector);\n  const densityButtonId = useId();\n  const densityMenuId = useId();\n  const [open, setOpen] = React.useState(false);\n  const buttonRef = React.useRef(null);\n  const handleRef = useForkRef(ref, buttonRef);\n  const densityOptions = [{\n    icon: /*#__PURE__*/_jsx(rootProps.slots.densityCompactIcon, {}),\n    label: apiRef.current.getLocaleText('toolbarDensityCompact'),\n    value: 'compact'\n  }, {\n    icon: /*#__PURE__*/_jsx(rootProps.slots.densityStandardIcon, {}),\n    label: apiRef.current.getLocaleText('toolbarDensityStandard'),\n    value: 'standard'\n  }, {\n    icon: /*#__PURE__*/_jsx(rootProps.slots.densityComfortableIcon, {}),\n    label: apiRef.current.getLocaleText('toolbarDensityComfortable'),\n    value: 'comfortable'\n  }];\n  const startIcon = React.useMemo(() => {\n    switch (density) {\n      case 'compact':\n        return /*#__PURE__*/_jsx(rootProps.slots.densityCompactIcon, {});\n      case 'comfortable':\n        return /*#__PURE__*/_jsx(rootProps.slots.densityComfortableIcon, {});\n      default:\n        return /*#__PURE__*/_jsx(rootProps.slots.densityStandardIcon, {});\n    }\n  }, [density, rootProps]);\n  const handleDensitySelectorOpen = event => {\n    setOpen(prevOpen => !prevOpen);\n    buttonProps.onClick?.(event);\n  };\n  const handleDensitySelectorClose = () => {\n    setOpen(false);\n  };\n  const handleDensityUpdate = newDensity => {\n    apiRef.current.setDensity(newDensity);\n    setOpen(false);\n  };\n\n  // Disable the button if the corresponding is disabled\n  if (rootProps.disableDensitySelector) {\n    return null;\n  }\n  const densityElements = densityOptions.map((option, index) => /*#__PURE__*/_jsx(rootProps.slots.baseMenuItem, {\n    onClick: () => handleDensityUpdate(option.value),\n    selected: option.value === density,\n    iconStart: option.icon,\n    children: option.label\n  }, index));\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(rootProps.slots.baseTooltip, _extends({\n      title: apiRef.current.getLocaleText('toolbarDensityLabel'),\n      enterDelay: 1000\n    }, rootProps.slotProps?.baseTooltip, tooltipProps, {\n      children: /*#__PURE__*/_jsx(rootProps.slots.baseButton, _extends({\n        size: \"small\",\n        startIcon: startIcon,\n        \"aria-label\": apiRef.current.getLocaleText('toolbarDensityLabel'),\n        \"aria-haspopup\": \"menu\",\n        \"aria-expanded\": open,\n        \"aria-controls\": open ? densityMenuId : undefined,\n        id: densityButtonId\n      }, rootProps.slotProps?.baseButton, buttonProps, {\n        onClick: handleDensitySelectorOpen,\n        ref: handleRef,\n        children: apiRef.current.getLocaleText('toolbarDensity')\n      }))\n    })), /*#__PURE__*/_jsx(GridMenu, {\n      open: open,\n      target: buttonRef.current,\n      onClose: handleDensitySelectorClose,\n      position: \"bottom-end\",\n      children: /*#__PURE__*/_jsx(rootProps.slots.baseMenuList, {\n        id: densityMenuId,\n        className: gridClasses.menuList,\n        \"aria-labelledby\": densityButtonId,\n        autoFocusItem: open,\n        children: densityElements\n      })\n    })]\n  });\n});\nif (process.env.NODE_ENV !== \"production\") GridToolbarDensitySelector.displayName = \"GridToolbarDensitySelector\";\nprocess.env.NODE_ENV !== \"production\" ? GridToolbarDensitySelector.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.object\n} : void 0;\nexport { GridToolbarDensitySelector };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}