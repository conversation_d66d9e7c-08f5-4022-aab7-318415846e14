{"ast": null, "code": "import { GRID_STRING_COL_DEF } from \"./gridStringColDef.js\";\nimport { GRID_NUMERIC_COL_DEF } from \"./gridNumericColDef.js\";\nimport { GRID_DATE_COL_DEF, GRID_DATETIME_COL_DEF } from \"./gridDateColDef.js\";\nimport { GRID_BOOLEAN_COL_DEF } from \"./gridBooleanColDef.js\";\nimport { GRID_SINGLE_SELECT_COL_DEF } from \"./gridSingleSelectColDef.js\";\nimport { GRID_ACTIONS_COL_DEF, GRID_ACTIONS_COLUMN_TYPE } from \"./gridActionsColDef.js\";\nexport const DEFAULT_GRID_COL_TYPE_KEY = 'string';\nexport const getGridDefaultColumnTypes = () => {\n  const nativeColumnTypes = {\n    string: GRID_STRING_COL_DEF,\n    number: GRID_NUMERIC_COL_DEF,\n    date: GRID_DATE_COL_DEF,\n    dateTime: GRID_DATETIME_COL_DEF,\n    boolean: GRID_BOOLEAN_COL_DEF,\n    singleSelect: GRID_SINGLE_SELECT_COL_DEF,\n    [GRID_ACTIONS_COLUMN_TYPE]: GRID_ACTIONS_COL_DEF,\n    custom: GRID_STRING_COL_DEF\n  };\n  return nativeColumnTypes;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}