{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nexport const propsStateInitializer = (state, props) => {\n  return _extends({}, state, {\n    props: {\n      getRowId: props.getRowId\n    }\n  });\n};\nexport const useGridProps = (apiRef, props) => {\n  React.useEffect(() => {\n    apiRef.current.setState(state => _extends({}, state, {\n      props: {\n        getRowId: props.getRowId\n      }\n    }));\n  }, [apiRef, props.getRowId]);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}