{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport { useGridEvent } from \"../../utils/useGridEvent.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { gridColumnLookupSelector } from \"../columns/gridColumnsSelector.js\";\nimport { gridSortedRowEntriesSelector, gridSortedRowIdsSelector, gridSortModelSelector } from \"./gridSortingSelector.js\";\nimport { GRID_ROOT_GROUP_ID, gridRowTreeSelector } from \"../rows/index.js\";\nimport { useFirstRender } from \"../../utils/useFirstRender.js\";\nimport { useGridRegisterStrategyProcessor, GRID_DEFAULT_STRATEGY } from \"../../core/strategyProcessing/index.js\";\nimport { buildAggregatedSortingApplier, mergeStateWithSortModel, getNextGridSortDirection, sanitizeSortModel } from \"./gridSortingUtils.js\";\nimport { useGridRegisterPipeProcessor } from \"../../core/pipeProcessing/index.js\";\nimport { getTreeNodeDescendants } from \"../rows/gridRowsUtils.js\";\nexport const sortingStateInitializer = (state, props) => {\n  const sortModel = props.sortModel ?? props.initialState?.sorting?.sortModel ?? [];\n  return _extends({}, state, {\n    sorting: {\n      sortModel: sanitizeSortModel(sortModel, props.disableMultipleColumnsSorting),\n      sortedRows: []\n    }\n  });\n};\n\n/**\n * @requires useGridRows (event)\n * @requires useGridColumns (event)\n */\nexport const useGridSorting = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridSorting');\n  apiRef.current.registerControlState({\n    stateId: 'sortModel',\n    propModel: props.sortModel,\n    propOnChange: props.onSortModelChange,\n    stateSelector: gridSortModelSelector,\n    changeEvent: 'sortModelChange'\n  });\n  const upsertSortModel = React.useCallback((field, sortItem) => {\n    const sortModel = gridSortModelSelector(apiRef);\n    const existingIdx = sortModel.findIndex(c => c.field === field);\n    let newSortModel = [...sortModel];\n    if (existingIdx > -1) {\n      if (sortItem?.sort == null) {\n        newSortModel.splice(existingIdx, 1);\n      } else {\n        newSortModel.splice(existingIdx, 1, sortItem);\n      }\n    } else {\n      newSortModel = [...sortModel, sortItem];\n    }\n    return newSortModel;\n  }, [apiRef]);\n  const createSortItem = React.useCallback((col, directionOverride) => {\n    const sortModel = gridSortModelSelector(apiRef);\n    const existing = sortModel.find(c => c.field === col.field);\n    if (existing) {\n      const nextSort = directionOverride === undefined ? getNextGridSortDirection(col.sortingOrder ?? props.sortingOrder, existing.sort) : directionOverride;\n      return nextSort === undefined ? undefined : _extends({}, existing, {\n        sort: nextSort\n      });\n    }\n    return {\n      field: col.field,\n      sort: directionOverride === undefined ? getNextGridSortDirection(col.sortingOrder ?? props.sortingOrder) : directionOverride\n    };\n  }, [apiRef, props.sortingOrder]);\n  const addColumnMenuItem = React.useCallback((columnMenuItems, colDef) => {\n    if (colDef == null || colDef.sortable === false || props.disableColumnSorting) {\n      return columnMenuItems;\n    }\n    const sortingOrder = colDef.sortingOrder || props.sortingOrder;\n    if (sortingOrder.some(item => !!item)) {\n      return [...columnMenuItems, 'columnMenuSortItem'];\n    }\n    return columnMenuItems;\n  }, [props.sortingOrder, props.disableColumnSorting]);\n\n  /**\n   * API METHODS\n   */\n  const applySorting = React.useCallback(() => {\n    apiRef.current.setState(state => {\n      if (props.sortingMode === 'server') {\n        logger.debug('Skipping sorting rows as sortingMode = server');\n        return _extends({}, state, {\n          sorting: _extends({}, state.sorting, {\n            sortedRows: getTreeNodeDescendants(gridRowTreeSelector(apiRef), GRID_ROOT_GROUP_ID, false)\n          })\n        });\n      }\n      const sortModel = gridSortModelSelector(apiRef);\n      const sortRowList = buildAggregatedSortingApplier(sortModel, apiRef);\n      const sortedRows = apiRef.current.applyStrategyProcessor('sorting', {\n        sortRowList\n      });\n      return _extends({}, state, {\n        sorting: _extends({}, state.sorting, {\n          sortedRows\n        })\n      });\n    });\n    apiRef.current.publishEvent('sortedRowsSet');\n  }, [apiRef, logger, props.sortingMode]);\n  const setSortModel = React.useCallback(model => {\n    const currentModel = gridSortModelSelector(apiRef);\n    if (currentModel !== model) {\n      logger.debug(`Setting sort model`);\n      apiRef.current.setState(mergeStateWithSortModel(model, props.disableMultipleColumnsSorting));\n      apiRef.current.applySorting();\n    }\n  }, [apiRef, logger, props.disableMultipleColumnsSorting]);\n  const sortColumn = React.useCallback((field, direction, allowMultipleSorting) => {\n    const column = apiRef.current.getColumn(field);\n    const sortItem = createSortItem(column, direction);\n    let sortModel;\n    if (!allowMultipleSorting || props.disableMultipleColumnsSorting) {\n      sortModel = sortItem?.sort == null ? [] : [sortItem];\n    } else {\n      sortModel = upsertSortModel(column.field, sortItem);\n    }\n    apiRef.current.setSortModel(sortModel);\n  }, [apiRef, upsertSortModel, createSortItem, props.disableMultipleColumnsSorting]);\n  const getSortModel = React.useCallback(() => gridSortModelSelector(apiRef), [apiRef]);\n  const getSortedRows = React.useCallback(() => {\n    const sortedRows = gridSortedRowEntriesSelector(apiRef);\n    return sortedRows.map(row => row.model);\n  }, [apiRef]);\n  const getSortedRowIds = React.useCallback(() => gridSortedRowIdsSelector(apiRef), [apiRef]);\n  const getRowIdFromRowIndex = React.useCallback(index => apiRef.current.getSortedRowIds()[index], [apiRef]);\n  const sortApi = {\n    getSortModel,\n    getSortedRows,\n    getSortedRowIds,\n    getRowIdFromRowIndex,\n    setSortModel,\n    sortColumn,\n    applySorting\n  };\n  useGridApiMethod(apiRef, sortApi, 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    const sortModelToExport = gridSortModelSelector(apiRef);\n    const shouldExportSortModel =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the model is controlled\n    props.sortModel != null ||\n    // Always export if the model has been initialized\n    props.initialState?.sorting?.sortModel != null ||\n    // Export if the model is not empty\n    sortModelToExport.length > 0;\n    if (!shouldExportSortModel) {\n      return prevState;\n    }\n    return _extends({}, prevState, {\n      sorting: {\n        sortModel: sortModelToExport\n      }\n    });\n  }, [apiRef, props.sortModel, props.initialState?.sorting?.sortModel]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    const sortModel = context.stateToRestore.sorting?.sortModel;\n    if (sortModel == null) {\n      return params;\n    }\n    apiRef.current.setState(mergeStateWithSortModel(sortModel, props.disableMultipleColumnsSorting));\n    return _extends({}, params, {\n      callbacks: [...params.callbacks, apiRef.current.applySorting]\n    });\n  }, [apiRef, props.disableMultipleColumnsSorting]);\n  const flatSortingMethod = React.useCallback(params => {\n    const rowTree = gridRowTreeSelector(apiRef);\n    const rootGroupNode = rowTree[GRID_ROOT_GROUP_ID];\n    const sortedChildren = params.sortRowList ? params.sortRowList(rootGroupNode.children.map(childId => rowTree[childId])) : [...rootGroupNode.children];\n    if (rootGroupNode.footerId != null) {\n      sortedChildren.push(rootGroupNode.footerId);\n    }\n    return sortedChildren;\n  }, [apiRef]);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n  useGridRegisterStrategyProcessor(apiRef, GRID_DEFAULT_STRATEGY, 'sorting', flatSortingMethod);\n\n  /**\n   * EVENTS\n   */\n  const handleColumnHeaderClick = React.useCallback(({\n    field,\n    colDef\n  }, event) => {\n    if (!colDef.sortable || props.disableColumnSorting) {\n      return;\n    }\n    const allowMultipleSorting = props.multipleColumnsSortingMode === 'always' || event.shiftKey || event.metaKey || event.ctrlKey;\n    sortColumn(field, undefined, allowMultipleSorting);\n  }, [sortColumn, props.disableColumnSorting, props.multipleColumnsSortingMode]);\n  const handleColumnHeaderKeyDown = React.useCallback(({\n    field,\n    colDef\n  }, event) => {\n    if (!colDef.sortable || props.disableColumnSorting) {\n      return;\n    }\n    // Ctrl + Enter opens the column menu\n    if (event.key === 'Enter' && !event.ctrlKey && !event.metaKey) {\n      sortColumn(field, undefined, props.multipleColumnsSortingMode === 'always' || event.shiftKey);\n    }\n  }, [sortColumn, props.disableColumnSorting, props.multipleColumnsSortingMode]);\n  const handleColumnsChange = React.useCallback(() => {\n    // When the columns change we check that the sorted columns are still part of the dataset\n    const sortModel = gridSortModelSelector(apiRef);\n    const latestColumns = gridColumnLookupSelector(apiRef);\n    if (sortModel.length > 0) {\n      const newModel = sortModel.filter(sortItem => latestColumns[sortItem.field]);\n      if (newModel.length < sortModel.length) {\n        apiRef.current.setSortModel(newModel);\n      }\n    }\n  }, [apiRef]);\n  const handleStrategyProcessorChange = React.useCallback(methodName => {\n    if (methodName === 'sorting') {\n      apiRef.current.applySorting();\n    }\n  }, [apiRef]);\n  useGridRegisterPipeProcessor(apiRef, 'columnMenu', addColumnMenuItem);\n  useGridEvent(apiRef, 'columnHeaderClick', handleColumnHeaderClick);\n  useGridEvent(apiRef, 'columnHeaderKeyDown', handleColumnHeaderKeyDown);\n  useGridEvent(apiRef, 'rowsSet', apiRef.current.applySorting);\n  useGridEvent(apiRef, 'columnsChange', handleColumnsChange);\n  useGridEvent(apiRef, 'activeStrategyProcessorChange', handleStrategyProcessorChange);\n\n  /**\n   * 1ST RENDER\n   */\n  useFirstRender(() => {\n    apiRef.current.applySorting();\n  });\n\n  /**\n   * EFFECTS\n   */\n  useEnhancedEffect(() => {\n    if (props.sortModel !== undefined) {\n      apiRef.current.setSortModel(props.sortModel);\n    }\n  }, [apiRef, props.sortModel]);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}