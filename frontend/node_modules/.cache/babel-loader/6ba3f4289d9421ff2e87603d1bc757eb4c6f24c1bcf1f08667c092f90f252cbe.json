{"ast": null, "code": "import { GridFilterInputValue } from \"../components/panel/filterPanel/GridFilterInputValue.js\";\nimport { escapeRegExp } from \"../utils/utils.js\";\nimport { GridFilterInputMultipleValue } from \"../components/panel/filterPanel/GridFilterInputMultipleValue.js\";\nimport { removeDiacritics } from \"../hooks/features/filter/gridFilterUtils.js\";\nexport const getGridStringQuickFilterFn = value => {\n  if (!value) {\n    return null;\n  }\n  const filterRegex = new RegExp(escapeRegExp(value), 'i');\n  return (_, row, column, apiRef) => {\n    let columnValue = apiRef.current.getRowFormattedValue(row, column);\n    if (apiRef.current.ignoreDiacritics) {\n      columnValue = removeDiacritics(columnValue);\n    }\n    return columnValue != null ? filterRegex.test(columnValue.toString()) : false;\n  };\n};\nconst createContainsFilterFn = (disableTrim, negate) => filterItem => {\n  if (!filterItem.value) {\n    return null;\n  }\n  const trimmedValue = disableTrim ? filterItem.value : filterItem.value.trim();\n  const filterRegex = new RegExp(escapeRegExp(trimmedValue), 'i');\n  return value => {\n    if (value == null) {\n      return negate;\n    }\n    const matches = filterRegex.test(String(value));\n    return negate ? !matches : matches;\n  };\n};\nconst createEqualityFilterFn = (disableTrim, negate) => filterItem => {\n  if (!filterItem.value) {\n    return null;\n  }\n  const trimmedValue = disableTrim ? filterItem.value : filterItem.value.trim();\n  const collator = new Intl.Collator(undefined, {\n    sensitivity: 'base',\n    usage: 'search'\n  });\n  return value => {\n    if (value == null) {\n      return negate;\n    }\n    const isEqual = collator.compare(trimmedValue, value.toString()) === 0;\n    return negate ? !isEqual : isEqual;\n  };\n};\nconst createEmptyFilterFn = negate => () => {\n  return value => {\n    const isEmpty = value === '' || value == null;\n    return negate ? !isEmpty : isEmpty;\n  };\n};\nexport const getGridStringOperators = (disableTrim = false) => [{\n  value: 'contains',\n  getApplyFilterFn: createContainsFilterFn(disableTrim, false),\n  InputComponent: GridFilterInputValue\n}, {\n  value: 'doesNotContain',\n  getApplyFilterFn: createContainsFilterFn(disableTrim, true),\n  InputComponent: GridFilterInputValue\n}, {\n  value: 'equals',\n  getApplyFilterFn: createEqualityFilterFn(disableTrim, false),\n  InputComponent: GridFilterInputValue\n}, {\n  value: 'doesNotEqual',\n  getApplyFilterFn: createEqualityFilterFn(disableTrim, true),\n  InputComponent: GridFilterInputValue\n}, {\n  value: 'startsWith',\n  getApplyFilterFn: filterItem => {\n    if (!filterItem.value) {\n      return null;\n    }\n    const filterItemValue = disableTrim ? filterItem.value : filterItem.value.trim();\n    const filterRegex = new RegExp(`^${escapeRegExp(filterItemValue)}.*$`, 'i');\n    return value => {\n      return value != null ? filterRegex.test(value.toString()) : false;\n    };\n  },\n  InputComponent: GridFilterInputValue\n}, {\n  value: 'endsWith',\n  getApplyFilterFn: filterItem => {\n    if (!filterItem.value) {\n      return null;\n    }\n    const filterItemValue = disableTrim ? filterItem.value : filterItem.value.trim();\n    const filterRegex = new RegExp(`.*${escapeRegExp(filterItemValue)}$`, 'i');\n    return value => {\n      return value != null ? filterRegex.test(value.toString()) : false;\n    };\n  },\n  InputComponent: GridFilterInputValue\n}, {\n  value: 'isEmpty',\n  getApplyFilterFn: createEmptyFilterFn(false),\n  requiresFilterValue: false\n}, {\n  value: 'isNotEmpty',\n  getApplyFilterFn: createEmptyFilterFn(true),\n  requiresFilterValue: false\n}, {\n  value: 'isAnyOf',\n  getApplyFilterFn: filterItem => {\n    if (!Array.isArray(filterItem.value) || filterItem.value.length === 0) {\n      return null;\n    }\n    const filterItemValue = disableTrim ? filterItem.value : filterItem.value.map(val => val.trim());\n    const collator = new Intl.Collator(undefined, {\n      sensitivity: 'base',\n      usage: 'search'\n    });\n    return value => value != null ? filterItemValue.some(filterValue => {\n      return collator.compare(filterValue, value.toString() || '') === 0;\n    }) : false;\n  },\n  InputComponent: GridFilterInputMultipleValue\n}];", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}