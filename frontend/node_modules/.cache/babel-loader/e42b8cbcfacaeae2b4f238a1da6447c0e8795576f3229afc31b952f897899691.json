{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport resolveProps from '@mui/utils/resolveProps';\nimport { DEFAULT_GRID_COL_TYPE_KEY, GRID_STRING_COL_DEF, getGridDefaultColumnTypes } from \"../../../colDef/index.js\";\nimport { gridColumnsStateSelector, gridColumnVisibilityModelSelector } from \"./gridColumnsSelector.js\";\nimport { clamp } from \"../../../utils/utils.js\";\nimport { gridDensityFactorSelector } from \"../density/densitySelector.js\";\nimport { gridHeaderFilteringEnabledSelector } from \"../headerFiltering/gridHeaderFilteringSelectors.js\";\nimport { gridColumnGroupsHeaderMaxDepthSelector } from \"../columnGrouping/gridColumnGroupsSelector.js\";\nexport const COLUMNS_DIMENSION_PROPERTIES = ['maxWidth', 'minWidth', 'width', 'flex'];\nconst COLUMN_TYPES = getGridDefaultColumnTypes();\n\n/**\n * Computes width for flex columns.\n * Based on CSS Flexbox specification:\n * https://drafts.csswg.org/css-flexbox-1/#resolve-flexible-lengths\n */\nexport function computeFlexColumnsWidth({\n  initialFreeSpace,\n  totalFlexUnits,\n  flexColumns\n}) {\n  const uniqueFlexColumns = new Set(flexColumns.map(col => col.field));\n  const flexColumnsLookup = {\n    all: {},\n    frozenFields: [],\n    freeze: field => {\n      const value = flexColumnsLookup.all[field];\n      if (value && value.frozen !== true) {\n        flexColumnsLookup.all[field].frozen = true;\n        flexColumnsLookup.frozenFields.push(field);\n      }\n    }\n  };\n\n  // Step 5 of https://drafts.csswg.org/css-flexbox-1/#resolve-flexible-lengths\n  function loopOverFlexItems() {\n    // 5a: If all the flex items on the line are frozen, free space has been distributed.\n    if (flexColumnsLookup.frozenFields.length === uniqueFlexColumns.size) {\n      return;\n    }\n    const violationsLookup = {\n      min: {},\n      max: {}\n    };\n    let remainingFreeSpace = initialFreeSpace;\n    let flexUnits = totalFlexUnits;\n    let totalViolation = 0;\n\n    // 5b: Calculate the remaining free space\n    flexColumnsLookup.frozenFields.forEach(field => {\n      remainingFreeSpace -= flexColumnsLookup.all[field].computedWidth;\n      flexUnits -= flexColumnsLookup.all[field].flex;\n    });\n    for (let i = 0; i < flexColumns.length; i += 1) {\n      const column = flexColumns[i];\n      if (flexColumnsLookup.all[column.field] && flexColumnsLookup.all[column.field].frozen === true) {\n        continue;\n      }\n\n      // 5c: Distribute remaining free space proportional to the flex factors\n      const widthPerFlexUnit = remainingFreeSpace / flexUnits;\n      let computedWidth = widthPerFlexUnit * column.flex;\n\n      // 5d: Fix min/max violations\n      if (computedWidth < column.minWidth) {\n        totalViolation += column.minWidth - computedWidth;\n        computedWidth = column.minWidth;\n        violationsLookup.min[column.field] = true;\n      } else if (computedWidth > column.maxWidth) {\n        totalViolation += column.maxWidth - computedWidth;\n        computedWidth = column.maxWidth;\n        violationsLookup.max[column.field] = true;\n      }\n      flexColumnsLookup.all[column.field] = {\n        frozen: false,\n        computedWidth,\n        flex: column.flex\n      };\n    }\n\n    // 5e: Freeze over-flexed items\n    if (totalViolation < 0) {\n      // Freeze all the items with max violations\n      Object.keys(violationsLookup.max).forEach(field => {\n        flexColumnsLookup.freeze(field);\n      });\n    } else if (totalViolation > 0) {\n      // Freeze all the items with min violations\n      Object.keys(violationsLookup.min).forEach(field => {\n        flexColumnsLookup.freeze(field);\n      });\n    } else {\n      // Freeze all items\n      flexColumns.forEach(({\n        field\n      }) => {\n        flexColumnsLookup.freeze(field);\n      });\n    }\n\n    // 5f: Return to the start of this loop\n    loopOverFlexItems();\n  }\n  loopOverFlexItems();\n  return flexColumnsLookup.all;\n}\n\n/**\n * Compute the `computedWidth` (ie: the width the column should have during rendering) based on the `width` / `flex` / `minWidth` / `maxWidth` properties of `GridColDef`.\n * The columns already have been merged with there `type` default values for `minWidth`, `maxWidth` and `width`, thus the `!` for those properties below.\n * TODO: Unit test this function in depth and only keep basic cases for the whole grid testing.\n * TODO: Improve the `GridColDef` typing to reflect the fact that `minWidth` / `maxWidth` and `width` can't be null after the merge with the `type` default values.\n */\nexport const hydrateColumnsWidth = (rawState, dimensions) => {\n  const columnsLookup = {};\n  let totalFlexUnits = 0;\n  let widthAllocatedBeforeFlex = 0;\n  const flexColumns = [];\n\n  // For the non-flex columns, compute their width\n  // For the flex columns, compute their minimum width and how much width must be allocated during the flex allocation\n  rawState.orderedFields.forEach(columnField => {\n    let column = rawState.lookup[columnField];\n    let computedWidth = 0;\n    let isFlex = false;\n    if (rawState.columnVisibilityModel[columnField] !== false) {\n      if (column.flex && column.flex > 0) {\n        totalFlexUnits += column.flex;\n        isFlex = true;\n      } else {\n        computedWidth = clamp(column.width || GRID_STRING_COL_DEF.width, column.minWidth || GRID_STRING_COL_DEF.minWidth, column.maxWidth || GRID_STRING_COL_DEF.maxWidth);\n      }\n      widthAllocatedBeforeFlex += computedWidth;\n    }\n    if (column.computedWidth !== computedWidth) {\n      column = _extends({}, column, {\n        computedWidth\n      });\n    }\n    if (isFlex) {\n      flexColumns.push(column);\n    }\n    columnsLookup[columnField] = column;\n  });\n  const availableWidth = dimensions === undefined ? 0 : dimensions.viewportOuterSize.width - (dimensions.hasScrollY ? dimensions.scrollbarSize : 0);\n  const initialFreeSpace = Math.max(availableWidth - widthAllocatedBeforeFlex, 0);\n\n  // Allocate the remaining space to the flex columns\n  if (totalFlexUnits > 0 && availableWidth > 0) {\n    const computedColumnWidths = computeFlexColumnsWidth({\n      initialFreeSpace,\n      totalFlexUnits,\n      flexColumns\n    });\n    Object.keys(computedColumnWidths).forEach(field => {\n      columnsLookup[field] = _extends({}, columnsLookup[field], {\n        computedWidth: computedColumnWidths[field].computedWidth\n      });\n    });\n  }\n  return _extends({}, rawState, {\n    lookup: columnsLookup\n  });\n};\n\n/**\n * Apply the order and the dimensions of the initial state.\n * The columns not registered in `orderedFields` will be placed after the imported columns.\n */\nexport const applyInitialState = (columnsState, initialState) => {\n  if (!initialState) {\n    return columnsState;\n  }\n  const {\n    orderedFields = [],\n    dimensions = {}\n  } = initialState;\n  const columnsWithUpdatedDimensions = Object.keys(dimensions);\n  if (columnsWithUpdatedDimensions.length === 0 && orderedFields.length === 0) {\n    return columnsState;\n  }\n  const orderedFieldsLookup = {};\n  const cleanOrderedFields = [];\n  for (let i = 0; i < orderedFields.length; i += 1) {\n    const field = orderedFields[i];\n\n    // Ignores the fields in the initialState that matches no field on the current column state\n    if (columnsState.lookup[field]) {\n      orderedFieldsLookup[field] = true;\n      cleanOrderedFields.push(field);\n    }\n  }\n  const newOrderedFields = cleanOrderedFields.length === 0 ? columnsState.orderedFields : [...cleanOrderedFields, ...columnsState.orderedFields.filter(field => !orderedFieldsLookup[field])];\n  const newColumnLookup = _extends({}, columnsState.lookup);\n  for (let i = 0; i < columnsWithUpdatedDimensions.length; i += 1) {\n    const field = columnsWithUpdatedDimensions[i];\n    const newColDef = _extends({}, newColumnLookup[field], {\n      hasBeenResized: true\n    });\n    Object.entries(dimensions[field]).forEach(([key, value]) => {\n      newColDef[key] = value === -1 ? Infinity : value;\n    });\n    newColumnLookup[field] = newColDef;\n  }\n  const newColumnsState = _extends({}, columnsState, {\n    orderedFields: newOrderedFields,\n    lookup: newColumnLookup\n  });\n  return newColumnsState;\n};\nexport function getDefaultColTypeDef(type) {\n  let colDef = COLUMN_TYPES[DEFAULT_GRID_COL_TYPE_KEY];\n  if (type && COLUMN_TYPES[type]) {\n    colDef = COLUMN_TYPES[type];\n  }\n  return colDef;\n}\nexport const createColumnsState = ({\n  apiRef,\n  columnsToUpsert,\n  initialState,\n  columnVisibilityModel = gridColumnVisibilityModelSelector(apiRef),\n  keepOnlyColumnsToUpsert = false,\n  updateInitialVisibilityModel = false\n}) => {\n  const isInsideStateInitializer = !apiRef.current.state.columns;\n  let columnsState;\n  if (isInsideStateInitializer) {\n    columnsState = {\n      orderedFields: [],\n      lookup: {},\n      columnVisibilityModel,\n      initialColumnVisibilityModel: columnVisibilityModel\n    };\n  } else {\n    const currentState = gridColumnsStateSelector(apiRef);\n    columnsState = {\n      orderedFields: keepOnlyColumnsToUpsert ? [] : [...currentState.orderedFields],\n      lookup: _extends({}, currentState.lookup),\n      // Will be cleaned later if keepOnlyColumnsToUpsert=true\n      columnVisibilityModel,\n      initialColumnVisibilityModel: updateInitialVisibilityModel ? columnVisibilityModel : currentState.initialColumnVisibilityModel\n    };\n  }\n  let columnsToKeep = {};\n  if (keepOnlyColumnsToUpsert && !isInsideStateInitializer) {\n    columnsToKeep = Object.keys(columnsState.lookup).reduce((acc, key) => _extends({}, acc, {\n      [key]: false\n    }), {});\n  }\n  const columnsToUpsertLookup = {};\n  columnsToUpsert.forEach(newColumn => {\n    const {\n      field\n    } = newColumn;\n    columnsToUpsertLookup[field] = true;\n    columnsToKeep[field] = true;\n    let existingState = columnsState.lookup[field];\n    if (existingState == null) {\n      existingState = _extends({}, getDefaultColTypeDef(newColumn.type), {\n        field,\n        hasBeenResized: false\n      });\n      columnsState.orderedFields.push(field);\n    } else if (keepOnlyColumnsToUpsert) {\n      columnsState.orderedFields.push(field);\n    }\n\n    // If the column type has changed - merge the existing state with the default column type definition\n    if (existingState && existingState.type !== newColumn.type) {\n      existingState = _extends({}, getDefaultColTypeDef(newColumn.type), {\n        field\n      });\n    }\n    let hasBeenResized = existingState.hasBeenResized;\n    COLUMNS_DIMENSION_PROPERTIES.forEach(key => {\n      if (newColumn[key] !== undefined) {\n        hasBeenResized = true;\n        if (newColumn[key] === -1) {\n          newColumn[key] = Infinity;\n        }\n      }\n    });\n    columnsState.lookup[field] = resolveProps(existingState, _extends({}, getDefaultColTypeDef(newColumn.type), newColumn, {\n      hasBeenResized\n    }));\n  });\n  if (keepOnlyColumnsToUpsert && !isInsideStateInitializer) {\n    Object.keys(columnsState.lookup).forEach(field => {\n      if (!columnsToKeep[field]) {\n        delete columnsState.lookup[field];\n      }\n    });\n  }\n  const columnsStateWithPreProcessing = apiRef.current.unstable_applyPipeProcessors('hydrateColumns', columnsState);\n  const columnsStateWithPortableColumns = applyInitialState(columnsStateWithPreProcessing, initialState);\n  return hydrateColumnsWidth(columnsStateWithPortableColumns, apiRef.current.getRootDimensions?.() ?? undefined);\n};\nexport function getFirstNonSpannedColumnToRender({\n  firstColumnToRender,\n  apiRef,\n  firstRowToRender,\n  lastRowToRender,\n  visibleRows\n}) {\n  let firstNonSpannedColumnToRender = firstColumnToRender;\n  let foundStableColumn = false;\n\n  // Keep checking columns until we find one that's not spanned in any visible row\n  while (!foundStableColumn && firstNonSpannedColumnToRender >= 0) {\n    foundStableColumn = true;\n    for (let i = firstRowToRender; i < lastRowToRender; i += 1) {\n      const row = visibleRows[i];\n      if (row) {\n        const rowId = visibleRows[i].id;\n        const cellColSpanInfo = apiRef.current.unstable_getCellColSpanInfo(rowId, firstNonSpannedColumnToRender);\n        if (cellColSpanInfo && cellColSpanInfo.spannedByColSpan && cellColSpanInfo.leftVisibleCellIndex < firstNonSpannedColumnToRender) {\n          firstNonSpannedColumnToRender = cellColSpanInfo.leftVisibleCellIndex;\n          foundStableColumn = false;\n          break; // Check the new column index against the visible rows, because it might be spanned\n        }\n      }\n    }\n  }\n  return firstNonSpannedColumnToRender;\n}\nexport function getTotalHeaderHeight(apiRef, props) {\n  if (props.listView) {\n    return 0;\n  }\n  const densityFactor = gridDensityFactorSelector(apiRef);\n  const maxDepth = gridColumnGroupsHeaderMaxDepthSelector(apiRef);\n  const isHeaderFilteringEnabled = gridHeaderFilteringEnabledSelector(apiRef);\n  const columnHeadersHeight = Math.floor(props.columnHeaderHeight * densityFactor);\n  const columnGroupHeadersHeight = Math.floor((props.columnGroupHeaderHeight ?? props.columnHeaderHeight) * densityFactor);\n  const filterHeadersHeight = isHeaderFilteringEnabled ? Math.floor((props.headerFilterHeight ?? props.columnHeaderHeight) * densityFactor) : 0;\n  return columnHeadersHeight + columnGroupHeadersHeight * maxDepth + filterHeadersHeight;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}