{"ast": null, "code": "import * as React from 'react';\nimport { styled } from '@mui/system';\nimport { fastMemo } from '@mui/x-internals/fastMemo';\nimport { vars } from \"../../constants/cssVariables.js\";\nimport { useGridSelector } from \"../../hooks/utils/useGridSelector.js\";\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { gridDimensionsSelector } from \"../../hooks/features/dimensions/index.js\";\nimport { gridClasses } from \"../../constants/index.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst Filler = styled('div')({\n  display: 'flex',\n  flexDirection: 'row',\n  width: 'var(--DataGrid-rowWidth)',\n  boxSizing: 'border-box'\n});\nconst Pinned = styled('div')({\n  position: 'sticky',\n  height: '100%',\n  boxSizing: 'border-box',\n  borderTop: '1px solid var(--rowBorderColor)',\n  backgroundColor: vars.cell.background.pinned\n});\nconst PinnedLeft = styled(Pinned)({\n  left: 0,\n  borderRight: '1px solid var(--rowBorderColor)'\n});\nconst PinnedRight = styled(Pinned)({\n  right: 0,\n  borderLeft: '1px solid var(--rowBorderColor)'\n});\nconst Main = styled('div')({\n  flexGrow: 1,\n  borderTop: '1px solid var(--rowBorderColor)'\n});\nfunction GridVirtualScrollerFiller({\n  rowsLength\n}) {\n  const apiRef = useGridApiContext();\n  const {\n    viewportOuterSize,\n    minimumSize,\n    hasScrollX,\n    hasScrollY,\n    scrollbarSize,\n    leftPinnedWidth,\n    rightPinnedWidth\n  } = useGridSelector(apiRef, gridDimensionsSelector);\n  const height = hasScrollX ? scrollbarSize : 0;\n  const needsLastRowBorder = viewportOuterSize.height - minimumSize.height > 0;\n  if (height === 0 && !needsLastRowBorder) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxs(Filler, {\n    className: gridClasses.filler,\n    role: \"presentation\",\n    style: {\n      height,\n      '--rowBorderColor': rowsLength === 0 ? 'transparent' : 'var(--DataGrid-rowBorderColor)'\n    },\n    children: [leftPinnedWidth > 0 && /*#__PURE__*/_jsx(PinnedLeft, {\n      className: gridClasses['filler--pinnedLeft'],\n      style: {\n        width: leftPinnedWidth\n      }\n    }), /*#__PURE__*/_jsx(Main, {}), rightPinnedWidth > 0 && /*#__PURE__*/_jsx(PinnedRight, {\n      className: gridClasses['filler--pinnedRight'],\n      style: {\n        width: rightPinnedWidth + (hasScrollY ? scrollbarSize : 0)\n      }\n    })]\n  });\n}\nconst Memoized = fastMemo(GridVirtualScrollerFiller);\nexport { Memoized as GridVirtualScrollerFiller };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}