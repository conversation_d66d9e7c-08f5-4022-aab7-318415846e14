{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport useId from '@mui/utils/useId';\nimport { useGridRootProps } from \"../../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction GridFilterInputMultipleValue(props) {\n  const {\n    item,\n    applyValue,\n    type,\n    apiRef,\n    focusElementRef,\n    slotProps\n  } = props;\n  const id = useId();\n  const [options, setOptions] = React.useState([]);\n  const [filterValueState, setFilterValueState] = React.useState(item.value || []);\n  const rootProps = useGridRootProps();\n  React.useEffect(() => {\n    const itemValue = item.value ?? [];\n    setFilterValueState(itemValue.map(String));\n  }, [item.value]);\n  const handleChange = React.useCallback((event, value) => {\n    setFilterValueState(value.map(String));\n    applyValue(_extends({}, item, {\n      value: [...value.map(filterItemValue => type === 'number' ? Number(filterItemValue) : filterItemValue)]\n    }));\n  }, [applyValue, item, type]);\n  const handleInputChange = React.useCallback((event, value) => {\n    if (value === '') {\n      setOptions([]);\n    } else {\n      setOptions([value]);\n    }\n  }, [setOptions]);\n  const BaseAutocomplete = rootProps.slots.baseAutocomplete;\n  return /*#__PURE__*/_jsx(BaseAutocomplete, _extends({\n    multiple: true,\n    freeSolo: true,\n    options: options,\n    id: id,\n    value: filterValueState,\n    onChange: handleChange,\n    onInputChange: handleInputChange,\n    label: apiRef.current.getLocaleText('filterPanelInputLabel'),\n    placeholder: apiRef.current.getLocaleText('filterPanelInputPlaceholder'),\n    slotProps: {\n      textField: {\n        type: type || 'text',\n        inputRef: focusElementRef\n      }\n    }\n  }, slotProps?.root));\n}\nprocess.env.NODE_ENV !== \"production\" ? GridFilterInputMultipleValue.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  apiRef: PropTypes.shape({\n    current: PropTypes.object.isRequired\n  }).isRequired,\n  applyValue: PropTypes.func.isRequired,\n  className: PropTypes.string,\n  clearButton: PropTypes.node,\n  disabled: PropTypes.bool,\n  focusElementRef: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.func, PropTypes.object]),\n  headerFilterMenu: PropTypes.node,\n  inputRef: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n    current: (props, propName) => {\n      if (props[propName] == null) {\n        return null;\n      }\n      if (typeof props[propName] !== 'object' || props[propName].nodeType !== 1) {\n        return new Error(`Expected prop '${propName}' to be of type Element`);\n      }\n      return null;\n    }\n  })]),\n  /**\n   * It is `true` if the filter either has a value or an operator with no value\n   * required is selected (for example `isEmpty`)\n   */\n  isFilterActive: PropTypes.bool,\n  item: PropTypes.shape({\n    field: PropTypes.string.isRequired,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    operator: PropTypes.string.isRequired,\n    value: PropTypes.any\n  }).isRequired,\n  onBlur: PropTypes.func,\n  onFocus: PropTypes.func,\n  slotProps: PropTypes.object,\n  tabIndex: PropTypes.number,\n  type: PropTypes.oneOf(['date', 'datetime-local', 'number', 'text'])\n} : void 0;\nexport { GridFilterInputMultipleValue };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}