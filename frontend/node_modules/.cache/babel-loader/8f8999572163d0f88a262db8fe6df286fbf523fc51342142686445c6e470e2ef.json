{"ast": null, "code": "import * as React from 'react';\nexport const ToolbarContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== \"production\") ToolbarContext.displayName = \"ToolbarContext\";\nexport function useToolbarContext() {\n  const context = React.useContext(ToolbarContext);\n  if (context === undefined) {\n    throw new Error('MUI X: Missing context. Toolbar subcomponents must be placed within a <Toolbar /> component.');\n  }\n  return context;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}