{"ast": null, "code": "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _toPropertyKey from \"@babel/runtime/helpers/esm/toPropertyKey\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"id\", \"field\"],\n  _excluded2 = [\"id\", \"field\"];\nimport * as React from 'react';\nimport { warnOnce } from '@mui/x-internals/warning';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport { isDeepEqual } from '@mui/x-internals/isDeepEqual';\nimport { useGridEvent, useGridEventPriority } from \"../../utils/useGridEvent.js\";\nimport { GridEditModes, GridCellModes } from \"../../../models/gridEditRowModel.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { gridEditRowsStateSelector } from \"./gridEditingSelectors.js\";\nimport { isPrintableKey, isPasteShortcut } from \"../../../utils/keyboardUtils.js\";\nimport { gridRowsLookupSelector } from \"../rows/gridRowsSelector.js\";\nimport { deepClone } from \"../../../utils/utils.js\";\nimport { GridCellEditStartReasons, GridCellEditStopReasons } from \"../../../models/params/gridEditCellParams.js\";\nimport { getDefaultCellValue } from \"./utils.js\";\nexport const useGridCellEditing = (apiRef, props) => {\n  const [cellModesModel, setCellModesModel] = React.useState({});\n  const cellModesModelRef = React.useRef(cellModesModel);\n  const prevCellModesModel = React.useRef({});\n  const {\n    processRowUpdate,\n    onProcessRowUpdateError,\n    cellModesModel: cellModesModelProp,\n    onCellModesModelChange\n  } = props;\n  const runIfEditModeIsCell = callback => (...args) => {\n    if (props.editMode === GridEditModes.Cell) {\n      callback(...args);\n    }\n  };\n  const throwIfNotEditable = React.useCallback((id, field) => {\n    const params = apiRef.current.getCellParams(id, field);\n    if (!apiRef.current.isCellEditable(params)) {\n      throw new Error(`MUI X: The cell with id=${id} and field=${field} is not editable.`);\n    }\n  }, [apiRef]);\n  const throwIfNotInMode = React.useCallback((id, field, mode) => {\n    if (apiRef.current.getCellMode(id, field) !== mode) {\n      throw new Error(`MUI X: The cell with id=${id} and field=${field} is not in ${mode} mode.`);\n    }\n  }, [apiRef]);\n  const handleCellDoubleClick = React.useCallback((params, event) => {\n    if (!params.isEditable) {\n      return;\n    }\n    if (params.cellMode === GridCellModes.Edit) {\n      return;\n    }\n    const newParams = _extends({}, params, {\n      reason: GridCellEditStartReasons.cellDoubleClick\n    });\n    apiRef.current.publishEvent('cellEditStart', newParams, event);\n  }, [apiRef]);\n  const handleCellFocusOut = React.useCallback((params, event) => {\n    if (params.cellMode === GridCellModes.View) {\n      return;\n    }\n    if (apiRef.current.getCellMode(params.id, params.field) === GridCellModes.View) {\n      return;\n    }\n    const newParams = _extends({}, params, {\n      reason: GridCellEditStopReasons.cellFocusOut\n    });\n    apiRef.current.publishEvent('cellEditStop', newParams, event);\n  }, [apiRef]);\n  const handleCellKeyDown = React.useCallback((params, event) => {\n    if (params.cellMode === GridCellModes.Edit) {\n      // Wait until IME is settled for Asian languages like Japanese and Chinese\n      // TODO: to replace at one point. See https://github.com/mui/material-ui/pull/39713#discussion_r1381678957.\n      if (event.which === 229) {\n        return;\n      }\n      let reason;\n      if (event.key === 'Escape') {\n        reason = GridCellEditStopReasons.escapeKeyDown;\n      } else if (event.key === 'Enter') {\n        reason = GridCellEditStopReasons.enterKeyDown;\n      } else if (event.key === 'Tab') {\n        reason = event.shiftKey ? GridCellEditStopReasons.shiftTabKeyDown : GridCellEditStopReasons.tabKeyDown;\n        event.preventDefault(); // Prevent going to the next element in the tab sequence\n      }\n      if (reason) {\n        const newParams = _extends({}, params, {\n          reason\n        });\n        apiRef.current.publishEvent('cellEditStop', newParams, event);\n      }\n    } else if (params.isEditable) {\n      let reason;\n      const canStartEditing = apiRef.current.unstable_applyPipeProcessors('canStartEditing', true, {\n        event,\n        cellParams: params,\n        editMode: 'cell'\n      });\n      if (!canStartEditing) {\n        return;\n      }\n      if (isPrintableKey(event)) {\n        reason = GridCellEditStartReasons.printableKeyDown;\n      } else if (isPasteShortcut(event)) {\n        reason = GridCellEditStartReasons.pasteKeyDown;\n      } else if (event.key === 'Enter') {\n        reason = GridCellEditStartReasons.enterKeyDown;\n      } else if (event.key === 'Backspace' || event.key === 'Delete') {\n        reason = GridCellEditStartReasons.deleteKeyDown;\n      }\n      if (reason) {\n        const newParams = _extends({}, params, {\n          reason,\n          key: event.key\n        });\n        apiRef.current.publishEvent('cellEditStart', newParams, event);\n      }\n    }\n  }, [apiRef]);\n  const handleCellEditStart = React.useCallback(params => {\n    const {\n      id,\n      field,\n      reason\n    } = params;\n    const startCellEditModeParams = {\n      id,\n      field\n    };\n    if (reason === GridCellEditStartReasons.printableKeyDown || reason === GridCellEditStartReasons.deleteKeyDown || reason === GridCellEditStartReasons.pasteKeyDown) {\n      startCellEditModeParams.deleteValue = true;\n    }\n    apiRef.current.startCellEditMode(startCellEditModeParams);\n  }, [apiRef]);\n  const handleCellEditStop = React.useCallback(params => {\n    const {\n      id,\n      field,\n      reason\n    } = params;\n    apiRef.current.runPendingEditCellValueMutation(id, field);\n    let cellToFocusAfter;\n    if (reason === GridCellEditStopReasons.enterKeyDown) {\n      cellToFocusAfter = 'below';\n    } else if (reason === GridCellEditStopReasons.tabKeyDown) {\n      cellToFocusAfter = 'right';\n    } else if (reason === GridCellEditStopReasons.shiftTabKeyDown) {\n      cellToFocusAfter = 'left';\n    }\n    const ignoreModifications = reason === 'escapeKeyDown';\n    apiRef.current.stopCellEditMode({\n      id,\n      field,\n      ignoreModifications,\n      cellToFocusAfter\n    });\n  }, [apiRef]);\n  const runIfNoFieldErrors = callback => async (...args) => {\n    if (callback) {\n      const {\n        id,\n        field\n      } = args[0];\n      const editRowsState = apiRef.current.state.editRows;\n      const hasFieldErrors = editRowsState[id][field]?.error;\n      if (!hasFieldErrors) {\n        callback(...args);\n      }\n    }\n  };\n  useGridEvent(apiRef, 'cellDoubleClick', runIfEditModeIsCell(handleCellDoubleClick));\n  useGridEvent(apiRef, 'cellFocusOut', runIfEditModeIsCell(handleCellFocusOut));\n  useGridEvent(apiRef, 'cellKeyDown', runIfEditModeIsCell(handleCellKeyDown));\n  useGridEvent(apiRef, 'cellEditStart', runIfEditModeIsCell(handleCellEditStart));\n  useGridEvent(apiRef, 'cellEditStop', runIfEditModeIsCell(handleCellEditStop));\n  useGridEventPriority(apiRef, 'cellEditStart', props.onCellEditStart);\n  useGridEventPriority(apiRef, 'cellEditStop', runIfNoFieldErrors(props.onCellEditStop));\n  const getCellMode = React.useCallback((id, field) => {\n    const editingState = gridEditRowsStateSelector(apiRef);\n    const isEditing = editingState[id] && editingState[id][field];\n    return isEditing ? GridCellModes.Edit : GridCellModes.View;\n  }, [apiRef]);\n  const updateCellModesModel = useEventCallback(newModel => {\n    const isNewModelDifferentFromProp = newModel !== props.cellModesModel;\n    if (onCellModesModelChange && isNewModelDifferentFromProp) {\n      onCellModesModelChange(newModel, {\n        api: apiRef.current\n      });\n    }\n    if (props.cellModesModel && isNewModelDifferentFromProp) {\n      return; // The prop always win\n    }\n    setCellModesModel(newModel);\n    cellModesModelRef.current = newModel;\n    apiRef.current.publishEvent('cellModesModelChange', newModel);\n  });\n  const updateFieldInCellModesModel = React.useCallback((id, field, newProps) => {\n    // We use the ref because it always contain the up-to-date value, different from the state\n    // that needs a rerender to reflect the new value\n    const newModel = _extends({}, cellModesModelRef.current);\n    if (newProps !== null) {\n      newModel[id] = _extends({}, newModel[id], {\n        [field]: _extends({}, newProps)\n      });\n    } else {\n      const _newModel$id = newModel[id],\n        otherFields = _objectWithoutPropertiesLoose(_newModel$id, [field].map(_toPropertyKey)); // Ensure that we have a new object, not a reference\n      newModel[id] = otherFields;\n      if (Object.keys(newModel[id]).length === 0) {\n        delete newModel[id];\n      }\n    }\n    updateCellModesModel(newModel);\n  }, [updateCellModesModel]);\n  const updateOrDeleteFieldState = React.useCallback((id, field, newProps) => {\n    apiRef.current.setState(state => {\n      const newEditingState = _extends({}, state.editRows);\n      if (newProps !== null) {\n        newEditingState[id] = _extends({}, newEditingState[id], {\n          [field]: _extends({}, newProps)\n        });\n      } else {\n        delete newEditingState[id][field];\n        if (Object.keys(newEditingState[id]).length === 0) {\n          delete newEditingState[id];\n        }\n      }\n      return _extends({}, state, {\n        editRows: newEditingState\n      });\n    });\n  }, [apiRef]);\n  const startCellEditMode = React.useCallback(params => {\n    const {\n        id,\n        field\n      } = params,\n      other = _objectWithoutPropertiesLoose(params, _excluded);\n    throwIfNotEditable(id, field);\n    throwIfNotInMode(id, field, GridCellModes.View);\n    updateFieldInCellModesModel(id, field, _extends({\n      mode: GridCellModes.Edit\n    }, other));\n  }, [throwIfNotEditable, throwIfNotInMode, updateFieldInCellModesModel]);\n  const updateStateToStartCellEditMode = useEventCallback(async params => {\n    const {\n      id,\n      field,\n      deleteValue,\n      initialValue\n    } = params;\n    const value = apiRef.current.getCellValue(id, field);\n    let newValue = value;\n    if (deleteValue) {\n      newValue = getDefaultCellValue(apiRef.current.getColumn(field));\n    } else if (initialValue) {\n      newValue = initialValue;\n    }\n    const column = apiRef.current.getColumn(field);\n    const shouldProcessEditCellProps = !!column.preProcessEditCellProps && deleteValue;\n    let newProps = {\n      value: newValue,\n      error: false,\n      isProcessingProps: shouldProcessEditCellProps\n    };\n    updateOrDeleteFieldState(id, field, newProps);\n    apiRef.current.setCellFocus(id, field);\n    if (shouldProcessEditCellProps) {\n      newProps = await Promise.resolve(column.preProcessEditCellProps({\n        id,\n        row: apiRef.current.getRow(id),\n        props: newProps,\n        hasChanged: newValue !== value\n      }));\n      // Check if still in edit mode before updating\n      if (apiRef.current.getCellMode(id, field) === GridCellModes.Edit) {\n        const editingState = gridEditRowsStateSelector(apiRef);\n        updateOrDeleteFieldState(id, field, _extends({}, newProps, {\n          value: editingState[id][field].value,\n          isProcessingProps: false\n        }));\n      }\n    }\n  });\n  const stopCellEditMode = React.useCallback(params => {\n    const {\n        id,\n        field\n      } = params,\n      other = _objectWithoutPropertiesLoose(params, _excluded2);\n    throwIfNotInMode(id, field, GridCellModes.Edit);\n    updateFieldInCellModesModel(id, field, _extends({\n      mode: GridCellModes.View\n    }, other));\n  }, [throwIfNotInMode, updateFieldInCellModesModel]);\n  const updateStateToStopCellEditMode = useEventCallback(async params => {\n    const {\n      id,\n      field,\n      ignoreModifications,\n      cellToFocusAfter = 'none'\n    } = params;\n    throwIfNotInMode(id, field, GridCellModes.Edit);\n    apiRef.current.runPendingEditCellValueMutation(id, field);\n    const finishCellEditMode = () => {\n      updateOrDeleteFieldState(id, field, null);\n      updateFieldInCellModesModel(id, field, null);\n      if (cellToFocusAfter !== 'none') {\n        apiRef.current.moveFocusToRelativeCell(id, field, cellToFocusAfter);\n      }\n    };\n    if (ignoreModifications) {\n      finishCellEditMode();\n      return;\n    }\n    const editingState = gridEditRowsStateSelector(apiRef);\n    const {\n      error,\n      isProcessingProps\n    } = editingState[id][field];\n    const row = apiRef.current.getRow(id);\n    if (error || isProcessingProps) {\n      // Attempt to change cell mode to \"view\" was not successful\n      // Update previous mode to allow another attempt\n      prevCellModesModel.current[id][field].mode = GridCellModes.Edit;\n      // Revert the mode in the cellModesModel prop back to \"edit\"\n      updateFieldInCellModesModel(id, field, {\n        mode: GridCellModes.Edit\n      });\n      return;\n    }\n    const rowUpdate = apiRef.current.getRowWithUpdatedValuesFromCellEditing(id, field);\n    if (props.dataSource?.updateRow) {\n      if (isDeepEqual(row, rowUpdate)) {\n        finishCellEditMode();\n        return;\n      }\n      const handleError = () => {\n        prevCellModesModel.current[id][field].mode = GridCellModes.Edit;\n        // Revert the mode in the cellModesModel prop back to \"edit\"\n        updateFieldInCellModesModel(id, field, {\n          mode: GridCellModes.Edit\n        });\n      };\n      const updateRowParams = {\n        rowId: id,\n        updatedRow: rowUpdate,\n        previousRow: row\n      };\n      try {\n        await apiRef.current.dataSource.editRow(updateRowParams);\n        finishCellEditMode();\n      } catch {\n        handleError();\n      }\n    } else if (processRowUpdate) {\n      const handleError = errorThrown => {\n        prevCellModesModel.current[id][field].mode = GridCellModes.Edit;\n        // Revert the mode in the cellModesModel prop back to \"edit\"\n        updateFieldInCellModesModel(id, field, {\n          mode: GridCellModes.Edit\n        });\n        if (onProcessRowUpdateError) {\n          onProcessRowUpdateError(errorThrown);\n        } else if (process.env.NODE_ENV !== 'production') {\n          warnOnce(['MUI X: A call to `processRowUpdate` threw an error which was not handled because `onProcessRowUpdateError` is missing.', 'To handle the error pass a callback to the `onProcessRowUpdateError` prop, for example `<DataGrid onProcessRowUpdateError={(error) => ...} />`.', 'For more detail, see https://mui.com/x/react-data-grid/editing/persistence/.'], 'error');\n        }\n      };\n      try {\n        Promise.resolve(processRowUpdate(rowUpdate, row, {\n          rowId: id\n        })).then(finalRowUpdate => {\n          apiRef.current.updateRows([finalRowUpdate]);\n          finishCellEditMode();\n        }).catch(handleError);\n      } catch (errorThrown) {\n        handleError(errorThrown);\n      }\n    } else {\n      apiRef.current.updateRows([rowUpdate]);\n      finishCellEditMode();\n    }\n  });\n  const setCellEditingEditCellValue = React.useCallback(async params => {\n    const {\n      id,\n      field,\n      value,\n      debounceMs,\n      unstable_skipValueParser: skipValueParser\n    } = params;\n    throwIfNotEditable(id, field);\n    throwIfNotInMode(id, field, GridCellModes.Edit);\n    const column = apiRef.current.getColumn(field);\n    const row = apiRef.current.getRow(id);\n    let parsedValue = value;\n    if (column.valueParser && !skipValueParser) {\n      parsedValue = column.valueParser(value, row, column, apiRef);\n    }\n    let editingState = gridEditRowsStateSelector(apiRef);\n    let newProps = _extends({}, editingState[id][field], {\n      value: parsedValue,\n      changeReason: debounceMs ? 'debouncedSetEditCellValue' : 'setEditCellValue'\n    });\n    if (column.preProcessEditCellProps) {\n      const hasChanged = value !== editingState[id][field].value;\n      newProps = _extends({}, newProps, {\n        isProcessingProps: true\n      });\n      updateOrDeleteFieldState(id, field, newProps);\n      newProps = await Promise.resolve(column.preProcessEditCellProps({\n        id,\n        row,\n        props: newProps,\n        hasChanged\n      }));\n    }\n\n    // Check again if the cell is in edit mode because the user may have\n    // discarded the changes while the props were being processed.\n    if (apiRef.current.getCellMode(id, field) === GridCellModes.View) {\n      return false;\n    }\n    editingState = gridEditRowsStateSelector(apiRef);\n    newProps = _extends({}, newProps, {\n      isProcessingProps: false\n    });\n    // We don't update the value with the one coming from the props pre-processing\n    // because when the promise resolves it may be already outdated. The only\n    // exception to this rule is when there's no pre-processing.\n    newProps.value = column.preProcessEditCellProps ? editingState[id][field].value : parsedValue;\n    updateOrDeleteFieldState(id, field, newProps);\n    editingState = gridEditRowsStateSelector(apiRef);\n    return !editingState[id]?.[field]?.error;\n  }, [apiRef, throwIfNotEditable, throwIfNotInMode, updateOrDeleteFieldState]);\n  const getRowWithUpdatedValuesFromCellEditing = React.useCallback((id, field) => {\n    const column = apiRef.current.getColumn(field);\n    const editingState = gridEditRowsStateSelector(apiRef);\n    const row = apiRef.current.getRow(id);\n    if (!editingState[id] || !editingState[id][field]) {\n      return apiRef.current.getRow(id);\n    }\n    const {\n      value\n    } = editingState[id][field];\n    return column.valueSetter ? column.valueSetter(value, row, column, apiRef) : _extends({}, row, {\n      [field]: value\n    });\n  }, [apiRef]);\n  const editingApi = {\n    getCellMode,\n    startCellEditMode,\n    stopCellEditMode\n  };\n  const editingPrivateApi = {\n    setCellEditingEditCellValue,\n    getRowWithUpdatedValuesFromCellEditing\n  };\n  useGridApiMethod(apiRef, editingApi, 'public');\n  useGridApiMethod(apiRef, editingPrivateApi, 'private');\n  React.useEffect(() => {\n    if (cellModesModelProp) {\n      updateCellModesModel(cellModesModelProp);\n    }\n  }, [cellModesModelProp, updateCellModesModel]);\n\n  // Run this effect synchronously so that the keyboard event can impact the yet-to-be-rendered input.\n  useEnhancedEffect(() => {\n    const rowsLookup = gridRowsLookupSelector(apiRef);\n\n    // Update the ref here because updateStateToStopCellEditMode may change it later\n    const copyOfPrevCellModes = prevCellModesModel.current;\n    prevCellModesModel.current = deepClone(cellModesModel); // Do a deep-clone because the attributes might be changed later\n\n    Object.entries(cellModesModel).forEach(([id, fields]) => {\n      Object.entries(fields).forEach(([field, params]) => {\n        const prevMode = copyOfPrevCellModes[id]?.[field]?.mode || GridCellModes.View;\n        const originalId = rowsLookup[id] ? apiRef.current.getRowId(rowsLookup[id]) : id;\n        if (params.mode === GridCellModes.Edit && prevMode === GridCellModes.View) {\n          updateStateToStartCellEditMode(_extends({\n            id: originalId,\n            field\n          }, params));\n        } else if (params.mode === GridCellModes.View && prevMode === GridCellModes.Edit) {\n          updateStateToStopCellEditMode(_extends({\n            id: originalId,\n            field\n          }, params));\n        }\n      });\n    });\n  }, [apiRef, cellModesModel, updateStateToStartCellEditMode, updateStateToStopCellEditMode]);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}