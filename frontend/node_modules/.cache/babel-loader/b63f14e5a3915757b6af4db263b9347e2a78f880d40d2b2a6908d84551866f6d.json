{"ast": null, "code": "import { gridFilteredSortedRowIdsSelector } from \"../filter/gridFilterSelector.js\";\nimport { gridRowSpanningHiddenCellsSelector } from \"../rows/gridRowSpanningSelectors.js\";\nexport const getLeftColumnIndex = ({\n  currentColIndex,\n  firstColIndex,\n  lastColIndex,\n  isRtl\n}) => {\n  if (isRtl) {\n    if (currentColIndex < lastColIndex) {\n      return currentColIndex + 1;\n    }\n  } else if (!isRtl) {\n    if (currentColIndex > firstColIndex) {\n      return currentColIndex - 1;\n    }\n  }\n  return null;\n};\nexport const getRightColumnIndex = ({\n  currentColIndex,\n  firstColIndex,\n  lastColIndex,\n  isRtl\n}) => {\n  if (isRtl) {\n    if (currentColIndex > firstColIndex) {\n      return currentColIndex - 1;\n    }\n  } else if (!isRtl) {\n    if (currentColIndex < lastColIndex) {\n      return currentColIndex + 1;\n    }\n  }\n  return null;\n};\nexport function findNonRowSpannedCell(apiRef, rowId, field, rowSpanScanDirection) {\n  const rowSpanHiddenCells = gridRowSpanningHiddenCellsSelector(apiRef);\n  if (!rowSpanHiddenCells[rowId]?.[field]) {\n    return rowId;\n  }\n  const filteredSortedRowIds = gridFilteredSortedRowIdsSelector(apiRef);\n  // find closest non row spanned cell in the given `rowSpanScanDirection`\n  let nextRowIndex = filteredSortedRowIds.indexOf(rowId) + (rowSpanScanDirection === 'down' ? 1 : -1);\n  while (nextRowIndex >= 0 && nextRowIndex < filteredSortedRowIds.length) {\n    const nextRowId = filteredSortedRowIds[nextRowIndex];\n    if (!rowSpanHiddenCells[nextRowId]?.[field]) {\n      return nextRowId;\n    }\n    nextRowIndex += rowSpanScanDirection === 'down' ? 1 : -1;\n  }\n  return rowId;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}