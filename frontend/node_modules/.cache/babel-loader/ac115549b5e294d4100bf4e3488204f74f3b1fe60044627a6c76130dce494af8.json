{"ast": null, "code": "import { gridFilterModelSelector } from \"../filter/gridFilterSelector.js\";\nimport { gridSortModelSelector } from \"../sorting/gridSortingSelector.js\";\nimport { gridPaginationModelSelector } from \"../pagination/gridPaginationSelector.js\";\nimport { createSelector } from \"../../../utils/createSelector.js\";\nexport const gridGetRowsParamsSelector = createSelector(gridFilterModelSelector, gridSortModelSelector, gridPaginationModelSelector, (filterModel, sortModel, paginationModel) => ({\n  groupKeys: [],\n  paginationModel,\n  sortModel,\n  filterModel,\n  start: paginationModel.page * paginationModel.pageSize,\n  end: paginationModel.page * paginationModel.pageSize + paginationModel.pageSize - 1\n}));", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}