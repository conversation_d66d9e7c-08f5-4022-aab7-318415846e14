{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { useGridRegisterPipeProcessor } from \"../../core/pipeProcessing/index.js\";\nimport { gridPreferencePanelStateSelector } from \"./gridPreferencePanelSelector.js\";\nexport const preferencePanelStateInitializer = (state, props) => _extends({}, state, {\n  preferencePanel: props.initialState?.preferencePanel ?? {\n    open: false\n  }\n});\n\n/**\n * TODO: Add a single `setPreferencePanel` method to avoid multiple `setState`\n */\nexport const useGridPreferencesPanel = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridPreferencesPanel');\n\n  /**\n   * API METHODS\n   */\n  const hidePreferences = React.useCallback(() => {\n    apiRef.current.setState(state => {\n      if (!state.preferencePanel.open) {\n        return state;\n      }\n      logger.debug('Hiding Preferences Panel');\n      const preferencePanelState = gridPreferencePanelStateSelector(apiRef);\n      apiRef.current.publishEvent('preferencePanelClose', {\n        openedPanelValue: preferencePanelState.openedPanelValue\n      });\n      return _extends({}, state, {\n        preferencePanel: {\n          open: false\n        }\n      });\n    });\n  }, [apiRef, logger]);\n  const showPreferences = React.useCallback((newValue, panelId, labelId) => {\n    logger.debug('Opening Preferences Panel');\n    apiRef.current.setState(state => _extends({}, state, {\n      preferencePanel: _extends({}, state.preferencePanel, {\n        open: true,\n        openedPanelValue: newValue,\n        panelId,\n        labelId\n      })\n    }));\n    apiRef.current.publishEvent('preferencePanelOpen', {\n      openedPanelValue: newValue\n    });\n  }, [logger, apiRef]);\n  useGridApiMethod(apiRef, {\n    showPreferences,\n    hidePreferences\n  }, 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    const preferencePanelToExport = gridPreferencePanelStateSelector(apiRef);\n    const shouldExportPreferencePanel =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the panel was initialized\n    props.initialState?.preferencePanel != null ||\n    // Always export if the panel is opened\n    preferencePanelToExport.open;\n    if (!shouldExportPreferencePanel) {\n      return prevState;\n    }\n    return _extends({}, prevState, {\n      preferencePanel: preferencePanelToExport\n    });\n  }, [apiRef, props.initialState?.preferencePanel]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    const preferencePanel = context.stateToRestore.preferencePanel;\n    if (preferencePanel != null) {\n      apiRef.current.setState(state => _extends({}, state, {\n        preferencePanel\n      }));\n    }\n    return params;\n  }, [apiRef]);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}