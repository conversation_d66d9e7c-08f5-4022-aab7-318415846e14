{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { styled } from '@mui/system';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { useGridConfiguration } from \"../../hooks/utils/useGridConfiguration.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst GridPanelAnchor = styled('div')({\n  position: 'absolute',\n  top: `var(--DataGrid-headersTotalHeight)`,\n  left: 0,\n  width: 'calc(100% - (var(--DataGrid-hasScrollY) * var(--DataGrid-scrollbarSize)))'\n});\nconst Element = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'Main',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState,\n      loadingOverlayVariant,\n      overlayType\n    } = props;\n    const hideContent = loadingOverlayVariant === 'skeleton' || overlayType === 'noColumnsOverlay';\n    return [styles.main, ownerState.hasPinnedRight && styles['main--hasPinnedRight'], hideContent && styles['main--hiddenContent']];\n  }\n})({\n  flexGrow: 1,\n  position: 'relative',\n  overflow: 'hidden',\n  display: 'flex',\n  flexDirection: 'column'\n});\nexport const GridMainContainer = forwardRef((props, ref) => {\n  const {\n    ownerState\n  } = props;\n  const rootProps = useGridRootProps();\n  const configuration = useGridConfiguration();\n  const ariaAttributes = configuration.hooks.useGridAriaAttributes();\n  return /*#__PURE__*/_jsxs(Element, _extends({\n    ownerState: ownerState,\n    className: props.className,\n    tabIndex: -1\n  }, ariaAttributes, rootProps.slotProps?.main, {\n    ref: ref,\n    children: [/*#__PURE__*/_jsx(GridPanelAnchor, {\n      role: \"presentation\",\n      \"data-id\": \"gridPanelAnchor\"\n    }), props.children]\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridMainContainer.displayName = \"GridMainContainer\";", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}