{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { GridSkeletonCell, GridColumnsPanel, GridFilterPanel, GridFooter, GridLoadingOverlay, GridNoRowsOverlay, GridPagination, GridPanel, GridRow, GridColumnHeaderFilterIconButton, GridRowCount, GridColumnsManagement, GridColumnHeaderSortIcon, GridNoColumnsOverlay } from \"../components/index.js\";\nimport { GridCell } from \"../components/cell/GridCell.js\";\nimport { GridColumnHeaders } from \"../components/GridColumnHeaders.js\";\nimport { GridColumnMenu } from \"../components/menu/columnMenu/GridColumnMenu.js\";\nimport { GridDetailPanels } from \"../components/GridDetailPanels.js\";\nimport { GridPinnedRows } from \"../components/GridPinnedRows.js\";\nimport { GridNoResultsOverlay } from \"../components/GridNoResultsOverlay.js\";\nimport materialSlots from \"../material/index.js\";\nimport { GridBottomContainer } from \"../components/virtualization/GridBottomContainer.js\";\nimport { GridToolbar } from \"../components/toolbarV8/GridToolbar.js\";\n\n// TODO: camelCase these key. It's a private helper now.\n// Remove then need to call `uncapitalizeObjectKeys`.\nexport const DATA_GRID_DEFAULT_SLOTS_COMPONENTS = _extends({}, materialSlots, {\n  cell: GridCell,\n  skeletonCell: GridSkeletonCell,\n  columnHeaderFilterIconButton: GridColumnHeaderFilterIconButton,\n  columnHeaderSortIcon: GridColumnHeaderSortIcon,\n  columnMenu: GridColumnMenu,\n  columnHeaders: GridColumnHeaders,\n  detailPanels: GridDetailPanels,\n  bottomContainer: GridBottomContainer,\n  footer: GridFooter,\n  footerRowCount: GridRowCount,\n  toolbar: GridToolbar,\n  pinnedRows: GridPinnedRows,\n  loadingOverlay: GridLoadingOverlay,\n  noResultsOverlay: GridNoResultsOverlay,\n  noRowsOverlay: GridNoRowsOverlay,\n  noColumnsOverlay: GridNoColumnsOverlay,\n  pagination: GridPagination,\n  filterPanel: GridFilterPanel,\n  columnsPanel: GridColumnsPanel,\n  columnsManagement: GridColumnsManagement,\n  panel: GridPanel,\n  row: GridRow\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}