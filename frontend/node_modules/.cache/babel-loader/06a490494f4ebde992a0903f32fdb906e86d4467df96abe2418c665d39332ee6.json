{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"render\", \"className\", \"onClick\", \"onPointerUp\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport useId from '@mui/utils/useId';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { useComponentRenderer } from '@mui/x-internals/useComponentRenderer';\nimport useForkRef from '@mui/utils/useForkRef';\nimport { useGridPanelContext } from \"../panel/GridPanelContext.js\";\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { gridFilterActiveItemsSelector, gridPreferencePanelStateSelector, GridPreferencePanelsValue, useGridSelector } from \"../../hooks/index.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * A button that opens and closes the filter panel.\n * It renders the `baseButton` slot.\n *\n * Demos:\n *\n * - [Filter Panel](https://mui.com/x/react-data-grid/components/filter-panel/)\n *\n * API:\n *\n * - [FilterPanelTrigger API](https://mui.com/x/api/data-grid/filter-panel-trigger/)\n */\nconst FilterPanelTrigger = forwardRef(function FilterPanelTrigger(props, ref) {\n  const {\n      render,\n      className,\n      onClick,\n      onPointerUp\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const rootProps = useGridRootProps();\n  const buttonId = useId();\n  const panelId = useId();\n  const apiRef = useGridApiContext();\n  const panelState = useGridSelector(apiRef, gridPreferencePanelStateSelector);\n  const open = panelState.open && panelState.openedPanelValue === GridPreferencePanelsValue.filters;\n  const activeFilters = useGridSelector(apiRef, gridFilterActiveItemsSelector);\n  const filterCount = activeFilters.length;\n  const state = {\n    open,\n    filterCount\n  };\n  const resolvedClassName = typeof className === 'function' ? className(state) : className;\n  const {\n    filterPanelTriggerRef\n  } = useGridPanelContext();\n  const handleRef = useForkRef(ref, filterPanelTriggerRef);\n  const handleClick = event => {\n    if (open) {\n      apiRef.current.hidePreferences();\n    } else {\n      apiRef.current.showPreferences(GridPreferencePanelsValue.filters, panelId, buttonId);\n    }\n    onClick?.(event);\n  };\n  const handlePointerUp = event => {\n    if (open) {\n      event.stopPropagation();\n    }\n    onPointerUp?.(event);\n  };\n  const element = useComponentRenderer(rootProps.slots.baseButton, render, _extends({}, rootProps.slotProps?.baseButton, {\n    id: buttonId,\n    'aria-haspopup': 'true',\n    'aria-expanded': open ? 'true' : undefined,\n    'aria-controls': open ? panelId : undefined,\n    onClick: handleClick,\n    onPointerUp: handlePointerUp,\n    className: resolvedClassName\n  }, other, {\n    ref: handleRef\n  }), state);\n  return /*#__PURE__*/_jsx(React.Fragment, {\n    children: element\n  });\n});\nif (process.env.NODE_ENV !== \"production\") FilterPanelTrigger.displayName = \"FilterPanelTrigger\";\nprocess.env.NODE_ENV !== \"production\" ? FilterPanelTrigger.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * A function to customize rendering of the component.\n   */\n  className: PropTypes.oneOfType([PropTypes.func, PropTypes.string]),\n  disabled: PropTypes.bool,\n  id: PropTypes.string,\n  /**\n   * A function to customize rendering of the component.\n   */\n  render: PropTypes.oneOfType([PropTypes.element, PropTypes.func]),\n  role: PropTypes.string,\n  size: PropTypes.oneOf(['large', 'medium', 'small']),\n  startIcon: PropTypes.node,\n  style: PropTypes.object,\n  tabIndex: PropTypes.number,\n  title: PropTypes.string,\n  touchRippleRef: PropTypes.any\n} : void 0;\nexport { FilterPanelTrigger };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}