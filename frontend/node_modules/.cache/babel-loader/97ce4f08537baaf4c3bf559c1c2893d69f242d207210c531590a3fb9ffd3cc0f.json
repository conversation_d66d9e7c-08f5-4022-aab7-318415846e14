{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function GridFooterPlaceholder() {\n  const rootProps = useGridRootProps();\n  if (rootProps.hideFooter) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(rootProps.slots.footer, _extends({}, rootProps.slotProps?.footer /* FIXME: typing error */));\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}