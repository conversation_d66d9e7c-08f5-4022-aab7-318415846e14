{"ast": null, "code": "/**\n * Signal to the underlying logic what version of the public component API\n * of the Data Grid is exposed.\n */\nexport let GridSignature = /*#__PURE__*/function (GridSignature) {\n  GridSignature[\"DataGrid\"] = \"DataGrid\";\n  GridSignature[\"DataGridPro\"] = \"DataGridPro\";\n  GridSignature[\"DataGridPremium\"] = \"DataGridPremium\";\n  return GridSignature;\n}({});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}