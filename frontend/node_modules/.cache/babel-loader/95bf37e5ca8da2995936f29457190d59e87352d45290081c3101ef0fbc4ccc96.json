{"ast": null, "code": "import * as React from 'react';\nimport { useGridSelector } from \"../../utils/useGridSelector.js\";\nimport { gridColumnGroupsHeaderMaxDepthSelector } from \"../columnGrouping/gridColumnGroupsSelector.js\";\nimport { useGridPrivateApiContext } from \"../../utils/useGridPrivateApiContext.js\";\nexport const useGridRowAriaAttributes = () => {\n  const apiRef = useGridPrivateApiContext();\n  const headerGroupingMaxDepth = useGridSelector(apiRef, gridColumnGroupsHeaderMaxDepthSelector);\n  return React.useCallback((rowNode, index) => {\n    const ariaAttributes = {};\n    const ariaRowIndex = index + headerGroupingMaxDepth + 2; // 1 for the header row and 1 as it's 1-based\n    ariaAttributes['aria-rowindex'] = ariaRowIndex;\n    if (apiRef.current.isRowSelectable(rowNode.id)) {\n      ariaAttributes['aria-selected'] = apiRef.current.isRowSelected(rowNode.id);\n    }\n    return ariaAttributes;\n  }, [apiRef, headerGroupingMaxDepth]);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}