{"ast": null, "code": "import * as React from 'react';\nimport { useGridEventPriority, useGridNativeEventListener } from \"../../utils/index.js\";\nimport { gridFocusCellSelector } from \"../focus/gridFocusStateSelector.js\";\nimport { serializeCellValue } from \"../export/serializers/csvSerializer.js\";\nimport { isCopyShortcut } from \"../../../utils/keyboardUtils.js\";\nimport { gridRowSelectionCountSelector } from \"../rowSelection/index.js\";\nfunction writeToClipboardPolyfill(data) {\n  const span = document.createElement('span');\n  span.style.whiteSpace = 'pre';\n  span.style.userSelect = 'all';\n  span.style.opacity = '0px';\n  span.textContent = data;\n  document.body.appendChild(span);\n  const range = document.createRange();\n  range.selectNode(span);\n  const selection = window.getSelection();\n  selection.removeAllRanges();\n  selection.addRange(range);\n  try {\n    document.execCommand('copy');\n  } finally {\n    document.body.removeChild(span);\n  }\n}\nfunction copyToClipboard(data) {\n  if (navigator.clipboard) {\n    navigator.clipboard.writeText(data).catch(() => {\n      writeToClipboardPolyfill(data);\n    });\n  } else {\n    writeToClipboardPolyfill(data);\n  }\n}\nfunction hasNativeSelection(element) {\n  // When getSelection is called on an <iframe> that is not displayed Firefox will return null.\n  if (window.getSelection()?.toString()) {\n    return true;\n  }\n\n  // window.getSelection() returns an empty string in Firefox for selections inside a form element.\n  // See: https://bugzilla.mozilla.org/show_bug.cgi?id=85686.\n  // Instead, we can use element.selectionStart that is only defined on form elements.\n  if (element && (element.selectionEnd || 0) - (element.selectionStart || 0) > 0) {\n    return true;\n  }\n  return false;\n}\n\n/**\n * @requires useGridCsvExport (method)\n * @requires useGridSelection (method)\n */\nexport const useGridClipboard = (apiRef, props) => {\n  const ignoreValueFormatterProp = props.ignoreValueFormatterDuringExport;\n  const ignoreValueFormatter = (typeof ignoreValueFormatterProp === 'object' ? ignoreValueFormatterProp?.clipboardExport : ignoreValueFormatterProp) || false;\n  const clipboardCopyCellDelimiter = props.clipboardCopyCellDelimiter;\n  const handleCopy = React.useCallback(event => {\n    if (!isCopyShortcut(event)) {\n      return;\n    }\n\n    // Do nothing if there's a native selection\n    if (hasNativeSelection(event.target)) {\n      return;\n    }\n    let textToCopy = '';\n    const selectedRowsCount = gridRowSelectionCountSelector(apiRef);\n    if (selectedRowsCount > 0) {\n      textToCopy = apiRef.current.getDataAsCsv({\n        includeHeaders: false,\n        delimiter: clipboardCopyCellDelimiter,\n        shouldAppendQuotes: false,\n        escapeFormulas: false\n      });\n    } else {\n      const focusedCell = gridFocusCellSelector(apiRef);\n      if (focusedCell) {\n        const cellParams = apiRef.current.getCellParams(focusedCell.id, focusedCell.field);\n        textToCopy = serializeCellValue(cellParams, {\n          csvOptions: {\n            delimiter: clipboardCopyCellDelimiter,\n            shouldAppendQuotes: false,\n            escapeFormulas: false\n          },\n          ignoreValueFormatter\n        });\n      }\n    }\n    textToCopy = apiRef.current.unstable_applyPipeProcessors('clipboardCopy', textToCopy);\n    if (textToCopy) {\n      copyToClipboard(textToCopy);\n      apiRef.current.publishEvent('clipboardCopy', textToCopy);\n    }\n  }, [apiRef, ignoreValueFormatter, clipboardCopyCellDelimiter]);\n  useGridNativeEventListener(apiRef, () => apiRef.current.rootElementRef.current, 'keydown', handleCopy);\n  useGridEventPriority(apiRef, 'clipboardCopy', props.onClipboardCopy);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}