{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { warnOnce } from '@mui/x-internals/warning';\nimport { gridRowNodeSelector } from \"../rows/gridRowsSelector.js\";\nexport const sanitizeSortModel = (model, disableMultipleColumnsSorting) => {\n  if (disableMultipleColumnsSorting && model.length > 1) {\n    if (process.env.NODE_ENV !== 'production') {\n      warnOnce(['MUI X: The `sortModel` can only contain a single item when the `disableMultipleColumnsSorting` prop is set to `true`.', 'If you are using the community version of the Data Grid, this prop is always `true`.'], 'error');\n    }\n    return [model[0]];\n  }\n  return model;\n};\nexport const mergeStateWithSortModel = (sortModel, disableMultipleColumnsSorting) => state => _extends({}, state, {\n  sorting: _extends({}, state.sorting, {\n    sortModel: sanitizeSortModel(sortModel, disableMultipleColumnsSorting)\n  })\n});\nconst isDesc = direction => direction === 'desc';\n\n/**\n * Transform an item of the sorting model into a method comparing two rows.\n * @param {GridSortItem} sortItem The sort item we want to apply.\n * @param {RefObject<GridApiCommunity>} apiRef The API of the grid.\n * @returns {GridParsedSortItem | null} The parsed sort item. Returns `null` is the sort item is not valid.\n */\nconst parseSortItem = (sortItem, apiRef) => {\n  const column = apiRef.current.getColumn(sortItem.field);\n  if (!column || sortItem.sort === null) {\n    return null;\n  }\n  let comparator;\n  if (column.getSortComparator) {\n    comparator = column.getSortComparator(sortItem.sort);\n  } else {\n    comparator = isDesc(sortItem.sort) ? (...args) => -1 * column.sortComparator(...args) : column.sortComparator;\n  }\n  if (!comparator) {\n    return null;\n  }\n  const getSortCellParams = id => ({\n    id,\n    field: column.field,\n    rowNode: gridRowNodeSelector(apiRef, id),\n    value: apiRef.current.getCellValue(id, column.field),\n    api: apiRef.current\n  });\n  return {\n    getSortCellParams,\n    comparator\n  };\n};\n/**\n * Compare two rows according to a list of valid sort items.\n * The `row1Params` and `row2Params` must have the same length as `parsedSortItems`,\n * and each of their index must contain the `GridSortCellParams` of the sort item with the same index.\n * @param {GridParsedSortItem[]} parsedSortItems All the sort items with which we want to compare the rows.\n * @param {GridRowAggregatedSortingParams} row1 The node and params of the 1st row for each sort item.\n * @param {GridRowAggregatedSortingParams} row2 The node and params of the 2nd row for each sort item.\n */\nconst compareRows = (parsedSortItems, row1, row2) => {\n  return parsedSortItems.reduce((res, item, index) => {\n    if (res !== 0) {\n      // return the results of the first comparator which distinguish the two rows\n      return res;\n    }\n    const sortCellParams1 = row1.params[index];\n    const sortCellParams2 = row2.params[index];\n    res = item.comparator(sortCellParams1.value, sortCellParams2.value, sortCellParams1, sortCellParams2);\n    return res;\n  }, 0);\n};\n\n/**\n * Generates a method to easily sort a list of rows according to the current sort model.\n * @param {GridSortModel} sortModel The model with which we want to sort the rows.\n * @param {RefObject<GridApiCommunity>} apiRef The API of the grid.\n * @returns {GridSortingModelApplier | null} A method that generates a list of sorted row ids from a list of rows according to the current sort model. If `null`, we consider that the rows should remain in the order there were provided.\n */\nexport const buildAggregatedSortingApplier = (sortModel, apiRef) => {\n  const comparatorList = sortModel.map(item => parseSortItem(item, apiRef)).filter(comparator => !!comparator);\n  if (comparatorList.length === 0) {\n    return null;\n  }\n  return rowList => rowList.map(node => ({\n    node,\n    params: comparatorList.map(el => el.getSortCellParams(node.id))\n  })).sort((a, b) => compareRows(comparatorList, a, b)).map(row => row.node.id);\n};\nexport const getNextGridSortDirection = (sortingOrder, current) => {\n  const currentIdx = sortingOrder.indexOf(current);\n  if (!current || currentIdx === -1 || currentIdx + 1 === sortingOrder.length) {\n    return sortingOrder[0];\n  }\n  return sortingOrder[currentIdx + 1];\n};\nconst gridNillComparator = (v1, v2) => {\n  if (v1 == null && v2 != null) {\n    return -1;\n  }\n  if (v2 == null && v1 != null) {\n    return 1;\n  }\n  if (v1 == null && v2 == null) {\n    return 0;\n  }\n  return null;\n};\nconst collator = new Intl.Collator();\nexport const gridStringOrNumberComparator = (value1, value2) => {\n  const nillResult = gridNillComparator(value1, value2);\n  if (nillResult !== null) {\n    return nillResult;\n  }\n  if (typeof value1 === 'string') {\n    return collator.compare(value1.toString(), value2.toString());\n  }\n  return value1 - value2;\n};\nexport const gridNumberComparator = (value1, value2) => {\n  const nillResult = gridNillComparator(value1, value2);\n  if (nillResult !== null) {\n    return nillResult;\n  }\n  return Number(value1) - Number(value2);\n};\nexport const gridDateComparator = (value1, value2) => {\n  const nillResult = gridNillComparator(value1, value2);\n  if (nillResult !== null) {\n    return nillResult;\n  }\n  if (value1 > value2) {\n    return 1;\n  }\n  if (value1 < value2) {\n    return -1;\n  }\n  return 0;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}