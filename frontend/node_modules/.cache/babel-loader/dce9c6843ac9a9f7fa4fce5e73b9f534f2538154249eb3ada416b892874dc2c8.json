{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useRtl } from '@mui/system/RtlProvider';\nexport const useGridIsRtl = apiRef => {\n  const isRtl = useRtl();\n  if (apiRef.current.state.isRtl === undefined) {\n    apiRef.current.state.isRtl = isRtl;\n  }\n  const isFirstEffect = React.useRef(true);\n  React.useEffect(() => {\n    if (isFirstEffect.current) {\n      isFirstEffect.current = false;\n    } else {\n      apiRef.current.setState(state => _extends({}, state, {\n        isRtl\n      }));\n    }\n  }, [apiRef, isRtl]);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}