{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from '@mui/system';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['footerContainer', 'withBorderColor']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst GridFooterContainerRoot = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'FooterContainer'\n})({\n  display: 'flex',\n  justifyContent: 'space-between',\n  alignItems: 'center',\n  minHeight: 52,\n  borderTop: '1px solid'\n});\nconst GridFooterContainer = forwardRef(function GridFooterContainer(props, ref) {\n  const {\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const rootProps = useGridRootProps();\n  const classes = useUtilityClasses(rootProps);\n  return /*#__PURE__*/_jsx(GridFooterContainerRoot, _extends({\n    className: clsx(classes.root, className),\n    ownerState: rootProps\n  }, other, {\n    ref: ref\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridFooterContainer.displayName = \"GridFooterContainer\";\nprocess.env.NODE_ENV !== \"production\" ? GridFooterContainer.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport { GridFooterContainer };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}