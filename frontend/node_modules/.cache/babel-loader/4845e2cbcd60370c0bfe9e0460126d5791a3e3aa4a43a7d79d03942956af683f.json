{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"skeletonRowsCount\", \"visibleColumns\", \"showFirstRowBorder\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport { styled } from '@mui/system';\nimport useForkRef from '@mui/utils/useForkRef';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { useGridApiContext } from \"../hooks/utils/useGridApiContext.js\";\nimport { useGridRootProps } from \"../hooks/utils/useGridRootProps.js\";\nimport { gridColumnPositionsSelector, gridDimensionsSelector, gridVisibleColumnDefinitionsSelector, gridVisiblePinnedColumnDefinitionsSelector, useGridEvent, useGridSelector } from \"../hooks/index.js\";\nimport { PinnedColumnPosition } from \"../internals/constants.js\";\nimport { gridColumnsTotalWidthSelector } from \"../hooks/features/dimensions/gridDimensionsSelectors.js\";\nimport { getDataGridUtilityClass, gridClasses } from \"../constants/gridClasses.js\";\nimport { getPinnedCellOffset } from \"../internals/utils/getPinnedCellOffset.js\";\nimport { shouldCellShowLeftBorder, shouldCellShowRightBorder } from \"../utils/cellBorderUtils.js\";\nimport { escapeOperandAttributeSelector } from \"../utils/domUtils.js\";\nimport { GridScrollbarFillerCell } from \"./GridScrollbarFillerCell.js\";\nimport { rtlFlipSide } from \"../utils/rtlFlipSide.js\";\nimport { attachPinnedStyle } from \"../internals/utils/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst SkeletonOverlay = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'SkeletonLoadingOverlay'\n})({\n  minWidth: '100%',\n  width: 'max-content',\n  // prevents overflow: clip; cutting off the x axis\n  height: '100%',\n  overflow: 'clip' // y axis is hidden while the x axis is allowed to overflow\n});\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['skeletonLoadingOverlay']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst getColIndex = el => parseInt(el.getAttribute('data-colindex'), 10);\nexport const GridSkeletonLoadingOverlayInner = forwardRef(function GridSkeletonLoadingOverlayInner(props, forwardedRef) {\n  const rootProps = useGridRootProps();\n  const {\n    slots\n  } = rootProps;\n  const isRtl = useRtl();\n  const classes = useUtilityClasses({\n    classes: rootProps.classes\n  });\n  const ref = React.useRef(null);\n  const handleRef = useForkRef(ref, forwardedRef);\n  const apiRef = useGridApiContext();\n  const dimensions = useGridSelector(apiRef, gridDimensionsSelector);\n  const totalWidth = useGridSelector(apiRef, gridColumnsTotalWidthSelector);\n  const positions = useGridSelector(apiRef, gridColumnPositionsSelector);\n  const inViewportCount = React.useMemo(() => positions.filter(value => value <= totalWidth).length, [totalWidth, positions]);\n  const {\n      skeletonRowsCount,\n      visibleColumns,\n      showFirstRowBorder\n    } = props,\n    rest = _objectWithoutPropertiesLoose(props, _excluded);\n  const allVisibleColumns = useGridSelector(apiRef, gridVisibleColumnDefinitionsSelector);\n  const columns = React.useMemo(() => allVisibleColumns.slice(0, inViewportCount), [allVisibleColumns, inViewportCount]);\n  const pinnedColumns = useGridSelector(apiRef, gridVisiblePinnedColumnDefinitionsSelector);\n  const getPinnedPosition = React.useCallback(field => {\n    if (pinnedColumns.left.findIndex(col => col.field === field) !== -1) {\n      return PinnedColumnPosition.LEFT;\n    }\n    if (pinnedColumns.right.findIndex(col => col.field === field) !== -1) {\n      return PinnedColumnPosition.RIGHT;\n    }\n    return undefined;\n  }, [pinnedColumns.left, pinnedColumns.right]);\n  const children = React.useMemo(() => {\n    const array = [];\n    for (let i = 0; i < skeletonRowsCount; i += 1) {\n      const rowCells = [];\n      for (let colIndex = 0; colIndex < columns.length; colIndex += 1) {\n        const column = columns[colIndex];\n        const pinnedPosition = getPinnedPosition(column.field);\n        const isPinnedLeft = pinnedPosition === PinnedColumnPosition.LEFT;\n        const isPinnedRight = pinnedPosition === PinnedColumnPosition.RIGHT;\n        const pinnedSide = rtlFlipSide(pinnedPosition, isRtl);\n        const sectionLength = pinnedSide ? pinnedColumns[pinnedSide].length // pinned section\n        : columns.length - pinnedColumns.left.length - pinnedColumns.right.length; // middle section\n        const sectionIndex = pinnedSide ? pinnedColumns[pinnedSide].findIndex(col => col.field === column.field) // pinned section\n        : colIndex - pinnedColumns.left.length; // middle section\n        const scrollbarWidth = dimensions.hasScrollY ? dimensions.scrollbarSize : 0;\n        const pinnedStyle = attachPinnedStyle({}, isRtl, pinnedPosition, getPinnedCellOffset(pinnedPosition, column.computedWidth, colIndex, positions, dimensions.columnsTotalWidth, scrollbarWidth));\n        const gridHasFiller = dimensions.columnsTotalWidth < dimensions.viewportOuterSize.width;\n        const showRightBorder = shouldCellShowRightBorder(pinnedPosition, sectionIndex, sectionLength, rootProps.showCellVerticalBorder, gridHasFiller);\n        const showLeftBorder = shouldCellShowLeftBorder(pinnedPosition, sectionIndex);\n        const isLastColumn = colIndex === columns.length - 1;\n        const isFirstPinnedRight = isPinnedRight && sectionIndex === 0;\n        const hasFillerBefore = isFirstPinnedRight && gridHasFiller;\n        const hasFillerAfter = isLastColumn && !isFirstPinnedRight && gridHasFiller;\n        const expandedWidth = dimensions.viewportOuterSize.width - dimensions.columnsTotalWidth;\n        const emptyCellWidth = Math.max(0, expandedWidth);\n        const emptyCell = /*#__PURE__*/_jsx(slots.skeletonCell, {\n          width: emptyCellWidth,\n          empty: true\n        }, `skeleton-filler-column-${i}`);\n        const hasScrollbarFiller = isLastColumn && scrollbarWidth !== 0;\n        if (hasFillerBefore) {\n          rowCells.push(emptyCell);\n        }\n        rowCells.push(/*#__PURE__*/_jsx(slots.skeletonCell, {\n          field: column.field,\n          type: column.type,\n          align: column.align,\n          width: \"var(--width)\",\n          height: dimensions.rowHeight,\n          \"data-colindex\": colIndex,\n          empty: visibleColumns && !visibleColumns.has(column.field),\n          className: clsx(isPinnedLeft && gridClasses['cell--pinnedLeft'], isPinnedRight && gridClasses['cell--pinnedRight'], showRightBorder && gridClasses['cell--withRightBorder'], showLeftBorder && gridClasses['cell--withLeftBorder']),\n          style: _extends({\n            '--width': `${column.computedWidth}px`\n          }, pinnedStyle)\n        }, `skeleton-column-${i}-${column.field}`));\n        if (hasFillerAfter) {\n          rowCells.push(emptyCell);\n        }\n        if (hasScrollbarFiller) {\n          rowCells.push(/*#__PURE__*/_jsx(GridScrollbarFillerCell, {\n            pinnedRight: pinnedColumns.right.length > 0\n          }, `skeleton-scrollbar-filler-${i}`));\n        }\n      }\n      array.push(/*#__PURE__*/_jsx(\"div\", {\n        className: clsx(gridClasses.row, gridClasses.rowSkeleton, i === 0 && !showFirstRowBorder && gridClasses['row--firstVisible']),\n        children: rowCells\n      }, `skeleton-row-${i}`));\n    }\n    return array;\n  }, [skeletonRowsCount, columns, getPinnedPosition, isRtl, pinnedColumns, dimensions.hasScrollY, dimensions.scrollbarSize, dimensions.columnsTotalWidth, dimensions.viewportOuterSize.width, dimensions.rowHeight, positions, rootProps.showCellVerticalBorder, slots, visibleColumns, showFirstRowBorder]);\n\n  // Sync the column resize of the overlay columns with the grid\n  const handleColumnResize = params => {\n    const {\n      colDef,\n      width\n    } = params;\n    const cells = ref.current?.querySelectorAll(`[data-field=\"${escapeOperandAttributeSelector(colDef.field)}\"]`);\n    if (!cells) {\n      throw new Error('MUI X: Expected skeleton cells to be defined with `data-field` attribute.');\n    }\n    const resizedColIndex = columns.findIndex(col => col.field === colDef.field);\n    const pinnedPosition = getPinnedPosition(colDef.field);\n    const isPinnedLeft = pinnedPosition === PinnedColumnPosition.LEFT;\n    const isPinnedRight = pinnedPosition === PinnedColumnPosition.RIGHT;\n    const currentWidth = getComputedStyle(cells[0]).getPropertyValue('--width');\n    const delta = parseInt(currentWidth, 10) - width;\n    if (cells) {\n      cells.forEach(element => {\n        element.style.setProperty('--width', `${width}px`);\n      });\n    }\n    if (isPinnedLeft) {\n      const pinnedCells = ref.current?.querySelectorAll(`.${gridClasses['cell--pinnedLeft']}`);\n      pinnedCells?.forEach(element => {\n        const colIndex = getColIndex(element);\n        if (colIndex > resizedColIndex) {\n          element.style.left = `${parseInt(getComputedStyle(element).left, 10) - delta}px`;\n        }\n      });\n    }\n    if (isPinnedRight) {\n      const pinnedCells = ref.current?.querySelectorAll(`.${gridClasses['cell--pinnedRight']}`);\n      pinnedCells?.forEach(element => {\n        const colIndex = getColIndex(element);\n        if (colIndex < resizedColIndex) {\n          element.style.right = `${parseInt(getComputedStyle(element).right, 10) + delta}px`;\n        }\n      });\n    }\n  };\n  useGridEvent(apiRef, 'columnResize', handleColumnResize);\n  return /*#__PURE__*/_jsx(SkeletonOverlay, _extends({\n    className: classes.root\n  }, rest, {\n    ref: handleRef,\n    children: children\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridSkeletonLoadingOverlayInner.displayName = \"GridSkeletonLoadingOverlayInner\";\nexport const GridSkeletonLoadingOverlay = forwardRef(function GridSkeletonLoadingOverlay(props, forwardedRef) {\n  const apiRef = useGridApiContext();\n  const dimensions = useGridSelector(apiRef, gridDimensionsSelector);\n  const viewportHeight = dimensions?.viewportInnerSize.height ?? 0;\n  const skeletonRowsCount = Math.ceil(viewportHeight / dimensions.rowHeight);\n  return /*#__PURE__*/_jsx(GridSkeletonLoadingOverlayInner, _extends({}, props, {\n    skeletonRowsCount: skeletonRowsCount,\n    ref: forwardedRef\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridSkeletonLoadingOverlay.displayName = \"GridSkeletonLoadingOverlay\";", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}