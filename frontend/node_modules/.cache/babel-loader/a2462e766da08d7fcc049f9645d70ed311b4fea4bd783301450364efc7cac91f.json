{"ast": null, "code": "import { createSelector as baseCreateSelector, createSelectorMemoized as baseCreateSelectorMemoized } from '@mui/x-internals/store';\nexport const createSelector = (...args) => {\n  const baseSelector = baseCreateSelector(...args);\n  const selector = (apiRef, a1, a2, a3) => baseSelector(unwrapIfNeeded(apiRef), a1, a2, a3);\n  return selector;\n};\nexport const createSelectorMemoized = (...args) => {\n  const baseSelector = baseCreateSelectorMemoized(...args);\n  const selector = (apiRef, a1, a2, a3) => baseSelector(unwrapIfNeeded(apiRef), a1, a2, a3);\n  return selector;\n};\n\n/**\n * Used to create the root selector for a feature. It assumes that the state is already initialized\n * and strips from the types the possibility of `apiRef` being `null`.\n * Users are warned about this in our documentation https://mui.com/x/react-data-grid/state/#direct-selector-access\n */\nexport const createRootSelector = fn => (apiRef, args) => fn(unwrapIfNeeded(apiRef), args);\nfunction unwrapIfNeeded(refOrState) {\n  if ('current' in refOrState) {\n    return refOrState.current.state;\n  }\n  return refOrState;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}