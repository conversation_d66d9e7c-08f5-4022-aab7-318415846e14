{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"defaultSlots\", \"defaultSlotProps\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { useGridColumnMenuSlots } from \"../../../hooks/features/columnMenu/useGridColumnMenuSlots.js\";\nimport { GridColumnMenuContainer } from \"./GridColumnMenuContainer.js\";\nimport { GridColumnMenuColumnsItem } from \"./menuItems/GridColumnMenuColumnsItem.js\";\nimport { GridColumnMenuFilterItem } from \"./menuItems/GridColumnMenuFilterItem.js\";\nimport { GridColumnMenuSortItem } from \"./menuItems/GridColumnMenuSortItem.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const GRID_COLUMN_MENU_SLOTS = {\n  columnMenuSortItem: GridColumnMenuSortItem,\n  columnMenuFilterItem: GridColumnMenuFilterItem,\n  columnMenuColumnsItem: GridColumnMenuColumnsItem\n};\nexport const GRID_COLUMN_MENU_SLOT_PROPS = {\n  columnMenuSortItem: {\n    displayOrder: 10\n  },\n  columnMenuFilterItem: {\n    displayOrder: 20\n  },\n  columnMenuColumnsItem: {\n    displayOrder: 30\n  }\n};\nconst GridGenericColumnMenu = forwardRef(function GridGenericColumnMenu(props, ref) {\n  const {\n      defaultSlots,\n      defaultSlotProps,\n      slots,\n      slotProps\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const orderedSlots = useGridColumnMenuSlots(_extends({}, other, {\n    defaultSlots,\n    defaultSlotProps,\n    slots,\n    slotProps\n  }));\n  return /*#__PURE__*/_jsx(GridColumnMenuContainer, _extends({}, other, {\n    ref: ref,\n    children: orderedSlots.map(([Component, otherProps], index) => /*#__PURE__*/_jsx(Component, _extends({}, otherProps), index))\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridGenericColumnMenu.displayName = \"GridGenericColumnMenu\";\nprocess.env.NODE_ENV !== \"production\" ? GridGenericColumnMenu.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  colDef: PropTypes.object.isRequired,\n  /**\n   * Initial `slotProps` - it is internal, to be overrriden by Pro or Premium packages\n   * @ignore - do not document.\n   */\n  defaultSlotProps: PropTypes.object.isRequired,\n  /**\n   * Initial `slots` - it is internal, to be overrriden by Pro or Premium packages\n   * @ignore - do not document.\n   */\n  defaultSlots: PropTypes.object.isRequired,\n  hideMenu: PropTypes.func.isRequired,\n  id: PropTypes.string,\n  labelledby: PropTypes.string,\n  open: PropTypes.bool.isRequired,\n  /**\n   * Could be used to pass new props or override props specific to a column menu component\n   * e.g. `displayOrder`\n   */\n  slotProps: PropTypes.object,\n  /**\n   * `slots` could be used to add new and (or) override default column menu items\n   * If you register a nee component you must pass it's `displayOrder` in `slotProps`\n   * or it will be placed in the end of the list\n   */\n  slots: PropTypes.object\n} : void 0;\nconst GridColumnMenu = forwardRef(function GridColumnMenu(props, ref) {\n  return /*#__PURE__*/_jsx(GridGenericColumnMenu, _extends({}, props, {\n    ref: ref,\n    defaultSlots: GRID_COLUMN_MENU_SLOTS,\n    defaultSlotProps: GRID_COLUMN_MENU_SLOT_PROPS\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridColumnMenu.displayName = \"GridColumnMenu\";\nprocess.env.NODE_ENV !== \"production\" ? GridColumnMenu.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  colDef: PropTypes.object.isRequired,\n  hideMenu: PropTypes.func.isRequired,\n  id: PropTypes.string,\n  labelledby: PropTypes.string,\n  open: PropTypes.bool.isRequired,\n  /**\n   * Could be used to pass new props or override props specific to a column menu component\n   * e.g. `displayOrder`\n   */\n  slotProps: PropTypes.object,\n  /**\n   * `slots` could be used to add new and (or) override default column menu items\n   * If you register a nee component you must pass it's `displayOrder` in `slotProps`\n   * or it will be placed in the end of the list\n   */\n  slots: PropTypes.object\n} : void 0;\nexport { GridColumnMenu, GridGenericColumnMenu };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}