{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { throwIfPageSizeExceedsTheLimit, getDefaultGridPaginationModel } from \"./gridPaginationUtils.js\";\nimport { useGridPaginationModel } from \"./useGridPaginationModel.js\";\nimport { useGridRowCount } from \"./useGridRowCount.js\";\nimport { useGridPaginationMeta } from \"./useGridPaginationMeta.js\";\nexport const paginationStateInitializer = (state, props) => {\n  const paginationModel = _extends({}, getDefaultGridPaginationModel(props.autoPageSize), props.paginationModel ?? props.initialState?.pagination?.paginationModel);\n  throwIfPageSizeExceedsTheLimit(paginationModel.pageSize, props.signature);\n  const rowCount = props.rowCount ?? props.initialState?.pagination?.rowCount ?? (props.paginationMode === 'client' ? state.rows?.totalRowCount : undefined);\n  const meta = props.paginationMeta ?? props.initialState?.pagination?.meta ?? {};\n  return _extends({}, state, {\n    pagination: _extends({}, state.pagination, {\n      paginationModel,\n      rowCount,\n      meta,\n      enabled: props.pagination === true,\n      paginationMode: props.paginationMode\n    })\n  });\n};\n\n/**\n * @requires useGridFilter (state)\n * @requires useGridDimensions (event) - can be after\n */\nexport const useGridPagination = (apiRef, props) => {\n  useGridPaginationMeta(apiRef, props);\n  useGridPaginationModel(apiRef, props);\n  useGridRowCount(apiRef, props);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}