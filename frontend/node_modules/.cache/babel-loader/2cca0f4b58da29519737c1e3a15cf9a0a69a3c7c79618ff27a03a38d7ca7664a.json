{"ast": null, "code": "import { createSelector, createRootSelector, createSelectorMemoized } from \"../../../utils/createSelector.js\";\nimport { gridDataRowIdsSelector, gridRowsLookupSelector } from \"../rows/gridRowsSelector.js\";\nimport { gridFilteredRowCountSelector } from \"../filter/gridFilterSelector.js\";\nimport { createRowSelectionManager } from \"../../../models/gridRowSelectionManager.js\";\nexport const gridRowSelectionStateSelector = createRootSelector(state => state.rowSelection);\nexport const gridRowSelectionManagerSelector = createSelectorMemoized(gridRowSelectionStateSelector, createRowSelectionManager);\nexport const gridRowSelectionCountSelector = createSelector(gridRowSelectionStateSelector, gridFilteredRowCountSelector, (selection, filteredRowCount) => {\n  if (selection.type === 'include') {\n    return selection.ids.size;\n  }\n  // In exclude selection, all rows are selectable.\n  return filteredRowCount - selection.ids.size;\n});\nexport const gridRowSelectionIdsSelector = createSelectorMemoized(gridRowSelectionStateSelector, gridRowsLookupSelector, gridDataRowIdsSelector, (selectionModel, rowsLookup, rowIds) => {\n  const map = new Map();\n  if (selectionModel.type === 'include') {\n    for (const id of selectionModel.ids) {\n      map.set(id, rowsLookup[id]);\n    }\n  } else {\n    for (let i = 0; i < rowIds.length; i += 1) {\n      const id = rowIds[i];\n      if (!selectionModel.ids.has(id)) {\n        map.set(id, rowsLookup[id]);\n      }\n    }\n  }\n  return map;\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}