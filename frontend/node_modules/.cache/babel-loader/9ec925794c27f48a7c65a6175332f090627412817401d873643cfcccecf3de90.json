{"ast": null, "code": "import { useGridEventPriority } from \"../../utils/useGridEvent.js\";\n/**\n * @requires useGridFocus (event) - can be after, async only\n * @requires useGridColumns (event) - can be after, async only\n */\nexport function useGridEvents(apiRef, props) {\n  useGridEventPriority(apiRef, 'columnHeaderClick', props.onColumnHeaderClick);\n  useGridEventPriority(apiRef, 'columnHeaderContextMenu', props.onColumnHeaderContextMenu);\n  useGridEventPriority(apiRef, 'columnHeaderDoubleClick', props.onColumnHeaderDoubleClick);\n  useGridEventPriority(apiRef, 'columnHeaderOver', props.onColumnHeaderOver);\n  useGridEventPriority(apiRef, 'columnHeaderOut', props.onColumnHeaderOut);\n  useGridEventPriority(apiRef, 'columnHeaderEnter', props.onColumnHeaderEnter);\n  useGridEventPriority(apiRef, 'columnHeaderLeave', props.onColumnHeaderLeave);\n  useGridEventPriority(apiRef, 'cellClick', props.onCellClick);\n  useGridEventPriority(apiRef, 'cellDoubleClick', props.onCellDoubleClick);\n  useGridEventPriority(apiRef, 'cellKeyDown', props.onCellKeyDown);\n  useGridEventPriority(apiRef, 'preferencePanelClose', props.onPreferencePanelClose);\n  useGridEventPriority(apiRef, 'preferencePanelOpen', props.onPreferencePanelOpen);\n  useGridEventPriority(apiRef, 'menuOpen', props.onMenuOpen);\n  useGridEventPriority(apiRef, 'menuClose', props.onMenuClose);\n  useGridEventPriority(apiRef, 'rowDoubleClick', props.onRowDoubleClick);\n  useGridEventPriority(apiRef, 'rowClick', props.onRowClick);\n  useGridEventPriority(apiRef, 'stateChange', props.onStateChange);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}