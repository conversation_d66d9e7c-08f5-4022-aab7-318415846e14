{"ast": null, "code": "import * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { useGridSelector } from \"../../../../hooks/utils/useGridSelector.js\";\nimport { gridSortModelSelector } from \"../../../../hooks/features/sorting/gridSortingSelector.js\";\nimport { useGridApiContext } from \"../../../../hooks/utils/useGridApiContext.js\";\nimport { useGridRootProps } from \"../../../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction GridColumnMenuSortItem(props) {\n  const {\n    colDef,\n    onClick\n  } = props;\n  const apiRef = useGridApiContext();\n  const sortModel = useGridSelector(apiRef, gridSortModelSelector);\n  const rootProps = useGridRootProps();\n  const sortDirection = React.useMemo(() => {\n    if (!colDef) {\n      return null;\n    }\n    const sortItem = sortModel.find(item => item.field === colDef.field);\n    return sortItem?.sort;\n  }, [colDef, sortModel]);\n  const sortingOrder = colDef.sortingOrder ?? rootProps.sortingOrder;\n  const onSortMenuItemClick = React.useCallback(event => {\n    onClick(event);\n    const direction = event.currentTarget.getAttribute('data-value') || null;\n    apiRef.current.sortColumn(colDef.field, direction === sortDirection ? null : direction);\n  }, [apiRef, colDef, onClick, sortDirection]);\n  if (rootProps.disableColumnSorting || !colDef || !colDef.sortable || !sortingOrder.some(item => !!item)) {\n    return null;\n  }\n  const getLabel = key => {\n    const label = apiRef.current.getLocaleText(key);\n    return typeof label === 'function' ? label(colDef) : label;\n  };\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [sortingOrder.includes('asc') && sortDirection !== 'asc' ? /*#__PURE__*/_jsx(rootProps.slots.baseMenuItem, {\n      onClick: onSortMenuItemClick,\n      \"data-value\": \"asc\",\n      iconStart: /*#__PURE__*/_jsx(rootProps.slots.columnMenuSortAscendingIcon, {\n        fontSize: \"small\"\n      }),\n      children: getLabel('columnMenuSortAsc')\n    }) : null, sortingOrder.includes('desc') && sortDirection !== 'desc' ? /*#__PURE__*/_jsx(rootProps.slots.baseMenuItem, {\n      onClick: onSortMenuItemClick,\n      \"data-value\": \"desc\",\n      iconStart: /*#__PURE__*/_jsx(rootProps.slots.columnMenuSortDescendingIcon, {\n        fontSize: \"small\"\n      }),\n      children: getLabel('columnMenuSortDesc')\n    }) : null, sortingOrder.includes(null) && sortDirection != null ? /*#__PURE__*/_jsx(rootProps.slots.baseMenuItem, {\n      onClick: onSortMenuItemClick,\n      iconStart: rootProps.slots.columnMenuUnsortIcon ? /*#__PURE__*/_jsx(rootProps.slots.columnMenuUnsortIcon, {\n        fontSize: \"small\"\n      }) : /*#__PURE__*/_jsx(\"span\", {}),\n      children: apiRef.current.getLocaleText('columnMenuUnsort')\n    }) : null]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GridColumnMenuSortItem.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  colDef: PropTypes.object.isRequired,\n  onClick: PropTypes.func.isRequired\n} : void 0;\nexport { GridColumnMenuSortItem };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}