{"ast": null, "code": "class IncludeManager {\n  constructor(model) {\n    this.data = void 0;\n    this.data = model.ids;\n  }\n  has(id) {\n    return this.data.has(id);\n  }\n  select(id) {\n    this.data.add(id);\n  }\n  unselect(id) {\n    this.data.delete(id);\n  }\n}\nclass ExcludeManager {\n  constructor(model) {\n    this.data = void 0;\n    this.data = model.ids;\n  }\n  has(id) {\n    return !this.data.has(id);\n  }\n  select(id) {\n    this.data.delete(id);\n  }\n  unselect(id) {\n    this.data.add(id);\n  }\n}\nexport const createRowSelectionManager = model => {\n  if (model.type === 'include') {\n    return new IncludeManager(model);\n  }\n  return new ExcludeManager(model);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}