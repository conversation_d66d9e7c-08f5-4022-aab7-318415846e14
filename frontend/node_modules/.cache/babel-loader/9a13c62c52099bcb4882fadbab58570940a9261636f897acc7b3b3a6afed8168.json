{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport useLazyRef from '@mui/utils/useLazyRef';\nimport useTimeout from '@mui/utils/useTimeout';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport reactMajor from '@mui/x-internals/reactMajor';\nimport { gridDimensionsSelector, gridColumnsTotalWidthSelector, gridContentHeightSelector, gridHasFillerSelector, gridRowHeightSelector, gridVerticalScrollbarWidthSelector } from \"../dimensions/gridDimensionsSelectors.js\";\nimport { useGridPrivateApiContext } from \"../../utils/useGridPrivateApiContext.js\";\nimport { useGridRootProps } from \"../../utils/useGridRootProps.js\";\nimport { useGridSelector } from \"../../utils/useGridSelector.js\";\nimport { useRunOnce } from \"../../utils/useRunOnce.js\";\nimport { gridVisibleColumnDefinitionsSelector, gridVisiblePinnedColumnDefinitionsSelector, gridColumnPositionsSelector, gridHasColSpanSelector } from \"../columns/gridColumnsSelector.js\";\nimport { gridPinnedRowsSelector, gridRowTreeSelector } from \"../rows/gridRowsSelector.js\";\nimport { useGridVisibleRows, getVisibleRows } from \"../../utils/useGridVisibleRows.js\";\nimport { useGridEventPriority } from \"../../utils/index.js\";\nimport * as platform from \"../../../utils/platform.js\";\nimport { clamp, range } from \"../../../utils/utils.js\";\nimport { gridRowsMetaSelector } from \"../rows/gridRowsMetaSelector.js\";\nimport { getFirstNonSpannedColumnToRender } from \"../columns/gridColumnsUtils.js\";\nimport { gridRenderContextSelector, gridVirtualizationRowEnabledSelector, gridVirtualizationColumnEnabledSelector } from \"./gridVirtualizationSelectors.js\";\nimport { EMPTY_RENDER_CONTEXT } from \"./useGridVirtualization.js\";\nimport { gridRowSpanningHiddenCellsOriginMapSelector } from \"../rows/gridRowSpanningSelectors.js\";\nimport { gridListColumnSelector } from \"../listView/gridListViewSelectors.js\";\nimport { minimalContentHeight } from \"../rows/gridRowsUtils.js\";\nimport { EMPTY_PINNED_COLUMN_FIELDS } from \"../columns/index.js\";\nimport { gridFocusedVirtualCellSelector } from \"./gridFocusedVirtualCellSelector.js\";\nimport { roundToDecimalPlaces } from \"../../../utils/roundToDecimalPlaces.js\";\nimport { isJSDOM } from \"../../../utils/isJSDOM.js\";\nimport { gridRowSelectionManagerSelector } from \"../rowSelection/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst MINIMUM_COLUMN_WIDTH = 50;\nvar ScrollDirection = /*#__PURE__*/function (ScrollDirection) {\n  ScrollDirection[ScrollDirection[\"NONE\"] = 0] = \"NONE\";\n  ScrollDirection[ScrollDirection[\"UP\"] = 1] = \"UP\";\n  ScrollDirection[ScrollDirection[\"DOWN\"] = 2] = \"DOWN\";\n  ScrollDirection[ScrollDirection[\"LEFT\"] = 3] = \"LEFT\";\n  ScrollDirection[ScrollDirection[\"RIGHT\"] = 4] = \"RIGHT\";\n  return ScrollDirection;\n}(ScrollDirection || {});\nconst EMPTY_SCROLL_POSITION = {\n  top: 0,\n  left: 0\n};\nexport const EMPTY_DETAIL_PANELS = Object.freeze(new Map());\nconst createScrollCache = (isRtl, rowBufferPx, columnBufferPx, verticalBuffer, horizontalBuffer) => ({\n  direction: ScrollDirection.NONE,\n  buffer: bufferForDirection(isRtl, ScrollDirection.NONE, rowBufferPx, columnBufferPx, verticalBuffer, horizontalBuffer)\n});\nexport const useGridVirtualScroller = () => {\n  const apiRef = useGridPrivateApiContext();\n  const rootProps = useGridRootProps();\n  const {\n    listView\n  } = rootProps;\n  const visibleColumns = useGridSelector(apiRef, () => listView ? [gridListColumnSelector(apiRef)] : gridVisibleColumnDefinitionsSelector(apiRef));\n  const enabledForRows = useGridSelector(apiRef, gridVirtualizationRowEnabledSelector) && !isJSDOM;\n  const enabledForColumns = useGridSelector(apiRef, gridVirtualizationColumnEnabledSelector) && !isJSDOM;\n  const pinnedRows = useGridSelector(apiRef, gridPinnedRowsSelector);\n  const pinnedColumnDefinitions = gridVisiblePinnedColumnDefinitionsSelector(apiRef);\n  const pinnedColumns = listView ? EMPTY_PINNED_COLUMN_FIELDS : pinnedColumnDefinitions;\n  const hasBottomPinnedRows = pinnedRows.bottom.length > 0;\n  const [panels, setPanels] = React.useState(EMPTY_DETAIL_PANELS);\n  const isRtl = useRtl();\n  const rowSelectionManager = useGridSelector(apiRef, gridRowSelectionManagerSelector);\n  const currentPage = useGridVisibleRows(apiRef);\n  const mainRef = apiRef.current.mainElementRef;\n  const scrollerRef = apiRef.current.virtualScrollerRef;\n  const scrollbarVerticalRef = apiRef.current.virtualScrollbarVerticalRef;\n  const scrollbarHorizontalRef = apiRef.current.virtualScrollbarHorizontalRef;\n  const hasColSpan = useGridSelector(apiRef, gridHasColSpanSelector);\n  const isRenderContextReady = React.useRef(false);\n  const rowHeight = useGridSelector(apiRef, gridRowHeightSelector);\n  const contentHeight = useGridSelector(apiRef, gridContentHeightSelector);\n  const columnsTotalWidth = useGridSelector(apiRef, gridColumnsTotalWidthSelector);\n  const needsHorizontalScrollbar = useGridSelector(apiRef, needsHorizontalScrollbarSelector);\n  const verticalScrollbarWidth = useGridSelector(apiRef, gridVerticalScrollbarWidthSelector);\n  const gridHasFiller = useGridSelector(apiRef, gridHasFillerSelector);\n  const previousSize = React.useRef(null);\n  const mainRefCallback = React.useCallback(node => {\n    mainRef.current = node;\n    if (!node) {\n      return undefined;\n    }\n    const initialRect = node.getBoundingClientRect();\n    let lastSize = {\n      width: roundToDecimalPlaces(initialRect.width, 1),\n      height: roundToDecimalPlaces(initialRect.height, 1)\n    };\n    if (!previousSize.current || lastSize.width !== previousSize.current.width && lastSize.height !== previousSize.current.height) {\n      previousSize.current = lastSize;\n      apiRef.current.publishEvent('resize', lastSize);\n    }\n    if (typeof ResizeObserver === 'undefined') {\n      return undefined;\n    }\n    const observer = new ResizeObserver(entries => {\n      const entry = entries[0];\n      if (!entry) {\n        return;\n      }\n      const newSize = {\n        width: roundToDecimalPlaces(entry.contentRect.width, 1),\n        height: roundToDecimalPlaces(entry.contentRect.height, 1)\n      };\n      if (newSize.width === lastSize.width && newSize.height === lastSize.height) {\n        return;\n      }\n      apiRef.current.publishEvent('resize', newSize);\n      lastSize = newSize;\n    });\n    observer.observe(node);\n    if (reactMajor >= 19) {\n      return () => {\n        mainRef.current = null;\n        observer.disconnect();\n      };\n    }\n    return undefined;\n  }, [apiRef, mainRef]);\n\n  /*\n   * Scroll context logic\n   * ====================\n   * We only render the cells contained in the `renderContext`. However, when the user starts scrolling the grid\n   * in a direction, we want to render as many cells as possible in that direction, as to avoid presenting white\n   * areas if the user scrolls too fast/far and the viewport ends up in a region we haven't rendered yet. To render\n   * more cells, we store some offsets to add to the viewport in `scrollCache.buffer`. Those offsets make the render\n   * context wider in the direction the user is going, but also makes the buffer around the viewport `0` for the\n   * dimension (horizontal or vertical) in which the user is not scrolling. So if the normal viewport is 8 columns\n   * wide, with a 1 column buffer (10 columns total), then we want it to be exactly 8 columns wide during vertical\n   * scroll.\n   * However, we don't want the rows in the old context to re-render from e.g. 10 columns to 8 columns, because that's\n   * work that's not necessary. Thus we store the context at the start of the scroll in `frozenContext`, and the rows\n   * that are part of this old context will keep their same render context as to avoid re-rendering.\n   */\n  const scrollPosition = React.useRef(rootProps.initialState?.scroll ?? EMPTY_SCROLL_POSITION);\n  const ignoreNextScrollEvent = React.useRef(false);\n  const previousContextScrollPosition = React.useRef(EMPTY_SCROLL_POSITION);\n  const previousRowContext = React.useRef(EMPTY_RENDER_CONTEXT);\n  const renderContext = useGridSelector(apiRef, gridRenderContextSelector);\n  const focusedVirtualCell = useGridSelector(apiRef, gridFocusedVirtualCellSelector);\n  const scrollTimeout = useTimeout();\n  const frozenContext = React.useRef(undefined);\n  const scrollCache = useLazyRef(() => createScrollCache(isRtl, rootProps.rowBufferPx, rootProps.columnBufferPx, rowHeight * 15, MINIMUM_COLUMN_WIDTH * 6)).current;\n  const updateRenderContext = React.useCallback(nextRenderContext => {\n    if (areRenderContextsEqual(nextRenderContext, apiRef.current.state.virtualization.renderContext)) {\n      return;\n    }\n    const didRowsIntervalChange = nextRenderContext.firstRowIndex !== previousRowContext.current.firstRowIndex || nextRenderContext.lastRowIndex !== previousRowContext.current.lastRowIndex;\n    apiRef.current.setState(state => {\n      return _extends({}, state, {\n        virtualization: _extends({}, state.virtualization, {\n          renderContext: nextRenderContext\n        })\n      });\n    });\n\n    // The lazy-loading hook is listening to `renderedRowsIntervalChange`,\n    // but only does something if we already have a render context, because\n    // otherwise we would call an update directly on mount\n    const isReady = gridDimensionsSelector(apiRef).isReady;\n    if (isReady && didRowsIntervalChange) {\n      previousRowContext.current = nextRenderContext;\n      apiRef.current.publishEvent('renderedRowsIntervalChange', nextRenderContext);\n    }\n    previousContextScrollPosition.current = scrollPosition.current;\n  }, [apiRef]);\n  const triggerUpdateRenderContext = useEventCallback(() => {\n    const scroller = scrollerRef.current;\n    if (!scroller) {\n      return undefined;\n    }\n    const dimensions = gridDimensionsSelector(apiRef);\n    const maxScrollTop = Math.ceil(dimensions.minimumSize.height - dimensions.viewportOuterSize.height);\n    const maxScrollLeft = Math.ceil(dimensions.minimumSize.width - dimensions.viewportInnerSize.width);\n\n    // Clamp the scroll position to the viewport to avoid re-calculating the render context for scroll bounce\n    const newScroll = {\n      top: clamp(scroller.scrollTop, 0, maxScrollTop),\n      left: isRtl ? clamp(scroller.scrollLeft, -maxScrollLeft, 0) : clamp(scroller.scrollLeft, 0, maxScrollLeft)\n    };\n    const dx = newScroll.left - scrollPosition.current.left;\n    const dy = newScroll.top - scrollPosition.current.top;\n    const isScrolling = dx !== 0 || dy !== 0;\n    scrollPosition.current = newScroll;\n    const direction = isScrolling ? directionForDelta(dx, dy) : ScrollDirection.NONE;\n\n    // Since previous render, we have scrolled...\n    const rowScroll = Math.abs(scrollPosition.current.top - previousContextScrollPosition.current.top);\n    const columnScroll = Math.abs(scrollPosition.current.left - previousContextScrollPosition.current.left);\n\n    // PERF: use the computed minimum column width instead of a static one\n    const didCrossThreshold = rowScroll >= rowHeight || columnScroll >= MINIMUM_COLUMN_WIDTH;\n    const didChangeDirection = scrollCache.direction !== direction;\n    const shouldUpdate = didCrossThreshold || didChangeDirection;\n    if (!shouldUpdate) {\n      return renderContext;\n    }\n\n    // Render a new context\n\n    if (didChangeDirection) {\n      switch (direction) {\n        case ScrollDirection.NONE:\n        case ScrollDirection.LEFT:\n        case ScrollDirection.RIGHT:\n          frozenContext.current = undefined;\n          break;\n        default:\n          frozenContext.current = renderContext;\n          break;\n      }\n    }\n    scrollCache.direction = direction;\n    scrollCache.buffer = bufferForDirection(isRtl, direction, rootProps.rowBufferPx, rootProps.columnBufferPx, rowHeight * 15, MINIMUM_COLUMN_WIDTH * 6);\n    const inputs = inputsSelector(apiRef, rootProps, enabledForRows, enabledForColumns);\n    const nextRenderContext = computeRenderContext(inputs, scrollPosition.current, scrollCache);\n    if (!areRenderContextsEqual(nextRenderContext, renderContext)) {\n      // Prevents batching render context changes\n      ReactDOM.flushSync(() => {\n        updateRenderContext(nextRenderContext);\n      });\n      scrollTimeout.start(1000, triggerUpdateRenderContext);\n    }\n    return nextRenderContext;\n  });\n  const forceUpdateRenderContext = () => {\n    // skip update if dimensions are not ready and virtualization is enabled\n    if (!gridDimensionsSelector(apiRef).isReady && (enabledForRows || enabledForColumns)) {\n      return;\n    }\n    const inputs = inputsSelector(apiRef, rootProps, enabledForRows, enabledForColumns);\n    const nextRenderContext = computeRenderContext(inputs, scrollPosition.current, scrollCache);\n    // Reset the frozen context when the render context changes, see the illustration in https://github.com/mui/mui-x/pull/12353\n    frozenContext.current = undefined;\n    updateRenderContext(nextRenderContext);\n  };\n  const handleScroll = useEventCallback(() => {\n    if (ignoreNextScrollEvent.current) {\n      ignoreNextScrollEvent.current = false;\n      return;\n    }\n    const nextRenderContext = triggerUpdateRenderContext();\n    apiRef.current.publishEvent('scrollPositionChange', {\n      top: scrollPosition.current.top,\n      left: scrollPosition.current.left,\n      renderContext: nextRenderContext\n    });\n  });\n  const handleWheel = useEventCallback(event => {\n    apiRef.current.publishEvent('virtualScrollerWheel', {}, event);\n  });\n  const handleTouchMove = useEventCallback(event => {\n    apiRef.current.publishEvent('virtualScrollerTouchMove', {}, event);\n  });\n  const getRows = (params = {}) => {\n    if (!params.rows && !currentPage.range) {\n      return [];\n    }\n    const rowTree = gridRowTreeSelector(apiRef);\n    let baseRenderContext = renderContext;\n    if (params.renderContext) {\n      baseRenderContext = params.renderContext;\n      baseRenderContext.firstColumnIndex = renderContext.firstColumnIndex;\n      baseRenderContext.lastColumnIndex = renderContext.lastColumnIndex;\n    }\n    const isLastSection = !hasBottomPinnedRows && params.position === undefined || hasBottomPinnedRows && params.position === 'bottom';\n    const isPinnedSection = params.position !== undefined;\n    let rowIndexOffset;\n    // FIXME: Why is the switch check exhaustiveness not validated with typescript-eslint?\n    // eslint-disable-next-line default-case\n    switch (params.position) {\n      case 'top':\n        rowIndexOffset = 0;\n        break;\n      case 'bottom':\n        rowIndexOffset = pinnedRows.top.length + currentPage.rows.length;\n        break;\n      case undefined:\n        rowIndexOffset = pinnedRows.top.length;\n        break;\n    }\n    const rowModels = params.rows ?? currentPage.rows;\n    const firstRowToRender = baseRenderContext.firstRowIndex;\n    const lastRowToRender = Math.min(baseRenderContext.lastRowIndex, rowModels.length);\n    const rowIndexes = params.rows ? range(0, params.rows.length) : range(firstRowToRender, lastRowToRender);\n    let virtualRowIndex = -1;\n    if (!isPinnedSection && focusedVirtualCell) {\n      if (focusedVirtualCell.rowIndex < firstRowToRender) {\n        rowIndexes.unshift(focusedVirtualCell.rowIndex);\n        virtualRowIndex = focusedVirtualCell.rowIndex;\n      }\n      if (focusedVirtualCell.rowIndex > lastRowToRender) {\n        rowIndexes.push(focusedVirtualCell.rowIndex);\n        virtualRowIndex = focusedVirtualCell.rowIndex;\n      }\n    }\n    const rows = [];\n    const rowProps = rootProps.slotProps?.row;\n    const columnPositions = gridColumnPositionsSelector(apiRef);\n    rowIndexes.forEach(rowIndexInPage => {\n      const {\n        id,\n        model\n      } = rowModels[rowIndexInPage];\n\n      // In certain cases, the state might already be updated and `currentPage.rows` (which sets `rowModels`)\n      // contains stale data.\n      // In that case, skip any further row processing.\n      // See:\n      // - https://github.com/mui/mui-x/issues/16638\n      // - https://github.com/mui/mui-x/issues/17022\n      if (!rowTree[id]) {\n        return;\n      }\n      const rowIndex = (currentPage?.range?.firstRowIndex || 0) + rowIndexOffset + rowIndexInPage;\n\n      // NOTE: This is an expensive feature, the colSpan code could be optimized.\n      if (hasColSpan) {\n        const minFirstColumn = pinnedColumns.left.length;\n        const maxLastColumn = visibleColumns.length - pinnedColumns.right.length;\n        apiRef.current.calculateColSpan({\n          rowId: id,\n          minFirstColumn,\n          maxLastColumn,\n          columns: visibleColumns\n        });\n        if (pinnedColumns.left.length > 0) {\n          apiRef.current.calculateColSpan({\n            rowId: id,\n            minFirstColumn: 0,\n            maxLastColumn: pinnedColumns.left.length,\n            columns: visibleColumns\n          });\n        }\n        if (pinnedColumns.right.length > 0) {\n          apiRef.current.calculateColSpan({\n            rowId: id,\n            minFirstColumn: visibleColumns.length - pinnedColumns.right.length,\n            maxLastColumn: visibleColumns.length,\n            columns: visibleColumns\n          });\n        }\n      }\n      const baseRowHeight = !apiRef.current.rowHasAutoHeight(id) ? apiRef.current.unstable_getRowHeight(id) : 'auto';\n      const isSelected = rowSelectionManager.has(id) && apiRef.current.isRowSelectable(id);\n      let isFirstVisible = false;\n      if (params.position === undefined) {\n        isFirstVisible = rowIndexInPage === 0;\n      }\n      let isLastVisible = false;\n      const isLastVisibleInSection = rowIndexInPage === rowModels.length - 1;\n      if (isLastSection) {\n        if (!isPinnedSection) {\n          const lastIndex = currentPage.rows.length - 1;\n          const isLastVisibleRowIndex = rowIndexInPage === lastIndex;\n          if (isLastVisibleRowIndex) {\n            isLastVisible = true;\n          }\n        } else {\n          isLastVisible = isLastVisibleInSection;\n        }\n      }\n      let currentRenderContext = baseRenderContext;\n      if (frozenContext.current && rowIndexInPage >= frozenContext.current.firstRowIndex && rowIndexInPage < frozenContext.current.lastRowIndex) {\n        currentRenderContext = frozenContext.current;\n      }\n      const isVirtualFocusRow = rowIndexInPage === virtualRowIndex;\n      const isVirtualFocusColumn = focusedVirtualCell?.rowIndex === rowIndex;\n      const offsetLeft = computeOffsetLeft(columnPositions, currentRenderContext, pinnedColumns.left.length);\n      const showBottomBorder = isLastVisibleInSection && params.position === 'top';\n      const firstColumnIndex = currentRenderContext.firstColumnIndex;\n      const lastColumnIndex = currentRenderContext.lastColumnIndex;\n      rows.push(/*#__PURE__*/_jsx(rootProps.slots.row, _extends({\n        row: model,\n        rowId: id,\n        index: rowIndex,\n        selected: isSelected,\n        offsetLeft: offsetLeft,\n        columnsTotalWidth: columnsTotalWidth,\n        rowHeight: baseRowHeight,\n        pinnedColumns: pinnedColumns,\n        visibleColumns: visibleColumns,\n        firstColumnIndex: firstColumnIndex,\n        lastColumnIndex: lastColumnIndex,\n        focusedColumnIndex: isVirtualFocusColumn ? focusedVirtualCell.columnIndex : undefined,\n        isFirstVisible: isFirstVisible,\n        isLastVisible: isLastVisible,\n        isNotVisible: isVirtualFocusRow,\n        showBottomBorder: showBottomBorder,\n        scrollbarWidth: verticalScrollbarWidth,\n        gridHasFiller: gridHasFiller\n      }, rowProps), id));\n      if (isVirtualFocusRow) {\n        return;\n      }\n      const panel = panels.get(id);\n      if (panel) {\n        rows.push(panel);\n      }\n      if (params.position === undefined && isLastVisibleInSection) {\n        rows.push(apiRef.current.getInfiniteLoadingTriggerElement?.({\n          lastRowId: id\n        }));\n      }\n    });\n    return rows;\n  };\n  const scrollerStyle = React.useMemo(() => ({\n    overflowX: !needsHorizontalScrollbar || listView ? 'hidden' : undefined,\n    overflowY: rootProps.autoHeight ? 'hidden' : undefined\n  }), [needsHorizontalScrollbar, rootProps.autoHeight, listView]);\n  const contentSize = React.useMemo(() => {\n    const size = {\n      width: needsHorizontalScrollbar ? columnsTotalWidth : 'auto',\n      flexBasis: contentHeight,\n      flexShrink: 0\n    };\n    if (size.flexBasis === 0) {\n      size.flexBasis = minimalContentHeight; // Give room to show the overlay when there no rows.\n    }\n    return size;\n  }, [columnsTotalWidth, contentHeight, needsHorizontalScrollbar]);\n  const onContentSizeApplied = React.useCallback(node => {\n    if (!node) {\n      return;\n    }\n    apiRef.current.publishEvent('virtualScrollerContentSizeChange', {\n      columnsTotalWidth,\n      contentHeight\n    });\n  }, [apiRef, columnsTotalWidth, contentHeight]);\n  useEnhancedEffect(() => {\n    if (!isRenderContextReady.current) {\n      return;\n    }\n    apiRef.current.updateRenderContext?.();\n  }, [apiRef, enabledForColumns, enabledForRows]);\n  useEnhancedEffect(() => {\n    if (listView) {\n      scrollerRef.current.scrollLeft = 0;\n    }\n  }, [listView, scrollerRef]);\n  useRunOnce(renderContext !== EMPTY_RENDER_CONTEXT, () => {\n    apiRef.current.publishEvent('scrollPositionChange', {\n      top: scrollPosition.current.top,\n      left: scrollPosition.current.left,\n      renderContext\n    });\n    isRenderContextReady.current = true;\n    if (rootProps.initialState?.scroll && scrollerRef.current) {\n      const scroller = scrollerRef.current;\n      const {\n        top,\n        left\n      } = rootProps.initialState.scroll;\n\n      // On initial mount, if we have columns available, we can restore the horizontal scroll immediately, but we need to skip the resulting scroll event, otherwise we would recalculate the render context at position top=0, left=restoredValue, but the initial render context is already calculated based on the initial value of scrollPosition ref.\n      const isScrollRestored = {\n        top: !(top > 0),\n        left: !(left > 0)\n      };\n      if (!isScrollRestored.left && columnsTotalWidth) {\n        scroller.scrollLeft = left;\n        ignoreNextScrollEvent.current = true;\n        isScrollRestored.left = true;\n      }\n\n      // For the sake of completeness, but I'm not sure if contentHeight is ever available at this point. Maybe when virtualisation is disabled?\n      if (!isScrollRestored.top && contentHeight) {\n        scroller.scrollTop = top;\n        ignoreNextScrollEvent.current = true;\n        isScrollRestored.top = true;\n      }\n\n      // To restore the vertical scroll, we need to wait until the rows are available in the DOM (otherwise there's nowhere to scroll), but before paint to avoid reflows\n      if (!isScrollRestored.top || !isScrollRestored.left) {\n        const unsubscribeContentSizeChange = apiRef.current.subscribeEvent('virtualScrollerContentSizeChange', params => {\n          if (!isScrollRestored.left && params.columnsTotalWidth) {\n            scroller.scrollLeft = left;\n            ignoreNextScrollEvent.current = true;\n            isScrollRestored.left = true;\n          }\n          if (!isScrollRestored.top && params.contentHeight) {\n            scroller.scrollTop = top;\n            ignoreNextScrollEvent.current = true;\n            isScrollRestored.top = true;\n          }\n          if (isScrollRestored.left && isScrollRestored.top) {\n            unsubscribeContentSizeChange();\n          }\n        });\n        return unsubscribeContentSizeChange;\n      }\n    }\n    return undefined;\n  });\n  apiRef.current.register('private', {\n    updateRenderContext: forceUpdateRenderContext\n  });\n  useGridEventPriority(apiRef, 'sortedRowsSet', forceUpdateRenderContext);\n  useGridEventPriority(apiRef, 'paginationModelChange', forceUpdateRenderContext);\n  useGridEventPriority(apiRef, 'columnsChange', forceUpdateRenderContext);\n  return {\n    renderContext,\n    setPanels,\n    getRows,\n    getContainerProps: () => ({\n      ref: mainRefCallback\n    }),\n    getScrollerProps: () => ({\n      ref: scrollerRef,\n      onScroll: handleScroll,\n      onWheel: handleWheel,\n      onTouchMove: handleTouchMove,\n      style: scrollerStyle,\n      role: 'presentation',\n      // `tabIndex` shouldn't be used along role=presentation, but it fixes a Firefox bug\n      // https://github.com/mui/mui-x/pull/13891#discussion_r1683416024\n      tabIndex: platform.isFirefox ? -1 : undefined\n    }),\n    getContentProps: () => ({\n      style: contentSize,\n      role: 'presentation',\n      ref: onContentSizeApplied\n    }),\n    getRenderZoneProps: () => ({\n      role: 'rowgroup'\n    }),\n    getScrollbarVerticalProps: () => ({\n      ref: scrollbarVerticalRef,\n      scrollPosition\n    }),\n    getScrollbarHorizontalProps: () => ({\n      ref: scrollbarHorizontalRef,\n      scrollPosition\n    }),\n    getScrollAreaProps: () => ({\n      scrollPosition\n    })\n  };\n};\n// dimension selectors\nfunction needsHorizontalScrollbarSelector(apiRef) {\n  return apiRef.current.state.dimensions.viewportOuterSize.width > 0 && apiRef.current.state.dimensions.columnsTotalWidth > apiRef.current.state.dimensions.viewportOuterSize.width;\n}\nfunction inputsSelector(apiRef, rootProps, enabledForRows, enabledForColumns) {\n  const dimensions = gridDimensionsSelector(apiRef);\n  const currentPage = getVisibleRows(apiRef, rootProps);\n  const visibleColumns = rootProps.listView ? [gridListColumnSelector(apiRef)] : gridVisibleColumnDefinitionsSelector(apiRef);\n  const hiddenCellsOriginMap = gridRowSpanningHiddenCellsOriginMapSelector(apiRef);\n  const lastRowId = apiRef.current.state.rows.dataRowIds.at(-1);\n  const lastColumn = visibleColumns.at(-1);\n  return {\n    enabledForRows,\n    enabledForColumns,\n    apiRef,\n    autoHeight: rootProps.autoHeight,\n    rowBufferPx: rootProps.rowBufferPx,\n    columnBufferPx: rootProps.columnBufferPx,\n    leftPinnedWidth: dimensions.leftPinnedWidth,\n    columnsTotalWidth: dimensions.columnsTotalWidth,\n    viewportInnerWidth: dimensions.viewportInnerSize.width,\n    viewportInnerHeight: dimensions.viewportInnerSize.height,\n    lastRowHeight: lastRowId !== undefined ? apiRef.current.unstable_getRowHeight(lastRowId) : 0,\n    lastColumnWidth: lastColumn?.computedWidth ?? 0,\n    rowsMeta: gridRowsMetaSelector(apiRef),\n    columnPositions: gridColumnPositionsSelector(apiRef),\n    rows: currentPage.rows,\n    range: currentPage.range,\n    pinnedColumns: gridVisiblePinnedColumnDefinitionsSelector(apiRef),\n    visibleColumns,\n    hiddenCellsOriginMap,\n    listView: rootProps.listView ?? false,\n    virtualizeColumnsWithAutoRowHeight: rootProps.virtualizeColumnsWithAutoRowHeight\n  };\n}\nfunction computeRenderContext(inputs, scrollPosition, scrollCache) {\n  const renderContext = {\n    firstRowIndex: 0,\n    lastRowIndex: inputs.rows.length,\n    firstColumnIndex: 0,\n    lastColumnIndex: inputs.visibleColumns.length\n  };\n  const {\n    top,\n    left\n  } = scrollPosition;\n  const realLeft = Math.abs(left) + inputs.leftPinnedWidth;\n  if (inputs.enabledForRows) {\n    // Clamp the value because the search may return an index out of bounds.\n    // In the last index, this is not needed because Array.slice doesn't include it.\n    let firstRowIndex = Math.min(getNearestIndexToRender(inputs, top, {\n      atStart: true,\n      lastPosition: inputs.rowsMeta.positions[inputs.rowsMeta.positions.length - 1] + inputs.lastRowHeight\n    }), inputs.rowsMeta.positions.length - 1);\n\n    // If any of the cells in the `firstRowIndex` is hidden due to an extended row span,\n    // Make sure the row from where the rowSpan is originated is visible.\n    const rowSpanHiddenCellOrigin = inputs.hiddenCellsOriginMap[firstRowIndex];\n    if (rowSpanHiddenCellOrigin) {\n      const minSpannedRowIndex = Math.min(...Object.values(rowSpanHiddenCellOrigin));\n      firstRowIndex = Math.min(firstRowIndex, minSpannedRowIndex);\n    }\n    const lastRowIndex = inputs.autoHeight ? firstRowIndex + inputs.rows.length : getNearestIndexToRender(inputs, top + inputs.viewportInnerHeight);\n    renderContext.firstRowIndex = firstRowIndex;\n    renderContext.lastRowIndex = lastRowIndex;\n  }\n  if (inputs.listView) {\n    return _extends({}, renderContext, {\n      lastColumnIndex: 1\n    });\n  }\n  if (inputs.enabledForColumns) {\n    let firstColumnIndex = 0;\n    let lastColumnIndex = inputs.columnPositions.length;\n    let hasRowWithAutoHeight = false;\n    const [firstRowToRender, lastRowToRender] = getIndexesToRender({\n      firstIndex: renderContext.firstRowIndex,\n      lastIndex: renderContext.lastRowIndex,\n      minFirstIndex: 0,\n      maxLastIndex: inputs.rows.length,\n      bufferBefore: scrollCache.buffer.rowBefore,\n      bufferAfter: scrollCache.buffer.rowAfter,\n      positions: inputs.rowsMeta.positions,\n      lastSize: inputs.lastRowHeight\n    });\n    if (!inputs.virtualizeColumnsWithAutoRowHeight) {\n      for (let i = firstRowToRender; i < lastRowToRender && !hasRowWithAutoHeight; i += 1) {\n        const row = inputs.rows[i];\n        hasRowWithAutoHeight = inputs.apiRef.current.rowHasAutoHeight(row.id);\n      }\n    }\n    if (!hasRowWithAutoHeight || inputs.virtualizeColumnsWithAutoRowHeight) {\n      firstColumnIndex = binarySearch(realLeft, inputs.columnPositions, {\n        atStart: true,\n        lastPosition: inputs.columnsTotalWidth\n      });\n      lastColumnIndex = binarySearch(realLeft + inputs.viewportInnerWidth, inputs.columnPositions);\n    }\n    renderContext.firstColumnIndex = firstColumnIndex;\n    renderContext.lastColumnIndex = lastColumnIndex;\n  }\n  const actualRenderContext = deriveRenderContext(inputs, renderContext, scrollCache);\n  return actualRenderContext;\n}\nfunction getNearestIndexToRender(inputs, offset, options) {\n  const lastMeasuredIndexRelativeToAllRows = inputs.apiRef.current.getLastMeasuredRowIndex();\n  let allRowsMeasured = lastMeasuredIndexRelativeToAllRows === Infinity;\n  if (inputs.range?.lastRowIndex && !allRowsMeasured) {\n    // Check if all rows in this page are already measured\n    allRowsMeasured = lastMeasuredIndexRelativeToAllRows >= inputs.range.lastRowIndex;\n  }\n  const lastMeasuredIndexRelativeToCurrentPage = clamp(lastMeasuredIndexRelativeToAllRows - (inputs.range?.firstRowIndex || 0), 0, inputs.rowsMeta.positions.length);\n  if (allRowsMeasured || inputs.rowsMeta.positions[lastMeasuredIndexRelativeToCurrentPage] >= offset) {\n    // If all rows were measured (when no row has \"auto\" as height) or all rows before the offset\n    // were measured, then use a binary search because it's faster.\n    return binarySearch(offset, inputs.rowsMeta.positions, options);\n  }\n\n  // Otherwise, use an exponential search.\n  // If rows have \"auto\" as height, their positions will be based on estimated heights.\n  // In this case, we can skip several steps until we find a position higher than the offset.\n  // Inspired by https://github.com/bvaughn/react-virtualized/blob/master/source/Grid/utils/CellSizeAndPositionManager.js\n  return exponentialSearch(offset, inputs.rowsMeta.positions, lastMeasuredIndexRelativeToCurrentPage, options);\n}\n\n/**\n * Accepts as input a raw render context (the area visible in the viewport) and adds\n * computes the actual render context based on pinned elements, buffer dimensions and\n * spanning.\n */\nfunction deriveRenderContext(inputs, nextRenderContext, scrollCache) {\n  const [firstRowToRender, lastRowToRender] = getIndexesToRender({\n    firstIndex: nextRenderContext.firstRowIndex,\n    lastIndex: nextRenderContext.lastRowIndex,\n    minFirstIndex: 0,\n    maxLastIndex: inputs.rows.length,\n    bufferBefore: scrollCache.buffer.rowBefore,\n    bufferAfter: scrollCache.buffer.rowAfter,\n    positions: inputs.rowsMeta.positions,\n    lastSize: inputs.lastRowHeight\n  });\n  const [initialFirstColumnToRender, lastColumnToRender] = getIndexesToRender({\n    firstIndex: nextRenderContext.firstColumnIndex,\n    lastIndex: nextRenderContext.lastColumnIndex,\n    minFirstIndex: inputs.pinnedColumns.left.length,\n    maxLastIndex: inputs.visibleColumns.length - inputs.pinnedColumns.right.length,\n    bufferBefore: scrollCache.buffer.columnBefore,\n    bufferAfter: scrollCache.buffer.columnAfter,\n    positions: inputs.columnPositions,\n    lastSize: inputs.lastColumnWidth\n  });\n  const firstColumnToRender = getFirstNonSpannedColumnToRender({\n    firstColumnToRender: initialFirstColumnToRender,\n    apiRef: inputs.apiRef,\n    firstRowToRender,\n    lastRowToRender,\n    visibleRows: inputs.rows\n  });\n  return {\n    firstRowIndex: firstRowToRender,\n    lastRowIndex: lastRowToRender,\n    firstColumnIndex: firstColumnToRender,\n    lastColumnIndex: lastColumnToRender\n  };\n}\n/**\n * Use binary search to avoid looping through all possible positions.\n * The `options.atStart` provides the possibility to match for the first element that\n * intersects the screen, even if said element's start position is before `offset`. In\n * other words, we search for `offset + width`.\n */\nfunction binarySearch(offset, positions, options = undefined, sliceStart = 0, sliceEnd = positions.length) {\n  if (positions.length <= 0) {\n    return -1;\n  }\n  if (sliceStart >= sliceEnd) {\n    return sliceStart;\n  }\n  const pivot = sliceStart + Math.floor((sliceEnd - sliceStart) / 2);\n  const position = positions[pivot];\n  let isBefore;\n  if (options?.atStart) {\n    const width = (pivot === positions.length - 1 ? options.lastPosition : positions[pivot + 1]) - position;\n    isBefore = offset - width < position;\n  } else {\n    isBefore = offset <= position;\n  }\n  return isBefore ? binarySearch(offset, positions, options, sliceStart, pivot) : binarySearch(offset, positions, options, pivot + 1, sliceEnd);\n}\nfunction exponentialSearch(offset, positions, index, options = undefined) {\n  let interval = 1;\n  while (index < positions.length && Math.abs(positions[index]) < offset) {\n    index += interval;\n    interval *= 2;\n  }\n  return binarySearch(offset, positions, options, Math.floor(index / 2), Math.min(index, positions.length));\n}\nfunction getIndexesToRender({\n  firstIndex,\n  lastIndex,\n  bufferBefore,\n  bufferAfter,\n  minFirstIndex,\n  maxLastIndex,\n  positions,\n  lastSize\n}) {\n  const firstPosition = positions[firstIndex] - bufferBefore;\n  const lastPosition = positions[lastIndex] + bufferAfter;\n  const firstIndexPadded = binarySearch(firstPosition, positions, {\n    atStart: true,\n    lastPosition: positions[positions.length - 1] + lastSize\n  });\n  const lastIndexPadded = binarySearch(lastPosition, positions);\n  return [clamp(firstIndexPadded, minFirstIndex, maxLastIndex), clamp(lastIndexPadded, minFirstIndex, maxLastIndex)];\n}\nexport function areRenderContextsEqual(context1, context2) {\n  if (context1 === context2) {\n    return true;\n  }\n  return context1.firstRowIndex === context2.firstRowIndex && context1.lastRowIndex === context2.lastRowIndex && context1.firstColumnIndex === context2.firstColumnIndex && context1.lastColumnIndex === context2.lastColumnIndex;\n}\nexport function computeOffsetLeft(columnPositions, renderContext, pinnedLeftLength) {\n  const left = (columnPositions[renderContext.firstColumnIndex] ?? 0) - (columnPositions[pinnedLeftLength] ?? 0);\n  return Math.abs(left);\n}\nfunction directionForDelta(dx, dy) {\n  if (dx === 0 && dy === 0) {\n    return ScrollDirection.NONE;\n  }\n  /* eslint-disable */\n  if (Math.abs(dy) >= Math.abs(dx)) {\n    if (dy > 0) {\n      return ScrollDirection.DOWN;\n    } else {\n      return ScrollDirection.UP;\n    }\n  } else {\n    if (dx > 0) {\n      return ScrollDirection.RIGHT;\n    } else {\n      return ScrollDirection.LEFT;\n    }\n  }\n  /* eslint-enable */\n}\nfunction bufferForDirection(isRtl, direction, rowBufferPx, columnBufferPx, verticalBuffer, horizontalBuffer) {\n  if (isRtl) {\n    switch (direction) {\n      case ScrollDirection.LEFT:\n        direction = ScrollDirection.RIGHT;\n        break;\n      case ScrollDirection.RIGHT:\n        direction = ScrollDirection.LEFT;\n        break;\n      default:\n    }\n  }\n  switch (direction) {\n    case ScrollDirection.NONE:\n      return {\n        rowAfter: rowBufferPx,\n        rowBefore: rowBufferPx,\n        columnAfter: columnBufferPx,\n        columnBefore: columnBufferPx\n      };\n    case ScrollDirection.LEFT:\n      return {\n        rowAfter: 0,\n        rowBefore: 0,\n        columnAfter: 0,\n        columnBefore: horizontalBuffer\n      };\n    case ScrollDirection.RIGHT:\n      return {\n        rowAfter: 0,\n        rowBefore: 0,\n        columnAfter: horizontalBuffer,\n        columnBefore: 0\n      };\n    case ScrollDirection.UP:\n      return {\n        rowAfter: 0,\n        rowBefore: verticalBuffer,\n        columnAfter: 0,\n        columnBefore: 0\n      };\n    case ScrollDirection.DOWN:\n      return {\n        rowAfter: verticalBuffer,\n        rowBefore: 0,\n        columnAfter: 0,\n        columnBefore: 0\n      };\n    default:\n      // eslint unable to figure out enum exhaustiveness\n      throw new Error('unreachable');\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}