{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useTheme } from '@mui/material/styles';\nimport { getThemeProps } from '@mui/system';\nimport { GRID_DEFAULT_LOCALE_TEXT } from \"../constants/index.js\";\nimport { DATA_GRID_DEFAULT_SLOTS_COMPONENTS } from \"../constants/defaultGridSlotsComponents.js\";\nimport { computeSlots } from \"../internals/utils/index.js\";\nimport { DATA_GRID_PROPS_DEFAULT_VALUES } from \"../constants/dataGridPropsDefaultValues.js\";\nconst DATA_GRID_FORCED_PROPS = {\n  disableMultipleColumnsFiltering: true,\n  disableMultipleColumnsSorting: true,\n  throttleRowsMs: undefined,\n  hideFooterRowCount: false,\n  pagination: true,\n  checkboxSelectionVisibleOnly: false,\n  disableColumnReorder: true,\n  keepColumnPositionIfDraggedOutside: false,\n  signature: 'DataGrid',\n  listView: false\n};\nconst getDataGridForcedProps = themedProps => _extends({}, DATA_GRID_FORCED_PROPS, themedProps.dataSource ? {\n  filterMode: 'server',\n  sortingMode: 'server',\n  paginationMode: 'server'\n} : {});\nconst defaultSlots = DATA_GRID_DEFAULT_SLOTS_COMPONENTS;\nexport const useDataGridProps = inProps => {\n  const theme = useTheme();\n  const themedProps = React.useMemo(() => getThemeProps({\n    props: inProps,\n    theme,\n    name: 'MuiDataGrid'\n  }), [theme, inProps]);\n  const localeText = React.useMemo(() => _extends({}, GRID_DEFAULT_LOCALE_TEXT, themedProps.localeText), [themedProps.localeText]);\n  const slots = React.useMemo(() => computeSlots({\n    defaultSlots,\n    slots: themedProps.slots\n  }), [themedProps.slots]);\n  const injectDefaultProps = React.useMemo(() => {\n    return Object.keys(DATA_GRID_PROPS_DEFAULT_VALUES).reduce((acc, key) => {\n      // @ts-ignore\n      acc[key] = themedProps[key] ?? DATA_GRID_PROPS_DEFAULT_VALUES[key];\n      return acc;\n    }, {});\n  }, [themedProps]);\n  return React.useMemo(() => _extends({}, themedProps, injectDefaultProps, {\n    localeText,\n    slots\n  }, getDataGridForcedProps(themedProps)), [themedProps, localeText, slots, injectDefaultProps]);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}