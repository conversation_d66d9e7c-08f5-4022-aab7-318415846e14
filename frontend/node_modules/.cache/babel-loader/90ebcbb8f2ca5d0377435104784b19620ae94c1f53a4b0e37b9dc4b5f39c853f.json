{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"render\", \"options\", \"onClick\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { useComponentRenderer } from '@mui/x-internals/useComponentRenderer';\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * A button that triggers a CSV export.\n * It renders the `baseButton` slot.\n *\n * Demos:\n *\n * - [Export](https://mui.com/x/react-data-grid/components/export/)\n *\n * API:\n *\n * - [ExportCsv API](https://mui.com/x/api/data-grid/export-csv/)\n */\nconst ExportCsv = forwardRef(function ExportCsv(props, ref) {\n  const {\n      render,\n      options,\n      onClick\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const rootProps = useGridRootProps();\n  const apiRef = useGridApiContext();\n  const handleClick = event => {\n    apiRef.current.exportDataAsCsv(options);\n    onClick?.(event);\n  };\n  const element = useComponentRenderer(rootProps.slots.baseButton, render, _extends({}, rootProps.slotProps?.baseButton, {\n    onClick: handleClick\n  }, other, {\n    ref\n  }));\n  return /*#__PURE__*/_jsx(React.Fragment, {\n    children: element\n  });\n});\nif (process.env.NODE_ENV !== \"production\") ExportCsv.displayName = \"ExportCsv\";\nprocess.env.NODE_ENV !== \"production\" ? ExportCsv.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  className: PropTypes.string,\n  disabled: PropTypes.bool,\n  id: PropTypes.string,\n  /**\n   * The options to apply on the CSV export.\n   * @demos\n   *   - [CSV export](/x/react-data-grid/export/#csv-export)\n   */\n  options: PropTypes.shape({\n    allColumns: PropTypes.bool,\n    delimiter: PropTypes.string,\n    escapeFormulas: PropTypes.bool,\n    fields: PropTypes.arrayOf(PropTypes.string),\n    fileName: PropTypes.string,\n    getRowsToExport: PropTypes.func,\n    includeColumnGroupsHeaders: PropTypes.bool,\n    includeHeaders: PropTypes.bool,\n    shouldAppendQuotes: PropTypes.bool,\n    utf8WithBom: PropTypes.bool\n  }),\n  /**\n   * A function to customize rendering of the component.\n   */\n  render: PropTypes.oneOfType([PropTypes.element, PropTypes.func]),\n  role: PropTypes.string,\n  size: PropTypes.oneOf(['large', 'medium', 'small']),\n  startIcon: PropTypes.node,\n  style: PropTypes.object,\n  tabIndex: PropTypes.number,\n  title: PropTypes.string,\n  touchRippleRef: PropTypes.any\n} : void 0;\nexport { ExportCsv };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}