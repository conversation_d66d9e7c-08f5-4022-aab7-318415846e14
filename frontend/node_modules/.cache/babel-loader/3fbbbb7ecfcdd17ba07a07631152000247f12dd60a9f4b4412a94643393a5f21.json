{"ast": null, "code": "import { createSelector, createRootSelector, createSelectorMemoized } from \"../../../utils/createSelector.js\";\n/**\n * @category ColumnGrouping\n * @ignore - do not document.\n */\nexport const gridColumnGroupingSelector = createRootSelector(state => state.columnGrouping);\nexport const gridColumnGroupsUnwrappedModelSelector = createSelectorMemoized(gridColumnGroupingSelector, columnGrouping => columnGrouping?.unwrappedGroupingModel ?? {});\nexport const gridColumnGroupsLookupSelector = createSelectorMemoized(gridColumnGroupingSelector, columnGrouping => columnGrouping?.lookup ?? {});\nexport const gridColumnGroupsHeaderStructureSelector = createSelectorMemoized(gridColumnGroupingSelector, columnGrouping => columnGrouping?.headerStructure ?? []);\nexport const gridColumnGroupsHeaderMaxDepthSelector = createSelector(gridColumnGroupingSelector, columnGrouping => columnGrouping?.maxDepth ?? 0);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}