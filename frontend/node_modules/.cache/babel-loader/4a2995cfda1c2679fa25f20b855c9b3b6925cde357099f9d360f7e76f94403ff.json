{"ast": null, "code": "import * as React from 'react';\nexport const useGridInitializeState = (initializer, privateApiRef, props) => {\n  const isInitialized = React.useRef(false);\n  if (!isInitialized.current) {\n    privateApiRef.current.state = initializer(privateApiRef.current.state, props, privateApiRef);\n    isInitialized.current = true;\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}