{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/Roles.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Button, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormGroup, FormControlLabel, Checkbox, Chip, IconButton, Tooltip } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, Security as SecurityIcon } from '@mui/icons-material';\nimport { toast } from 'react-toastify';\nimport apiService from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Roles = () => {\n  _s();\n  const [roles, setRoles] = useState([]);\n  const [permissions, setPermissions] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [dialogOpen, setDialogOpen] = useState(false);\n  const [editingRole, setEditingRole] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    permission_ids: []\n  });\n  useEffect(() => {\n    fetchRoles();\n    fetchPermissions();\n  }, []);\n  const fetchRoles = async () => {\n    try {\n      const rolesData = await apiService.getAllRoles();\n      setRoles(rolesData);\n    } catch (error) {\n      toast.error('Erreur lors du chargement des rôles');\n    }\n  };\n  const fetchPermissions = async () => {\n    try {\n      const permissionsData = await apiService.getAllPermissions();\n      setPermissions(permissionsData);\n      setLoading(false);\n    } catch (error) {\n      toast.error('Erreur lors du chargement des permissions');\n      setLoading(false);\n    }\n  };\n  const handleOpenDialog = role => {\n    if (role) {\n      setEditingRole(role);\n      setFormData({\n        name: role.name,\n        description: role.description,\n        permission_ids: role.permissions.map(p => p.id)\n      });\n    } else {\n      setEditingRole(null);\n      setFormData({\n        name: '',\n        description: '',\n        permission_ids: []\n      });\n    }\n    setDialogOpen(true);\n  };\n  const handleCloseDialog = () => {\n    setDialogOpen(false);\n    setEditingRole(null);\n    setFormData({\n      name: '',\n      description: '',\n      permission_ids: []\n    });\n  };\n  const handlePermissionChange = (permissionId, checked) => {\n    setFormData(prev => ({\n      ...prev,\n      permission_ids: checked ? [...prev.permission_ids, permissionId] : prev.permission_ids.filter(id => id !== permissionId)\n    }));\n  };\n  const handleSubmit = async () => {\n    try {\n      if (editingRole) {\n        await api.put(`/roles/${editingRole.id}`, formData);\n        toast.success('Rôle modifié avec succès');\n      } else {\n        await api.post('/roles/', formData);\n        toast.success('Rôle créé avec succès');\n      }\n      handleCloseDialog();\n      fetchRoles();\n    } catch (error) {\n      toast.error('Erreur lors de la sauvegarde du rôle');\n    }\n  };\n  const handleDelete = async roleId => {\n    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce rôle ?')) {\n      try {\n        await api.delete(`/roles/${roleId}`);\n        toast.success('Rôle supprimé avec succès');\n        fetchRoles();\n      } catch (error) {\n        toast.error('Erreur lors de la suppression du rôle');\n      }\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        mt: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        children: \"Chargement...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(SecurityIcon, {\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          component: \"h1\",\n          children: \"Gestion des R\\xF4les\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 22\n        }, this),\n        onClick: () => handleOpenDialog(),\n        children: \"Nouveau R\\xF4le\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Nom\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Permissions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"center\",\n              children: \"Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: roles.map(role => /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle1\",\n                fontWeight: \"bold\",\n                children: role.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: role.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  flexWrap: 'wrap',\n                  gap: 0.5\n                },\n                children: role.permissions.map(permission => /*#__PURE__*/_jsxDEV(Chip, {\n                  label: permission.name,\n                  size: \"small\",\n                  color: \"primary\",\n                  variant: \"outlined\"\n                }, permission.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"Modifier\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  color: \"primary\",\n                  onClick: () => handleOpenDialog(role),\n                  children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 215,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"Supprimer\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  color: \"error\",\n                  onClick: () => handleDelete(role.id),\n                  children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 223,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 17\n            }, this)]\n          }, role.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: dialogOpen,\n      onClose: handleCloseDialog,\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: editingRole ? 'Modifier le rôle' : 'Créer un nouveau rôle'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Nom du r\\xF4le\",\n            value: formData.name,\n            onChange: e => setFormData(prev => ({\n              ...prev,\n              name: e.target.value\n            })),\n            margin: \"normal\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Description\",\n            value: formData.description,\n            onChange: e => setFormData(prev => ({\n              ...prev,\n              description: e.target.value\n            })),\n            margin: \"normal\",\n            multiline: true,\n            rows: 3\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              mt: 3,\n              mb: 2\n            },\n            children: \"Permissions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n            children: permissions.map(permission => /*#__PURE__*/_jsxDEV(FormControlLabel, {\n              control: /*#__PURE__*/_jsxDEV(Checkbox, {\n                checked: formData.permission_ids.includes(permission.id),\n                onChange: e => handlePermissionChange(permission.id, e.target.checked)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 21\n              }, this),\n              label: /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: permission.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: permission.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 21\n              }, this)\n            }, permission.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseDialog,\n          children: \"Annuler\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmit,\n          variant: \"contained\",\n          disabled: !formData.name.trim(),\n          children: editingRole ? 'Modifier' : 'Créer'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 234,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 160,\n    columnNumber: 5\n  }, this);\n};\n_s(Roles, \"QF01a6wt7phJKHm3wMUO2wTnafk=\");\n_c = Roles;\nexport default Roles;\nvar _c;\n$RefreshReg$(_c, \"Roles\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "FormGroup", "FormControlLabel", "Checkbox", "Chip", "IconButton", "<PERSON><PERSON><PERSON>", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Security", "SecurityIcon", "toast", "apiService", "jsxDEV", "_jsxDEV", "Roles", "_s", "roles", "setRoles", "permissions", "setPermissions", "loading", "setLoading", "dialogOpen", "setDialogOpen", "editingRole", "setEditingRole", "formData", "setFormData", "name", "description", "permission_ids", "fetchRoles", "fetchPermissions", "rolesData", "getAllRoles", "error", "permissionsData", "getAllPermissions", "handleOpenDialog", "role", "map", "p", "id", "handleCloseDialog", "handlePermissionChange", "permissionId", "checked", "prev", "filter", "handleSubmit", "api", "put", "success", "post", "handleDelete", "roleId", "window", "confirm", "delete", "sx", "display", "justifyContent", "mt", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "alignItems", "mb", "gap", "color", "variant", "component", "startIcon", "onClick", "align", "fontWeight", "flexWrap", "permission", "label", "size", "title", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "pt", "value", "onChange", "e", "target", "margin", "required", "multiline", "rows", "control", "includes", "disabled", "trim", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/Roles.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  FormGroup,\n  FormControlLabel,\n  Checkbox,\n  Chip,\n  IconButton,\n  Tooltip,\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Security as SecurityIcon,\n} from '@mui/icons-material';\nimport { toast } from 'react-toastify';\nimport apiService from '../services/api';\n\ninterface Permission {\n  id: number;\n  code: string;\n  name: string;\n  description: string;\n}\n\ninterface Role {\n  id: number;\n  name: string;\n  description: string;\n  permissions: Permission[];\n}\n\nconst Roles: React.FC = () => {\n  const [roles, setRoles] = useState<Role[]>([]);\n  const [permissions, setPermissions] = useState<Permission[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [dialogOpen, setDialogOpen] = useState(false);\n  const [editingRole, setEditingRole] = useState<Role | null>(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    permission_ids: [] as number[],\n  });\n\n  useEffect(() => {\n    fetchRoles();\n    fetchPermissions();\n  }, []);\n\n  const fetchRoles = async () => {\n    try {\n      const rolesData = await apiService.getAllRoles();\n      setRoles(rolesData);\n    } catch (error) {\n      toast.error('Erreur lors du chargement des rôles');\n    }\n  };\n\n  const fetchPermissions = async () => {\n    try {\n      const permissionsData = await apiService.getAllPermissions();\n      setPermissions(permissionsData);\n      setLoading(false);\n    } catch (error) {\n      toast.error('Erreur lors du chargement des permissions');\n      setLoading(false);\n    }\n  };\n\n  const handleOpenDialog = (role?: Role) => {\n    if (role) {\n      setEditingRole(role);\n      setFormData({\n        name: role.name,\n        description: role.description,\n        permission_ids: role.permissions.map(p => p.id),\n      });\n    } else {\n      setEditingRole(null);\n      setFormData({\n        name: '',\n        description: '',\n        permission_ids: [],\n      });\n    }\n    setDialogOpen(true);\n  };\n\n  const handleCloseDialog = () => {\n    setDialogOpen(false);\n    setEditingRole(null);\n    setFormData({\n      name: '',\n      description: '',\n      permission_ids: [],\n    });\n  };\n\n  const handlePermissionChange = (permissionId: number, checked: boolean) => {\n    setFormData(prev => ({\n      ...prev,\n      permission_ids: checked\n        ? [...prev.permission_ids, permissionId]\n        : prev.permission_ids.filter(id => id !== permissionId)\n    }));\n  };\n\n  const handleSubmit = async () => {\n    try {\n      if (editingRole) {\n        await api.put(`/roles/${editingRole.id}`, formData);\n        toast.success('Rôle modifié avec succès');\n      } else {\n        await api.post('/roles/', formData);\n        toast.success('Rôle créé avec succès');\n      }\n      handleCloseDialog();\n      fetchRoles();\n    } catch (error) {\n      toast.error('Erreur lors de la sauvegarde du rôle');\n    }\n  };\n\n  const handleDelete = async (roleId: number) => {\n    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce rôle ?')) {\n      try {\n        await api.delete(`/roles/${roleId}`);\n        toast.success('Rôle supprimé avec succès');\n        fetchRoles();\n      } catch (error) {\n        toast.error('Erreur lors de la suppression du rôle');\n      }\n    }\n  };\n\n  if (loading) {\n    return (\n      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>\n        <Typography>Chargement...</Typography>\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ p: 3 }}>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n          <SecurityIcon color=\"primary\" />\n          <Typography variant=\"h4\" component=\"h1\">\n            Gestion des Rôles\n          </Typography>\n        </Box>\n        <Button\n          variant=\"contained\"\n          startIcon={<AddIcon />}\n          onClick={() => handleOpenDialog()}\n        >\n          Nouveau Rôle\n        </Button>\n      </Box>\n\n      <TableContainer component={Paper}>\n        <Table>\n          <TableHead>\n            <TableRow>\n              <TableCell>Nom</TableCell>\n              <TableCell>Description</TableCell>\n              <TableCell>Permissions</TableCell>\n              <TableCell align=\"center\">Actions</TableCell>\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {roles.map((role) => (\n              <TableRow key={role.id}>\n                <TableCell>\n                  <Typography variant=\"subtitle1\" fontWeight=\"bold\">\n                    {role.name}\n                  </Typography>\n                </TableCell>\n                <TableCell>{role.description}</TableCell>\n                <TableCell>\n                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>\n                    {role.permissions.map((permission) => (\n                      <Chip\n                        key={permission.id}\n                        label={permission.name}\n                        size=\"small\"\n                        color=\"primary\"\n                        variant=\"outlined\"\n                      />\n                    ))}\n                  </Box>\n                </TableCell>\n                <TableCell align=\"center\">\n                  <Tooltip title=\"Modifier\">\n                    <IconButton\n                      color=\"primary\"\n                      onClick={() => handleOpenDialog(role)}\n                    >\n                      <EditIcon />\n                    </IconButton>\n                  </Tooltip>\n                  <Tooltip title=\"Supprimer\">\n                    <IconButton\n                      color=\"error\"\n                      onClick={() => handleDelete(role.id)}\n                    >\n                      <DeleteIcon />\n                    </IconButton>\n                  </Tooltip>\n                </TableCell>\n              </TableRow>\n            ))}\n          </TableBody>\n        </Table>\n      </TableContainer>\n\n      {/* Dialog pour créer/modifier un rôle */}\n      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          {editingRole ? 'Modifier le rôle' : 'Créer un nouveau rôle'}\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 2 }}>\n            <TextField\n              fullWidth\n              label=\"Nom du rôle\"\n              value={formData.name}\n              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}\n              margin=\"normal\"\n              required\n            />\n            <TextField\n              fullWidth\n              label=\"Description\"\n              value={formData.description}\n              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}\n              margin=\"normal\"\n              multiline\n              rows={3}\n            />\n            \n            <Typography variant=\"h6\" sx={{ mt: 3, mb: 2 }}>\n              Permissions\n            </Typography>\n            <FormGroup>\n              {permissions.map((permission) => (\n                <FormControlLabel\n                  key={permission.id}\n                  control={\n                    <Checkbox\n                      checked={formData.permission_ids.includes(permission.id)}\n                      onChange={(e) => handlePermissionChange(permission.id, e.target.checked)}\n                    />\n                  }\n                  label={\n                    <Box>\n                      <Typography variant=\"body1\">{permission.name}</Typography>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        {permission.description}\n                      </Typography>\n                    </Box>\n                  }\n                />\n              ))}\n            </FormGroup>\n          </Box>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCloseDialog}>Annuler</Button>\n          <Button\n            onClick={handleSubmit}\n            variant=\"contained\"\n            disabled={!formData.name.trim()}\n          >\n            {editingRole ? 'Modifier' : 'Créer'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default Roles;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,SAAS,EACTC,gBAAgB,EAChBC,QAAQ,EACRC,IAAI,EACJC,UAAU,EACVC,OAAO,QACF,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,QACnB,qBAAqB;AAC5B,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,UAAU,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAgBzC,MAAMC,KAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGtC,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACuC,WAAW,EAAEC,cAAc,CAAC,GAAGxC,QAAQ,CAAe,EAAE,CAAC;EAChE,MAAM,CAACyC,OAAO,EAAEC,UAAU,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2C,UAAU,EAAEC,aAAa,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC6C,WAAW,EAAEC,cAAc,CAAC,GAAG9C,QAAQ,CAAc,IAAI,CAAC;EACjE,MAAM,CAAC+C,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAAC;IACvCiD,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE;EAClB,CAAC,CAAC;EAEFlD,SAAS,CAAC,MAAM;IACdmD,UAAU,CAAC,CAAC;IACZC,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAME,SAAS,GAAG,MAAMtB,UAAU,CAACuB,WAAW,CAAC,CAAC;MAChDjB,QAAQ,CAACgB,SAAS,CAAC;IACrB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdzB,KAAK,CAACyB,KAAK,CAAC,qCAAqC,CAAC;IACpD;EACF,CAAC;EAED,MAAMH,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMI,eAAe,GAAG,MAAMzB,UAAU,CAAC0B,iBAAiB,CAAC,CAAC;MAC5DlB,cAAc,CAACiB,eAAe,CAAC;MAC/Bf,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAOc,KAAK,EAAE;MACdzB,KAAK,CAACyB,KAAK,CAAC,2CAA2C,CAAC;MACxDd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiB,gBAAgB,GAAIC,IAAW,IAAK;IACxC,IAAIA,IAAI,EAAE;MACRd,cAAc,CAACc,IAAI,CAAC;MACpBZ,WAAW,CAAC;QACVC,IAAI,EAAEW,IAAI,CAACX,IAAI;QACfC,WAAW,EAAEU,IAAI,CAACV,WAAW;QAC7BC,cAAc,EAAES,IAAI,CAACrB,WAAW,CAACsB,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE;MAChD,CAAC,CAAC;IACJ,CAAC,MAAM;MACLjB,cAAc,CAAC,IAAI,CAAC;MACpBE,WAAW,CAAC;QACVC,IAAI,EAAE,EAAE;QACRC,WAAW,EAAE,EAAE;QACfC,cAAc,EAAE;MAClB,CAAC,CAAC;IACJ;IACAP,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMoB,iBAAiB,GAAGA,CAAA,KAAM;IAC9BpB,aAAa,CAAC,KAAK,CAAC;IACpBE,cAAc,CAAC,IAAI,CAAC;IACpBE,WAAW,CAAC;MACVC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE;IAClB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMc,sBAAsB,GAAGA,CAACC,YAAoB,EAAEC,OAAgB,KAAK;IACzEnB,WAAW,CAACoB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPjB,cAAc,EAAEgB,OAAO,GACnB,CAAC,GAAGC,IAAI,CAACjB,cAAc,EAAEe,YAAY,CAAC,GACtCE,IAAI,CAACjB,cAAc,CAACkB,MAAM,CAACN,EAAE,IAAIA,EAAE,KAAKG,YAAY;IAC1D,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMI,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,IAAIzB,WAAW,EAAE;QACf,MAAM0B,GAAG,CAACC,GAAG,CAAC,UAAU3B,WAAW,CAACkB,EAAE,EAAE,EAAEhB,QAAQ,CAAC;QACnDhB,KAAK,CAAC0C,OAAO,CAAC,0BAA0B,CAAC;MAC3C,CAAC,MAAM;QACL,MAAMF,GAAG,CAACG,IAAI,CAAC,SAAS,EAAE3B,QAAQ,CAAC;QACnChB,KAAK,CAAC0C,OAAO,CAAC,uBAAuB,CAAC;MACxC;MACAT,iBAAiB,CAAC,CAAC;MACnBZ,UAAU,CAAC,CAAC;IACd,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdzB,KAAK,CAACyB,KAAK,CAAC,sCAAsC,CAAC;IACrD;EACF,CAAC;EAED,MAAMmB,YAAY,GAAG,MAAOC,MAAc,IAAK;IAC7C,IAAIC,MAAM,CAACC,OAAO,CAAC,8CAA8C,CAAC,EAAE;MAClE,IAAI;QACF,MAAMP,GAAG,CAACQ,MAAM,CAAC,UAAUH,MAAM,EAAE,CAAC;QACpC7C,KAAK,CAAC0C,OAAO,CAAC,2BAA2B,CAAC;QAC1CrB,UAAU,CAAC,CAAC;MACd,CAAC,CAAC,OAAOI,KAAK,EAAE;QACdzB,KAAK,CAACyB,KAAK,CAAC,uCAAuC,CAAC;MACtD;IACF;EACF,CAAC;EAED,IAAIf,OAAO,EAAE;IACX,oBACEP,OAAA,CAAChC,GAAG;MAAC8E,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,eAC5DlD,OAAA,CAAC/B,UAAU;QAAAiF,QAAA,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CAAC;EAEV;EAEA,oBACEtD,OAAA,CAAChC,GAAG;IAAC8E,EAAE,EAAE;MAAElB,CAAC,EAAE;IAAE,CAAE;IAAAsB,QAAA,gBAChBlD,OAAA,CAAChC,GAAG;MAAC8E,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEO,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAN,QAAA,gBACzFlD,OAAA,CAAChC,GAAG;QAAC8E,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEQ,UAAU,EAAE,QAAQ;UAAEE,GAAG,EAAE;QAAE,CAAE;QAAAP,QAAA,gBACzDlD,OAAA,CAACJ,YAAY;UAAC8D,KAAK,EAAC;QAAS;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChCtD,OAAA,CAAC/B,UAAU;UAAC0F,OAAO,EAAC,IAAI;UAACC,SAAS,EAAC,IAAI;UAAAV,QAAA,EAAC;QAExC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACNtD,OAAA,CAAC9B,MAAM;QACLyF,OAAO,EAAC,WAAW;QACnBE,SAAS,eAAE7D,OAAA,CAACV,OAAO;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBQ,OAAO,EAAEA,CAAA,KAAMrC,gBAAgB,CAAC,CAAE;QAAAyB,QAAA,EACnC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENtD,OAAA,CAACzB,cAAc;MAACqF,SAAS,EAAEzF,KAAM;MAAA+E,QAAA,eAC/BlD,OAAA,CAAC5B,KAAK;QAAA8E,QAAA,gBACJlD,OAAA,CAACxB,SAAS;UAAA0E,QAAA,eACRlD,OAAA,CAACvB,QAAQ;YAAAyE,QAAA,gBACPlD,OAAA,CAAC1B,SAAS;cAAA4E,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC1BtD,OAAA,CAAC1B,SAAS;cAAA4E,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAClCtD,OAAA,CAAC1B,SAAS;cAAA4E,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAClCtD,OAAA,CAAC1B,SAAS;cAACyF,KAAK,EAAC,QAAQ;cAAAb,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZtD,OAAA,CAAC3B,SAAS;UAAA6E,QAAA,EACP/C,KAAK,CAACwB,GAAG,CAAED,IAAI,iBACd1B,OAAA,CAACvB,QAAQ;YAAAyE,QAAA,gBACPlD,OAAA,CAAC1B,SAAS;cAAA4E,QAAA,eACRlD,OAAA,CAAC/B,UAAU;gBAAC0F,OAAO,EAAC,WAAW;gBAACK,UAAU,EAAC,MAAM;gBAAAd,QAAA,EAC9CxB,IAAI,CAACX;cAAI;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACZtD,OAAA,CAAC1B,SAAS;cAAA4E,QAAA,EAAExB,IAAI,CAACV;YAAW;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACzCtD,OAAA,CAAC1B,SAAS;cAAA4E,QAAA,eACRlD,OAAA,CAAChC,GAAG;gBAAC8E,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEkB,QAAQ,EAAE,MAAM;kBAAER,GAAG,EAAE;gBAAI,CAAE;gBAAAP,QAAA,EACtDxB,IAAI,CAACrB,WAAW,CAACsB,GAAG,CAAEuC,UAAU,iBAC/BlE,OAAA,CAACd,IAAI;kBAEHiF,KAAK,EAAED,UAAU,CAACnD,IAAK;kBACvBqD,IAAI,EAAC,OAAO;kBACZV,KAAK,EAAC,SAAS;kBACfC,OAAO,EAAC;gBAAU,GAJbO,UAAU,CAACrC,EAAE;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAKnB,CACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eACZtD,OAAA,CAAC1B,SAAS;cAACyF,KAAK,EAAC,QAAQ;cAAAb,QAAA,gBACvBlD,OAAA,CAACZ,OAAO;gBAACiF,KAAK,EAAC,UAAU;gBAAAnB,QAAA,eACvBlD,OAAA,CAACb,UAAU;kBACTuE,KAAK,EAAC,SAAS;kBACfI,OAAO,EAAEA,CAAA,KAAMrC,gBAAgB,CAACC,IAAI,CAAE;kBAAAwB,QAAA,eAEtClD,OAAA,CAACR,QAAQ;oBAAA2D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACVtD,OAAA,CAACZ,OAAO;gBAACiF,KAAK,EAAC,WAAW;gBAAAnB,QAAA,eACxBlD,OAAA,CAACb,UAAU;kBACTuE,KAAK,EAAC,OAAO;kBACbI,OAAO,EAAEA,CAAA,KAAMrB,YAAY,CAACf,IAAI,CAACG,EAAE,CAAE;kBAAAqB,QAAA,eAErClD,OAAA,CAACN,UAAU;oBAAAyD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA,GArCC5B,IAAI,CAACG,EAAE;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAsCZ,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAGjBtD,OAAA,CAACtB,MAAM;MAAC4F,IAAI,EAAE7D,UAAW;MAAC8D,OAAO,EAAEzC,iBAAkB;MAAC0C,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAAvB,QAAA,gBAC3ElD,OAAA,CAACrB,WAAW;QAAAuE,QAAA,EACTvC,WAAW,GAAG,kBAAkB,GAAG;MAAuB;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC,eACdtD,OAAA,CAACpB,aAAa;QAAAsE,QAAA,eACZlD,OAAA,CAAChC,GAAG;UAAC8E,EAAE,EAAE;YAAE4B,EAAE,EAAE;UAAE,CAAE;UAAAxB,QAAA,gBACjBlD,OAAA,CAAClB,SAAS;YACR2F,SAAS;YACTN,KAAK,EAAC,gBAAa;YACnBQ,KAAK,EAAE9D,QAAQ,CAACE,IAAK;YACrB6D,QAAQ,EAAGC,CAAC,IAAK/D,WAAW,CAACoB,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAEnB,IAAI,EAAE8D,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAC,CAAE;YAC1EI,MAAM,EAAC,QAAQ;YACfC,QAAQ;UAAA;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACFtD,OAAA,CAAClB,SAAS;YACR2F,SAAS;YACTN,KAAK,EAAC,aAAa;YACnBQ,KAAK,EAAE9D,QAAQ,CAACG,WAAY;YAC5B4D,QAAQ,EAAGC,CAAC,IAAK/D,WAAW,CAACoB,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAElB,WAAW,EAAE6D,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAC,CAAE;YACjFI,MAAM,EAAC,QAAQ;YACfE,SAAS;YACTC,IAAI,EAAE;UAAE;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEFtD,OAAA,CAAC/B,UAAU;YAAC0F,OAAO,EAAC,IAAI;YAACb,EAAE,EAAE;cAAEG,EAAE,EAAE,CAAC;cAAEO,EAAE,EAAE;YAAE,CAAE;YAAAN,QAAA,EAAC;UAE/C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbtD,OAAA,CAACjB,SAAS;YAAAmE,QAAA,EACP7C,WAAW,CAACsB,GAAG,CAAEuC,UAAU,iBAC1BlE,OAAA,CAAChB,gBAAgB;cAEfmG,OAAO,eACLnF,OAAA,CAACf,QAAQ;gBACPgD,OAAO,EAAEpB,QAAQ,CAACI,cAAc,CAACmE,QAAQ,CAAClB,UAAU,CAACrC,EAAE,CAAE;gBACzD+C,QAAQ,EAAGC,CAAC,IAAK9C,sBAAsB,CAACmC,UAAU,CAACrC,EAAE,EAAEgD,CAAC,CAACC,MAAM,CAAC7C,OAAO;cAAE;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CACF;cACDa,KAAK,eACHnE,OAAA,CAAChC,GAAG;gBAAAkF,QAAA,gBACFlD,OAAA,CAAC/B,UAAU;kBAAC0F,OAAO,EAAC,OAAO;kBAAAT,QAAA,EAAEgB,UAAU,CAACnD;gBAAI;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAC1DtD,OAAA,CAAC/B,UAAU;kBAAC0F,OAAO,EAAC,OAAO;kBAACD,KAAK,EAAC,gBAAgB;kBAAAR,QAAA,EAC/CgB,UAAU,CAAClD;gBAAW;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YACN,GAdIY,UAAU,CAACrC,EAAE;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAenB,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChBtD,OAAA,CAACnB,aAAa;QAAAqE,QAAA,gBACZlD,OAAA,CAAC9B,MAAM;UAAC4F,OAAO,EAAEhC,iBAAkB;UAAAoB,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACpDtD,OAAA,CAAC9B,MAAM;UACL4F,OAAO,EAAE1B,YAAa;UACtBuB,OAAO,EAAC,WAAW;UACnB0B,QAAQ,EAAE,CAACxE,QAAQ,CAACE,IAAI,CAACuE,IAAI,CAAC,CAAE;UAAApC,QAAA,EAE/BvC,WAAW,GAAG,UAAU,GAAG;QAAO;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACpD,EAAA,CAzPID,KAAe;AAAAsF,EAAA,GAAftF,KAAe;AA2PrB,eAAeA,KAAK;AAAC,IAAAsF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}