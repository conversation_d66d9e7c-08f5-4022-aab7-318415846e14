{"ast": null, "code": "import { GridFilterInputDate } from \"../components/panel/filterPanel/GridFilterInputDate.js\";\nfunction buildApplyFilterFn(filterItem, compareFn, showTime, keepHours) {\n  if (!filterItem.value) {\n    return null;\n  }\n  const date = new Date(filterItem.value);\n  if (showTime) {\n    date.setSeconds(0, 0);\n  } else {\n    // In GMT-X timezone, the date will be one day behind.\n    // For 2022-08-16:\n    // GMT+2: Tue Aug 16 2022 02:00:00 GMT+0200\n    // GMT-4: Mon Aug 15 2022 20:00:00 GMT-0400\n    //\n    // We need to add the offset before resetting the hours.\n    date.setMinutes(date.getMinutes() + date.getTimezoneOffset());\n    date.setHours(0, 0, 0, 0);\n  }\n  const time = date.getTime();\n  return value => {\n    if (!value) {\n      return false;\n    }\n    if (keepHours) {\n      return compareFn(value.getTime(), time);\n    }\n\n    // Make a copy of the date to not reset the hours in the original object\n    const dateCopy = new Date(value);\n    if (showTime) {\n      dateCopy.setSeconds(0, 0);\n    } else {\n      dateCopy.setHours(0, 0, 0, 0);\n    }\n    return compareFn(dateCopy.getTime(), time);\n  };\n}\nexport const getGridDateOperators = showTime => [{\n  value: 'is',\n  getApplyFilterFn: filterItem => {\n    return buildApplyFilterFn(filterItem, (value1, value2) => value1 === value2, showTime);\n  },\n  InputComponent: GridFilterInputDate,\n  InputComponentProps: {\n    type: showTime ? 'datetime-local' : 'date'\n  }\n}, {\n  value: 'not',\n  getApplyFilterFn: filterItem => {\n    return buildApplyFilterFn(filterItem, (value1, value2) => value1 !== value2, showTime);\n  },\n  InputComponent: GridFilterInputDate,\n  InputComponentProps: {\n    type: showTime ? 'datetime-local' : 'date'\n  }\n}, {\n  value: 'after',\n  getApplyFilterFn: filterItem => {\n    return buildApplyFilterFn(filterItem, (value1, value2) => value1 > value2, showTime);\n  },\n  InputComponent: GridFilterInputDate,\n  InputComponentProps: {\n    type: showTime ? 'datetime-local' : 'date'\n  }\n}, {\n  value: 'onOrAfter',\n  getApplyFilterFn: filterItem => {\n    return buildApplyFilterFn(filterItem, (value1, value2) => value1 >= value2, showTime);\n  },\n  InputComponent: GridFilterInputDate,\n  InputComponentProps: {\n    type: showTime ? 'datetime-local' : 'date'\n  }\n}, {\n  value: 'before',\n  getApplyFilterFn: filterItem => {\n    return buildApplyFilterFn(filterItem, (value1, value2) => value1 < value2, showTime, !showTime);\n  },\n  InputComponent: GridFilterInputDate,\n  InputComponentProps: {\n    type: showTime ? 'datetime-local' : 'date'\n  }\n}, {\n  value: 'onOrBefore',\n  getApplyFilterFn: filterItem => {\n    return buildApplyFilterFn(filterItem, (value1, value2) => value1 <= value2, showTime);\n  },\n  InputComponent: GridFilterInputDate,\n  InputComponentProps: {\n    type: showTime ? 'datetime-local' : 'date'\n  }\n}, {\n  value: 'isEmpty',\n  getApplyFilterFn: () => {\n    return value => {\n      return value == null;\n    };\n  },\n  requiresFilterValue: false\n}, {\n  value: 'isNotEmpty',\n  getApplyFilterFn: () => {\n    return value => {\n      return value != null;\n    };\n  },\n  requiresFilterValue: false\n}];", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}