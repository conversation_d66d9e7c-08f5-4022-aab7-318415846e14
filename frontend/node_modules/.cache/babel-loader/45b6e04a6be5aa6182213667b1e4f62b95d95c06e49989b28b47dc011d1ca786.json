{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"field\", \"id\", \"formattedValue\", \"row\", \"rowNode\", \"colDef\", \"isEditable\", \"cellMode\", \"hasFocus\", \"tabIndex\", \"api\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { useGridSelector } from \"../../hooks/utils/useGridSelector.js\";\nimport { checkboxPropsSelector } from \"../../hooks/features/rowSelection/utils.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['checkboxInput']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst GridCellCheckboxForwardRef = forwardRef(function GridCellCheckboxRenderer(props, ref) {\n  const {\n      field,\n      id,\n      rowNode,\n      tabIndex\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const ownerState = {\n    classes: rootProps.classes\n  };\n  const classes = useUtilityClasses(ownerState);\n  const handleChange = event => {\n    const params = {\n      value: event.target.checked,\n      id\n    };\n    apiRef.current.publishEvent('rowSelectionCheckboxChange', params, event);\n  };\n  React.useLayoutEffect(() => {\n    if (tabIndex === 0) {\n      const element = apiRef.current.getCellElement(id, field);\n      if (element) {\n        element.tabIndex = -1;\n      }\n    }\n  }, [apiRef, tabIndex, id, field]);\n  const handleKeyDown = React.useCallback(event => {\n    if (event.key === ' ') {\n      // We call event.stopPropagation to avoid selecting the row and also scrolling to bottom\n      // TODO: Remove and add a check inside useGridKeyboardNavigation\n      event.stopPropagation();\n    }\n  }, []);\n  const isSelectable = apiRef.current.isRowSelectable(id);\n  const {\n    isIndeterminate,\n    isChecked\n  } = useGridSelector(apiRef, checkboxPropsSelector, {\n    groupId: id,\n    autoSelectParents: rootProps.rowSelectionPropagation?.parents ?? false\n  });\n  if (rowNode.type === 'footer' || rowNode.type === 'pinnedRow') {\n    return null;\n  }\n  const label = apiRef.current.getLocaleText(isChecked && !isIndeterminate ? 'checkboxSelectionUnselectRow' : 'checkboxSelectionSelectRow');\n  return /*#__PURE__*/_jsx(rootProps.slots.baseCheckbox, _extends({\n    tabIndex: tabIndex,\n    checked: isChecked && !isIndeterminate,\n    onChange: handleChange,\n    className: classes.root,\n    slotProps: {\n      htmlInput: {\n        'aria-label': label,\n        name: 'select_row'\n      }\n    },\n    onKeyDown: handleKeyDown,\n    indeterminate: isIndeterminate,\n    disabled: !isSelectable\n  }, rootProps.slotProps?.baseCheckbox, other, {\n    ref: ref\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridCellCheckboxForwardRef.displayName = \"GridCellCheckboxForwardRef\";\nprocess.env.NODE_ENV !== \"production\" ? GridCellCheckboxForwardRef.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * GridApi that let you manipulate the grid.\n   */\n  api: PropTypes.object.isRequired,\n  /**\n   * The mode of the cell.\n   */\n  cellMode: PropTypes.oneOf(['edit', 'view']).isRequired,\n  /**\n   * The column of the row that the current cell belongs to.\n   */\n  colDef: PropTypes.object.isRequired,\n  /**\n   * The column field of the cell that triggered the event.\n   */\n  field: PropTypes.string.isRequired,\n  /**\n   * A ref allowing to set imperative focus.\n   * It can be passed to the element that should receive focus.\n   * @ignore - do not document.\n   */\n  focusElementRef: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n    current: PropTypes.shape({\n      focus: PropTypes.func.isRequired\n    })\n  })]),\n  /**\n   * The cell value formatted with the column valueFormatter.\n   */\n  formattedValue: PropTypes.any,\n  /**\n   * If true, the cell is the active element.\n   */\n  hasFocus: PropTypes.bool.isRequired,\n  /**\n   * The grid row id.\n   */\n  id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,\n  /**\n   * If true, the cell is editable.\n   */\n  isEditable: PropTypes.bool,\n  /**\n   * The row model of the row that the current cell belongs to.\n   */\n  row: PropTypes.any.isRequired,\n  /**\n   * The node of the row that the current cell belongs to.\n   */\n  rowNode: PropTypes.object.isRequired,\n  /**\n   * the tabIndex value.\n   */\n  tabIndex: PropTypes.oneOf([-1, 0]).isRequired,\n  /**\n   * The cell value.\n   * If the column has `valueGetter`, use `params.row` to directly access the fields.\n   */\n  value: PropTypes.any\n} : void 0;\nexport { GridCellCheckboxForwardRef };\nexport const GridCellCheckboxRenderer = GridCellCheckboxForwardRef;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}