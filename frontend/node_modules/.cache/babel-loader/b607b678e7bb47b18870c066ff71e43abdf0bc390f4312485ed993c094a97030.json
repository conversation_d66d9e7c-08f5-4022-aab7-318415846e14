{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nexport function computeSlots({\n  defaultSlots,\n  slots\n}) {\n  const overrides = slots;\n  if (!overrides || Object.keys(overrides).length === 0) {\n    return defaultSlots;\n  }\n  const result = _extends({}, defaultSlots);\n  Object.keys(overrides).forEach(key => {\n    const k = key;\n    if (overrides[k] !== undefined) {\n      result[k] = overrides[k];\n    }\n  });\n  return result;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}