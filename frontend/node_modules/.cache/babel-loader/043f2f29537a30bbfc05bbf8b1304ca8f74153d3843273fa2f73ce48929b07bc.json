{"ast": null, "code": "export class FinalizationRegistryBasedCleanupTracking {\n  constructor() {\n    this.registry = new FinalizationRegistry(unsubscribe => {\n      if (typeof unsubscribe === 'function') {\n        unsubscribe();\n      }\n    });\n  }\n  register(object, unsubscribe, unregisterToken) {\n    this.registry.register(object, unsubscribe, unregisterToken);\n  }\n  unregister(unregisterToken) {\n    this.registry.unregister(unregisterToken);\n  }\n\n  // eslint-disable-next-line class-methods-use-this\n  reset() {}\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}