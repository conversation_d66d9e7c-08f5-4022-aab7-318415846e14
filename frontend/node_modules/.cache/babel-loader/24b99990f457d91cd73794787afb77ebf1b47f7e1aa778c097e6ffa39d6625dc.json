{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport { styled } from '@mui/system';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = (props, overflowedContent) => {\n  const {\n    classes\n  } = props;\n  const slots = {\n    root: ['virtualScrollerContent', overflowedContent && 'virtualScrollerContent--overflowed']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst VirtualScrollerContentRoot = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'VirtualScrollerContent',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.virtualScrollerContent, ownerState.overflowedContent && styles['virtualScrollerContent--overflowed']];\n  }\n})({});\nconst GridVirtualScrollerContent = forwardRef(function GridVirtualScrollerContent(props, ref) {\n  const rootProps = useGridRootProps();\n  const overflowedContent = !rootProps.autoHeight && props.style?.minHeight === 'auto';\n  const classes = useUtilityClasses(rootProps, overflowedContent);\n  const ownerState = {\n    classes: rootProps.classes,\n    overflowedContent\n  };\n  return /*#__PURE__*/_jsx(VirtualScrollerContentRoot, _extends({}, props, {\n    ownerState: ownerState,\n    className: clsx(classes.root, props.className),\n    ref: ref\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridVirtualScrollerContent.displayName = \"GridVirtualScrollerContent\";\nexport { GridVirtualScrollerContent };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}