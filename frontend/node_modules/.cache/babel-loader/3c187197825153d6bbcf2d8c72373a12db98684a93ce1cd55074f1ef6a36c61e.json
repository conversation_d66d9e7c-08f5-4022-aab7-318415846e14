{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { useGridSelector } from \"../hooks/utils/useGridSelector.js\";\nimport { gridTopLevelRowCountSelector } from \"../hooks/features/rows/gridRowsSelector.js\";\nimport { gridRowSelectionCountSelector } from \"../hooks/features/rowSelection/gridRowSelectionSelector.js\";\nimport { gridFilteredTopLevelRowCountSelector } from \"../hooks/features/filter/gridFilterSelector.js\";\nimport { useGridApiContext } from \"../hooks/utils/useGridApiContext.js\";\nimport { GridSelectedRowCount } from \"./GridSelectedRowCount.js\";\nimport { GridFooterContainer } from \"./containers/GridFooterContainer.js\";\nimport { useGridRootProps } from \"../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst GridFooter = forwardRef(function GridFooter(props, ref) {\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const totalTopLevelRowCount = useGridSelector(apiRef, gridTopLevelRowCountSelector);\n  const selectedRowCount = useGridSelector(apiRef, gridRowSelectionCountSelector);\n  const visibleTopLevelRowCount = useGridSelector(apiRef, gridFilteredTopLevelRowCountSelector);\n  const selectedRowCountElement = !rootProps.hideFooterSelectedRowCount && selectedRowCount > 0 ? /*#__PURE__*/_jsx(GridSelectedRowCount, {\n    selectedRowCount: selectedRowCount\n  }) : /*#__PURE__*/_jsx(\"div\", {});\n  const rowCountElement = !rootProps.hideFooterRowCount && !rootProps.pagination ? /*#__PURE__*/_jsx(rootProps.slots.footerRowCount, _extends({}, rootProps.slotProps?.footerRowCount, {\n    rowCount: totalTopLevelRowCount,\n    visibleRowCount: visibleTopLevelRowCount\n  })) : null;\n  const paginationElement = rootProps.pagination && !rootProps.hideFooterPagination && rootProps.slots.pagination && /*#__PURE__*/_jsx(rootProps.slots.pagination, {});\n  return /*#__PURE__*/_jsxs(GridFooterContainer, _extends({}, props, {\n    ref: ref,\n    children: [selectedRowCountElement, rowCountElement, paginationElement]\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridFooter.displayName = \"GridFooter\";\nprocess.env.NODE_ENV !== \"production\" ? GridFooter.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport { GridFooter };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}