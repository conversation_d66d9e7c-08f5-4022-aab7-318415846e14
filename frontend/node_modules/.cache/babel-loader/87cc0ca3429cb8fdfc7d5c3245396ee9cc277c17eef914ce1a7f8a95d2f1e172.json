{"ast": null, "code": "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"displayOrder\"];\nimport * as React from 'react';\nimport { useGridRootProps } from \"../../utils/useGridRootProps.js\";\nimport { useGridPrivateApiContext } from \"../../utils/useGridPrivateApiContext.js\";\nconst useGridColumnMenuSlots = props => {\n  const apiRef = useGridPrivateApiContext();\n  const rootProps = useGridRootProps();\n  const {\n    defaultSlots,\n    defaultSlotProps,\n    slots = {},\n    slotProps = {},\n    hideMenu,\n    colDef,\n    addDividers = true\n  } = props;\n  const processedComponents = React.useMemo(() => _extends({}, defaultSlots, slots), [defaultSlots, slots]);\n  const processedSlotProps = React.useMemo(() => {\n    if (!slotProps || Object.keys(slotProps).length === 0) {\n      return defaultSlotProps;\n    }\n    const mergedProps = _extends({}, slotProps);\n    Object.entries(defaultSlotProps).forEach(([key, currentSlotProps]) => {\n      mergedProps[key] = _extends({}, currentSlotProps, slotProps[key] || {});\n    });\n    return mergedProps;\n  }, [defaultSlotProps, slotProps]);\n  const defaultItems = apiRef.current.unstable_applyPipeProcessors('columnMenu', [], props.colDef);\n  const userItems = React.useMemo(() => {\n    const defaultComponentKeys = Object.keys(defaultSlots);\n    return Object.keys(slots).filter(key => !defaultComponentKeys.includes(key));\n  }, [slots, defaultSlots]);\n  return React.useMemo(() => {\n    const uniqueItems = Array.from(new Set([...defaultItems, ...userItems]));\n    const cleansedItems = uniqueItems.filter(key => processedComponents[key] != null);\n    const sorted = cleansedItems.sort((a, b) => {\n      const leftItemProps = processedSlotProps[a];\n      const rightItemProps = processedSlotProps[b];\n      const leftDisplayOrder = Number.isFinite(leftItemProps?.displayOrder) ? leftItemProps.displayOrder : 100;\n      const rightDisplayOrder = Number.isFinite(rightItemProps?.displayOrder) ? rightItemProps.displayOrder : 100;\n      return leftDisplayOrder - rightDisplayOrder;\n    });\n    return sorted.reduce((acc, key, index) => {\n      let itemProps = {\n        colDef,\n        onClick: hideMenu\n      };\n      const processedComponentProps = processedSlotProps[key];\n      if (processedComponentProps) {\n        const customProps = _objectWithoutPropertiesLoose(processedComponentProps, _excluded);\n        itemProps = _extends({}, itemProps, customProps);\n      }\n      return addDividers && index !== sorted.length - 1 ? [...acc, [processedComponents[key], itemProps], [rootProps.slots.baseDivider, {}]] : [...acc, [processedComponents[key], itemProps]];\n    }, []);\n  }, [addDividers, colDef, defaultItems, hideMenu, processedComponents, processedSlotProps, userItems, rootProps.slots.baseDivider]);\n};\nexport { useGridColumnMenuSlots };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}