{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport composeClasses from '@mui/utils/composeClasses';\nimport useId from '@mui/utils/useId';\nimport { useGridSelector } from \"../../hooks/index.js\";\nimport { gridPreferencePanelSelectorWithLabel, gridPreferencePanelStateSelector } from \"../../hooks/features/preferencesPanel/gridPreferencePanelSelector.js\";\nimport { GridPreferencePanelsValue } from \"../../hooks/features/preferencesPanel/gridPreferencePanelsValue.js\";\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { GridIconButtonContainer } from \"./GridIconButtonContainer.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    icon: ['filterIcon']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nfunction GridColumnHeaderFilterIconButtonWrapped(props) {\n  if (!props.counter) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(GridColumnHeaderFilterIconButton, _extends({}, props));\n}\nprocess.env.NODE_ENV !== \"production\" ? GridColumnHeaderFilterIconButtonWrapped.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  counter: PropTypes.number,\n  field: PropTypes.string.isRequired,\n  onClick: PropTypes.func\n} : void 0;\nfunction GridColumnHeaderFilterIconButton(props) {\n  const {\n    counter,\n    field,\n    onClick\n  } = props;\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const ownerState = _extends({}, props, {\n    classes: rootProps.classes\n  });\n  const classes = useUtilityClasses(ownerState);\n  const labelId = useId();\n  const isOpen = useGridSelector(apiRef, gridPreferencePanelSelectorWithLabel, labelId);\n  const panelId = useId();\n  const toggleFilter = React.useCallback(event => {\n    event.preventDefault();\n    event.stopPropagation();\n    const {\n      open,\n      openedPanelValue\n    } = gridPreferencePanelStateSelector(apiRef);\n    if (open && openedPanelValue === GridPreferencePanelsValue.filters) {\n      apiRef.current.hideFilterPanel();\n    } else {\n      apiRef.current.showFilterPanel(undefined, panelId, labelId);\n    }\n    if (onClick) {\n      onClick(apiRef.current.getColumnHeaderParams(field), event);\n    }\n  }, [apiRef, field, onClick, panelId, labelId]);\n  if (!counter) {\n    return null;\n  }\n  const iconButton = /*#__PURE__*/_jsx(rootProps.slots.baseIconButton, _extends({\n    id: labelId,\n    onClick: toggleFilter,\n    \"aria-label\": apiRef.current.getLocaleText('columnHeaderFiltersLabel'),\n    size: \"small\",\n    tabIndex: -1,\n    \"aria-haspopup\": \"menu\",\n    \"aria-expanded\": isOpen,\n    \"aria-controls\": isOpen ? panelId : undefined\n  }, rootProps.slotProps?.baseIconButton, {\n    children: /*#__PURE__*/_jsx(rootProps.slots.columnFilteredIcon, {\n      className: classes.icon,\n      fontSize: \"small\"\n    })\n  }));\n  return /*#__PURE__*/_jsx(rootProps.slots.baseTooltip, _extends({\n    title: apiRef.current.getLocaleText('columnHeaderFiltersTooltipActive')(counter),\n    enterDelay: 1000\n  }, rootProps.slotProps?.baseTooltip, {\n    children: /*#__PURE__*/_jsxs(GridIconButtonContainer, {\n      children: [counter > 1 && /*#__PURE__*/_jsx(rootProps.slots.baseBadge, {\n        badgeContent: counter,\n        color: \"default\",\n        children: iconButton\n      }), counter === 1 && iconButton]\n    })\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? GridColumnHeaderFilterIconButton.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  counter: PropTypes.number,\n  field: PropTypes.string.isRequired,\n  onClick: PropTypes.func\n} : void 0;\nexport { GridColumnHeaderFilterIconButtonWrapped as GridColumnHeaderFilterIconButton };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}