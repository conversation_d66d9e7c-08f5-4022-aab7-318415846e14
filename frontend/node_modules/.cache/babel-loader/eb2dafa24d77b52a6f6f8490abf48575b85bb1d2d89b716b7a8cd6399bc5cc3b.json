{"ast": null, "code": "import { gridVisibleRowsSelector } from \"../features/pagination/gridPaginationSelector.js\";\nimport { useGridSelector } from \"./index.js\";\nexport const getVisibleRows = (apiRef, props) => {\n  return gridVisibleRowsSelector(apiRef);\n};\n\n/**\n * Computes the list of rows that are reachable by scroll.\n * Depending on whether pagination is enabled, it will return the rows in the current page.\n * - If the pagination is disabled or in server mode, it equals all the visible rows.\n * - If the row tree has several layers, it contains up to `state.pageSize` top level rows and all their descendants.\n * - If the row tree is flat, it only contains up to `state.pageSize` rows.\n */\n\nexport const useGridVisibleRows = (apiRef, props) => {\n  return useGridSelector(apiRef, gridVisibleRowsSelector);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}