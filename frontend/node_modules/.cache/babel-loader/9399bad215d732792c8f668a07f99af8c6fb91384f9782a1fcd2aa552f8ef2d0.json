{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"id\", \"value\", \"formattedValue\", \"api\", \"field\", \"row\", \"rowNode\", \"colDef\", \"cellMode\", \"isEditable\", \"tabIndex\", \"hasFocus\", \"isValidating\", \"debounceMs\", \"isProcessingProps\", \"onValueChange\", \"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport composeClasses from '@mui/utils/composeClasses';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport { styled } from '@mui/material/styles';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { NotRendered } from \"../../utils/assert.js\";\nimport { vars } from \"../../constants/cssVariables.js\";\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['editInputCell']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst GridEditInputCellRoot = styled(NotRendered, {\n  name: 'MuiDataGrid',\n  slot: 'EditInputCell'\n})({\n  font: vars.typography.font.body,\n  padding: '1px 0',\n  '& input': {\n    padding: '0 16px',\n    height: '100%'\n  }\n});\nconst GridEditInputCell = forwardRef((props, ref) => {\n  const rootProps = useGridRootProps();\n  const {\n      id,\n      value,\n      field,\n      colDef,\n      hasFocus,\n      debounceMs = 200,\n      isProcessingProps,\n      onValueChange,\n      slotProps\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const apiRef = useGridApiContext();\n  const inputRef = React.useRef(null);\n  const [valueState, setValueState] = React.useState(value);\n  const classes = useUtilityClasses(rootProps);\n  const handleChange = React.useCallback(async event => {\n    const newValue = event.target.value;\n    const column = apiRef.current.getColumn(field);\n    let parsedValue = newValue;\n    if (column.valueParser) {\n      parsedValue = column.valueParser(newValue, apiRef.current.getRow(id), column, apiRef);\n    }\n    setValueState(parsedValue);\n    apiRef.current.setEditCellValue({\n      id,\n      field,\n      value: parsedValue,\n      debounceMs,\n      unstable_skipValueParser: true\n    }, event);\n    if (onValueChange) {\n      await onValueChange(event, newValue);\n    }\n  }, [apiRef, debounceMs, field, id, onValueChange]);\n  const meta = apiRef.current.unstable_getEditCellMeta(id, field);\n  React.useEffect(() => {\n    if (meta?.changeReason !== 'debouncedSetEditCellValue') {\n      setValueState(value);\n    }\n  }, [meta, value]);\n  useEnhancedEffect(() => {\n    if (hasFocus) {\n      inputRef.current.focus();\n    }\n  }, [hasFocus]);\n  return /*#__PURE__*/_jsx(GridEditInputCellRoot, _extends({\n    as: rootProps.slots.baseInput,\n    inputRef: inputRef,\n    className: classes.root,\n    ownerState: rootProps,\n    fullWidth: true,\n    type: colDef.type === 'number' ? colDef.type : 'text',\n    value: valueState ?? '',\n    onChange: handleChange,\n    endAdornment: isProcessingProps ? /*#__PURE__*/_jsx(rootProps.slots.loadIcon, {\n      fontSize: \"small\",\n      color: \"action\"\n    }) : undefined\n  }, other, slotProps?.root, {\n    ref: ref\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridEditInputCell.displayName = \"GridEditInputCell\";\nprocess.env.NODE_ENV !== \"production\" ? GridEditInputCell.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * GridApi that let you manipulate the grid.\n   */\n  api: PropTypes.object.isRequired,\n  /**\n   * The mode of the cell.\n   */\n  cellMode: PropTypes.oneOf(['edit', 'view']).isRequired,\n  changeReason: PropTypes.oneOf(['debouncedSetEditCellValue', 'setEditCellValue']),\n  /**\n   * The column of the row that the current cell belongs to.\n   */\n  colDef: PropTypes.object.isRequired,\n  debounceMs: PropTypes.number,\n  /**\n   * The column field of the cell that triggered the event.\n   */\n  field: PropTypes.string.isRequired,\n  /**\n   * The cell value formatted with the column valueFormatter.\n   */\n  formattedValue: PropTypes.any,\n  /**\n   * If true, the cell is the active element.\n   */\n  hasFocus: PropTypes.bool.isRequired,\n  /**\n   * The grid row id.\n   */\n  id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,\n  /**\n   * If true, the cell is editable.\n   */\n  isEditable: PropTypes.bool,\n  isProcessingProps: PropTypes.bool,\n  isValidating: PropTypes.bool,\n  /**\n   * Callback called when the value is changed by the user.\n   * @param {React.ChangeEvent<HTMLInputElement>} event The event source of the callback.\n   * @param {Date | null} newValue The value that is going to be passed to `apiRef.current.setEditCellValue`.\n   * @returns {Promise<void> | void} A promise to be awaited before calling `apiRef.current.setEditCellValue`\n   */\n  onValueChange: PropTypes.func,\n  /**\n   * The row model of the row that the current cell belongs to.\n   */\n  row: PropTypes.any.isRequired,\n  /**\n   * The node of the row that the current cell belongs to.\n   */\n  rowNode: PropTypes.object.isRequired,\n  slotProps: PropTypes.object,\n  /**\n   * the tabIndex value.\n   */\n  tabIndex: PropTypes.oneOf([-1, 0]).isRequired,\n  /**\n   * The cell value.\n   * If the column has `valueGetter`, use `params.row` to directly access the fields.\n   */\n  value: PropTypes.any\n} : void 0;\nexport { GridEditInputCell };\nexport const renderEditInputCell = params => /*#__PURE__*/_jsx(GridEditInputCell, _extends({}, params));\nif (process.env.NODE_ENV !== \"production\") renderEditInputCell.displayName = \"renderEditInputCell\";", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}