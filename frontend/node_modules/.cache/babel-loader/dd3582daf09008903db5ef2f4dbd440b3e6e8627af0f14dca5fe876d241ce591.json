{"ast": null, "code": "import * as React from 'react';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { gridColumnPositionsSelector, gridVisibleColumnDefinitionsSelector } from \"../columns/gridColumnsSelector.js\";\nimport { useGridSelector } from \"../../utils/useGridSelector.js\";\nimport { gridPageSelector, gridPageSizeSelector } from \"../pagination/gridPaginationSelector.js\";\nimport { gridRowCountSelector } from \"../rows/gridRowsSelector.js\";\nimport { gridRowsMetaSelector } from \"../rows/gridRowsMetaSelector.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { gridExpandedSortedRowEntriesSelector } from \"../filter/gridFilterSelector.js\";\nimport { gridDimensionsSelector } from \"../dimensions/index.js\";\nimport { gridListColumnSelector } from \"../listView/gridListViewSelectors.js\";\n\n// Logic copied from https://www.w3.org/TR/wai-aria-practices/examples/listbox/js/listbox.js\n// Similar to https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollIntoView\nfunction scrollIntoView(dimensions) {\n  const {\n    containerSize,\n    scrollPosition,\n    elementSize,\n    elementOffset\n  } = dimensions;\n  const elementEnd = elementOffset + elementSize;\n  // Always scroll to top when cell is higher than viewport to avoid scroll jump\n  // See https://github.com/mui/mui-x/issues/4513 and https://github.com/mui/mui-x/issues/4514\n  if (elementSize > containerSize) {\n    return elementOffset;\n  }\n  if (elementEnd - containerSize > scrollPosition) {\n    return elementEnd - containerSize;\n  }\n  if (elementOffset < scrollPosition) {\n    return elementOffset;\n  }\n  return undefined;\n}\n\n/**\n * @requires useGridPagination (state) - can be after, async only\n * @requires useGridColumns (state) - can be after, async only\n * @requires useGridRows (state) - can be after, async only\n * @requires useGridRowsMeta (state) - can be after, async only\n * @requires useGridFilter (state)\n * @requires useGridColumnSpanning (method)\n */\nexport const useGridScroll = (apiRef, props) => {\n  const isRtl = useRtl();\n  const logger = useGridLogger(apiRef, 'useGridScroll');\n  const colRef = apiRef.current.columnHeadersContainerRef;\n  const virtualScrollerRef = apiRef.current.virtualScrollerRef;\n  const visibleSortedRows = useGridSelector(apiRef, gridExpandedSortedRowEntriesSelector);\n  const scrollToIndexes = React.useCallback(params => {\n    const dimensions = gridDimensionsSelector(apiRef);\n    const totalRowCount = gridRowCountSelector(apiRef);\n    const visibleColumns = props.listView ? [gridListColumnSelector(apiRef)] : gridVisibleColumnDefinitionsSelector(apiRef);\n    const scrollToHeader = params.rowIndex == null;\n    if (!scrollToHeader && totalRowCount === 0 || visibleColumns.length === 0) {\n      return false;\n    }\n    logger.debug(`Scrolling to cell at row ${params.rowIndex}, col: ${params.colIndex} `);\n    let scrollCoordinates = {};\n    if (params.colIndex !== undefined) {\n      const columnPositions = gridColumnPositionsSelector(apiRef);\n      let cellWidth;\n      if (typeof params.rowIndex !== 'undefined') {\n        const rowId = visibleSortedRows[params.rowIndex]?.id;\n        const cellColSpanInfo = apiRef.current.unstable_getCellColSpanInfo(rowId, params.colIndex);\n        if (cellColSpanInfo && !cellColSpanInfo.spannedByColSpan) {\n          cellWidth = cellColSpanInfo.cellProps.width;\n        }\n      }\n      if (typeof cellWidth === 'undefined') {\n        cellWidth = visibleColumns[params.colIndex].computedWidth;\n      }\n      // When using RTL, `scrollLeft` becomes negative, so we must ensure that we only compare values.\n      scrollCoordinates.left = scrollIntoView({\n        containerSize: dimensions.viewportOuterSize.width,\n        scrollPosition: Math.abs(virtualScrollerRef.current.scrollLeft),\n        elementSize: cellWidth,\n        elementOffset: columnPositions[params.colIndex]\n      });\n    }\n    if (params.rowIndex !== undefined) {\n      const rowsMeta = gridRowsMetaSelector(apiRef);\n      const page = gridPageSelector(apiRef);\n      const pageSize = gridPageSizeSelector(apiRef);\n      const elementIndex = !props.pagination ? params.rowIndex : params.rowIndex - page * pageSize;\n      const targetOffsetHeight = rowsMeta.positions[elementIndex + 1] ? rowsMeta.positions[elementIndex + 1] - rowsMeta.positions[elementIndex] : rowsMeta.currentPageTotalHeight - rowsMeta.positions[elementIndex];\n      scrollCoordinates.top = scrollIntoView({\n        containerSize: dimensions.viewportInnerSize.height,\n        scrollPosition: virtualScrollerRef.current.scrollTop,\n        elementSize: targetOffsetHeight,\n        elementOffset: rowsMeta.positions[elementIndex]\n      });\n    }\n    scrollCoordinates = apiRef.current.unstable_applyPipeProcessors('scrollToIndexes', scrollCoordinates, params);\n    if (typeof scrollCoordinates.left !== undefined || typeof scrollCoordinates.top !== undefined) {\n      apiRef.current.scroll(scrollCoordinates);\n      return true;\n    }\n    return false;\n  }, [logger, apiRef, virtualScrollerRef, props.pagination, visibleSortedRows, props.listView]);\n  const scroll = React.useCallback(params => {\n    if (virtualScrollerRef.current && params.left !== undefined && colRef.current) {\n      const direction = isRtl ? -1 : 1;\n      colRef.current.scrollLeft = params.left;\n      virtualScrollerRef.current.scrollLeft = direction * params.left;\n      logger.debug(`Scrolling left: ${params.left}`);\n    }\n    if (virtualScrollerRef.current && params.top !== undefined) {\n      virtualScrollerRef.current.scrollTop = params.top;\n      logger.debug(`Scrolling top: ${params.top}`);\n    }\n    logger.debug(`Scrolling, updating container, and viewport`);\n  }, [virtualScrollerRef, isRtl, colRef, logger]);\n  const getScrollPosition = React.useCallback(() => {\n    if (!virtualScrollerRef?.current) {\n      return {\n        top: 0,\n        left: 0\n      };\n    }\n    return {\n      top: virtualScrollerRef.current.scrollTop,\n      left: virtualScrollerRef.current.scrollLeft\n    };\n  }, [virtualScrollerRef]);\n  const scrollApi = {\n    scroll,\n    scrollToIndexes,\n    getScrollPosition\n  };\n  useGridApiMethod(apiRef, scrollApi, 'public');\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}