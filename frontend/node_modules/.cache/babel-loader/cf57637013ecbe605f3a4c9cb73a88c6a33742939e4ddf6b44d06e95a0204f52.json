{"ast": null, "code": "import * as React from 'react';\nimport { GridConfigurationContext } from \"../../components/GridConfigurationContext.js\";\nexport const useGridConfiguration = () => {\n  const configuration = React.useContext(GridConfigurationContext);\n  if (configuration === undefined) {\n    throw new Error(['MUI X: Could not find the Data Grid configuration context.', 'It looks like you rendered your component outside of a DataGrid, DataGridPro or DataGridPremium parent component.', 'This can also happen if you are bundling multiple versions of the Data Grid.'].join('\\n'));\n  }\n  return configuration;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}