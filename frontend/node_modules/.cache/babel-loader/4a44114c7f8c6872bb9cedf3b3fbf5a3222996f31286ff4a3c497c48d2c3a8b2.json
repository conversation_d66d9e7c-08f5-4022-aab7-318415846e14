{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"render\", \"className\", \"onClick\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { useComponentRenderer } from '@mui/x-internals/useComponentRenderer';\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { useQuickFilterContext } from \"./QuickFilterContext.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * A button that resets the filter value.\n * It renders the `baseIconButton` slot.\n *\n * Demos:\n *\n * - [Quick Filter](https://mui.com/x/react-data-grid/components/quick-filter/)\n *\n * API:\n *\n * - [QuickFilterClear API](https://mui.com/x/api/data-grid/quick-filter-clear/)\n */\nconst QuickFilterClear = forwardRef(function QuickFilterClear(props, ref) {\n  const {\n      render,\n      className,\n      onClick\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const rootProps = useGridRootProps();\n  const {\n    state,\n    clearValue\n  } = useQuickFilterContext();\n  const resolvedClassName = typeof className === 'function' ? className(state) : className;\n  const handleClick = event => {\n    clearValue();\n    onClick?.(event);\n  };\n  const element = useComponentRenderer(rootProps.slots.baseIconButton, render, _extends({}, rootProps.slotProps?.baseIconButton, {\n    className: resolvedClassName,\n    tabIndex: -1\n  }, other, {\n    onClick: handleClick,\n    ref\n  }), state);\n  return /*#__PURE__*/_jsx(React.Fragment, {\n    children: element\n  });\n});\nif (process.env.NODE_ENV !== \"production\") QuickFilterClear.displayName = \"QuickFilterClear\";\nprocess.env.NODE_ENV !== \"production\" ? QuickFilterClear.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  className: PropTypes.oneOfType([PropTypes.func, PropTypes.string]),\n  color: PropTypes.oneOf(['default', 'inherit', 'primary']),\n  disabled: PropTypes.bool,\n  edge: PropTypes.oneOf(['end', 'start', false]),\n  id: PropTypes.string,\n  label: PropTypes.string,\n  /**\n   * A function to customize rendering of the component.\n   */\n  render: PropTypes.oneOfType([PropTypes.element, PropTypes.func]),\n  role: PropTypes.string,\n  size: PropTypes.oneOf(['large', 'medium', 'small']),\n  style: PropTypes.object,\n  tabIndex: PropTypes.number,\n  title: PropTypes.string,\n  touchRippleRef: PropTypes.any\n} : void 0;\nexport { QuickFilterClear };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}