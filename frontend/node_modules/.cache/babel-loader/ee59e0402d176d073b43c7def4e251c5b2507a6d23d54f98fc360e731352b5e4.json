{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"render\", \"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { styled } from '@mui/system';\nimport composeClasses from '@mui/utils/composeClasses';\nimport clsx from 'clsx';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { useComponentRenderer } from '@mui/x-internals/useComponentRenderer';\nimport { vars } from \"../../constants/cssVariables.js\";\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { ToolbarContext } from \"./ToolbarContext.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { sortByDocumentPosition } from \"./utils.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['toolbar']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst ToolbarRoot = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'Toolbar'\n})({\n  flex: 0,\n  display: 'flex',\n  alignItems: 'center',\n  justifyContent: 'end',\n  gap: vars.spacing(0.25),\n  padding: vars.spacing(0.75),\n  minHeight: 52,\n  boxSizing: 'border-box',\n  borderBottom: `1px solid ${vars.colors.border.base}`\n});\n\n/**\n * The top level Toolbar component that provides context to child components.\n * It renders a styled `<div />` element.\n *\n * Demos:\n *\n * - [Toolbar](https://mui.com/x/react-data-grid/components/toolbar/)\n *\n * API:\n *\n * - [Toolbar API](https://mui.com/x/api/data-grid/toolbar/)\n */\nconst Toolbar = forwardRef(function Toolbar(props, ref) {\n  const {\n      render,\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const rootProps = useGridRootProps();\n  const classes = useUtilityClasses(rootProps);\n  const [focusableItemId, setFocusableItemId] = React.useState(null);\n  const [items, setItems] = React.useState([]);\n  const getSortedItems = React.useCallback(() => items.sort(sortByDocumentPosition), [items]);\n  const findEnabledItem = React.useCallback((startIndex, step, wrap = true) => {\n    let index = startIndex;\n    const sortedItems = getSortedItems();\n    const itemCount = sortedItems.length;\n\n    // Look for enabled items in the specified direction\n    for (let i = 0; i < itemCount; i += 1) {\n      index += step;\n\n      // Handle wrapping around the ends\n      if (index >= itemCount) {\n        if (!wrap) {\n          return -1;\n        }\n        index = 0;\n      } else if (index < 0) {\n        if (!wrap) {\n          return -1;\n        }\n        index = itemCount - 1;\n      }\n\n      // Return if we found an enabled item\n      if (!sortedItems[index].ref.current?.disabled && sortedItems[index].ref.current?.ariaDisabled !== 'true') {\n        return index;\n      }\n    }\n\n    // If we've checked all items and found none enabled\n    return -1;\n  }, [getSortedItems]);\n  const registerItem = React.useCallback((id, itemRef) => {\n    setItems(prevItems => [...prevItems, {\n      id,\n      ref: itemRef\n    }]);\n  }, []);\n  const unregisterItem = React.useCallback(id => {\n    setItems(prevItems => prevItems.filter(i => i.id !== id));\n  }, []);\n  const onItemKeyDown = React.useCallback(event => {\n    if (!focusableItemId) {\n      return;\n    }\n    const sortedItems = getSortedItems();\n    const focusableItemIndex = sortedItems.findIndex(item => item.id === focusableItemId);\n    let newIndex = -1;\n    if (event.key === 'ArrowRight') {\n      event.preventDefault();\n      newIndex = findEnabledItem(focusableItemIndex, 1);\n    } else if (event.key === 'ArrowLeft') {\n      event.preventDefault();\n      newIndex = findEnabledItem(focusableItemIndex, -1);\n    } else if (event.key === 'Home') {\n      event.preventDefault();\n      newIndex = findEnabledItem(-1, 1, false);\n    } else if (event.key === 'End') {\n      event.preventDefault();\n      newIndex = findEnabledItem(sortedItems.length, -1, false);\n    }\n\n    // TODO: Check why this is necessary\n    if (newIndex >= 0 && newIndex < sortedItems.length) {\n      const item = sortedItems[newIndex];\n      setFocusableItemId(item.id);\n      item.ref.current?.focus();\n    }\n  }, [getSortedItems, focusableItemId, findEnabledItem]);\n  const onItemFocus = React.useCallback(id => {\n    if (focusableItemId !== id) {\n      setFocusableItemId(id);\n    }\n  }, [focusableItemId, setFocusableItemId]);\n  const onItemDisabled = React.useCallback(id => {\n    const sortedItems = getSortedItems();\n    const currentIndex = sortedItems.findIndex(item => item.id === id);\n    const newIndex = findEnabledItem(currentIndex, 1);\n    if (newIndex >= 0 && newIndex < sortedItems.length) {\n      const item = sortedItems[newIndex];\n      setFocusableItemId(item.id);\n      item.ref.current?.focus();\n    }\n  }, [getSortedItems, findEnabledItem]);\n  React.useEffect(() => {\n    const sortedItems = getSortedItems();\n    if (sortedItems.length > 0) {\n      // Set initial focusable item\n      if (!focusableItemId) {\n        setFocusableItemId(sortedItems[0].id);\n        return;\n      }\n      const focusableItemIndex = sortedItems.findIndex(item => item.id === focusableItemId);\n      if (!sortedItems[focusableItemIndex]) {\n        // Last item has been removed from the items array\n        const item = sortedItems[sortedItems.length - 1];\n        if (item) {\n          setFocusableItemId(item.id);\n          item.ref.current?.focus();\n        }\n      } else if (focusableItemIndex === -1) {\n        // Focused item has been removed from the items array\n        const item = sortedItems[focusableItemIndex];\n        if (item) {\n          setFocusableItemId(item.id);\n          item.ref.current?.focus();\n        }\n      }\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [getSortedItems, findEnabledItem]);\n  const contextValue = React.useMemo(() => ({\n    focusableItemId,\n    registerItem,\n    unregisterItem,\n    onItemKeyDown,\n    onItemFocus,\n    onItemDisabled\n  }), [focusableItemId, registerItem, unregisterItem, onItemKeyDown, onItemFocus, onItemDisabled]);\n  const element = useComponentRenderer(ToolbarRoot, render, _extends({\n    role: 'toolbar',\n    'aria-orientation': 'horizontal',\n    'aria-label': rootProps.label || undefined,\n    className: clsx(classes.root, className)\n  }, other, {\n    ref\n  }));\n  return /*#__PURE__*/_jsx(ToolbarContext.Provider, {\n    value: contextValue,\n    children: element\n  });\n});\nif (process.env.NODE_ENV !== \"production\") Toolbar.displayName = \"Toolbar\";\nprocess.env.NODE_ENV !== \"production\" ? Toolbar.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * A function to customize rendering of the component.\n   */\n  render: PropTypes.oneOfType([PropTypes.element, PropTypes.func])\n} : void 0;\nexport { Toolbar };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}