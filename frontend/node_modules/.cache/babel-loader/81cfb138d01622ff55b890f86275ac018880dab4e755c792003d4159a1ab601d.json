{"ast": null, "code": "import { lruMemoize, createSelectorCreator } from 'reselect';\n/* eslint-disable no-underscore-dangle */ // __cacheKey__\n\nconst reselectCreateSelector = createSelectorCreator({\n  memoize: lruMemoize,\n  memoizeOptions: {\n    maxSize: 1,\n    equalityCheck: Object.is\n  }\n});\n/* eslint-disable id-denylist */\nexport const createSelector = (a, b, c, d, e, f, ...other) => {\n  if (other.length > 0) {\n    throw new Error('Unsupported number of selectors');\n  }\n  let selector;\n  if (a && b && c && d && e && f) {\n    selector = (state, a1, a2, a3) => {\n      const va = a(state, a1, a2, a3);\n      const vb = b(state, a1, a2, a3);\n      const vc = c(state, a1, a2, a3);\n      const vd = d(state, a1, a2, a3);\n      const ve = e(state, a1, a2, a3);\n      return f(va, vb, vc, vd, ve, a1, a2, a3);\n    };\n  } else if (a && b && c && d && e) {\n    selector = (state, a1, a2, a3) => {\n      const va = a(state, a1, a2, a3);\n      const vb = b(state, a1, a2, a3);\n      const vc = c(state, a1, a2, a3);\n      const vd = d(state, a1, a2, a3);\n      return e(va, vb, vc, vd, a1, a2, a3);\n    };\n  } else if (a && b && c && d) {\n    selector = (state, a1, a2, a3) => {\n      const va = a(state, a1, a2, a3);\n      const vb = b(state, a1, a2, a3);\n      const vc = c(state, a1, a2, a3);\n      return d(va, vb, vc, a1, a2, a3);\n    };\n  } else if (a && b && c) {\n    selector = (state, a1, a2, a3) => {\n      const va = a(state, a1, a2, a3);\n      const vb = b(state, a1, a2, a3);\n      return c(va, vb, a1, a2, a3);\n    };\n  } else if (a && b) {\n    selector = (state, a1, a2, a3) => {\n      const va = a(state, a1, a2, a3);\n      return b(va, a1, a2, a3);\n    };\n  } else if (a) {\n    selector = a;\n  } else {\n    throw new Error('Missing arguments');\n  }\n  return selector;\n};\n/* eslint-enable id-denylist */\n\nexport const createSelectorMemoized = (...selectors) => {\n  const cache = new WeakMap();\n  let nextCacheId = 1;\n  const combiner = selectors[selectors.length - 1];\n  const nSelectors = selectors.length - 1 || 1;\n  // (s1, s2, ..., sN, a1, a2, a3) => { ... }\n  const argsLength = Math.max(combiner.length - nSelectors, 0);\n  if (argsLength > 3) {\n    throw new Error('Unsupported number of arguments');\n  }\n\n  // prettier-ignore\n  const selector = (state, a1, a2, a3) => {\n    let cacheKey = state.__cacheKey__;\n    if (!cacheKey) {\n      cacheKey = {\n        id: nextCacheId\n      };\n      state.__cacheKey__ = cacheKey;\n      nextCacheId += 1;\n    }\n    let fn = cache.get(cacheKey);\n    if (!fn) {\n      let reselectArgs = selectors;\n      const selectorArgs = [undefined, undefined, undefined];\n      switch (argsLength) {\n        case 0:\n          break;\n        case 1:\n          {\n            reselectArgs = [...selectors.slice(0, -1), () => selectorArgs[0], combiner];\n            break;\n          }\n        case 2:\n          {\n            reselectArgs = [...selectors.slice(0, -1), () => selectorArgs[0], () => selectorArgs[1], combiner];\n            break;\n          }\n        case 3:\n          {\n            reselectArgs = [...selectors.slice(0, -1), () => selectorArgs[0], () => selectorArgs[1], () => selectorArgs[2], combiner];\n            break;\n          }\n        default:\n          throw new Error('Unsupported number of arguments');\n      }\n      fn = reselectCreateSelector(...reselectArgs);\n      fn.selectorArgs = selectorArgs;\n      cache.set(cacheKey, fn);\n    }\n\n    /* eslint-disable no-fallthrough */\n\n    switch (argsLength) {\n      case 3:\n        fn.selectorArgs[2] = a3;\n      case 2:\n        fn.selectorArgs[1] = a2;\n      case 1:\n        fn.selectorArgs[0] = a1;\n      case 0:\n      default:\n    }\n    switch (argsLength) {\n      case 0:\n        return fn(state);\n      case 1:\n        return fn(state, a1);\n      case 2:\n        return fn(state, a1, a2);\n      case 3:\n        return fn(state, a1, a2, a3);\n      default:\n        throw new Error('unreachable');\n    }\n  };\n  return selector;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}