{"ast": null, "code": "import { isDeepEqual } from '@mui/x-internals/isDeepEqual';\nimport { isLeaf } from \"../../../models/gridColumnGrouping.js\";\n// This is the recurrence function that help writing `unwrapGroupingColumnModel()`\nconst recurrentUnwrapGroupingColumnModel = (columnGroupNode, parents, unwrappedGroupingModelToComplete) => {\n  if (isLeaf(columnGroupNode)) {\n    if (unwrappedGroupingModelToComplete[columnGroupNode.field] !== undefined) {\n      throw new Error([`MUI X: columnGroupingModel contains duplicated field`, `column field ${columnGroupNode.field} occurs two times in the grouping model:`, `- ${unwrappedGroupingModelToComplete[columnGroupNode.field].join(' > ')}`, `- ${parents.join(' > ')}`].join('\\n'));\n    }\n    unwrappedGroupingModelToComplete[columnGroupNode.field] = parents;\n    return;\n  }\n  const {\n    groupId,\n    children\n  } = columnGroupNode;\n  children.forEach(child => {\n    recurrentUnwrapGroupingColumnModel(child, [...parents, groupId], unwrappedGroupingModelToComplete);\n  });\n};\n\n/**\n * This is a function that provide for each column the array of its parents.\n * Parents are ordered from the root to the leaf.\n * @param columnGroupingModel The model such as provided in DataGrid props\n * @returns An object `{[field]: groupIds}` where `groupIds` is the parents of the column `field`\n */\nexport const unwrapGroupingColumnModel = columnGroupingModel => {\n  if (!columnGroupingModel) {\n    return {};\n  }\n  const unwrappedSubTree = {};\n  columnGroupingModel.forEach(columnGroupNode => {\n    recurrentUnwrapGroupingColumnModel(columnGroupNode, [], unwrappedSubTree);\n  });\n  return unwrappedSubTree;\n};\nexport const getColumnGroupsHeaderStructure = (orderedColumns, unwrappedGroupingModel, pinnedFields) => {\n  const getParents = field => unwrappedGroupingModel[field] ?? [];\n  const groupingHeaderStructure = [];\n  const maxDepth = Math.max(...orderedColumns.map(field => getParents(field).length));\n  const haveSameParents = (field1, field2, depth) => isDeepEqual(getParents(field1).slice(0, depth + 1), getParents(field2).slice(0, depth + 1));\n  const haveDifferentContainers = (field1, field2) => {\n    if (pinnedFields?.left && pinnedFields.left.includes(field1) && !pinnedFields.left.includes(field2)) {\n      return true;\n    }\n    if (pinnedFields?.right && !pinnedFields.right.includes(field1) && pinnedFields.right.includes(field2)) {\n      return true;\n    }\n    return false;\n  };\n  for (let depth = 0; depth < maxDepth; depth += 1) {\n    const depthStructure = orderedColumns.reduce((structure, newField) => {\n      const groupId = getParents(newField)[depth] ?? null;\n      if (structure.length === 0) {\n        return [{\n          columnFields: [newField],\n          groupId\n        }];\n      }\n      const lastGroup = structure[structure.length - 1];\n      const prevField = lastGroup.columnFields[lastGroup.columnFields.length - 1];\n      const prevGroupId = lastGroup.groupId;\n      if (prevGroupId !== groupId || !haveSameParents(prevField, newField, depth) ||\n      // Fix for https://github.com/mui/mui-x/issues/7041\n      haveDifferentContainers(prevField, newField)) {\n        // It's a new group\n        return [...structure, {\n          columnFields: [newField],\n          groupId\n        }];\n      }\n\n      // It extends the previous group\n      return [...structure.slice(0, structure.length - 1), {\n        columnFields: [...lastGroup.columnFields, newField],\n        groupId\n      }];\n    }, []);\n    groupingHeaderStructure.push(depthStructure);\n  }\n  return groupingHeaderStructure;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}