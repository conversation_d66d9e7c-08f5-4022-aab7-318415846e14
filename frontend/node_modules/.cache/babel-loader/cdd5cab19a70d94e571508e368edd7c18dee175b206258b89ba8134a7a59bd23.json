{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { GRID_STRING_COL_DEF } from \"./gridStringColDef.js\";\nimport { renderEditSingleSelectCell } from \"../components/cell/GridEditSingleSelectCell.js\";\nimport { getGridSingleSelectOperators } from \"./gridSingleSelectOperators.js\";\nimport { getValueOptions, isSingleSelectColDef } from \"../components/panel/filterPanel/filterPanelUtils.js\";\nimport { isObject } from \"../utils/utils.js\";\nimport { gridRowIdSelector } from \"../hooks/core/gridPropsSelectors.js\";\nconst isArrayOfObjects = options => {\n  return typeof options[0] === 'object';\n};\nconst defaultGetOptionValue = value => {\n  return isObject(value) ? value.value : value;\n};\nconst defaultGetOptionLabel = value => {\n  return isObject(value) ? value.label : String(value);\n};\nexport const GRID_SINGLE_SELECT_COL_DEF = _extends({}, GRID_STRING_COL_DEF, {\n  type: 'singleSelect',\n  getOptionLabel: defaultGetOptionLabel,\n  getOptionValue: defaultGetOptionValue,\n  valueFormatter(value, row, colDef, apiRef) {\n    const rowId = gridRowIdSelector(apiRef, row);\n    if (!isSingleSelectColDef(colDef)) {\n      return '';\n    }\n    const valueOptions = getValueOptions(colDef, {\n      id: rowId,\n      row\n    });\n    if (value == null) {\n      return '';\n    }\n    if (!valueOptions) {\n      return value;\n    }\n    if (!isArrayOfObjects(valueOptions)) {\n      return colDef.getOptionLabel(value);\n    }\n    const valueOption = valueOptions.find(option => colDef.getOptionValue(option) === value);\n    return valueOption ? colDef.getOptionLabel(valueOption) : '';\n  },\n  renderEditCell: renderEditSingleSelectCell,\n  filterOperators: getGridSingleSelectOperators(),\n  // @ts-ignore\n  pastedValueParser: (value, row, column) => {\n    const colDef = column;\n    const valueOptions = getValueOptions(colDef) || [];\n    const getOptionValue = colDef.getOptionValue;\n    const valueOption = valueOptions.find(option => {\n      if (getOptionValue(option) === value) {\n        return true;\n      }\n      return false;\n    });\n    if (valueOption) {\n      return value;\n    }\n    // do not paste the value if it is not in the valueOptions\n    return undefined;\n  }\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}