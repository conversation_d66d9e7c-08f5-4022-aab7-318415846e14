{"ast": null, "code": "export function getKeyDefault(params) {\n  return JSON.stringify([params.filterModel, params.sortModel, params.start, params.end]);\n}\nexport class GridDataSourceCacheDefault {\n  constructor({\n    ttl = 300_000,\n    getKey = getKeyDefault\n  }) {\n    this.cache = void 0;\n    this.ttl = void 0;\n    this.getKey = void 0;\n    this.cache = {};\n    this.ttl = ttl;\n    this.getKey = getKey;\n  }\n  set(key, value) {\n    const keyString = this.getKey(key);\n    const expiry = Date.now() + this.ttl;\n    this.cache[keyString] = {\n      value,\n      expiry\n    };\n  }\n  get(key) {\n    const keyString = this.getKey(key);\n    const entry = this.cache[keyString];\n    if (!entry) {\n      return undefined;\n    }\n    if (Date.now() > entry.expiry) {\n      delete this.cache[keyString];\n      return undefined;\n    }\n    return entry.value;\n  }\n  clear() {\n    this.cache = {};\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}