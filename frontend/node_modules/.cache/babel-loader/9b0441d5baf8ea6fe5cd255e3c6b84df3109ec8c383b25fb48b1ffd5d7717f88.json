{"ast": null, "code": "export class GridGetRowsError extends Error {\n  constructor(options) {\n    super(options.message);\n    /**\n     * The parameters used in the failed request\n     */\n    this.params = void 0;\n    /**\n     * The original error that caused this error\n     */\n    this.cause = void 0;\n    this.name = 'GridGetRowsError';\n    this.params = options.params;\n    this.cause = options.cause;\n  }\n}\nexport class GridUpdateRowError extends Error {\n  constructor(options) {\n    super(options.message);\n    /**\n     * The parameters used in the failed request\n     */\n    this.params = void 0;\n    /**\n     * The original error that caused this error\n     */\n    this.cause = void 0;\n    this.name = 'GridUpdateRowError';\n    this.params = options.params;\n    this.cause = options.cause;\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}