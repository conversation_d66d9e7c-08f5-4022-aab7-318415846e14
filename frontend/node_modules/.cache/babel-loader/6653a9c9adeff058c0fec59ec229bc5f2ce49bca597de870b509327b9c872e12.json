{"ast": null, "code": "import * as React from 'react';\nimport { fastObjectShallowCompare } from '@mui/x-internals/fastObjectShallowCompare';\nimport { warnOnce } from '@mui/x-internals/warning';\nimport { useSyncExternalStore } from 'use-sync-external-store/shim';\nimport { useLazyRef } from \"./useLazyRef.js\";\nconst defaultCompare = Object.is;\nexport const objectShallowCompare = fastObjectShallowCompare;\nconst arrayShallowCompare = (a, b) => {\n  if (a === b) {\n    return true;\n  }\n  return a.length === b.length && a.every((v, i) => v === b[i]);\n};\nexport const argsEqual = (prev, curr) => {\n  let fn = Object.is;\n  if (curr instanceof Array) {\n    fn = arrayShallowCompare;\n  } else if (curr instanceof Object) {\n    fn = objectShallowCompare;\n  }\n  return fn(prev, curr);\n};\nconst createRefs = () => ({\n  state: null,\n  equals: null,\n  selector: null,\n  args: undefined\n});\nconst EMPTY = [];\nconst emptyGetSnapshot = () => null;\nexport function useGridSelector(apiRef, selector, args = undefined, equals = defaultCompare) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (!apiRef.current.state) {\n      warnOnce(['MUI X: `useGridSelector` has been called before the initialization of the state.', 'This hook can only be used inside the context of the grid.']);\n    }\n  }\n  const refs = useLazyRef(createRefs);\n  const didInit = refs.current.selector !== null;\n  const [state, setState] = React.useState(\n  // We don't use an initialization function to avoid allocations\n  didInit ? null : selector(apiRef, args));\n  refs.current.state = state;\n  refs.current.equals = equals;\n  refs.current.selector = selector;\n  const prevArgs = refs.current.args;\n  refs.current.args = args;\n  if (didInit && !argsEqual(prevArgs, args)) {\n    const newState = refs.current.selector(apiRef, refs.current.args);\n    if (!refs.current.equals(refs.current.state, newState)) {\n      refs.current.state = newState;\n      setState(newState);\n    }\n  }\n  const subscribe = React.useCallback(() => {\n    if (refs.current.subscription) {\n      return null;\n    }\n    refs.current.subscription = apiRef.current.store.subscribe(() => {\n      const newState = refs.current.selector(apiRef, refs.current.args);\n      if (!refs.current.equals(refs.current.state, newState)) {\n        refs.current.state = newState;\n        setState(newState);\n      }\n    });\n    return null;\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  EMPTY);\n  const unsubscribe = React.useCallback(() => {\n    return () => {\n      if (refs.current.subscription) {\n        refs.current.subscription();\n        refs.current.subscription = undefined;\n      }\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, EMPTY);\n  useSyncExternalStore(unsubscribe, subscribe, emptyGetSnapshot);\n  return state;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}