{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridSelector } from \"../../utils/index.js\";\nimport { useGridApiContext } from \"../../utils/useGridApiContext.js\";\nimport { useGridRootProps } from \"../../utils/useGridRootProps.js\";\nimport { gridExpandedRowCountSelector } from \"../filter/index.js\";\nimport { gridRowCountSelector, gridRowsLoadingSelector } from \"../rows/index.js\";\nimport { gridPinnedRowsCountSelector } from \"../rows/gridRowsSelector.js\";\nimport { GridOverlayWrapper } from \"../../../components/base/GridOverlays.js\";\nimport { gridVisibleColumnDefinitionsSelector } from \"../columns/index.js\";\nimport { gridPivotActiveSelector } from \"../pivoting/index.js\";\n\n/**\n * Uses the grid state to determine which overlay to display.\n * Returns the active overlay type and the active loading overlay variant.\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const useGridOverlays = () => {\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const totalRowCount = useGridSelector(apiRef, gridRowCountSelector);\n  const visibleRowCount = useGridSelector(apiRef, gridExpandedRowCountSelector);\n  const pinnedRowsCount = useGridSelector(apiRef, gridPinnedRowsCountSelector);\n  const visibleColumns = useGridSelector(apiRef, gridVisibleColumnDefinitionsSelector);\n  const noRows = totalRowCount === 0 && pinnedRowsCount === 0;\n  const loading = useGridSelector(apiRef, gridRowsLoadingSelector);\n  const pivotActive = useGridSelector(apiRef, gridPivotActiveSelector);\n  const showNoRowsOverlay = !loading && noRows;\n  const showNoResultsOverlay = !loading && totalRowCount > 0 && visibleRowCount === 0;\n  const showNoColumnsOverlay = !loading && visibleColumns.length === 0;\n  const showEmptyPivotOverlay = showNoRowsOverlay && pivotActive;\n  let overlayType = null;\n  let loadingOverlayVariant = null;\n  if (showNoRowsOverlay) {\n    overlayType = 'noRowsOverlay';\n  }\n  if (showNoColumnsOverlay) {\n    overlayType = 'noColumnsOverlay';\n  }\n  if (showEmptyPivotOverlay) {\n    overlayType = 'emptyPivotOverlay';\n  }\n  if (showNoResultsOverlay) {\n    overlayType = 'noResultsOverlay';\n  }\n  if (loading) {\n    overlayType = 'loadingOverlay';\n    loadingOverlayVariant = rootProps.slotProps?.loadingOverlay?.[noRows ? 'noRowsVariant' : 'variant'] ?? (noRows ? 'skeleton' : 'linear-progress');\n  }\n  const overlaysProps = {\n    overlayType: overlayType,\n    loadingOverlayVariant\n  };\n  const getOverlay = () => {\n    if (!overlayType) {\n      return null;\n    }\n    const Overlay = rootProps.slots?.[overlayType];\n    const overlayProps = rootProps.slotProps?.[overlayType];\n    return /*#__PURE__*/_jsx(GridOverlayWrapper, _extends({}, overlaysProps, {\n      children: /*#__PURE__*/_jsx(Overlay, _extends({}, overlayProps))\n    }));\n  };\n  if (process.env.NODE_ENV !== \"production\") getOverlay.displayName = \"getOverlay\";\n  return {\n    getOverlay,\n    overlaysProps\n  };\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}