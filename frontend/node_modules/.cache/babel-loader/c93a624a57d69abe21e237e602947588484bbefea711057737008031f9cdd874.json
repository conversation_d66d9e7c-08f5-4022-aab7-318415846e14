{"ast": null, "code": "import { createSelector, createRootSelector } from \"../../../utils/createSelector.js\";\nconst gridRowSpanningStateSelector = createRootSelector(state => state.rowSpanning);\nexport const gridRowSpanningHiddenCellsSelector = createSelector(gridRowSpanningStateSelector, rowSpanning => rowSpanning.hiddenCells);\nexport const gridRowSpanningSpannedCellsSelector = createSelector(gridRowSpanningStateSelector, rowSpanning => rowSpanning.spannedCells);\nexport const gridRowSpanningHiddenCellsOriginMapSelector = createSelector(gridRowSpanningStateSelector, rowSpanning => rowSpanning.hiddenCellOriginMap);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}