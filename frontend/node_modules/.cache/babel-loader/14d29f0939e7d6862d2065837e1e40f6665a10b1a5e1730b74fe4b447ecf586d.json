{"ast": null, "code": "import * as React from 'react';\nimport { EventManager } from '@mui/x-internals/EventManager';\nimport { Store } from '@mui/x-internals/store';\nimport { useGridApiMethod } from \"../utils/useGridApiMethod.js\";\nimport { GridSignature } from \"../../constants/signature.js\";\nconst SYMBOL_API_PRIVATE = Symbol('mui.api_private');\nconst isSyntheticEvent = event => {\n  return event.isPropagationStopped !== undefined;\n};\nexport function unwrapPrivateAPI(publicApi) {\n  return publicApi[SYMBOL_API_PRIVATE];\n}\nlet globalId = 0;\nfunction createPrivateAPI(publicApiRef) {\n  const existingPrivateApi = publicApiRef.current?.[SYMBOL_API_PRIVATE];\n  if (existingPrivateApi) {\n    return existingPrivateApi;\n  }\n  const state = {};\n  const privateApi = {\n    state,\n    store: Store.create(state),\n    instanceId: {\n      id: globalId\n    }\n  };\n  globalId += 1;\n  privateApi.getPublicApi = () => publicApiRef.current;\n  privateApi.register = (visibility, methods) => {\n    Object.keys(methods).forEach(methodName => {\n      const method = methods[methodName];\n      const currentPrivateMethod = privateApi[methodName];\n      if (currentPrivateMethod?.spying === true) {\n        currentPrivateMethod.target = method;\n      } else {\n        privateApi[methodName] = method;\n      }\n      if (visibility === 'public') {\n        const publicApi = publicApiRef.current;\n        const currentPublicMethod = publicApi[methodName];\n        if (currentPublicMethod?.spying === true) {\n          currentPublicMethod.target = method;\n        } else {\n          publicApi[methodName] = method;\n        }\n      }\n    });\n  };\n  privateApi.register('private', {\n    caches: {},\n    eventManager: new EventManager()\n  });\n  return privateApi;\n}\nfunction createPublicAPI(privateApiRef) {\n  const publicApi = {\n    get state() {\n      return privateApiRef.current.state;\n    },\n    get store() {\n      return privateApiRef.current.store;\n    },\n    get instanceId() {\n      return privateApiRef.current.instanceId;\n    },\n    [SYMBOL_API_PRIVATE]: privateApiRef.current\n  };\n  return publicApi;\n}\nexport function useGridApiInitialization(inputApiRef, props) {\n  const publicApiRef = React.useRef(null);\n  const privateApiRef = React.useRef(null);\n  if (!privateApiRef.current) {\n    privateApiRef.current = createPrivateAPI(publicApiRef);\n  }\n  if (!publicApiRef.current) {\n    publicApiRef.current = createPublicAPI(privateApiRef);\n  }\n  const publishEvent = React.useCallback((...args) => {\n    const [name, params, event = {}] = args;\n    event.defaultMuiPrevented = false;\n    if (isSyntheticEvent(event) && event.isPropagationStopped()) {\n      return;\n    }\n    const details = props.signature === GridSignature.DataGridPro || props.signature === GridSignature.DataGridPremium ? {\n      api: privateApiRef.current.getPublicApi()\n    } : {};\n    privateApiRef.current.eventManager.emit(name, params, event, details);\n  }, [privateApiRef, props.signature]);\n  const subscribeEvent = React.useCallback((event, handler, options) => {\n    privateApiRef.current.eventManager.on(event, handler, options);\n    const api = privateApiRef.current;\n    return () => {\n      api.eventManager.removeListener(event, handler);\n    };\n  }, [privateApiRef]);\n  useGridApiMethod(privateApiRef, {\n    subscribeEvent,\n    publishEvent\n  }, 'public');\n  if (inputApiRef && !inputApiRef.current?.state) {\n    inputApiRef.current = publicApiRef.current;\n  }\n  React.useImperativeHandle(inputApiRef, () => publicApiRef.current, [publicApiRef]);\n  React.useEffect(() => {\n    const api = privateApiRef.current;\n    return () => {\n      api.publishEvent('unmount');\n    };\n  }, [privateApiRef]);\n  return privateApiRef;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}