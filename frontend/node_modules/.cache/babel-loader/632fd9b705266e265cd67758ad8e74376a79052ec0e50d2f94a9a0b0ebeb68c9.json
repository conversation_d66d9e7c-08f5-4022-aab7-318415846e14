{"ast": null, "code": "export function throttle(func, wait = 166) {\n  let timeout;\n  let lastArgs;\n  const later = () => {\n    timeout = undefined;\n    func(...lastArgs);\n  };\n  function throttled(...args) {\n    lastArgs = args;\n    if (timeout === undefined) {\n      timeout = setTimeout(later, wait);\n    }\n  }\n  throttled.clear = () => {\n    clearTimeout(timeout);\n    timeout = undefined;\n  };\n  return throttled;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}