{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"resizable\", \"resizing\", \"height\", \"side\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport composeClasses from '@mui/utils/composeClasses';\nimport capitalize from '@mui/utils/capitalize';\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nvar GridColumnHeaderSeparatorSides = /*#__PURE__*/function (GridColumnHeaderSeparatorSides) {\n  GridColumnHeaderSeparatorSides[\"Left\"] = \"left\";\n  GridColumnHeaderSeparatorSides[\"Right\"] = \"right\";\n  return GridColumnHeaderSeparatorSides;\n}(GridColumnHeaderSeparatorSides || {});\nconst useUtilityClasses = ownerState => {\n  const {\n    resizable,\n    resizing,\n    classes,\n    side\n  } = ownerState;\n  const slots = {\n    root: ['columnSeparator', resizable && 'columnSeparator--resizable', resizing && 'columnSeparator--resizing', side && `columnSeparator--side${capitalize(side)}`],\n    icon: ['iconSeparator']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nfunction GridColumnHeaderSeparatorRaw(props) {\n  const {\n      height,\n      side = GridColumnHeaderSeparatorSides.Right\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const rootProps = useGridRootProps();\n  const ownerState = _extends({}, props, {\n    side,\n    classes: rootProps.classes\n  });\n  const classes = useUtilityClasses(ownerState);\n  const stopClick = React.useCallback(event => {\n    event.preventDefault();\n    event.stopPropagation();\n  }, []);\n  return (/*#__PURE__*/\n    // eslint-disable-next-line jsx-a11y/click-events-have-key-events,jsx-a11y/no-static-element-interactions\n    _jsx(\"div\", _extends({\n      className: classes.root,\n      style: {\n        minHeight: height\n      }\n    }, other, {\n      onClick: stopClick,\n      children: /*#__PURE__*/_jsx(rootProps.slots.columnResizeIcon, {\n        className: classes.icon\n      })\n    }))\n  );\n}\nconst GridColumnHeaderSeparator = /*#__PURE__*/React.memo(GridColumnHeaderSeparatorRaw);\nif (process.env.NODE_ENV !== \"production\") GridColumnHeaderSeparator.displayName = \"GridColumnHeaderSeparator\";\nprocess.env.NODE_ENV !== \"production\" ? GridColumnHeaderSeparatorRaw.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  height: PropTypes.number.isRequired,\n  resizable: PropTypes.bool.isRequired,\n  resizing: PropTypes.bool.isRequired,\n  side: PropTypes.oneOf(['left', 'right'])\n} : void 0;\nexport { GridColumnHeaderSeparator, GridColumnHeaderSeparatorSides };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}