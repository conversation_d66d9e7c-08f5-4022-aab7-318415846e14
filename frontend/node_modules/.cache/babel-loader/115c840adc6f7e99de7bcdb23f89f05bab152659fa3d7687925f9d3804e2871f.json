{"ast": null, "code": "const warnedOnceCache = new Set();\n\n/**\n * Logs a message to the console on development mode. The warning will only be logged once.\n *\n * The message is the log's cache key. Two identical messages will only be logged once.\n *\n * This function is a no-op in production.\n *\n * @param message the message to log\n * @param gravity the gravity of the warning. Defaults to `'warning'`.\n * @returns\n */\nexport function warnOnce(message, gravity = 'warning') {\n  if (process.env.NODE_ENV === 'production') {\n    return;\n  }\n  const cleanMessage = Array.isArray(message) ? message.join('\\n') : message;\n  if (!warnedOnceCache.has(cleanMessage)) {\n    warnedOnceCache.add(cleanMessage);\n    if (gravity === 'error') {\n      console.error(cleanMessage);\n    } else {\n      console.warn(cleanMessage);\n    }\n  }\n}\nexport function clearWarningsCache() {\n  warnedOnceCache.clear();\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}