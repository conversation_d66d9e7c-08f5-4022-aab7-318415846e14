{"ast": null, "code": "import * as React from 'react';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nexport function useGridApiMethod(privateApiRef, apiMethods, visibility) {\n  const isFirstRender = React.useRef(true);\n  useEnhancedEffect(() => {\n    isFirstRender.current = false;\n    privateApiRef.current.register(visibility, apiMethods);\n  }, [privateApiRef, visibility, apiMethods]);\n  if (isFirstRender.current) {\n    privateApiRef.current.register(visibility, apiMethods);\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}