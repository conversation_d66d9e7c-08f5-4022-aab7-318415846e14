{"ast": null, "code": "export function isNumber(value) {\n  return typeof value === 'number' && !Number.isNaN(value);\n}\nexport function isFunction(value) {\n  return typeof value === 'function';\n}\nexport function isObject(value) {\n  return typeof value === 'object' && value !== null;\n}\nexport function localStorageAvailable() {\n  try {\n    // Incognito mode might reject access to the localStorage for security reasons.\n    // window isn't defined on Node.js\n    // https://stackoverflow.com/questions/16427636/check-if-localstorage-is-available\n    const key = '__some_random_key_you_are_not_going_to_use__';\n    window.localStorage.setItem(key, key);\n    window.localStorage.removeItem(key);\n    return true;\n  } catch (err) {\n    return false;\n  }\n}\nexport function escapeRegExp(value) {\n  return value.replace(/[-[\\]{}()*+?.,\\\\^$|#\\s]/g, '\\\\$&');\n}\n\n/**\n * Follows the CSS specification behavior for min and max\n * If min > max, then the min have priority\n */\nexport const clamp = (value, min, max) => Math.max(min, Math.min(max, value));\n\n/**\n * Create an array containing the range [from, to[\n */\nexport function range(from, to) {\n  return Array.from({\n    length: to - from\n  }).map((_, i) => from + i);\n}\n\n// Pseudo random number. See https://stackoverflow.com/a/47593316\nfunction mulberry32(a) {\n  return () => {\n    /* eslint-disable */\n    let t = a += 0x6d2b79f5;\n    t = Math.imul(t ^ t >>> 15, t | 1);\n    t ^= t + Math.imul(t ^ t >>> 7, t | 61);\n    return ((t ^ t >>> 14) >>> 0) / 4294967296;\n    /* eslint-enable */\n  };\n}\n\n/**\n * Create a random number generator from a seed. The seed\n * ensures that the random number generator produces the\n * same sequence of 'random' numbers on every render. It\n * returns a function that generates a random number between\n * a specified min and max.\n */\nexport function createRandomNumberGenerator(seed) {\n  const random = mulberry32(seed);\n  return (min, max) => min + (max - min) * random();\n}\nexport function deepClone(obj) {\n  if (typeof structuredClone === 'function') {\n    return structuredClone(obj);\n  }\n  return JSON.parse(JSON.stringify(obj));\n}\n\n/**\n * Mark a value as used so eslint doesn't complain. Use this instead\n * of a `eslint-disable-next-line react-hooks/exhaustive-deps` because\n * that hint disables checks on all values instead of just one.\n */\nexport function eslintUseValue(_) {}\nexport const runIf = (condition, fn) => params => {\n  if (condition) {\n    fn(params);\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}