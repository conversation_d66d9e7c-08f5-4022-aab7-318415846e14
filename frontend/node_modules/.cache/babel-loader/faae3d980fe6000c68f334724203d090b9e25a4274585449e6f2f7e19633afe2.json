{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useLazyRef from '@mui/utils/useLazyRef';\nimport { isObjectEmpty } from '@mui/x-internals/isObjectEmpty';\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { gridRowCountSelector, gridRowsLookupSelector, gridRowTreeSelector, gridRowGroupingNameSelector, gridRowTreeDepthsSelector, gridDataRowIdsSelector, gridRowMaximumTreeDepthSelector, gridRowGroupsToFetchSelector, gridRowNodeSelector, gridDataRowsSelector } from \"./gridRowsSelector.js\";\nimport { gridRowIdSelector } from \"../../core/gridPropsSelectors.js\";\nimport { useTimeout } from \"../../utils/useTimeout.js\";\nimport { GridSignature } from \"../../../constants/signature.js\";\nimport { useGridEvent } from \"../../utils/useGridEvent.js\";\nimport { getVisibleRows } from \"../../utils/useGridVisibleRows.js\";\nimport { gridSortedRowIdsSelector } from \"../sorting/gridSortingSelector.js\";\nimport { gridFilteredRowsLookupSelector } from \"../filter/gridFilterSelector.js\";\nimport { getTreeNodeDescendants, createRowsInternalCache, getRowsStateFromCache, isAutogeneratedRowNode, GRID_ROOT_GROUP_ID, GRID_ID_AUTOGENERATED, updateCacheWithNewRows, getTopLevelRowCount, getRowIdFromRowModel, computeRowsUpdates } from \"./gridRowsUtils.js\";\nimport { useGridRegisterPipeApplier } from \"../../core/pipeProcessing/index.js\";\nimport { GridStrategyGroup } from \"../../core/strategyProcessing/index.js\";\nimport { gridPivotActiveSelector } from \"../pivoting/index.js\";\nexport const rowsStateInitializer = (state, props, apiRef) => {\n  const isDataSourceAvailable = !!props.dataSource;\n  apiRef.current.caches.rows = createRowsInternalCache({\n    rows: isDataSourceAvailable ? [] : props.rows,\n    getRowId: props.getRowId,\n    loading: props.loading,\n    rowCount: props.rowCount\n  });\n  return _extends({}, state, {\n    rows: getRowsStateFromCache({\n      apiRef,\n      rowCountProp: props.rowCount,\n      loadingProp: isDataSourceAvailable ? true : props.loading,\n      previousTree: null,\n      previousTreeDepths: null\n    })\n  });\n};\nexport const useGridRows = (apiRef, props) => {\n  if (process.env.NODE_ENV !== 'production') {\n    try {\n      // Freeze the `rows` prop so developers have a fast failure if they try to use Array.prototype.push().\n      Object.freeze(props.rows);\n    } catch (error) {\n      // Sometimes, it's impossible to freeze, so we give up on it.\n    }\n  }\n  const logger = useGridLogger(apiRef, 'useGridRows');\n  const lastUpdateMs = React.useRef(Date.now());\n  const lastRowCount = React.useRef(props.rowCount);\n  const timeout = useTimeout();\n  const getRow = React.useCallback(id => {\n    const model = gridRowsLookupSelector(apiRef)[id];\n    if (model) {\n      return model;\n    }\n    const node = gridRowNodeSelector(apiRef, id);\n    if (node && isAutogeneratedRowNode(node)) {\n      return {\n        [GRID_ID_AUTOGENERATED]: id\n      };\n    }\n    return null;\n  }, [apiRef]);\n  const getRowId = React.useCallback(row => gridRowIdSelector(apiRef, row), [apiRef]);\n  const throttledRowsChange = React.useCallback(({\n    cache,\n    throttle\n  }) => {\n    const run = () => {\n      lastUpdateMs.current = Date.now();\n      apiRef.current.setState(state => _extends({}, state, {\n        rows: getRowsStateFromCache({\n          apiRef,\n          rowCountProp: props.rowCount,\n          loadingProp: props.loading,\n          previousTree: gridRowTreeSelector(apiRef),\n          previousTreeDepths: gridRowTreeDepthsSelector(apiRef),\n          previousGroupsToFetch: gridRowGroupsToFetchSelector(apiRef)\n        })\n      }));\n      apiRef.current.publishEvent('rowsSet');\n    };\n    timeout.clear();\n    apiRef.current.caches.rows = cache;\n    if (!throttle) {\n      run();\n      return;\n    }\n    const throttleRemainingTimeMs = props.throttleRowsMs - (Date.now() - lastUpdateMs.current);\n    if (throttleRemainingTimeMs > 0) {\n      timeout.start(throttleRemainingTimeMs, run);\n      return;\n    }\n    run();\n  }, [props.throttleRowsMs, props.rowCount, props.loading, apiRef, timeout]);\n\n  /**\n   * API METHODS\n   */\n  const setRows = React.useCallback(rows => {\n    logger.debug(`Updating all rows, new length ${rows.length}`);\n    if (gridPivotActiveSelector(apiRef)) {\n      apiRef.current.updateNonPivotRows(rows, false);\n      return;\n    }\n    const cache = createRowsInternalCache({\n      rows,\n      getRowId: props.getRowId,\n      loading: props.loading,\n      rowCount: props.rowCount\n    });\n    const prevCache = apiRef.current.caches.rows;\n    cache.rowsBeforePartialUpdates = prevCache.rowsBeforePartialUpdates;\n    throttledRowsChange({\n      cache,\n      throttle: true\n    });\n  }, [logger, props.getRowId, props.loading, props.rowCount, throttledRowsChange, apiRef]);\n  const updateRows = React.useCallback(updates => {\n    if (props.signature === GridSignature.DataGrid && updates.length > 1) {\n      throw new Error(['MUI X: You cannot update several rows at once in `apiRef.current.updateRows` on the DataGrid.', 'You need to upgrade to DataGridPro or DataGridPremium component to unlock this feature.'].join('\\n'));\n    }\n    if (gridPivotActiveSelector(apiRef)) {\n      apiRef.current.updateNonPivotRows(updates);\n      return;\n    }\n    const nonPinnedRowsUpdates = computeRowsUpdates(apiRef, updates, props.getRowId);\n    const cache = updateCacheWithNewRows({\n      updates: nonPinnedRowsUpdates,\n      getRowId: props.getRowId,\n      previousCache: apiRef.current.caches.rows\n    });\n    throttledRowsChange({\n      cache,\n      throttle: true\n    });\n  }, [props.signature, props.getRowId, throttledRowsChange, apiRef]);\n  const updateNestedRows = React.useCallback((updates, groupKeys) => {\n    const nonPinnedRowsUpdates = computeRowsUpdates(apiRef, updates, props.getRowId);\n    const cache = updateCacheWithNewRows({\n      updates: nonPinnedRowsUpdates,\n      getRowId: props.getRowId,\n      previousCache: apiRef.current.caches.rows,\n      groupKeys: groupKeys ?? []\n    });\n    throttledRowsChange({\n      cache,\n      throttle: false\n    });\n  }, [props.getRowId, throttledRowsChange, apiRef]);\n  const setLoading = React.useCallback(loading => {\n    logger.debug(`Setting loading to ${loading}`);\n    apiRef.current.setState(state => _extends({}, state, {\n      rows: _extends({}, state.rows, {\n        loading\n      })\n    }));\n    apiRef.current.caches.rows.loadingPropBeforePartialUpdates = loading;\n  }, [apiRef, logger]);\n  const getRowModels = React.useCallback(() => {\n    const dataRows = gridDataRowIdsSelector(apiRef);\n    const idRowsLookup = gridRowsLookupSelector(apiRef);\n    return new Map(dataRows.map(id => [id, idRowsLookup[id] ?? {}]));\n  }, [apiRef]);\n  const getRowsCount = React.useCallback(() => gridRowCountSelector(apiRef), [apiRef]);\n  const getAllRowIds = React.useCallback(() => gridDataRowIdsSelector(apiRef), [apiRef]);\n  const getRowIndexRelativeToVisibleRows = React.useCallback(id => {\n    const {\n      rowIdToIndexMap\n    } = getVisibleRows(apiRef);\n    return rowIdToIndexMap.get(id);\n  }, [apiRef]);\n  const setRowChildrenExpansion = React.useCallback((id, isExpanded) => {\n    const currentNode = gridRowNodeSelector(apiRef, id);\n    if (!currentNode) {\n      throw new Error(`MUI X: No row with id #${id} found.`);\n    }\n    if (currentNode.type !== 'group') {\n      throw new Error('MUI X: Only group nodes can be expanded or collapsed.');\n    }\n    const newNode = _extends({}, currentNode, {\n      childrenExpanded: isExpanded\n    });\n    apiRef.current.setState(state => {\n      return _extends({}, state, {\n        rows: _extends({}, state.rows, {\n          tree: _extends({}, state.rows.tree, {\n            [id]: newNode\n          })\n        })\n      });\n    });\n    apiRef.current.publishEvent('rowExpansionChange', newNode);\n  }, [apiRef]);\n  const getRowNode = React.useCallback(id => gridRowNodeSelector(apiRef, id) ?? null, [apiRef]);\n  const getRowGroupChildren = React.useCallback(({\n    skipAutoGeneratedRows = true,\n    groupId,\n    applySorting,\n    applyFiltering\n  }) => {\n    const tree = gridRowTreeSelector(apiRef);\n    let children;\n    if (applySorting) {\n      const groupNode = tree[groupId];\n      if (!groupNode) {\n        return [];\n      }\n      const sortedRowIds = gridSortedRowIdsSelector(apiRef);\n      children = [];\n      const startIndex = sortedRowIds.findIndex(id => id === groupId) + 1;\n      for (let index = startIndex; index < sortedRowIds.length && tree[sortedRowIds[index]].depth > groupNode.depth; index += 1) {\n        const id = sortedRowIds[index];\n        if (!skipAutoGeneratedRows || !isAutogeneratedRowNode(tree[id])) {\n          children.push(id);\n        }\n      }\n    } else {\n      children = getTreeNodeDescendants(tree, groupId, skipAutoGeneratedRows);\n    }\n    if (applyFiltering) {\n      const filteredRowsLookup = gridFilteredRowsLookupSelector(apiRef);\n      children = isObjectEmpty(filteredRowsLookup) ? children : children.filter(childId => filteredRowsLookup[childId] !== false);\n    }\n    return children;\n  }, [apiRef]);\n  const setRowIndex = React.useCallback((rowId, targetIndex) => {\n    const node = gridRowNodeSelector(apiRef, rowId);\n    if (!node) {\n      throw new Error(`MUI X: No row with id #${rowId} found.`);\n    }\n    if (node.parent !== GRID_ROOT_GROUP_ID) {\n      throw new Error(`MUI X: The row reordering do not support reordering of grouped rows yet.`);\n    }\n    if (node.type !== 'leaf') {\n      throw new Error(`MUI X: The row reordering do not support reordering of footer or grouping rows.`);\n    }\n    apiRef.current.setState(state => {\n      const group = gridRowTreeSelector(apiRef)[GRID_ROOT_GROUP_ID];\n      const allRows = group.children;\n      const oldIndex = allRows.findIndex(row => row === rowId);\n      if (oldIndex === -1 || oldIndex === targetIndex) {\n        return state;\n      }\n      logger.debug(`Moving row ${rowId} to index ${targetIndex}`);\n      const updatedRows = [...allRows];\n      updatedRows.splice(targetIndex, 0, updatedRows.splice(oldIndex, 1)[0]);\n      return _extends({}, state, {\n        rows: _extends({}, state.rows, {\n          tree: _extends({}, state.rows.tree, {\n            [GRID_ROOT_GROUP_ID]: _extends({}, group, {\n              children: updatedRows\n            })\n          })\n        })\n      });\n    });\n    apiRef.current.publishEvent('rowsSet');\n  }, [apiRef, logger]);\n  const replaceRows = React.useCallback((firstRowToRender, newRows) => {\n    if (props.signature === GridSignature.DataGrid && newRows.length > 1) {\n      throw new Error(['MUI X: You cannot replace rows using `apiRef.current.unstable_replaceRows` on the DataGrid.', 'You need to upgrade to DataGridPro or DataGridPremium component to unlock this feature.'].join('\\n'));\n    }\n    if (newRows.length === 0) {\n      return;\n    }\n    const treeDepth = gridRowMaximumTreeDepthSelector(apiRef);\n    if (treeDepth > 1) {\n      throw new Error('`apiRef.current.unstable_replaceRows` is not compatible with tree data and row grouping');\n    }\n    const tree = _extends({}, gridRowTreeSelector(apiRef));\n    const dataRowIdToModelLookup = _extends({}, gridRowsLookupSelector(apiRef));\n    const rootGroup = tree[GRID_ROOT_GROUP_ID];\n    const rootGroupChildren = [...rootGroup.children];\n    const seenIds = new Set();\n    for (let i = 0; i < newRows.length; i += 1) {\n      const rowModel = newRows[i];\n      const rowId = getRowIdFromRowModel(rowModel, props.getRowId, 'A row was provided without id when calling replaceRows().');\n      const [removedRowId] = rootGroupChildren.splice(firstRowToRender + i, 1, rowId);\n      if (!seenIds.has(removedRowId)) {\n        delete dataRowIdToModelLookup[removedRowId];\n        delete tree[removedRowId];\n      }\n      const rowTreeNodeConfig = {\n        id: rowId,\n        depth: 0,\n        parent: GRID_ROOT_GROUP_ID,\n        type: 'leaf',\n        groupingKey: null\n      };\n      dataRowIdToModelLookup[rowId] = rowModel;\n      tree[rowId] = rowTreeNodeConfig;\n      seenIds.add(rowId);\n    }\n    tree[GRID_ROOT_GROUP_ID] = _extends({}, rootGroup, {\n      children: rootGroupChildren\n    });\n\n    // Removes potential remaining skeleton rows from the dataRowIds.\n    const dataRowIds = rootGroupChildren.filter(childId => tree[childId]?.type === 'leaf');\n    apiRef.current.caches.rows.dataRowIdToModelLookup = dataRowIdToModelLookup;\n    apiRef.current.setState(state => _extends({}, state, {\n      rows: _extends({}, state.rows, {\n        loading: props.loading,\n        totalRowCount: Math.max(props.rowCount || 0, rootGroupChildren.length),\n        dataRowIdToModelLookup,\n        dataRowIds,\n        tree\n      })\n    }));\n    apiRef.current.publishEvent('rowsSet');\n  }, [apiRef, props.signature, props.getRowId, props.loading, props.rowCount]);\n  const rowApi = {\n    getRow,\n    setLoading,\n    getRowId,\n    getRowModels,\n    getRowsCount,\n    getAllRowIds,\n    setRows,\n    updateRows,\n    getRowNode,\n    getRowIndexRelativeToVisibleRows,\n    unstable_replaceRows: replaceRows\n  };\n  const rowProApi = {\n    setRowIndex,\n    setRowChildrenExpansion,\n    getRowGroupChildren\n  };\n  const rowProPrivateApi = {\n    updateNestedRows\n  };\n\n  /**\n   * EVENTS\n   */\n  const groupRows = React.useCallback(() => {\n    logger.info(`Row grouping pre-processing have changed, regenerating the row tree`);\n    let cache;\n    if (apiRef.current.caches.rows.rowsBeforePartialUpdates === props.rows) {\n      // The `props.rows` did not change since the last row grouping\n      // We can use the current rows cache which contains the partial updates done recently.\n      cache = _extends({}, apiRef.current.caches.rows, {\n        updates: {\n          type: 'full',\n          rows: gridDataRowIdsSelector(apiRef)\n        }\n      });\n    } else {\n      // The `props.rows` has changed since the last row grouping\n      // We must use the new `props.rows` on the new grouping\n      // This occurs because this event is triggered before the `useEffect` on the rows when both the grouping pre-processing and the rows changes on the same render\n      cache = createRowsInternalCache({\n        rows: props.rows,\n        getRowId: props.getRowId,\n        loading: props.loading,\n        rowCount: props.rowCount\n      });\n    }\n    throttledRowsChange({\n      cache,\n      throttle: false\n    });\n  }, [logger, apiRef, props.rows, props.getRowId, props.loading, props.rowCount, throttledRowsChange]);\n  const previousDataSource = useLazyRef(() => props.dataSource);\n  const handleStrategyProcessorChange = React.useCallback(methodName => {\n    if (props.dataSource && props.dataSource !== previousDataSource.current) {\n      previousDataSource.current = props.dataSource;\n      return;\n    }\n    if (methodName === 'rowTreeCreation') {\n      groupRows();\n    }\n  }, [groupRows, previousDataSource, props.dataSource]);\n  const handleStrategyActivityChange = React.useCallback(() => {\n    // `rowTreeCreation` is the only processor ran when `strategyAvailabilityChange` is fired.\n    // All the other processors listen to `rowsSet` which will be published by the `groupRows` method below.\n    if (apiRef.current.getActiveStrategy(GridStrategyGroup.RowTree) !== gridRowGroupingNameSelector(apiRef)) {\n      groupRows();\n    }\n  }, [apiRef, groupRows]);\n  useGridEvent(apiRef, 'activeStrategyProcessorChange', handleStrategyProcessorChange);\n  useGridEvent(apiRef, 'strategyAvailabilityChange', handleStrategyActivityChange);\n\n  /**\n   * APPLIERS\n   */\n  const applyHydrateRowsProcessor = React.useCallback(() => {\n    apiRef.current.setState(state => {\n      const response = apiRef.current.unstable_applyPipeProcessors('hydrateRows', {\n        tree: gridRowTreeSelector(apiRef),\n        treeDepths: gridRowTreeDepthsSelector(apiRef),\n        dataRowIds: gridDataRowIdsSelector(apiRef),\n        dataRowIdToModelLookup: gridRowsLookupSelector(apiRef)\n      });\n      return _extends({}, state, {\n        rows: _extends({}, state.rows, response, {\n          totalTopLevelRowCount: getTopLevelRowCount({\n            tree: response.tree,\n            rowCountProp: props.rowCount\n          })\n        })\n      });\n    });\n    apiRef.current.publishEvent('rowsSet');\n  }, [apiRef, props.rowCount]);\n  useGridRegisterPipeApplier(apiRef, 'hydrateRows', applyHydrateRowsProcessor);\n  useGridApiMethod(apiRef, rowApi, 'public');\n  useGridApiMethod(apiRef, rowProApi, props.signature === GridSignature.DataGrid ? 'private' : 'public');\n  useGridApiMethod(apiRef, rowProPrivateApi, 'private');\n\n  // The effect do not track any value defined synchronously during the 1st render by hooks called after `useGridRows`\n  // As a consequence, the state generated by the 1st run of this useEffect will always be equal to the initialization one\n  const isFirstRender = React.useRef(true);\n  React.useEffect(() => {\n    if (isFirstRender.current) {\n      isFirstRender.current = false;\n      return;\n    }\n    let isRowCountPropUpdated = false;\n    if (props.rowCount !== lastRowCount.current) {\n      isRowCountPropUpdated = true;\n      lastRowCount.current = props.rowCount;\n    }\n    const currentRows = props.dataSource ? gridDataRowsSelector(apiRef) : props.rows;\n    const areNewRowsAlreadyInState = apiRef.current.caches.rows.rowsBeforePartialUpdates === currentRows;\n    const isNewLoadingAlreadyInState = apiRef.current.caches.rows.loadingPropBeforePartialUpdates === props.loading;\n    const isNewRowCountAlreadyInState = apiRef.current.caches.rows.rowCountPropBeforePartialUpdates === props.rowCount;\n\n    // The new rows have already been applied (most likely in the `'rowGroupsPreProcessingChange'` listener)\n    if (areNewRowsAlreadyInState) {\n      // If the loading prop has changed, we need to update its value in the state because it won't be done by `throttledRowsChange`\n      if (!isNewLoadingAlreadyInState) {\n        apiRef.current.setState(state => _extends({}, state, {\n          rows: _extends({}, state.rows, {\n            loading: props.loading\n          })\n        }));\n        apiRef.current.caches.rows.loadingPropBeforePartialUpdates = props.loading;\n      }\n      if (!isNewRowCountAlreadyInState) {\n        apiRef.current.setState(state => _extends({}, state, {\n          rows: _extends({}, state.rows, {\n            totalRowCount: Math.max(props.rowCount || 0, state.rows.totalRowCount),\n            totalTopLevelRowCount: Math.max(props.rowCount || 0, state.rows.totalTopLevelRowCount)\n          })\n        }));\n        apiRef.current.caches.rows.rowCountPropBeforePartialUpdates = props.rowCount;\n      }\n      if (!isRowCountPropUpdated) {\n        return;\n      }\n    }\n    logger.debug(`Updating all rows, new length ${currentRows?.length}`);\n    throttledRowsChange({\n      cache: createRowsInternalCache({\n        rows: currentRows,\n        getRowId: props.getRowId,\n        loading: props.loading,\n        rowCount: props.rowCount\n      }),\n      throttle: false\n    });\n  }, [props.rows, props.rowCount, props.getRowId, props.loading, props.dataSource, logger, throttledRowsChange, apiRef]);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}