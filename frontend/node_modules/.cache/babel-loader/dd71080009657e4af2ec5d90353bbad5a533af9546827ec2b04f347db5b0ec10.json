{"ast": null, "code": "import { GridLogicOperator } from \"../../../models/gridFilterItem.js\";\nexport const defaultGridFilterLookup = {\n  filteredRowsLookup: {},\n  filteredChildrenCountLookup: {},\n  filteredDescendantCountLookup: {}\n};\nexport const getDefaultGridFilterModel = () => ({\n  items: [],\n  logicOperator: GridLogicOperator.And,\n  quickFilterValues: [],\n  quickFilterLogicOperator: GridLogicOperator.And\n});\n\n/**\n * @param {GridValidRowModel} row The model of the row we want to filter.\n * @param {(filterItem: GridFilterItem) => boolean} shouldApplyItem An optional callback to allow the filtering engine to only apply some items.\n * @param {GridAggregatedFilterItemApplierResult} result The previous result of the filtering engine.\n */\n\n/**\n * Visibility status for each row.\n * A row is visible if it is passing the filters AND if its parents are expanded.\n * If a row is not registered in this lookup, it is visible.\n */", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}