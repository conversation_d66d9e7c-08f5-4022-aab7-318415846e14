{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"className\", \"classes\", \"onClose\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { styled } from '@mui/material/styles';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { vars } from \"../../constants/cssVariables.js\";\nimport { useCSSVariablesClass } from \"../../utils/css/context.js\";\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { NotRendered } from \"../../utils/assert.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const gridPanelClasses = generateUtilityClasses('MuiDataGrid', ['panel', 'paper']);\nconst GridPanelRoot = styled(NotRendered, {\n  name: 'MuiDataGrid',\n  slot: 'panel'\n})({\n  zIndex: vars.zIndex.panel\n});\nconst GridPanelContent = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'panelContent'\n})({\n  backgroundColor: vars.colors.background.overlay,\n  borderRadius: vars.radius.base,\n  boxShadow: vars.shadows.overlay,\n  display: 'flex',\n  maxWidth: `calc(100vw - ${vars.spacing(2)})`,\n  overflow: 'auto'\n});\nconst GridPanel = forwardRef((props, ref) => {\n  const {\n      children,\n      className,\n      onClose\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const classes = gridPanelClasses;\n  const [isPlaced, setIsPlaced] = React.useState(false);\n  const variablesClass = useCSSVariablesClass();\n  const onDidShow = useEventCallback(() => setIsPlaced(true));\n  const onDidHide = useEventCallback(() => setIsPlaced(false));\n  const handleClickAway = useEventCallback(() => {\n    onClose?.();\n  });\n  const handleKeyDown = useEventCallback(event => {\n    if (event.key === 'Escape') {\n      onClose?.();\n    }\n  });\n  const [fallbackTarget, setFallbackTarget] = React.useState(null);\n  React.useEffect(() => {\n    const panelAnchor = apiRef.current.rootElementRef?.current?.querySelector('[data-id=\"gridPanelAnchor\"]');\n    if (panelAnchor) {\n      setFallbackTarget(panelAnchor);\n    }\n  }, [apiRef]);\n  if (!fallbackTarget) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(GridPanelRoot, _extends({\n    as: rootProps.slots.basePopper,\n    ownerState: rootProps,\n    placement: \"bottom-end\",\n    className: clsx(classes.panel, className, variablesClass),\n    flip: true,\n    onDidShow: onDidShow,\n    onDidHide: onDidHide,\n    onClickAway: handleClickAway,\n    clickAwayMouseEvent: \"onPointerUp\",\n    clickAwayTouchEvent: false,\n    focusTrap: true\n  }, other, rootProps.slotProps?.basePopper, {\n    target: props.target ?? fallbackTarget,\n    ref: ref,\n    children: /*#__PURE__*/_jsx(GridPanelContent, {\n      className: classes.paper,\n      ownerState: rootProps,\n      onKeyDown: handleKeyDown,\n      children: isPlaced && children\n    })\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridPanel.displayName = \"GridPanel\";\nprocess.env.NODE_ENV !== \"production\" ? GridPanel.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  className: PropTypes.string,\n  flip: PropTypes.bool,\n  id: PropTypes.string,\n  onClose: PropTypes.func,\n  open: PropTypes.bool.isRequired,\n  target: PropTypes /* @typescript-to-proptypes-ignore */.any\n} : void 0;\nexport { GridPanel };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}