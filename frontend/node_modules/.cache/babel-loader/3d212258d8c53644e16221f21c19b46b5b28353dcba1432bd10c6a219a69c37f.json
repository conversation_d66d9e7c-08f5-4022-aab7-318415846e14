{"ast": null, "code": "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _toPropertyKey from \"@babel/runtime/helpers/esm/toPropertyKey\";\nimport * as React from 'react';\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\n/**\n * Implement the Pipeline Pattern\n *\n * More information and detailed example in (TODO add link to technical doc when ready)\n *\n * Some plugins contains custom logic to enrich data provided by other plugins or components.\n * For instance, the row grouping plugin needs to add / remove the grouping columns when the grid columns are updated.\n *\n * =====================================================================================================================\n *\n * The plugin containing the custom logic must use:\n *\n * - `useGridRegisterPipeProcessor` to register their processor.\n *\n * - `apiRef.current.requestPipeProcessorsApplication` to imperatively re-apply a group.\n *   This method should be used in last resort.\n *   Most of the time, the application should be triggered by an update on the deps of the processor.\n *\n * =====================================================================================================================\n *\n * The plugin or component that needs to enrich its data must use:\n *\n * - `apiRef.current.unstable_applyPipeProcessors` to run in chain all the processors of a given group.\n *\n * - `useGridRegisterPipeApplier` to re-apply the whole pipe when requested.\n *   The applier will be called when:\n *   * a processor is registered.\n *   * `apiRef.current.requestPipeProcessorsApplication` is called for the given group.\n */\nexport const useGridPipeProcessing = apiRef => {\n  const cache = React.useRef({});\n  const isRunning = React.useRef(false);\n  const runAppliers = React.useCallback(groupCache => {\n    if (isRunning.current || !groupCache) {\n      return;\n    }\n    isRunning.current = true;\n    Object.values(groupCache.appliers).forEach(callback => {\n      callback();\n    });\n    isRunning.current = false;\n  }, []);\n  const registerPipeProcessor = React.useCallback((group, id, processor) => {\n    if (!cache.current[group]) {\n      cache.current[group] = {\n        processors: new Map(),\n        processorsAsArray: [],\n        appliers: {}\n      };\n    }\n    const groupCache = cache.current[group];\n    const oldProcessor = groupCache.processors.get(id);\n    if (oldProcessor !== processor) {\n      groupCache.processors.set(id, processor);\n      groupCache.processorsAsArray = Array.from(cache.current[group].processors.values()).filter(processorValue => processorValue !== null);\n      runAppliers(groupCache);\n    }\n    return () => {\n      cache.current[group].processors.set(id, null);\n      cache.current[group].processorsAsArray = Array.from(cache.current[group].processors.values()).filter(processorValue => processorValue !== null);\n    };\n  }, [runAppliers]);\n  const registerPipeApplier = React.useCallback((group, id, applier) => {\n    if (!cache.current[group]) {\n      cache.current[group] = {\n        processors: new Map(),\n        processorsAsArray: [],\n        appliers: {}\n      };\n    }\n    cache.current[group].appliers[id] = applier;\n    return () => {\n      const _appliers = cache.current[group].appliers,\n        otherAppliers = _objectWithoutPropertiesLoose(_appliers, [id].map(_toPropertyKey));\n      cache.current[group].appliers = otherAppliers;\n    };\n  }, []);\n  const requestPipeProcessorsApplication = React.useCallback(group => {\n    runAppliers(cache.current[group]);\n  }, [runAppliers]);\n  const applyPipeProcessors = React.useCallback((...args) => {\n    const [group, value, context] = args;\n    if (!cache.current[group]) {\n      return value;\n    }\n    const processors = cache.current[group].processorsAsArray;\n    let result = value;\n    for (let i = 0; i < processors.length; i += 1) {\n      result = processors[i](result, context);\n    }\n    return result;\n  }, []);\n  const preProcessingPrivateApi = {\n    registerPipeProcessor,\n    registerPipeApplier,\n    requestPipeProcessorsApplication\n  };\n  const preProcessingPublicApi = {\n    unstable_applyPipeProcessors: applyPipeProcessors\n  };\n  useGridApiMethod(apiRef, preProcessingPrivateApi, 'private');\n  useGridApiMethod(apiRef, preProcessingPublicApi, 'public');\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}