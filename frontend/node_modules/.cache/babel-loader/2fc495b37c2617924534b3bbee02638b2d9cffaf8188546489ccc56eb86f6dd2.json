{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { styled } from '@mui/system';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { minimalContentHeight } from \"../../hooks/features/rows/gridRowsUtils.js\";\nimport { useGridSelector } from \"../../hooks/utils/useGridSelector.js\";\nimport { gridDimensionsSelector } from \"../../hooks/features/dimensions/index.js\";\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst GridOverlayWrapperRoot = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'OverlayWrapper',\n  shouldForwardProp: prop => prop !== 'overlayType' && prop !== 'loadingOverlayVariant' && prop !== 'right'\n})(({\n  overlayType,\n  loadingOverlayVariant,\n  right\n}) =>\n// Skeleton overlay should flow with the scroll container and not be sticky\nloadingOverlayVariant !== 'skeleton' ? {\n  position: 'sticky',\n  // To stay in place while scrolling\n  top: 'var(--DataGrid-headersTotalHeight)',\n  // TODO: take pinned rows into account\n  left: 0,\n  right: `${right}px`,\n  width: 0,\n  // To stay above the content instead of shifting it down\n  height: 0,\n  // To stay above the content instead of shifting it down\n  zIndex: overlayType === 'loadingOverlay' ? 5 // Should be above pinned columns, pinned rows, and detail panel\n  : 4 // Should be above pinned columns and detail panel\n} : {});\nconst GridOverlayWrapperInner = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'OverlayWrapperInner',\n  shouldForwardProp: prop => prop !== 'overlayType' && prop !== 'loadingOverlayVariant'\n})({});\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['overlayWrapper'],\n    inner: ['overlayWrapperInner']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nexport function GridOverlayWrapper(props) {\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const dimensions = useGridSelector(apiRef, gridDimensionsSelector);\n  let height = Math.max(dimensions.viewportOuterSize.height - dimensions.topContainerHeight - dimensions.bottomContainerHeight - (dimensions.hasScrollX ? dimensions.scrollbarSize : 0), 0);\n  if (height === 0) {\n    height = minimalContentHeight;\n  }\n  const classes = useUtilityClasses(_extends({}, props, {\n    classes: rootProps.classes\n  }));\n  return /*#__PURE__*/_jsx(GridOverlayWrapperRoot, _extends({\n    className: classes.root\n  }, props, {\n    right: dimensions.columnsTotalWidth - dimensions.viewportOuterSize.width,\n    children: /*#__PURE__*/_jsx(GridOverlayWrapperInner, _extends({\n      className: classes.inner,\n      style: {\n        height,\n        width: dimensions.viewportOuterSize.width\n      }\n    }, props))\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? GridOverlayWrapper.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  loadingOverlayVariant: PropTypes.oneOf(['circular-progress', 'linear-progress', 'skeleton']),\n  overlayType: PropTypes.oneOf(['loadingOverlay', 'noResultsOverlay', 'noRowsOverlay', 'noColumnsOverlay', 'emptyPivotOverlay'])\n} : void 0;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}