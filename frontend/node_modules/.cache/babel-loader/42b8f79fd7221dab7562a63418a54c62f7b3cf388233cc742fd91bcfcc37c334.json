{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { styled } from '@mui/system';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { gridHasBottomFillerSelector, gridHasScrollXSelector, gridHasScrollYSelector } from \"../../hooks/features/dimensions/gridDimensionsSelectors.js\";\nimport { GridScrollArea } from \"../GridScrollArea.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { useGridSelector } from \"../../hooks/utils/useGridSelector.js\";\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { useGridVirtualScroller } from \"../../hooks/features/virtualization/useGridVirtualScroller.js\";\nimport { useGridOverlays } from \"../../hooks/features/overlays/useGridOverlays.js\";\nimport { GridHeaders } from \"../GridHeaders.js\";\nimport { GridMainContainer as Container } from \"./GridMainContainer.js\";\nimport { GridTopContainer as TopContainer } from \"./GridTopContainer.js\";\nimport { GridVirtualScrollerContent as Content } from \"./GridVirtualScrollerContent.js\";\nimport { GridVirtualScrollerFiller as SpaceFiller } from \"./GridVirtualScrollerFiller.js\";\nimport { GridVirtualScrollerRenderZone as RenderZone } from \"./GridVirtualScrollerRenderZone.js\";\nimport { GridVirtualScrollbar as Scrollbar } from \"./GridVirtualScrollbar.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    hasScrollX,\n    hasPinnedRight,\n    loadingOverlayVariant,\n    overlayType\n  } = ownerState;\n  const hideContent = loadingOverlayVariant === 'skeleton' || overlayType === 'noColumnsOverlay';\n  const slots = {\n    root: ['main', hasPinnedRight && 'main--hasPinnedRight', hideContent && 'main--hiddenContent'],\n    scroller: ['virtualScroller', hasScrollX && 'virtualScroller--hasScrollX']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst Scroller = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'VirtualScroller',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.virtualScroller, ownerState.hasScrollX && styles['virtualScroller--hasScrollX']];\n  }\n})({\n  position: 'relative',\n  height: '100%',\n  flexGrow: 1,\n  overflow: 'scroll',\n  scrollbarWidth: 'none' /* Firefox */,\n  display: 'flex',\n  flexDirection: 'column',\n  '&::-webkit-scrollbar': {\n    display: 'none' /* Safari and Chrome */\n  },\n  '@media print': {\n    overflow: 'hidden'\n  },\n  // See https://github.com/mui/mui-x/issues/10547\n  zIndex: 0\n});\nconst hasPinnedRightSelector = apiRef => apiRef.current.state.dimensions.rightPinnedWidth > 0;\nfunction GridVirtualScroller(props) {\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const hasScrollY = useGridSelector(apiRef, gridHasScrollYSelector);\n  const hasScrollX = useGridSelector(apiRef, gridHasScrollXSelector);\n  const hasPinnedRight = useGridSelector(apiRef, hasPinnedRightSelector);\n  const hasBottomFiller = useGridSelector(apiRef, gridHasBottomFillerSelector);\n  const {\n    getOverlay,\n    overlaysProps\n  } = useGridOverlays();\n  const ownerState = _extends({\n    classes: rootProps.classes,\n    hasScrollX,\n    hasPinnedRight\n  }, overlaysProps);\n  const classes = useUtilityClasses(ownerState);\n  const virtualScroller = useGridVirtualScroller();\n  const {\n    getContainerProps,\n    getScrollerProps,\n    getContentProps,\n    getRenderZoneProps,\n    getScrollbarVerticalProps,\n    getScrollbarHorizontalProps,\n    getRows,\n    getScrollAreaProps\n  } = virtualScroller;\n  const rows = getRows();\n  return /*#__PURE__*/_jsxs(Container, _extends({\n    className: classes.root\n  }, getContainerProps(), {\n    ownerState: ownerState,\n    children: [/*#__PURE__*/_jsx(GridScrollArea, _extends({\n      scrollDirection: \"left\"\n    }, getScrollAreaProps())), /*#__PURE__*/_jsx(GridScrollArea, _extends({\n      scrollDirection: \"right\"\n    }, getScrollAreaProps())), /*#__PURE__*/_jsxs(Scroller, _extends({\n      className: classes.scroller\n    }, getScrollerProps(), {\n      ownerState: ownerState,\n      children: [/*#__PURE__*/_jsxs(TopContainer, {\n        children: [!rootProps.listView && /*#__PURE__*/_jsx(GridHeaders, {}), /*#__PURE__*/_jsx(rootProps.slots.pinnedRows, {\n          position: \"top\",\n          virtualScroller: virtualScroller\n        })]\n      }), getOverlay(), /*#__PURE__*/_jsx(Content, _extends({}, getContentProps(), {\n        children: /*#__PURE__*/_jsxs(RenderZone, _extends({}, getRenderZoneProps(), {\n          children: [rows, /*#__PURE__*/_jsx(rootProps.slots.detailPanels, {\n            virtualScroller: virtualScroller\n          })]\n        }))\n      })), hasBottomFiller && /*#__PURE__*/_jsx(SpaceFiller, {\n        rowsLength: rows.length\n      }), /*#__PURE__*/_jsx(rootProps.slots.bottomContainer, {\n        children: /*#__PURE__*/_jsx(rootProps.slots.pinnedRows, {\n          position: \"bottom\",\n          virtualScroller: virtualScroller\n        })\n      })]\n    })), hasScrollX && !rootProps.listView && /*#__PURE__*/_jsx(Scrollbar, _extends({\n      position: \"horizontal\"\n    }, getScrollbarHorizontalProps())), hasScrollY && /*#__PURE__*/_jsx(Scrollbar, _extends({\n      position: \"vertical\"\n    }, getScrollbarVerticalProps())), props.children]\n  }));\n}\nexport { GridVirtualScroller };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}