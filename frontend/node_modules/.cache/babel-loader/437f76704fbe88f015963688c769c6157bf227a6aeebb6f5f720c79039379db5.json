{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nexport const EMPTY_RENDER_CONTEXT = {\n  firstRowIndex: 0,\n  lastRowIndex: 0,\n  firstColumnIndex: 0,\n  lastColumnIndex: 0\n};\nexport const virtualizationStateInitializer = (state, props) => {\n  const {\n    disableVirtualization,\n    autoHeight\n  } = props;\n  const virtualization = {\n    enabled: !disableVirtualization,\n    enabledForColumns: !disableVirtualization,\n    enabledForRows: !disableVirtualization && !autoHeight,\n    renderContext: EMPTY_RENDER_CONTEXT\n  };\n  return _extends({}, state, {\n    virtualization\n  });\n};\nexport function useGridVirtualization(apiRef, props) {\n  /*\n   * API METHODS\n   */\n\n  const setVirtualization = enabled => {\n    apiRef.current.setState(state => _extends({}, state, {\n      virtualization: _extends({}, state.virtualization, {\n        enabled,\n        enabledForColumns: enabled,\n        enabledForRows: enabled && !props.autoHeight\n      })\n    }));\n  };\n  const setColumnVirtualization = enabled => {\n    apiRef.current.setState(state => _extends({}, state, {\n      virtualization: _extends({}, state.virtualization, {\n        enabledForColumns: enabled\n      })\n    }));\n  };\n  const api = {\n    unstable_setVirtualization: setVirtualization,\n    unstable_setColumnVirtualization: setColumnVirtualization\n  };\n  useGridApiMethod(apiRef, api, 'public');\n\n  /*\n   * EFFECTS\n   */\n\n  /* eslint-disable react-hooks/exhaustive-deps */\n  React.useEffect(() => {\n    setVirtualization(!props.disableVirtualization);\n  }, [props.disableVirtualization, props.autoHeight]);\n  /* eslint-enable react-hooks/exhaustive-deps */\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}