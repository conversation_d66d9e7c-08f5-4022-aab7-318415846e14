{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport { styled } from '@mui/material/styles';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['panelWrapper']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst GridPanelWrapperRoot = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'PanelWrapper'\n})({\n  display: 'flex',\n  flexDirection: 'column',\n  flex: 1,\n  '&:focus': {\n    outline: 0\n  }\n});\nconst GridPanelWrapper = forwardRef(function GridPanelWrapper(props, ref) {\n  const {\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const rootProps = useGridRootProps();\n  const classes = useUtilityClasses(rootProps);\n  return /*#__PURE__*/_jsx(GridPanelWrapperRoot, _extends({\n    tabIndex: -1,\n    className: clsx(classes.root, className),\n    ownerState: rootProps\n  }, other, {\n    ref: ref\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridPanelWrapper.displayName = \"GridPanelWrapper\";\nexport { GridPanelWrapper };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}