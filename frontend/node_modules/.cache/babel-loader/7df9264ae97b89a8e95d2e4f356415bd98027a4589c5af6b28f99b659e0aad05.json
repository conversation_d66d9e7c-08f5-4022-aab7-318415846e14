{"ast": null, "code": "import * as React from 'react';\nimport { styled } from '@mui/system';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useForkRef from '@mui/utils/useForkRef';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { useOnMount } from \"../../hooks/utils/useOnMount.js\";\nimport { useGridPrivateApiContext } from \"../../hooks/utils/useGridPrivateApiContext.js\";\nimport { gridDimensionsSelector, useGridSelector } from \"../../hooks/index.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = (ownerState, position) => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['scrollbar', `scrollbar--${position}`],\n    content: ['scrollbarContent']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst Scrollbar = styled('div')({\n  position: 'absolute',\n  display: 'inline-block',\n  zIndex: 60,\n  '&:hover': {\n    zIndex: 70\n  },\n  // In macOS Safari and Gnome Web, scrollbars are overlaid and don't affect the layout. So we consider\n  // their size to be 0px throughout all the calculations, but the floating scrollbar container does need\n  // to appear and have a real size. We set it to 14px because it seems like an acceptable value and we\n  // don't have a method to find the required size for scrollbars on those platforms.\n  '--size': 'calc(max(var(--DataGrid-scrollbarSize), 14px))'\n});\nconst ScrollbarVertical = styled(Scrollbar)({\n  width: 'var(--size)',\n  height: 'calc(var(--DataGrid-hasScrollY) * (100% - var(--DataGrid-topContainerHeight) - var(--DataGrid-bottomContainerHeight) - var(--DataGrid-hasScrollX) * var(--DataGrid-scrollbarSize)))',\n  overflowY: 'auto',\n  overflowX: 'hidden',\n  // Disable focus-visible style, it's a scrollbar.\n  outline: 0,\n  '& > div': {\n    width: 'var(--size)'\n  },\n  top: 'var(--DataGrid-topContainerHeight)',\n  right: '0px'\n});\nconst ScrollbarHorizontal = styled(Scrollbar)({\n  width: '100%',\n  height: 'var(--size)',\n  overflowY: 'hidden',\n  overflowX: 'auto',\n  // Disable focus-visible style, it's a scrollbar.\n  outline: 0,\n  '& > div': {\n    height: 'var(--size)'\n  },\n  bottom: '0px'\n});\nconst GridVirtualScrollbar = forwardRef(function GridVirtualScrollbar(props, ref) {\n  const apiRef = useGridPrivateApiContext();\n  const rootProps = useGridRootProps();\n  const isLocked = React.useRef(false);\n  const lastPosition = React.useRef(0);\n  const scrollbarRef = React.useRef(null);\n  const contentRef = React.useRef(null);\n  const classes = useUtilityClasses(rootProps, props.position);\n  const dimensions = useGridSelector(apiRef, gridDimensionsSelector);\n  const propertyDimension = props.position === 'vertical' ? 'height' : 'width';\n  const propertyScroll = props.position === 'vertical' ? 'scrollTop' : 'scrollLeft';\n  const propertyScrollPosition = props.position === 'vertical' ? 'top' : 'left';\n  const hasScroll = props.position === 'vertical' ? dimensions.hasScrollX : dimensions.hasScrollY;\n  const contentSize = dimensions.minimumSize[propertyDimension] + (hasScroll ? dimensions.scrollbarSize : 0);\n  const scrollbarSize = props.position === 'vertical' ? dimensions.viewportInnerSize.height : dimensions.viewportOuterSize.width;\n  const scrollbarInnerSize = scrollbarSize * (contentSize / dimensions.viewportOuterSize[propertyDimension]);\n  const onScrollerScroll = useEventCallback(() => {\n    const scrollbar = scrollbarRef.current;\n    const scrollPosition = props.scrollPosition.current;\n    if (!scrollbar) {\n      return;\n    }\n    if (scrollPosition[propertyScrollPosition] === lastPosition.current) {\n      return;\n    }\n    lastPosition.current = scrollPosition[propertyScrollPosition];\n    if (isLocked.current) {\n      isLocked.current = false;\n      return;\n    }\n    isLocked.current = true;\n    const value = scrollPosition[propertyScrollPosition] / contentSize;\n    scrollbar[propertyScroll] = value * scrollbarInnerSize;\n  });\n  const onScrollbarScroll = useEventCallback(() => {\n    const scroller = apiRef.current.virtualScrollerRef.current;\n    const scrollbar = scrollbarRef.current;\n    if (!scrollbar) {\n      return;\n    }\n    if (isLocked.current) {\n      isLocked.current = false;\n      return;\n    }\n    isLocked.current = true;\n    const value = scrollbar[propertyScroll] / scrollbarInnerSize;\n    scroller[propertyScroll] = value * contentSize;\n  });\n  useOnMount(() => {\n    const scroller = apiRef.current.virtualScrollerRef.current;\n    const scrollbar = scrollbarRef.current;\n    const options = {\n      passive: true\n    };\n    scroller.addEventListener('scroll', onScrollerScroll, options);\n    scrollbar.addEventListener('scroll', onScrollbarScroll, options);\n    return () => {\n      scroller.removeEventListener('scroll', onScrollerScroll, options);\n      scrollbar.removeEventListener('scroll', onScrollbarScroll, options);\n    };\n  });\n  React.useEffect(() => {\n    const content = contentRef.current;\n    content.style.setProperty(propertyDimension, `${scrollbarInnerSize}px`);\n  }, [scrollbarInnerSize, propertyDimension]);\n  const Container = props.position === 'vertical' ? ScrollbarVertical : ScrollbarHorizontal;\n  return /*#__PURE__*/_jsx(Container, {\n    ref: useForkRef(ref, scrollbarRef),\n    className: classes.root,\n    style: props.position === 'vertical' && rootProps.listView ? {\n      height: '100%',\n      top: 0\n    } : undefined,\n    tabIndex: -1,\n    \"aria-hidden\": \"true\"\n    // tabIndex does not prevent focus with a mouse click, throwing a console error\n    // https://github.com/mui/mui-x/issues/16706\n    ,\n\n    onFocus: event => {\n      event.target.blur();\n    },\n    children: /*#__PURE__*/_jsx(\"div\", {\n      ref: contentRef,\n      className: classes.content\n    })\n  });\n});\nif (process.env.NODE_ENV !== \"production\") GridVirtualScrollbar.displayName = \"GridVirtualScrollbar\";\nexport { GridVirtualScrollbar };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}