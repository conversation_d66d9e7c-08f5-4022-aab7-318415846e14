{"ast": null, "code": "import { GridEditModes } from \"../models/gridEditRowModel.js\";\n/**\n * The default values of `DataGridPropsWithDefaultValues` to inject in the props of DataGrid.\n */\nexport const DATA_GRID_PROPS_DEFAULT_VALUES = {\n  autoHeight: false,\n  autoPageSize: false,\n  autosizeOnMount: false,\n  checkboxSelection: false,\n  checkboxSelectionVisibleOnly: false,\n  clipboardCopyCellDelimiter: '\\t',\n  columnBufferPx: 150,\n  columnHeaderHeight: 56,\n  disableAutosize: false,\n  disableColumnFilter: false,\n  disableColumnMenu: false,\n  disableColumnReorder: false,\n  disableColumnResize: false,\n  disableColumnSelector: false,\n  disableColumnSorting: false,\n  disableDensitySelector: false,\n  disableEval: false,\n  disableMultipleColumnsFiltering: false,\n  disableMultipleColumnsSorting: false,\n  disableMultipleRowSelection: false,\n  disableRowSelectionOnClick: false,\n  disableVirtualization: false,\n  editMode: GridEditModes.Cell,\n  filterDebounceMs: 150,\n  filterMode: 'client',\n  hideFooter: false,\n  hideFooterPagination: false,\n  hideFooterRowCount: false,\n  hideFooterSelectedRowCount: false,\n  ignoreDiacritics: false,\n  ignoreValueFormatterDuringExport: false,\n  keepColumnPositionIfDraggedOutside: false,\n  keepNonExistentRowsSelected: false,\n  loading: false,\n  logger: console,\n  logLevel: process.env.NODE_ENV === 'production' ? 'error' : 'warn',\n  pageSizeOptions: [25, 50, 100],\n  pagination: false,\n  paginationMode: 'client',\n  resizeThrottleMs: 60,\n  rowBufferPx: 150,\n  rowHeight: 52,\n  rows: [],\n  rowSelection: true,\n  rowSpacingType: 'margin',\n  rowSpanning: false,\n  showCellVerticalBorder: false,\n  showColumnVerticalBorder: false,\n  showToolbar: false,\n  sortingMode: 'client',\n  sortingOrder: ['asc', 'desc', null],\n  throttleRowsMs: 0,\n  virtualizeColumnsWithAutoRowHeight: false\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}