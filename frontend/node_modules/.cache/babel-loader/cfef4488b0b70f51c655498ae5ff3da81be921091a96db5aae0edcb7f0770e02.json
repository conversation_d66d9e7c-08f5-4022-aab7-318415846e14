{"ast": null, "code": "import { createSelector, createRootSelector } from \"../../../utils/createSelector.js\";\nexport const gridDimensionsSelector = createRootSelector(state => state.dimensions);\n\n/**\n * Get the summed width of all the visible columns.\n * @category Visible Columns\n */\nexport const gridColumnsTotalWidthSelector = createSelector(gridDimensionsSelector, dimensions => dimensions.columnsTotalWidth);\nexport const gridRowHeightSelector = createSelector(gridDimensionsSelector, dimensions => dimensions.rowHeight);\nexport const gridContentHeightSelector = createSelector(gridDimensionsSelector, dimensions => dimensions.contentSize.height);\nexport const gridHasScrollXSelector = createSelector(gridDimensionsSelector, dimensions => dimensions.hasScrollX);\nexport const gridHasScrollYSelector = createSelector(gridDimensionsSelector, dimensions => dimensions.hasScrollY);\nexport const gridHasFillerSelector = createSelector(gridDimensionsSelector, dimensions => dimensions.columnsTotalWidth < dimensions.viewportOuterSize.width);\nexport const gridHeaderHeightSelector = createSelector(gridDimensionsSelector, dimensions => dimensions.headerHeight);\nexport const gridGroupHeaderHeightSelector = createSelector(gridDimensionsSelector, dimensions => dimensions.groupHeaderHeight);\nexport const gridHeaderFilterHeightSelector = createSelector(gridDimensionsSelector, dimensions => dimensions.headerFilterHeight);\nexport const gridHorizontalScrollbarHeightSelector = createSelector(gridDimensionsSelector, dimensions => dimensions.hasScrollX ? dimensions.scrollbarSize : 0);\nexport const gridVerticalScrollbarWidthSelector = createSelector(gridDimensionsSelector, dimensions => dimensions.hasScrollY ? dimensions.scrollbarSize : 0);\nexport const gridHasBottomFillerSelector = createSelector(gridDimensionsSelector, gridHorizontalScrollbarHeightSelector, (dimensions, height) => {\n  const needsLastRowBorder = dimensions.viewportOuterSize.height - dimensions.minimumSize.height > 0;\n  if (height === 0 && !needsLastRowBorder) {\n    return false;\n  }\n  return true;\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}