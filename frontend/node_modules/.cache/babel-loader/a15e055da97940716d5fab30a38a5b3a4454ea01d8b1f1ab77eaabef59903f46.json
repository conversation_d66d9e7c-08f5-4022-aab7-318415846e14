{"ast": null, "code": "import { GridSignature } from \"../../../constants/signature.js\";\nconst MAX_PAGE_SIZE = 100;\nexport const defaultPageSize = autoPageSize => autoPageSize ? 0 : 100;\nexport const getPageCount = (rowCount, pageSize, page) => {\n  if (pageSize > 0 && rowCount > 0) {\n    return Math.ceil(rowCount / pageSize);\n  }\n  if (rowCount === -1) {\n    // With unknown row-count, we can assume a page after the current one\n    return page + 2;\n  }\n  return 0;\n};\nexport const getDefaultGridPaginationModel = autoPageSize => ({\n  page: 0,\n  pageSize: autoPageSize ? 0 : 100\n});\nexport const getValidPage = (page, pageCount = 0) => {\n  if (pageCount === 0) {\n    return page;\n  }\n  return Math.max(Math.min(page, pageCount - 1), 0);\n};\nexport const throwIfPageSizeExceedsTheLimit = (pageSize, signatureProp) => {\n  if (signatureProp === GridSignature.DataGrid && pageSize > MAX_PAGE_SIZE) {\n    throw new Error(['MUI X: `pageSize` cannot exceed 100 in the MIT version of the DataGrid.', 'You need to upgrade to DataGridPro or DataGridPremium component to unlock this feature.'].join('\\n'));\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}