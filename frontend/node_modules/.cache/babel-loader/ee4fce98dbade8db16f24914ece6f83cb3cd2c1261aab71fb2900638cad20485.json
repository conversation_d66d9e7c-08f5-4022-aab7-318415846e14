{"ast": null, "code": "import { useGridInitialization } from \"../hooks/core/useGridInitialization.js\";\nimport { useGridInitializeState } from \"../hooks/utils/useGridInitializeState.js\";\nimport { useGridClipboard } from \"../hooks/features/clipboard/useGridClipboard.js\";\nimport { columnMenuStateInitializer, useGridColumnMenu } from \"../hooks/features/columnMenu/useGridColumnMenu.js\";\nimport { useGridColumns, columnsStateInitializer } from \"../hooks/features/columns/useGridColumns.js\";\nimport { densityStateInitializer, useGridDensity } from \"../hooks/features/density/useGridDensity.js\";\nimport { useGridCsvExport } from \"../hooks/features/export/useGridCsvExport.js\";\nimport { useGridPrintExport } from \"../hooks/features/export/useGridPrintExport.js\";\nimport { useGridFilter, filterStateInitializer } from \"../hooks/features/filter/useGridFilter.js\";\nimport { focusStateInitializer, useGridFocus } from \"../hooks/features/focus/useGridFocus.js\";\nimport { useGridKeyboardNavigation } from \"../hooks/features/keyboardNavigation/useGridKeyboardNavigation.js\";\nimport { useGridPagination, paginationStateInitializer } from \"../hooks/features/pagination/useGridPagination.js\";\nimport { useGridPreferencesPanel, preferencePanelStateInitializer } from \"../hooks/features/preferencesPanel/useGridPreferencesPanel.js\";\nimport { useGridEditing, editingStateInitializer } from \"../hooks/features/editing/useGridEditing.js\";\nimport { useGridRows, rowsStateInitializer } from \"../hooks/features/rows/useGridRows.js\";\nimport { useGridRowsPreProcessors } from \"../hooks/features/rows/useGridRowsPreProcessors.js\";\nimport { useGridParamsApi } from \"../hooks/features/rows/useGridParamsApi.js\";\nimport { rowSelectionStateInitializer, useGridRowSelection } from \"../hooks/features/rowSelection/useGridRowSelection.js\";\nimport { useGridRowSelectionPreProcessors } from \"../hooks/features/rowSelection/useGridRowSelectionPreProcessors.js\";\nimport { useGridSorting, sortingStateInitializer } from \"../hooks/features/sorting/useGridSorting.js\";\nimport { useGridScroll } from \"../hooks/features/scroll/useGridScroll.js\";\nimport { useGridEvents } from \"../hooks/features/events/useGridEvents.js\";\nimport { dimensionsStateInitializer, useGridDimensions } from \"../hooks/features/dimensions/useGridDimensions.js\";\nimport { rowsMetaStateInitializer, useGridRowsMeta } from \"../hooks/features/rows/useGridRowsMeta.js\";\nimport { useGridStatePersistence } from \"../hooks/features/statePersistence/useGridStatePersistence.js\";\nimport { useGridColumnSpanning } from \"../hooks/features/columns/useGridColumnSpanning.js\";\nimport { useGridColumnGrouping, columnGroupsStateInitializer } from \"../hooks/features/columnGrouping/useGridColumnGrouping.js\";\nimport { useGridVirtualization, virtualizationStateInitializer } from \"../hooks/features/virtualization/index.js\";\nimport { columnResizeStateInitializer, useGridColumnResize } from \"../hooks/features/columnResize/useGridColumnResize.js\";\nimport { rowSpanningStateInitializer, useGridRowSpanning } from \"../hooks/features/rows/useGridRowSpanning.js\";\nimport { listViewStateInitializer, useGridListView } from \"../hooks/features/listView/useGridListView.js\";\nimport { propsStateInitializer } from \"../hooks/core/useGridProps.js\";\nimport { useGridDataSource } from \"../hooks/features/dataSource/useGridDataSource.js\";\nexport const useDataGridComponent = (apiRef, props) => {\n  useGridInitialization(apiRef, props);\n\n  /**\n   * Register all pre-processors called during state initialization here.\n   */\n  useGridRowSelectionPreProcessors(apiRef, props);\n  useGridRowsPreProcessors(apiRef);\n\n  /**\n   * Register all state initializers here.\n   */\n  useGridInitializeState(propsStateInitializer, apiRef, props);\n  useGridInitializeState(rowSelectionStateInitializer, apiRef, props);\n  useGridInitializeState(columnsStateInitializer, apiRef, props);\n  useGridInitializeState(rowsStateInitializer, apiRef, props);\n  useGridInitializeState(paginationStateInitializer, apiRef, props);\n  useGridInitializeState(editingStateInitializer, apiRef, props);\n  useGridInitializeState(focusStateInitializer, apiRef, props);\n  useGridInitializeState(sortingStateInitializer, apiRef, props);\n  useGridInitializeState(preferencePanelStateInitializer, apiRef, props);\n  useGridInitializeState(filterStateInitializer, apiRef, props);\n  useGridInitializeState(rowSpanningStateInitializer, apiRef, props);\n  useGridInitializeState(densityStateInitializer, apiRef, props);\n  useGridInitializeState(columnResizeStateInitializer, apiRef, props);\n  useGridInitializeState(columnMenuStateInitializer, apiRef, props);\n  useGridInitializeState(columnGroupsStateInitializer, apiRef, props);\n  useGridInitializeState(virtualizationStateInitializer, apiRef, props);\n  useGridInitializeState(dimensionsStateInitializer, apiRef, props);\n  useGridInitializeState(rowsMetaStateInitializer, apiRef, props);\n  useGridInitializeState(listViewStateInitializer, apiRef, props);\n  useGridKeyboardNavigation(apiRef, props);\n  useGridRowSelection(apiRef, props);\n  useGridColumns(apiRef, props);\n  useGridRows(apiRef, props);\n  useGridRowSpanning(apiRef, props);\n  useGridParamsApi(apiRef, props);\n  useGridColumnSpanning(apiRef);\n  useGridColumnGrouping(apiRef, props);\n  useGridEditing(apiRef, props);\n  useGridFocus(apiRef, props);\n  useGridPreferencesPanel(apiRef, props);\n  useGridFilter(apiRef, props);\n  useGridSorting(apiRef, props);\n  useGridDensity(apiRef, props);\n  useGridColumnResize(apiRef, props);\n  useGridPagination(apiRef, props);\n  useGridRowsMeta(apiRef, props);\n  useGridScroll(apiRef, props);\n  useGridColumnMenu(apiRef);\n  useGridCsvExport(apiRef, props);\n  useGridPrintExport(apiRef, props);\n  useGridClipboard(apiRef, props);\n  useGridDimensions(apiRef, props);\n  useGridEvents(apiRef, props);\n  useGridStatePersistence(apiRef);\n  useGridVirtualization(apiRef, props);\n  useGridListView(apiRef, props);\n  useGridDataSource(apiRef, props);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}