{"ast": null, "code": "import { createRootSelector, createSelector, createSelectorMemoized } from \"../../../utils/createSelector.js\";\n/**\n * Get the columns state\n * @category Virtualization\n */\nexport const gridVirtualizationSelector = createRootSelector(state => state.virtualization);\n\n/**\n * Get the enabled state for virtualization\n * @category Virtualization\n * @deprecated Use `gridVirtualizationColumnEnabledSelector` and `gridVirtualizationRowEnabledSelector`\n */\nexport const gridVirtualizationEnabledSelector = createSelector(gridVirtualizationSelector, state => state.enabled);\n\n/**\n * Get the enabled state for column virtualization\n * @category Virtualization\n */\nexport const gridVirtualizationColumnEnabledSelector = createSelector(gridVirtualizationSelector, state => state.enabledForColumns);\n\n/**\n * Get the enabled state for row virtualization\n * @category Virtualization\n */\nexport const gridVirtualizationRowEnabledSelector = createSelector(gridVirtualizationSelector, state => state.enabledForRows);\n\n/**\n * Get the render context\n * @category Virtualization\n * @ignore - do not document.\n */\nexport const gridRenderContextSelector = createSelector(gridVirtualizationSelector, state => state.renderContext);\nconst firstColumnIndexSelector = createRootSelector(state => state.virtualization.renderContext.firstColumnIndex);\nconst lastColumnIndexSelector = createRootSelector(state => state.virtualization.renderContext.lastColumnIndex);\n\n/**\n * Get the render context, with only columns filled in.\n * This is cached, so it can be used to only re-render when the column interval changes.\n * @category Virtualization\n * @ignore - do not document.\n */\nexport const gridRenderContextColumnsSelector = createSelectorMemoized(firstColumnIndexSelector, lastColumnIndexSelector, (firstColumnIndex, lastColumnIndex) => ({\n  firstColumnIndex,\n  lastColumnIndex\n}));", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}