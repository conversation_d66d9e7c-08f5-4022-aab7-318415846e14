{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useLazyRef from '@mui/utils/useLazyRef';\nimport { GRID_DETAIL_PANEL_TOGGLE_FIELD } from \"../../../internals/constants.js\";\nimport { gridVisibleColumnDefinitionsSelector } from \"../columns/gridColumnsSelector.js\";\nimport { getVisibleRows } from \"../../utils/useGridVisibleRows.js\";\nimport { gridRenderContextSelector } from \"../virtualization/gridVirtualizationSelectors.js\";\nimport { getUnprocessedRange, isRowContextInitialized, getCellValue } from \"./gridRowSpanningUtils.js\";\nimport { GRID_CHECKBOX_SELECTION_FIELD } from \"../../../colDef/gridCheckboxSelectionColDef.js\";\nimport { useGridEvent } from \"../../utils/useGridEvent.js\";\nimport { runIf } from \"../../../utils/utils.js\";\nimport { gridPageSizeSelector } from \"../pagination/index.js\";\nimport { gridDataRowIdsSelector } from \"./gridRowsSelector.js\";\nconst EMPTY_STATE = {\n  spannedCells: {},\n  hiddenCells: {},\n  hiddenCellOriginMap: {}\n};\nconst EMPTY_RANGE = {\n  firstRowIndex: 0,\n  lastRowIndex: 0\n};\nconst skippedFields = new Set([GRID_CHECKBOX_SELECTION_FIELD, '__reorder__', GRID_DETAIL_PANEL_TOGGLE_FIELD]);\n/**\n * Default number of rows to process during state initialization to avoid flickering.\n * Number `20` is arbitrarily chosen to be large enough to cover most of the cases without\n * compromising performance.\n */\nconst DEFAULT_ROWS_TO_PROCESS = 20;\nconst computeRowSpanningState = (apiRef, colDefs, visibleRows, range, rangeToProcess, resetState, processedRange) => {\n  const spannedCells = resetState ? {} : _extends({}, apiRef.current.state.rowSpanning.spannedCells);\n  const hiddenCells = resetState ? {} : _extends({}, apiRef.current.state.rowSpanning.hiddenCells);\n  const hiddenCellOriginMap = resetState ? {} : _extends({}, apiRef.current.state.rowSpanning.hiddenCellOriginMap);\n  if (resetState) {\n    processedRange = EMPTY_RANGE;\n  }\n  colDefs.forEach(colDef => {\n    if (skippedFields.has(colDef.field)) {\n      return;\n    }\n    for (let index = rangeToProcess.firstRowIndex; index < rangeToProcess.lastRowIndex; index += 1) {\n      const row = visibleRows[index];\n      if (hiddenCells[row.id]?.[colDef.field]) {\n        continue;\n      }\n      const cellValue = getCellValue(row.model, colDef, apiRef);\n      if (cellValue == null) {\n        continue;\n      }\n      let spannedRowId = row.id;\n      let spannedRowIndex = index;\n      let rowSpan = 0;\n\n      // For first index, also scan in the previous rows to handle the reset state case e.g by sorting\n      const backwardsHiddenCells = [];\n      if (index === rangeToProcess.firstRowIndex) {\n        let prevIndex = index - 1;\n        let prevRowEntry = visibleRows[prevIndex];\n        while (prevIndex >= range.firstRowIndex && prevRowEntry && getCellValue(prevRowEntry.model, colDef, apiRef) === cellValue) {\n          const currentRow = visibleRows[prevIndex + 1];\n          if (hiddenCells[currentRow.id]) {\n            hiddenCells[currentRow.id][colDef.field] = true;\n          } else {\n            hiddenCells[currentRow.id] = {\n              [colDef.field]: true\n            };\n          }\n          backwardsHiddenCells.push(index);\n          rowSpan += 1;\n          spannedRowId = prevRowEntry.id;\n          spannedRowIndex = prevIndex;\n          prevIndex -= 1;\n          prevRowEntry = visibleRows[prevIndex];\n        }\n      }\n      backwardsHiddenCells.forEach(hiddenCellIndex => {\n        if (hiddenCellOriginMap[hiddenCellIndex]) {\n          hiddenCellOriginMap[hiddenCellIndex][colDef.field] = spannedRowIndex;\n        } else {\n          hiddenCellOriginMap[hiddenCellIndex] = {\n            [colDef.field]: spannedRowIndex\n          };\n        }\n      });\n\n      // Scan the next rows\n      let relativeIndex = index + 1;\n      while (relativeIndex <= range.lastRowIndex && visibleRows[relativeIndex] && getCellValue(visibleRows[relativeIndex].model, colDef, apiRef) === cellValue) {\n        const currentRow = visibleRows[relativeIndex];\n        if (hiddenCells[currentRow.id]) {\n          hiddenCells[currentRow.id][colDef.field] = true;\n        } else {\n          hiddenCells[currentRow.id] = {\n            [colDef.field]: true\n          };\n        }\n        if (hiddenCellOriginMap[relativeIndex]) {\n          hiddenCellOriginMap[relativeIndex][colDef.field] = spannedRowIndex;\n        } else {\n          hiddenCellOriginMap[relativeIndex] = {\n            [colDef.field]: spannedRowIndex\n          };\n        }\n        relativeIndex += 1;\n        rowSpan += 1;\n      }\n      if (rowSpan > 0) {\n        if (spannedCells[spannedRowId]) {\n          spannedCells[spannedRowId][colDef.field] = rowSpan + 1;\n        } else {\n          spannedCells[spannedRowId] = {\n            [colDef.field]: rowSpan + 1\n          };\n        }\n      }\n    }\n    processedRange = {\n      firstRowIndex: Math.min(processedRange.firstRowIndex, rangeToProcess.firstRowIndex),\n      lastRowIndex: Math.max(processedRange.lastRowIndex, rangeToProcess.lastRowIndex)\n    };\n  });\n  return {\n    spannedCells,\n    hiddenCells,\n    hiddenCellOriginMap,\n    processedRange\n  };\n};\nconst getInitialRangeToProcess = (props, apiRef) => {\n  const rowCount = gridDataRowIdsSelector(apiRef).length;\n  if (props.pagination) {\n    const pageSize = gridPageSizeSelector(apiRef);\n    let paginationLastRowIndex = DEFAULT_ROWS_TO_PROCESS;\n    if (pageSize > 0) {\n      paginationLastRowIndex = pageSize - 1;\n    }\n    return {\n      firstRowIndex: 0,\n      lastRowIndex: Math.min(paginationLastRowIndex, rowCount)\n    };\n  }\n  return {\n    firstRowIndex: 0,\n    lastRowIndex: Math.min(DEFAULT_ROWS_TO_PROCESS, rowCount)\n  };\n};\n\n/**\n * @requires columnsStateInitializer (method) - should be initialized before\n * @requires rowsStateInitializer (method) - should be initialized before\n * @requires filterStateInitializer (method) - should be initialized before\n */\nexport const rowSpanningStateInitializer = (state, props, apiRef) => {\n  if (!props.rowSpanning) {\n    return _extends({}, state, {\n      rowSpanning: EMPTY_STATE\n    });\n  }\n  const rowIds = state.rows.dataRowIds || [];\n  const orderedFields = state.columns.orderedFields || [];\n  const dataRowIdToModelLookup = state.rows.dataRowIdToModelLookup;\n  const columnsLookup = state.columns.lookup;\n  const isFilteringPending = Boolean(state.filter.filterModel.items.length) || Boolean(state.filter.filterModel.quickFilterValues?.length);\n  if (!rowIds.length || !orderedFields.length || !dataRowIdToModelLookup || !columnsLookup || isFilteringPending) {\n    return _extends({}, state, {\n      rowSpanning: EMPTY_STATE\n    });\n  }\n  const rangeToProcess = getInitialRangeToProcess(props, apiRef);\n  const rows = rowIds.map(id => ({\n    id,\n    model: dataRowIdToModelLookup[id]\n  }));\n  const colDefs = orderedFields.map(field => columnsLookup[field]);\n  const {\n    spannedCells,\n    hiddenCells,\n    hiddenCellOriginMap\n  } = computeRowSpanningState(apiRef, colDefs, rows, rangeToProcess, rangeToProcess, true, EMPTY_RANGE);\n  return _extends({}, state, {\n    rowSpanning: {\n      spannedCells,\n      hiddenCells,\n      hiddenCellOriginMap\n    }\n  });\n};\nexport const useGridRowSpanning = (apiRef, props) => {\n  const processedRange = useLazyRef(() => {\n    return apiRef.current.state.rowSpanning !== EMPTY_STATE ? getInitialRangeToProcess(props, apiRef) : EMPTY_RANGE;\n  });\n  const updateRowSpanningState = React.useCallback((renderContext, resetState = false) => {\n    const {\n      range,\n      rows: visibleRows\n    } = getVisibleRows(apiRef, {\n      pagination: props.pagination,\n      paginationMode: props.paginationMode\n    });\n    if (range === null || !isRowContextInitialized(renderContext)) {\n      return;\n    }\n    if (resetState) {\n      processedRange.current = EMPTY_RANGE;\n    }\n    const rangeToProcess = getUnprocessedRange({\n      firstRowIndex: renderContext.firstRowIndex,\n      lastRowIndex: Math.min(renderContext.lastRowIndex, range.lastRowIndex + 1)\n    }, processedRange.current);\n    if (rangeToProcess === null) {\n      return;\n    }\n    const colDefs = gridVisibleColumnDefinitionsSelector(apiRef);\n    const {\n      spannedCells,\n      hiddenCells,\n      hiddenCellOriginMap,\n      processedRange: newProcessedRange\n    } = computeRowSpanningState(apiRef, colDefs, visibleRows, range, rangeToProcess, resetState, processedRange.current);\n    processedRange.current = newProcessedRange;\n    const newSpannedCellsCount = Object.keys(spannedCells).length;\n    const newHiddenCellsCount = Object.keys(hiddenCells).length;\n    const currentSpannedCellsCount = Object.keys(apiRef.current.state.rowSpanning.spannedCells).length;\n    const currentHiddenCellsCount = Object.keys(apiRef.current.state.rowSpanning.hiddenCells).length;\n    const shouldUpdateState = resetState || newSpannedCellsCount !== currentSpannedCellsCount || newHiddenCellsCount !== currentHiddenCellsCount;\n    const hasNoSpannedCells = newSpannedCellsCount === 0 && currentSpannedCellsCount === 0;\n    if (!shouldUpdateState || hasNoSpannedCells) {\n      return;\n    }\n    apiRef.current.setState(state => {\n      return _extends({}, state, {\n        rowSpanning: {\n          spannedCells,\n          hiddenCells,\n          hiddenCellOriginMap\n        }\n      });\n    });\n  }, [apiRef, processedRange, props.pagination, props.paginationMode]);\n\n  // Reset events trigger a full re-computation of the row spanning state:\n  // - The `unstable_rowSpanning` prop is updated (feature flag)\n  // - The filtering is applied\n  // - The sorting is applied\n  // - The `paginationModel` is updated\n  // - The rows are updated\n  const resetRowSpanningState = React.useCallback(() => {\n    const renderContext = gridRenderContextSelector(apiRef);\n    if (!isRowContextInitialized(renderContext)) {\n      return;\n    }\n    updateRowSpanningState(renderContext, true);\n  }, [apiRef, updateRowSpanningState]);\n  useGridEvent(apiRef, 'renderedRowsIntervalChange', runIf(props.rowSpanning, updateRowSpanningState));\n  useGridEvent(apiRef, 'sortedRowsSet', runIf(props.rowSpanning, resetRowSpanningState));\n  useGridEvent(apiRef, 'paginationModelChange', runIf(props.rowSpanning, resetRowSpanningState));\n  useGridEvent(apiRef, 'filteredRowsSet', runIf(props.rowSpanning, resetRowSpanningState));\n  useGridEvent(apiRef, 'columnsChange', runIf(props.rowSpanning, resetRowSpanningState));\n  React.useEffect(() => {\n    if (!props.rowSpanning) {\n      if (apiRef.current.state.rowSpanning !== EMPTY_STATE) {\n        apiRef.current.setState(state => _extends({}, state, {\n          rowSpanning: EMPTY_STATE\n        }));\n      }\n    } else if (apiRef.current.state.rowSpanning === EMPTY_STATE) {\n      resetRowSpanningState();\n    }\n  }, [apiRef, resetRowSpanningState, props.rowSpanning]);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}