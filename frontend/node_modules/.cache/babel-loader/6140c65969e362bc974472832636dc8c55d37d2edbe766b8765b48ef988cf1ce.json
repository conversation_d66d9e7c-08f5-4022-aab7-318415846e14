{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport ownerDocument from '@mui/utils/ownerDocument';\nimport useLazyRef from '@mui/utils/useLazyRef';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { findGridCellElementsFromCol, findGridElement, findLeftPinnedCellsAfterCol, findRightPinnedCellsBeforeCol, getFieldFromHeaderElem, findHeaderElementFromField, getFieldsFromGroupHeaderElem, findGroupHeaderElementsFromField, findGridHeader, findGridCells, findParentElementFromClassName, findLeftPinnedHeadersAfterCol, findRightPinnedHeadersBeforeCol, escapeOperandAttributeSelector } from \"../../../utils/domUtils.js\";\nimport { DEFAULT_GRID_AUTOSIZE_OPTIONS } from \"./gridColumnResizeApi.js\";\nimport { gridClasses } from \"../../../constants/gridClasses.js\";\nimport { useGridEvent, useGridApiMethod, useGridEventPriority, useGridLogger, useGridNativeEventListener, useGridSelector, useOnMount } from \"../../utils/index.js\";\nimport { gridVirtualizationColumnEnabledSelector } from \"../virtualization/index.js\";\nimport { createControllablePromise } from \"../../../utils/createControllablePromise.js\";\nimport { clamp } from \"../../../utils/utils.js\";\nimport { useTimeout } from \"../../utils/useTimeout.js\";\nimport { GridPinnedColumnPosition } from \"../columns/gridColumnsInterfaces.js\";\nimport { gridColumnsStateSelector } from \"../columns/index.js\";\nfunction trackFinger(event, currentTouchId) {\n  if (currentTouchId !== undefined && event.changedTouches) {\n    for (let i = 0; i < event.changedTouches.length; i += 1) {\n      const touch = event.changedTouches[i];\n      if (touch.identifier === currentTouchId) {\n        return {\n          x: touch.clientX,\n          y: touch.clientY\n        };\n      }\n    }\n    return false;\n  }\n  return {\n    x: event.clientX,\n    y: event.clientY\n  };\n}\nfunction computeNewWidth(initialOffsetToSeparator, clickX, columnBounds, resizeDirection) {\n  let newWidth = initialOffsetToSeparator;\n  if (resizeDirection === 'Right') {\n    newWidth += clickX - columnBounds.left;\n  } else {\n    newWidth += columnBounds.right - clickX;\n  }\n  return newWidth;\n}\nfunction computeOffsetToSeparator(clickX, columnBounds, resizeDirection) {\n  if (resizeDirection === 'Left') {\n    return clickX - columnBounds.left;\n  }\n  return columnBounds.right - clickX;\n}\nfunction flipResizeDirection(side) {\n  if (side === 'Right') {\n    return 'Left';\n  }\n  return 'Right';\n}\nfunction getResizeDirection(separator, isRtl) {\n  const side = separator.classList.contains(gridClasses['columnSeparator--sideRight']) ? 'Right' : 'Left';\n  if (isRtl) {\n    // Resizing logic should be mirrored in the RTL case\n    return flipResizeDirection(side);\n  }\n  return side;\n}\nfunction preventClick(event) {\n  event.preventDefault();\n  event.stopImmediatePropagation();\n}\n\n/**\n * Checker that returns a promise that resolves when the column virtualization\n * is disabled.\n */\nfunction useColumnVirtualizationDisabled(apiRef) {\n  const promise = React.useRef(undefined);\n  const selector = () => gridVirtualizationColumnEnabledSelector(apiRef);\n  const value = useGridSelector(apiRef, selector);\n  React.useEffect(() => {\n    if (promise.current && value === false) {\n      promise.current.resolve();\n      promise.current = undefined;\n    }\n  });\n  const asyncCheck = () => {\n    if (!promise.current) {\n      if (selector() === false) {\n        return Promise.resolve();\n      }\n      promise.current = createControllablePromise();\n    }\n    return promise.current;\n  };\n  return asyncCheck;\n}\n\n/**\n * Basic statistical outlier detection, checks if the value is `F * IQR` away from\n * the Q1 and Q3 boundaries. IQR: interquartile range.\n */\nfunction excludeOutliers(inputValues, factor) {\n  if (inputValues.length < 4) {\n    return inputValues;\n  }\n  const values = inputValues.slice();\n  values.sort((a, b) => a - b);\n  const q1 = values[Math.floor(values.length * 0.25)];\n  const q3 = values[Math.floor(values.length * 0.75) - 1];\n  const iqr = q3 - q1;\n\n  // We make a small adjustment if `iqr < 5` for the cases where the IQR is\n  // very small (for example zero) due to very close by values in the input data.\n  // Otherwise, with an IQR of `0`, anything outside that would be considered\n  // an outlier, but it makes more sense visually to allow for this 5px variance\n  // rather than showing a cropped cell.\n  const deviation = iqr < 5 ? 5 : iqr * factor;\n  return values.filter(v => v > q1 - deviation && v < q3 + deviation);\n}\nfunction extractColumnWidths(apiRef, options, columns) {\n  const widthByField = {};\n  const root = apiRef.current.rootElementRef.current;\n  root.classList.add(gridClasses.autosizing);\n  columns.forEach(column => {\n    const cells = findGridCells(apiRef.current, column.field);\n    const widths = cells.map(cell => {\n      return cell.getBoundingClientRect().width ?? 0;\n    });\n    const filteredWidths = options.includeOutliers ? widths : excludeOutliers(widths, options.outliersFactor);\n    if (options.includeHeaders) {\n      const header = findGridHeader(apiRef.current, column.field);\n      if (header) {\n        const title = header.querySelector(`.${gridClasses.columnHeaderTitle}`);\n        const content = header.querySelector(`.${gridClasses.columnHeaderTitleContainerContent}`);\n        const iconContainer = header.querySelector(`.${gridClasses.iconButtonContainer}`);\n        const menuContainer = header.querySelector(`.${gridClasses.menuIcon}`);\n        const element = title ?? content;\n        const style = window.getComputedStyle(header, null);\n        const paddingWidth = parseInt(style.paddingLeft, 10) + parseInt(style.paddingRight, 10);\n        const contentWidth = element.scrollWidth + 1;\n        const width = contentWidth + paddingWidth + (iconContainer?.clientWidth ?? 0) + (menuContainer?.clientWidth ?? 0);\n        filteredWidths.push(width);\n      }\n    }\n    const hasColumnMin = column.minWidth !== -Infinity && column.minWidth !== undefined;\n    const hasColumnMax = column.maxWidth !== Infinity && column.maxWidth !== undefined;\n    const min = hasColumnMin ? column.minWidth : 0;\n    const max = hasColumnMax ? column.maxWidth : Infinity;\n    const maxContent = filteredWidths.length === 0 ? 0 : Math.max(...filteredWidths);\n    widthByField[column.field] = clamp(maxContent, min, max);\n  });\n  root.classList.remove(gridClasses.autosizing);\n  return widthByField;\n}\nexport const columnResizeStateInitializer = state => _extends({}, state, {\n  columnResize: {\n    resizingColumnField: ''\n  }\n});\nfunction createResizeRefs() {\n  return {\n    colDef: undefined,\n    initialColWidth: 0,\n    initialTotalWidth: 0,\n    previousMouseClickEvent: undefined,\n    columnHeaderElement: undefined,\n    headerFilterElement: undefined,\n    groupHeaderElements: [],\n    cellElements: [],\n    leftPinnedCellsAfter: [],\n    rightPinnedCellsBefore: [],\n    fillerLeft: undefined,\n    fillerRight: undefined,\n    leftPinnedHeadersAfter: [],\n    rightPinnedHeadersBefore: []\n  };\n}\n\n/**\n * @requires useGridColumns (method, event)\n * TODO: improve experience for last column\n */\nexport const useGridColumnResize = (apiRef, props) => {\n  const isRtl = useRtl();\n  const logger = useGridLogger(apiRef, 'useGridColumnResize');\n  const refs = useLazyRef(createResizeRefs).current;\n\n  // To improve accessibility, the separator has padding on both sides.\n  // Clicking inside the padding area should be treated as a click in the separator.\n  // This ref stores the offset between the click and the separator.\n  const initialOffsetToSeparator = React.useRef(null);\n  const resizeDirection = React.useRef(null);\n  const stopResizeEventTimeout = useTimeout();\n  const touchId = React.useRef(undefined);\n  const updateWidth = newWidth => {\n    logger.debug(`Updating width to ${newWidth} for col ${refs.colDef.field}`);\n    const prevWidth = refs.columnHeaderElement.offsetWidth;\n    const widthDiff = newWidth - prevWidth;\n    const columnWidthDiff = newWidth - refs.initialColWidth;\n    if (columnWidthDiff > 0) {\n      const newTotalWidth = refs.initialTotalWidth + columnWidthDiff;\n      apiRef.current.rootElementRef?.current?.style.setProperty('--DataGrid-rowWidth', `${newTotalWidth}px`);\n    }\n    refs.colDef.computedWidth = newWidth;\n    refs.colDef.width = newWidth;\n    refs.colDef.flex = 0;\n    refs.columnHeaderElement.style.width = `${newWidth}px`;\n    const headerFilterElement = refs.headerFilterElement;\n    if (headerFilterElement) {\n      headerFilterElement.style.width = `${newWidth}px`;\n    }\n    refs.groupHeaderElements.forEach(element => {\n      const div = element;\n      let finalWidth;\n      if (div.getAttribute('aria-colspan') === '1') {\n        finalWidth = `${newWidth}px`;\n      } else {\n        // Cell with colspan > 1 cannot be just updated width new width.\n        // Instead, we add width diff to the current width.\n        finalWidth = `${div.offsetWidth + widthDiff}px`;\n      }\n      div.style.width = finalWidth;\n    });\n    refs.cellElements.forEach(element => {\n      const div = element;\n      let finalWidth;\n      if (div.getAttribute('aria-colspan') === '1') {\n        finalWidth = `${newWidth}px`;\n      } else {\n        // Cell with colspan > 1 cannot be just updated width new width.\n        // Instead, we add width diff to the current width.\n        finalWidth = `${div.offsetWidth + widthDiff}px`;\n      }\n      div.style.setProperty('--width', finalWidth);\n    });\n    const pinnedPosition = apiRef.current.unstable_applyPipeProcessors('isColumnPinned', false, refs.colDef.field);\n    if (pinnedPosition === GridPinnedColumnPosition.LEFT) {\n      updateProperty(refs.fillerLeft, 'width', widthDiff);\n      refs.leftPinnedCellsAfter.forEach(cell => {\n        updateProperty(cell, 'left', widthDiff);\n      });\n      refs.leftPinnedHeadersAfter.forEach(header => {\n        updateProperty(header, 'left', widthDiff);\n      });\n    }\n    if (pinnedPosition === GridPinnedColumnPosition.RIGHT) {\n      updateProperty(refs.fillerRight, 'width', widthDiff);\n      refs.rightPinnedCellsBefore.forEach(cell => {\n        updateProperty(cell, 'right', widthDiff);\n      });\n      refs.rightPinnedHeadersBefore.forEach(header => {\n        updateProperty(header, 'right', widthDiff);\n      });\n    }\n  };\n  const finishResize = nativeEvent => {\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    stopListening();\n\n    // Prevent double-clicks from being interpreted as two separate clicks\n    if (refs.previousMouseClickEvent) {\n      const prevEvent = refs.previousMouseClickEvent;\n      const prevTimeStamp = prevEvent.timeStamp;\n      const prevClientX = prevEvent.clientX;\n      const prevClientY = prevEvent.clientY;\n\n      // Check if the current event is part of a double-click\n      if (nativeEvent.timeStamp - prevTimeStamp < 300 && nativeEvent.clientX === prevClientX && nativeEvent.clientY === prevClientY) {\n        refs.previousMouseClickEvent = undefined;\n        apiRef.current.publishEvent('columnResizeStop', null, nativeEvent);\n        return;\n      }\n    }\n    if (refs.colDef) {\n      apiRef.current.setColumnWidth(refs.colDef.field, refs.colDef.width);\n      logger.debug(`Updating col ${refs.colDef.field} with new width: ${refs.colDef.width}`);\n\n      // Since during resizing we update the columns width outside of React, React is unable to\n      // reapply the right style properties. We need to sync the state manually.\n      // So we reapply the same logic as in https://github.com/mui/mui-x/blob/0511bf65543ca05d2602a5a3e0a6156f2fc8e759/packages/x-data-grid/src/hooks/features/columnHeaders/useGridColumnHeaders.tsx#L405\n      const columnsState = gridColumnsStateSelector(apiRef);\n      refs.groupHeaderElements.forEach(element => {\n        const fields = getFieldsFromGroupHeaderElem(element);\n        const div = element;\n        const newWidth = fields.reduce((acc, field) => {\n          if (columnsState.columnVisibilityModel[field] !== false) {\n            return acc + columnsState.lookup[field].computedWidth;\n          }\n          return acc;\n        }, 0);\n        const finalWidth = `${newWidth}px`;\n        div.style.width = finalWidth;\n      });\n    }\n    stopResizeEventTimeout.start(0, () => {\n      apiRef.current.publishEvent('columnResizeStop', null, nativeEvent);\n    });\n  };\n  const storeReferences = (colDef, separator, xStart) => {\n    const root = apiRef.current.rootElementRef.current;\n    refs.initialColWidth = colDef.computedWidth;\n    refs.initialTotalWidth = apiRef.current.getRootDimensions().rowWidth;\n    refs.colDef = colDef;\n    refs.columnHeaderElement = findHeaderElementFromField(apiRef.current.columnHeadersContainerRef.current, colDef.field);\n    const headerFilterElement = root.querySelector(`.${gridClasses.headerFilterRow} [data-field=\"${escapeOperandAttributeSelector(colDef.field)}\"]`);\n    if (headerFilterElement) {\n      refs.headerFilterElement = headerFilterElement;\n    }\n    refs.groupHeaderElements = findGroupHeaderElementsFromField(apiRef.current.columnHeadersContainerRef?.current, colDef.field);\n    refs.cellElements = findGridCellElementsFromCol(refs.columnHeaderElement, apiRef.current);\n    refs.fillerLeft = findGridElement(apiRef.current, isRtl ? 'filler--pinnedRight' : 'filler--pinnedLeft');\n    refs.fillerRight = findGridElement(apiRef.current, isRtl ? 'filler--pinnedLeft' : 'filler--pinnedRight');\n    const pinnedPosition = apiRef.current.unstable_applyPipeProcessors('isColumnPinned', false, refs.colDef.field);\n    refs.leftPinnedCellsAfter = pinnedPosition !== GridPinnedColumnPosition.LEFT ? [] : findLeftPinnedCellsAfterCol(apiRef.current, refs.columnHeaderElement, isRtl);\n    refs.rightPinnedCellsBefore = pinnedPosition !== GridPinnedColumnPosition.RIGHT ? [] : findRightPinnedCellsBeforeCol(apiRef.current, refs.columnHeaderElement, isRtl);\n    refs.leftPinnedHeadersAfter = pinnedPosition !== GridPinnedColumnPosition.LEFT ? [] : findLeftPinnedHeadersAfterCol(apiRef.current, refs.columnHeaderElement, isRtl);\n    refs.rightPinnedHeadersBefore = pinnedPosition !== GridPinnedColumnPosition.RIGHT ? [] : findRightPinnedHeadersBeforeCol(apiRef.current, refs.columnHeaderElement, isRtl);\n    resizeDirection.current = getResizeDirection(separator, isRtl);\n    initialOffsetToSeparator.current = computeOffsetToSeparator(xStart, refs.columnHeaderElement.getBoundingClientRect(), resizeDirection.current);\n  };\n  const handleResizeMouseUp = useEventCallback(finishResize);\n  const handleResizeMouseMove = useEventCallback(nativeEvent => {\n    // Cancel move in case some other element consumed a mouseup event and it was not fired.\n    if (nativeEvent.buttons === 0) {\n      handleResizeMouseUp(nativeEvent);\n      return;\n    }\n    let newWidth = computeNewWidth(initialOffsetToSeparator.current, nativeEvent.clientX, refs.columnHeaderElement.getBoundingClientRect(), resizeDirection.current);\n    newWidth = clamp(newWidth, refs.colDef.minWidth, refs.colDef.maxWidth);\n    updateWidth(newWidth);\n    const params = {\n      element: refs.columnHeaderElement,\n      colDef: refs.colDef,\n      width: newWidth\n    };\n    apiRef.current.publishEvent('columnResize', params, nativeEvent);\n  });\n  const handleTouchEnd = useEventCallback(nativeEvent => {\n    const finger = trackFinger(nativeEvent, touchId.current);\n    if (!finger) {\n      return;\n    }\n    finishResize(nativeEvent);\n  });\n  const handleTouchMove = useEventCallback(nativeEvent => {\n    const finger = trackFinger(nativeEvent, touchId.current);\n    if (!finger) {\n      return;\n    }\n\n    // Cancel move in case some other element consumed a touchmove event and it was not fired.\n    if (nativeEvent.type === 'mousemove' && nativeEvent.buttons === 0) {\n      handleTouchEnd(nativeEvent);\n      return;\n    }\n    let newWidth = computeNewWidth(initialOffsetToSeparator.current, finger.x, refs.columnHeaderElement.getBoundingClientRect(), resizeDirection.current);\n    newWidth = clamp(newWidth, refs.colDef.minWidth, refs.colDef.maxWidth);\n    updateWidth(newWidth);\n    const params = {\n      element: refs.columnHeaderElement,\n      colDef: refs.colDef,\n      width: newWidth\n    };\n    apiRef.current.publishEvent('columnResize', params, nativeEvent);\n  });\n  const handleTouchStart = useEventCallback(event => {\n    const cellSeparator = findParentElementFromClassName(event.target, gridClasses['columnSeparator--resizable']);\n    // Let the event bubble if the target is not a col separator\n    if (!cellSeparator) {\n      return;\n    }\n    const touch = event.changedTouches[0];\n    if (touch != null) {\n      // A number that uniquely identifies the current finger in the touch session.\n      touchId.current = touch.identifier;\n    }\n    const columnHeaderElement = findParentElementFromClassName(event.target, gridClasses.columnHeader);\n    const field = getFieldFromHeaderElem(columnHeaderElement);\n    const colDef = apiRef.current.getColumn(field);\n    logger.debug(`Start Resize on col ${colDef.field}`);\n    apiRef.current.publishEvent('columnResizeStart', {\n      field\n    }, event);\n    storeReferences(colDef, cellSeparator, touch.clientX);\n    const doc = ownerDocument(event.currentTarget);\n    doc.addEventListener('touchmove', handleTouchMove);\n    doc.addEventListener('touchend', handleTouchEnd);\n  });\n  const stopListening = React.useCallback(() => {\n    const doc = ownerDocument(apiRef.current.rootElementRef.current);\n    doc.body.style.removeProperty('cursor');\n    doc.removeEventListener('mousemove', handleResizeMouseMove);\n    doc.removeEventListener('mouseup', handleResizeMouseUp);\n    doc.removeEventListener('touchmove', handleTouchMove);\n    doc.removeEventListener('touchend', handleTouchEnd);\n    // The click event runs right after the mouseup event, we want to wait until it\n    // has been canceled before removing our handler.\n    setTimeout(() => {\n      doc.removeEventListener('click', preventClick, true);\n    }, 100);\n    if (refs.columnHeaderElement) {\n      refs.columnHeaderElement.style.pointerEvents = 'unset';\n    }\n  }, [apiRef, refs, handleResizeMouseMove, handleResizeMouseUp, handleTouchMove, handleTouchEnd]);\n  const handleResizeStart = React.useCallback(({\n    field\n  }) => {\n    apiRef.current.setState(state => _extends({}, state, {\n      columnResize: _extends({}, state.columnResize, {\n        resizingColumnField: field\n      })\n    }));\n  }, [apiRef]);\n  const handleResizeStop = React.useCallback(() => {\n    apiRef.current.setState(state => _extends({}, state, {\n      columnResize: _extends({}, state.columnResize, {\n        resizingColumnField: ''\n      })\n    }));\n  }, [apiRef]);\n  const handleColumnResizeMouseDown = useEventCallback(({\n    colDef\n  }, event) => {\n    // Only handle left clicks\n    if (event.button !== 0) {\n      return;\n    }\n\n    // Skip if the column isn't resizable\n    if (!event.currentTarget.classList.contains(gridClasses['columnSeparator--resizable'])) {\n      return;\n    }\n\n    // Avoid text selection\n    event.preventDefault();\n    logger.debug(`Start Resize on col ${colDef.field}`);\n    apiRef.current.publishEvent('columnResizeStart', {\n      field: colDef.field\n    }, event);\n    storeReferences(colDef, event.currentTarget, event.clientX);\n    const doc = ownerDocument(apiRef.current.rootElementRef.current);\n    doc.body.style.cursor = 'col-resize';\n    refs.previousMouseClickEvent = event.nativeEvent;\n    doc.addEventListener('mousemove', handleResizeMouseMove);\n    doc.addEventListener('mouseup', handleResizeMouseUp);\n\n    // Prevent the click event if we have resized the column.\n    // Fixes https://github.com/mui/mui-x/issues/4777\n    doc.addEventListener('click', preventClick, true);\n  });\n  const handleColumnSeparatorDoubleClick = useEventCallback((params, event) => {\n    if (props.disableAutosize) {\n      return;\n    }\n\n    // Only handle left clicks\n    if (event.button !== 0) {\n      return;\n    }\n    const column = apiRef.current.state.columns.lookup[params.field];\n    if (column.resizable === false) {\n      return;\n    }\n    apiRef.current.autosizeColumns(_extends({}, props.autosizeOptions, {\n      disableColumnVirtualization: false,\n      columns: [column.field]\n    }));\n  });\n\n  /**\n   * API METHODS\n   */\n\n  const columnVirtualizationDisabled = useColumnVirtualizationDisabled(apiRef);\n  const isAutosizingRef = React.useRef(false);\n  const autosizeColumns = React.useCallback(async userOptions => {\n    const root = apiRef.current.rootElementRef?.current;\n    if (!root) {\n      return;\n    }\n    if (isAutosizingRef.current) {\n      return;\n    }\n    isAutosizingRef.current = true;\n    const state = gridColumnsStateSelector(apiRef);\n    const options = _extends({}, DEFAULT_GRID_AUTOSIZE_OPTIONS, userOptions, {\n      columns: userOptions?.columns ?? state.orderedFields\n    });\n    options.columns = options.columns.filter(c => state.columnVisibilityModel[c] !== false);\n    const columns = options.columns.map(c => apiRef.current.state.columns.lookup[c]);\n    try {\n      if (!props.disableVirtualization && options.disableColumnVirtualization) {\n        apiRef.current.unstable_setColumnVirtualization(false);\n        await columnVirtualizationDisabled();\n      }\n      const widthByField = extractColumnWidths(apiRef, options, columns);\n      const newColumns = columns.map(column => _extends({}, column, {\n        width: widthByField[column.field],\n        computedWidth: widthByField[column.field],\n        flex: 0\n      }));\n      if (options.expand) {\n        const visibleColumns = state.orderedFields.map(field => state.lookup[field]).filter(c => state.columnVisibilityModel[c.field] !== false);\n        const totalWidth = visibleColumns.reduce((total, column) => total + (widthByField[column.field] ?? column.computedWidth ?? column.width), 0);\n        const dimensions = apiRef.current.getRootDimensions();\n        const availableWidth = dimensions.viewportInnerSize.width;\n        const remainingWidth = availableWidth - totalWidth;\n        if (remainingWidth > 0) {\n          const widthPerColumn = remainingWidth / (newColumns.length || 1);\n          newColumns.forEach(column => {\n            column.width += widthPerColumn;\n            column.computedWidth += widthPerColumn;\n          });\n        }\n      }\n      apiRef.current.updateColumns(newColumns);\n      newColumns.forEach((newColumn, index) => {\n        if (newColumn.width !== columns[index].width) {\n          const width = newColumn.width;\n          apiRef.current.publishEvent('columnWidthChange', {\n            element: apiRef.current.getColumnHeaderElement(newColumn.field),\n            colDef: newColumn,\n            width\n          });\n        }\n      });\n    } finally {\n      if (!props.disableVirtualization) {\n        apiRef.current.unstable_setColumnVirtualization(true);\n      }\n      isAutosizingRef.current = false;\n    }\n  }, [apiRef, columnVirtualizationDisabled, props.disableVirtualization]);\n\n  /**\n   * EFFECTS\n   */\n\n  React.useEffect(() => stopListening, [stopListening]);\n  useOnMount(() => {\n    if (props.autosizeOnMount) {\n      Promise.resolve().then(() => {\n        apiRef.current.autosizeColumns(props.autosizeOptions);\n      });\n    }\n  });\n  useGridNativeEventListener(apiRef, () => apiRef.current.columnHeadersContainerRef?.current, 'touchstart', handleTouchStart, {\n    passive: true\n  });\n  useGridApiMethod(apiRef, {\n    autosizeColumns\n  }, 'public');\n  useGridEvent(apiRef, 'columnResizeStop', handleResizeStop);\n  useGridEvent(apiRef, 'columnResizeStart', handleResizeStart);\n  useGridEvent(apiRef, 'columnSeparatorMouseDown', handleColumnResizeMouseDown);\n  useGridEvent(apiRef, 'columnSeparatorDoubleClick', handleColumnSeparatorDoubleClick);\n  useGridEventPriority(apiRef, 'columnResize', props.onColumnResize);\n  useGridEventPriority(apiRef, 'columnWidthChange', props.onColumnWidthChange);\n};\nfunction updateProperty(element, property, delta) {\n  if (!element) {\n    return;\n  }\n  element.style[property] = `${parseInt(element.style[property], 10) + delta}px`;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}