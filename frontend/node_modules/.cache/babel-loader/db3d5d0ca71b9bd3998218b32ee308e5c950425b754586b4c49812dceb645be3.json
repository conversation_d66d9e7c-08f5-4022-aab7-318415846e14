{"ast": null, "code": "export const GRID_TREE_DATA_GROUPING_FIELD = '__tree_data_group__';\nexport const GRID_ROW_GROUPING_SINGLE_GROUPING_FIELD = '__row_group_by_columns_group__';\nexport const GRID_DETAIL_PANEL_TOGGLE_FIELD = '__detail_panel_toggle__';\nexport let PinnedColumnPosition = /*#__PURE__*/function (PinnedColumnPosition) {\n  PinnedColumnPosition[PinnedColumnPosition[\"NONE\"] = 0] = \"NONE\";\n  PinnedColumnPosition[PinnedColumnPosition[\"LEFT\"] = 1] = \"LEFT\";\n  PinnedColumnPosition[PinnedColumnPosition[\"RIGHT\"] = 2] = \"RIGHT\";\n  PinnedColumnPosition[PinnedColumnPosition[\"VIRTUAL\"] = 3] = \"VIRTUAL\";\n  return PinnedColumnPosition;\n}({});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}