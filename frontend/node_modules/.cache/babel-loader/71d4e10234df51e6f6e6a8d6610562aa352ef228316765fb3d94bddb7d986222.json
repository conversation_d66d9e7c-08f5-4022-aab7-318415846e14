{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"selected\", \"rowId\", \"row\", \"index\", \"style\", \"rowHeight\", \"className\", \"visibleColumns\", \"pinnedColumns\", \"offsetLeft\", \"columnsTotalWidth\", \"firstColumnIndex\", \"lastColumnIndex\", \"focusedColumnIndex\", \"isFirstVisible\", \"isLastVisible\", \"isNotVisible\", \"showBottomBorder\", \"scrollbarWidth\", \"gridHasFiller\", \"onClick\", \"onDoubleClick\", \"onMouseEnter\", \"onMouseLeave\", \"onMouseOut\", \"onMouseOver\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport useForkRef from '@mui/utils/useForkRef';\nimport { fastMemo } from '@mui/x-internals/fastMemo';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { isObjectEmpty } from '@mui/x-internals/isObjectEmpty';\nimport { GridEditModes, GridCellModes } from \"../models/gridEditRowModel.js\";\nimport { gridClasses } from \"../constants/gridClasses.js\";\nimport { composeGridClasses } from \"../utils/composeGridClasses.js\";\nimport { useGridRootProps } from \"../hooks/utils/useGridRootProps.js\";\nimport { shouldCellShowLeftBorder, shouldCellShowRightBorder } from \"../utils/cellBorderUtils.js\";\nimport { gridColumnPositionsSelector } from \"../hooks/features/columns/gridColumnsSelector.js\";\nimport { useGridSelector, objectShallowCompare } from \"../hooks/utils/useGridSelector.js\";\nimport { useGridVisibleRows } from \"../hooks/utils/useGridVisibleRows.js\";\nimport { findParentElementFromClassName, isEventTargetInPortal } from \"../utils/domUtils.js\";\nimport { GRID_CHECKBOX_SELECTION_COL_DEF } from \"../colDef/gridCheckboxSelectionColDef.js\";\nimport { GRID_ACTIONS_COLUMN_TYPE } from \"../colDef/gridActionsColDef.js\";\nimport { GRID_DETAIL_PANEL_TOGGLE_FIELD, PinnedColumnPosition } from \"../internals/constants.js\";\nimport { gridSortModelSelector } from \"../hooks/features/sorting/gridSortingSelector.js\";\nimport { gridRowMaximumTreeDepthSelector, gridRowNodeSelector } from \"../hooks/features/rows/gridRowsSelector.js\";\nimport { gridEditRowsStateSelector, gridRowIsEditingSelector } from \"../hooks/features/editing/gridEditingSelectors.js\";\nimport { GridScrollbarFillerCell as ScrollbarFiller } from \"./GridScrollbarFillerCell.js\";\nimport { getPinnedCellOffset } from \"../internals/utils/getPinnedCellOffset.js\";\nimport { useGridConfiguration } from \"../hooks/utils/useGridConfiguration.js\";\nimport { useGridPrivateApiContext } from \"../hooks/utils/useGridPrivateApiContext.js\";\nimport { createSelector } from \"../utils/createSelector.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst isRowReorderingEnabledSelector = createSelector(gridEditRowsStateSelector, (editRows, rowReordering) => {\n  if (!rowReordering) {\n    return false;\n  }\n  const isEditingRows = !isObjectEmpty(editRows);\n  return !isEditingRows;\n});\nconst GridRow = forwardRef(function GridRow(props, refProp) {\n  const {\n      selected,\n      rowId,\n      row,\n      index,\n      style: styleProp,\n      rowHeight,\n      className,\n      visibleColumns,\n      pinnedColumns,\n      offsetLeft,\n      columnsTotalWidth,\n      firstColumnIndex,\n      lastColumnIndex,\n      focusedColumnIndex,\n      isFirstVisible,\n      isLastVisible,\n      isNotVisible,\n      showBottomBorder,\n      scrollbarWidth,\n      gridHasFiller,\n      onClick,\n      onDoubleClick,\n      onMouseEnter,\n      onMouseLeave,\n      onMouseOut,\n      onMouseOver\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const apiRef = useGridPrivateApiContext();\n  const configuration = useGridConfiguration();\n  const ref = React.useRef(null);\n  const rootProps = useGridRootProps();\n  const currentPage = useGridVisibleRows(apiRef, rootProps);\n  const sortModel = useGridSelector(apiRef, gridSortModelSelector);\n  const treeDepth = useGridSelector(apiRef, gridRowMaximumTreeDepthSelector);\n  const columnPositions = useGridSelector(apiRef, gridColumnPositionsSelector);\n  const rowReordering = rootProps.rowReordering;\n  const isRowReorderingEnabled = useGridSelector(apiRef, isRowReorderingEnabledSelector, rowReordering);\n  const handleRef = useForkRef(ref, refProp);\n  const rowNode = gridRowNodeSelector(apiRef, rowId);\n  const editing = useGridSelector(apiRef, gridRowIsEditingSelector, {\n    rowId,\n    editMode: rootProps.editMode\n  });\n  const editable = rootProps.editMode === GridEditModes.Row;\n  const hasFocusCell = focusedColumnIndex !== undefined;\n  const hasVirtualFocusCellLeft = hasFocusCell && focusedColumnIndex >= pinnedColumns.left.length && focusedColumnIndex < firstColumnIndex;\n  const hasVirtualFocusCellRight = hasFocusCell && focusedColumnIndex < visibleColumns.length - pinnedColumns.right.length && focusedColumnIndex >= lastColumnIndex;\n  const classes = composeGridClasses(rootProps.classes, {\n    root: ['row', selected && 'selected', editable && 'row--editable', editing && 'row--editing', isFirstVisible && 'row--firstVisible', isLastVisible && 'row--lastVisible', showBottomBorder && 'row--borderBottom', rowHeight === 'auto' && 'row--dynamicHeight']\n  });\n  const getRowAriaAttributes = configuration.hooks.useGridRowAriaAttributes();\n  React.useLayoutEffect(() => {\n    if (currentPage.range) {\n      const rowIndex = apiRef.current.getRowIndexRelativeToVisibleRows(rowId);\n      // Pinned rows are not part of the visible rows\n      if (rowIndex !== undefined) {\n        apiRef.current.unstable_setLastMeasuredRowIndex(rowIndex);\n      }\n    }\n    if (ref.current && rowHeight === 'auto') {\n      return apiRef.current.observeRowHeight(ref.current, rowId);\n    }\n    return undefined;\n  }, [apiRef, currentPage.range, rowHeight, rowId]);\n  const publish = React.useCallback((eventName, propHandler) => event => {\n    // Ignore portal\n    if (isEventTargetInPortal(event)) {\n      return;\n    }\n\n    // The row might have been deleted\n    if (!apiRef.current.getRow(rowId)) {\n      return;\n    }\n    apiRef.current.publishEvent(eventName, apiRef.current.getRowParams(rowId), event);\n    if (propHandler) {\n      propHandler(event);\n    }\n  }, [apiRef, rowId]);\n  const publishClick = React.useCallback(event => {\n    const cell = findParentElementFromClassName(event.target, gridClasses.cell);\n    const field = cell?.getAttribute('data-field');\n\n    // Check if the field is available because the cell that fills the empty\n    // space of the row has no field.\n    if (field) {\n      // User clicked in the checkbox added by checkboxSelection\n      if (field === GRID_CHECKBOX_SELECTION_COL_DEF.field) {\n        return;\n      }\n\n      // User opened a detail panel\n      if (field === GRID_DETAIL_PANEL_TOGGLE_FIELD) {\n        return;\n      }\n\n      // User reorders a row\n      if (field === '__reorder__') {\n        return;\n      }\n\n      // User is editing a cell\n      if (apiRef.current.getCellMode(rowId, field) === GridCellModes.Edit) {\n        return;\n      }\n\n      // User clicked a button from the \"actions\" column type\n      const column = apiRef.current.getColumn(field);\n      if (column?.type === GRID_ACTIONS_COLUMN_TYPE) {\n        return;\n      }\n    }\n    publish('rowClick', onClick)(event);\n  }, [apiRef, onClick, publish, rowId]);\n  const {\n    slots,\n    slotProps,\n    disableColumnReorder\n  } = rootProps;\n  const heightEntry = useGridSelector(apiRef, () => _extends({}, apiRef.current.getRowHeightEntry(rowId)), undefined, objectShallowCompare);\n  const style = React.useMemo(() => {\n    if (isNotVisible) {\n      return {\n        opacity: 0,\n        width: 0,\n        height: 0\n      };\n    }\n    const rowStyle = _extends({}, styleProp, {\n      maxHeight: rowHeight === 'auto' ? 'none' : rowHeight,\n      // max-height doesn't support \"auto\"\n      minHeight: rowHeight,\n      '--height': typeof rowHeight === 'number' ? `${rowHeight}px` : rowHeight\n    });\n    if (heightEntry.spacingTop) {\n      const property = rootProps.rowSpacingType === 'border' ? 'borderTopWidth' : 'marginTop';\n      rowStyle[property] = heightEntry.spacingTop;\n    }\n    if (heightEntry.spacingBottom) {\n      const property = rootProps.rowSpacingType === 'border' ? 'borderBottomWidth' : 'marginBottom';\n      let propertyValue = rowStyle[property];\n      // avoid overriding existing value\n      if (typeof propertyValue !== 'number') {\n        propertyValue = parseInt(propertyValue || '0', 10);\n      }\n      propertyValue += heightEntry.spacingBottom;\n      rowStyle[property] = propertyValue;\n    }\n    return rowStyle;\n  }, [isNotVisible, rowHeight, styleProp, heightEntry, rootProps.rowSpacingType]);\n  const rowClassNames = apiRef.current.unstable_applyPipeProcessors('rowClassName', [], rowId);\n  const ariaAttributes = getRowAriaAttributes(rowNode, index);\n  if (typeof rootProps.getRowClassName === 'function') {\n    const indexRelativeToCurrentPage = index - (currentPage.range?.firstRowIndex || 0);\n    const rowParams = _extends({}, apiRef.current.getRowParams(rowId), {\n      isFirstVisible: indexRelativeToCurrentPage === 0,\n      isLastVisible: indexRelativeToCurrentPage === currentPage.rows.length - 1,\n      indexRelativeToCurrentPage\n    });\n    rowClassNames.push(rootProps.getRowClassName(rowParams));\n  }\n  const getCell = (column, indexInSection, indexRelativeToAllColumns, sectionLength, pinnedPosition = PinnedColumnPosition.NONE) => {\n    const cellColSpanInfo = apiRef.current.unstable_getCellColSpanInfo(rowId, indexRelativeToAllColumns);\n    if (cellColSpanInfo?.spannedByColSpan) {\n      return null;\n    }\n    const width = cellColSpanInfo?.cellProps.width ?? column.computedWidth;\n    const colSpan = cellColSpanInfo?.cellProps.colSpan ?? 1;\n    const pinnedOffset = getPinnedCellOffset(pinnedPosition, column.computedWidth, indexRelativeToAllColumns, columnPositions, columnsTotalWidth, scrollbarWidth);\n    if (rowNode.type === 'skeletonRow') {\n      return /*#__PURE__*/_jsx(slots.skeletonCell, {\n        type: column.type,\n        width: width,\n        height: rowHeight,\n        field: column.field,\n        align: column.align\n      }, column.field);\n    }\n\n    // when the cell is a reorder cell we are not allowing to reorder the col\n    // fixes https://github.com/mui/mui-x/issues/11126\n    const isReorderCell = column.field === '__reorder__';\n    const canReorderColumn = !(disableColumnReorder || column.disableReorder);\n    const canReorderRow = isRowReorderingEnabled && !sortModel.length && treeDepth <= 1;\n    const disableDragEvents = !(canReorderColumn || isReorderCell && canReorderRow);\n    const cellIsNotVisible = pinnedPosition === PinnedColumnPosition.VIRTUAL;\n    const showLeftBorder = shouldCellShowLeftBorder(pinnedPosition, indexInSection);\n    const showRightBorder = shouldCellShowRightBorder(pinnedPosition, indexInSection, sectionLength, rootProps.showCellVerticalBorder, gridHasFiller);\n    return /*#__PURE__*/_jsx(slots.cell, _extends({\n      column: column,\n      width: width,\n      rowId: rowId,\n      align: column.align || 'left',\n      colIndex: indexRelativeToAllColumns,\n      colSpan: colSpan,\n      disableDragEvents: disableDragEvents,\n      isNotVisible: cellIsNotVisible,\n      pinnedOffset: pinnedOffset,\n      pinnedPosition: pinnedPosition,\n      showLeftBorder: showLeftBorder,\n      showRightBorder: showRightBorder,\n      row: row,\n      rowNode: rowNode\n    }, slotProps?.cell), column.field);\n  };\n  if (process.env.NODE_ENV !== \"production\") getCell.displayName = \"getCell\";\n  const leftCells = pinnedColumns.left.map((column, i) => {\n    const indexRelativeToAllColumns = i;\n    return getCell(column, i, indexRelativeToAllColumns, pinnedColumns.left.length, PinnedColumnPosition.LEFT);\n  });\n  const rightCells = pinnedColumns.right.map((column, i) => {\n    const indexRelativeToAllColumns = visibleColumns.length - pinnedColumns.right.length + i;\n    return getCell(column, i, indexRelativeToAllColumns, pinnedColumns.right.length, PinnedColumnPosition.RIGHT);\n  });\n  const middleColumnsLength = visibleColumns.length - pinnedColumns.left.length - pinnedColumns.right.length;\n  const cells = [];\n  if (hasVirtualFocusCellLeft) {\n    cells.push(getCell(visibleColumns[focusedColumnIndex], focusedColumnIndex - pinnedColumns.left.length, focusedColumnIndex, middleColumnsLength, PinnedColumnPosition.VIRTUAL));\n  }\n  for (let i = firstColumnIndex; i < lastColumnIndex; i += 1) {\n    const column = visibleColumns[i];\n    const indexInSection = i - pinnedColumns.left.length;\n    if (!column) {\n      continue;\n    }\n    cells.push(getCell(column, indexInSection, i, middleColumnsLength));\n  }\n  if (hasVirtualFocusCellRight) {\n    cells.push(getCell(visibleColumns[focusedColumnIndex], focusedColumnIndex - pinnedColumns.left.length, focusedColumnIndex, middleColumnsLength, PinnedColumnPosition.VIRTUAL));\n  }\n  const eventHandlers = row ? {\n    onClick: publishClick,\n    onDoubleClick: publish('rowDoubleClick', onDoubleClick),\n    onMouseEnter: publish('rowMouseEnter', onMouseEnter),\n    onMouseLeave: publish('rowMouseLeave', onMouseLeave),\n    onMouseOut: publish('rowMouseOut', onMouseOut),\n    onMouseOver: publish('rowMouseOver', onMouseOver)\n  } : null;\n  return /*#__PURE__*/_jsxs(\"div\", _extends({\n    \"data-id\": rowId,\n    \"data-rowindex\": index,\n    role: \"row\",\n    className: clsx(...rowClassNames, classes.root, className),\n    style: style\n  }, ariaAttributes, eventHandlers, other, {\n    ref: handleRef,\n    children: [leftCells, /*#__PURE__*/_jsx(\"div\", {\n      role: \"presentation\",\n      className: gridClasses.cellOffsetLeft,\n      style: {\n        width: offsetLeft\n      }\n    }), cells, /*#__PURE__*/_jsx(\"div\", {\n      role: \"presentation\",\n      className: clsx(gridClasses.cell, gridClasses.cellEmpty)\n    }), rightCells, scrollbarWidth !== 0 && /*#__PURE__*/_jsx(ScrollbarFiller, {\n      pinnedRight: pinnedColumns.right.length > 0,\n      borderTop: !isFirstVisible\n    })]\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridRow.displayName = \"GridRow\";\nprocess.env.NODE_ENV !== \"production\" ? GridRow.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  columnsTotalWidth: PropTypes.number.isRequired,\n  firstColumnIndex: PropTypes.number.isRequired,\n  /**\n   * Determines which cell has focus.\n   * If `null`, no cell in this row has focus.\n   */\n  focusedColumnIndex: PropTypes.number,\n  gridHasFiller: PropTypes.bool.isRequired,\n  /**\n   * Index of the row in the whole sorted and filtered dataset.\n   * If some rows above have expanded children, this index also take those children into account.\n   */\n  index: PropTypes.number.isRequired,\n  isFirstVisible: PropTypes.bool.isRequired,\n  isLastVisible: PropTypes.bool.isRequired,\n  isNotVisible: PropTypes.bool.isRequired,\n  lastColumnIndex: PropTypes.number.isRequired,\n  offsetLeft: PropTypes.number.isRequired,\n  onClick: PropTypes.func,\n  onDoubleClick: PropTypes.func,\n  onMouseEnter: PropTypes.func,\n  onMouseLeave: PropTypes.func,\n  pinnedColumns: PropTypes.object.isRequired,\n  row: PropTypes.object.isRequired,\n  rowHeight: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.number]).isRequired,\n  rowId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,\n  scrollbarWidth: PropTypes.number.isRequired,\n  selected: PropTypes.bool.isRequired,\n  showBottomBorder: PropTypes.bool.isRequired,\n  visibleColumns: PropTypes.arrayOf(PropTypes.object).isRequired\n} : void 0;\nconst MemoizedGridRow = fastMemo(GridRow);\nexport { MemoizedGridRow as GridRow };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}