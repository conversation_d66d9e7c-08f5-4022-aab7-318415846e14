{"ast": null, "code": "import { PinnedColumnPosition } from \"../constants.js\";\nexport const getPinnedCellOffset = (pinnedPosition, computedWidth, columnIndex, columnPositions, columnsTotalWidth, scrollbarWidth) => {\n  let pinnedOffset;\n  switch (pinnedPosition) {\n    case PinnedColumnPosition.LEFT:\n      pinnedOffset = columnPositions[columnIndex];\n      break;\n    case PinnedColumnPosition.RIGHT:\n      pinnedOffset = columnsTotalWidth - columnPositions[columnIndex] - computedWidth + scrollbarWidth;\n      break;\n    default:\n      pinnedOffset = undefined;\n      break;\n  }\n  return pinnedOffset;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}