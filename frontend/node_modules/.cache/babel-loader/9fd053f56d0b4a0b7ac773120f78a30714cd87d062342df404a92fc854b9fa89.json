{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport useId from '@mui/utils/useId';\nimport useForkRef from '@mui/utils/useForkRef';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { GridMenu } from \"../menu/GridMenu.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { gridClasses } from \"../../constants/gridClasses.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst GridToolbarExportContainer = forwardRef(function GridToolbarExportContainer(props, ref) {\n  const {\n    children,\n    slotProps = {}\n  } = props;\n  const buttonProps = slotProps.button || {};\n  const tooltipProps = slotProps.tooltip || {};\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const exportButtonId = useId();\n  const exportMenuId = useId();\n  const [open, setOpen] = React.useState(false);\n  const buttonRef = React.useRef(null);\n  const handleRef = useForkRef(ref, buttonRef);\n  const handleMenuOpen = event => {\n    setOpen(prevOpen => !prevOpen);\n    buttonProps.onClick?.(event);\n  };\n  const handleMenuClose = () => setOpen(false);\n  if (children == null) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(rootProps.slots.baseTooltip, _extends({\n      title: apiRef.current.getLocaleText('toolbarExportLabel'),\n      enterDelay: 1000\n    }, rootProps.slotProps?.baseTooltip, tooltipProps, {\n      children: /*#__PURE__*/_jsx(rootProps.slots.baseButton, _extends({\n        size: \"small\",\n        startIcon: /*#__PURE__*/_jsx(rootProps.slots.exportIcon, {}),\n        \"aria-expanded\": open,\n        \"aria-label\": apiRef.current.getLocaleText('toolbarExportLabel'),\n        \"aria-haspopup\": \"menu\",\n        \"aria-controls\": open ? exportMenuId : undefined,\n        id: exportButtonId\n      }, rootProps.slotProps?.baseButton, buttonProps, {\n        onClick: handleMenuOpen,\n        ref: handleRef,\n        children: apiRef.current.getLocaleText('toolbarExport')\n      }))\n    })), /*#__PURE__*/_jsx(GridMenu, {\n      open: open,\n      target: buttonRef.current,\n      onClose: handleMenuClose,\n      position: \"bottom-end\",\n      children: /*#__PURE__*/_jsx(rootProps.slots.baseMenuList, {\n        id: exportMenuId,\n        className: gridClasses.menuList,\n        \"aria-labelledby\": exportButtonId,\n        autoFocusItem: open,\n        children: React.Children.map(children, child => {\n          if (! /*#__PURE__*/React.isValidElement(child)) {\n            return child;\n          }\n          return /*#__PURE__*/React.cloneElement(child, {\n            hideMenu: handleMenuClose\n          });\n        })\n      })\n    })]\n  });\n});\nif (process.env.NODE_ENV !== \"production\") GridToolbarExportContainer.displayName = \"GridToolbarExportContainer\";\nprocess.env.NODE_ENV !== \"production\" ? GridToolbarExportContainer.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.object\n} : void 0;\nexport { GridToolbarExportContainer };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}