{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridRootProps } from \"../hooks/utils/useGridRootProps.js\";\nimport { GridPreferencesPanel } from \"./panel/GridPreferencesPanel.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport function GridHeader() {\n  const rootProps = useGridRootProps();\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(GridPreferencesPanel, {}), rootProps.showToolbar && /*#__PURE__*/_jsx(rootProps.slots.toolbar, _extends({}, rootProps.slotProps?.toolbar))]\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}