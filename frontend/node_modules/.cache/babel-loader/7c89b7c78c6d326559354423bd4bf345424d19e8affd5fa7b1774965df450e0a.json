{"ast": null, "code": "import * as React from 'react';\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridEvent } from \"../../utils/useGridEvent.js\";\n/**\n * @requires useGridColumns (method, event)\n * @requires useGridParamsApi (method)\n */\nexport const useGridColumnSpanning = apiRef => {\n  const lookup = React.useRef({});\n  const getCellColSpanInfo = (rowId, columnIndex) => {\n    return lookup.current[rowId]?.[columnIndex];\n  };\n  const resetColSpan = () => {\n    lookup.current = {};\n  };\n\n  // Calculate `colSpan` for each cell in the row\n  const calculateColSpan = React.useCallback(({\n    rowId,\n    minFirstColumn,\n    maxLastColumn,\n    columns\n  }) => {\n    for (let i = minFirstColumn; i < maxLastColumn; i += 1) {\n      const cellProps = calculateCellColSpan({\n        apiRef,\n        lookup: lookup.current,\n        columnIndex: i,\n        rowId,\n        minFirstColumnIndex: minFirstColumn,\n        maxLastColumnIndex: maxLastColumn,\n        columns\n      });\n      if (cellProps.colSpan > 1) {\n        i += cellProps.colSpan - 1;\n      }\n    }\n  }, [apiRef]);\n  const columnSpanningPublicApi = {\n    unstable_getCellColSpanInfo: getCellColSpanInfo\n  };\n  const columnSpanningPrivateApi = {\n    resetColSpan,\n    calculateColSpan\n  };\n  useGridApiMethod(apiRef, columnSpanningPublicApi, 'public');\n  useGridApiMethod(apiRef, columnSpanningPrivateApi, 'private');\n  useGridEvent(apiRef, 'columnOrderChange', resetColSpan);\n};\nfunction calculateCellColSpan(params) {\n  const {\n    apiRef,\n    lookup,\n    columnIndex,\n    rowId,\n    minFirstColumnIndex,\n    maxLastColumnIndex,\n    columns\n  } = params;\n  const columnsLength = columns.length;\n  const column = columns[columnIndex];\n  const row = apiRef.current.getRow(rowId);\n  const value = apiRef.current.getRowValue(row, column);\n  const colSpan = typeof column.colSpan === 'function' ? column.colSpan(value, row, column, apiRef) : column.colSpan;\n  if (!colSpan || colSpan === 1) {\n    setCellColSpanInfo(lookup, rowId, columnIndex, {\n      spannedByColSpan: false,\n      cellProps: {\n        colSpan: 1,\n        width: column.computedWidth\n      }\n    });\n    return {\n      colSpan: 1\n    };\n  }\n  let width = column.computedWidth;\n  for (let j = 1; j < colSpan; j += 1) {\n    const nextColumnIndex = columnIndex + j;\n    // Cells should be spanned only within their column section (left-pinned, right-pinned and unpinned).\n    if (nextColumnIndex >= minFirstColumnIndex && nextColumnIndex < maxLastColumnIndex) {\n      const nextColumn = columns[nextColumnIndex];\n      width += nextColumn.computedWidth;\n      setCellColSpanInfo(lookup, rowId, columnIndex + j, {\n        spannedByColSpan: true,\n        rightVisibleCellIndex: Math.min(columnIndex + colSpan, columnsLength - 1),\n        leftVisibleCellIndex: columnIndex\n      });\n    }\n    setCellColSpanInfo(lookup, rowId, columnIndex, {\n      spannedByColSpan: false,\n      cellProps: {\n        colSpan,\n        width\n      }\n    });\n  }\n  return {\n    colSpan\n  };\n}\nfunction setCellColSpanInfo(lookup, rowId, columnIndex, cellColSpanInfo) {\n  if (!lookup[rowId]) {\n    lookup[rowId] = {};\n  }\n  lookup[rowId][columnIndex] = cellColSpanInfo;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}