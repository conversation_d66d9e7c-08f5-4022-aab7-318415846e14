{"ast": null, "code": "import { GridFilterInputBoolean, sanitizeFilterItemValue } from \"../components/panel/filterPanel/GridFilterInputBoolean.js\";\nexport const getGridBooleanOperators = () => [{\n  value: 'is',\n  getApplyFilterFn: filterItem => {\n    const sanitizedValue = sanitizeFilterItemValue(filterItem.value);\n    if (sanitizedValue === undefined) {\n      return null;\n    }\n    return value => Boolean(value) === sanitizedValue;\n  },\n  InputComponent: GridFilterInputBoolean\n}];", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}