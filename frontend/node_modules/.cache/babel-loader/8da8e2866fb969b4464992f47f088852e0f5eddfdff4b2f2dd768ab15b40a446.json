{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useId from '@mui/utils/useId';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { gridColumnGroupsLookupSelector } from \"../../hooks/features/columnGrouping/gridColumnGroupsSelector.js\";\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { useGridSelector } from \"../../hooks/utils/useGridSelector.js\";\nimport { GridGenericColumnHeaderItem } from \"./GridGenericColumnHeaderItem.js\";\nimport { isEventTargetInPortal } from \"../../utils/domUtils.js\";\nimport { PinnedColumnPosition } from \"../../internals/constants.js\";\nimport { attachPinnedStyle } from \"../../internals/utils/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    headerAlign,\n    isDragging,\n    isLastColumn,\n    showLeftBorder,\n    showRightBorder,\n    groupId,\n    pinnedPosition\n  } = ownerState;\n  const slots = {\n    root: ['columnHeader', headerAlign === 'left' && 'columnHeader--alignLeft', headerAlign === 'center' && 'columnHeader--alignCenter', headerAlign === 'right' && 'columnHeader--alignRight', isDragging && 'columnHeader--moving', showRightBorder && 'columnHeader--withRightBorder', showLeftBorder && 'columnHeader--withLeftBorder', 'withBorderColor', groupId === null ? 'columnHeader--emptyGroup' : 'columnHeader--filledGroup', pinnedPosition === PinnedColumnPosition.LEFT && 'columnHeader--pinnedLeft', pinnedPosition === PinnedColumnPosition.RIGHT && 'columnHeader--pinnedRight', isLastColumn && 'columnHeader--last'],\n    draggableContainer: ['columnHeaderDraggableContainer'],\n    titleContainer: ['columnHeaderTitleContainer', 'withBorderColor'],\n    titleContainerContent: ['columnHeaderTitleContainerContent']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nfunction GridColumnGroupHeader(props) {\n  const {\n    groupId,\n    width,\n    depth,\n    maxDepth,\n    fields,\n    height,\n    colIndex,\n    hasFocus,\n    tabIndex,\n    isLastColumn,\n    pinnedPosition,\n    pinnedOffset\n  } = props;\n  const rootProps = useGridRootProps();\n  const isRtl = useRtl();\n  const headerCellRef = React.useRef(null);\n  const apiRef = useGridApiContext();\n  const columnGroupsLookup = useGridSelector(apiRef, gridColumnGroupsLookupSelector);\n  const group = groupId ? columnGroupsLookup[groupId] : {};\n  const {\n    headerName = groupId ?? '',\n    description = '',\n    headerAlign = undefined\n  } = group;\n  let headerComponent;\n  const render = groupId && columnGroupsLookup[groupId]?.renderHeaderGroup;\n  const renderParams = React.useMemo(() => ({\n    groupId,\n    headerName,\n    description,\n    depth,\n    maxDepth,\n    fields,\n    colIndex,\n    isLastColumn\n  }), [groupId, headerName, description, depth, maxDepth, fields, colIndex, isLastColumn]);\n  if (groupId && render) {\n    headerComponent = render(renderParams);\n  }\n  const ownerState = _extends({}, props, {\n    classes: rootProps.classes,\n    headerAlign,\n    depth,\n    isDragging: false\n  });\n  const label = headerName ?? groupId;\n  const id = useId();\n  const elementId = groupId === null ? `empty-group-cell-${id}` : groupId;\n  const classes = useUtilityClasses(ownerState);\n  React.useLayoutEffect(() => {\n    if (hasFocus) {\n      const focusableElement = headerCellRef.current.querySelector('[tabindex=\"0\"]');\n      const elementToFocus = focusableElement || headerCellRef.current;\n      elementToFocus?.focus();\n    }\n  }, [apiRef, hasFocus]);\n  const publish = React.useCallback(eventName => event => {\n    // Ignore portal\n    // See https://github.com/mui/mui-x/issues/1721\n    if (isEventTargetInPortal(event)) {\n      return;\n    }\n    apiRef.current.publishEvent(eventName, renderParams, event);\n  },\n  // For now this is stupid, because renderParams change all the time.\n  // Need to move it's computation in the api, such that for a given depth+columnField, I can get the group parameters\n  [apiRef, renderParams]);\n  const mouseEventsHandlers = React.useMemo(() => ({\n    onKeyDown: publish('columnGroupHeaderKeyDown'),\n    onFocus: publish('columnGroupHeaderFocus'),\n    onBlur: publish('columnGroupHeaderBlur')\n  }), [publish]);\n  const headerClassName = typeof group.headerClassName === 'function' ? group.headerClassName(renderParams) : group.headerClassName;\n  const style = React.useMemo(() => attachPinnedStyle(_extends({}, props.style), isRtl, pinnedPosition, pinnedOffset), [pinnedPosition, pinnedOffset, props.style, isRtl]);\n  return /*#__PURE__*/_jsx(GridGenericColumnHeaderItem, _extends({\n    ref: headerCellRef,\n    classes: classes,\n    columnMenuOpen: false,\n    colIndex: colIndex,\n    height: height,\n    isResizing: false,\n    sortDirection: null,\n    hasFocus: false,\n    tabIndex: tabIndex,\n    isDraggable: false,\n    headerComponent: headerComponent,\n    headerClassName: headerClassName,\n    description: description,\n    elementId: elementId,\n    width: width,\n    columnMenuIconButton: null,\n    columnTitleIconButtons: null,\n    resizable: false,\n    label: label,\n    \"aria-colspan\": fields.length\n    // The fields are wrapped between |-...-| to avoid confusion between fields \"id\" and \"id2\" when using selector data-fields~=\n    ,\n\n    \"data-fields\": `|-${fields.join('-|-')}-|`,\n    style: style\n  }, mouseEventsHandlers));\n}\nexport { GridColumnGroupHeader };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}