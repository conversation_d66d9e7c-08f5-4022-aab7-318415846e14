{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"logicOperators\", \"columnsSort\", \"filterFormProps\", \"getColumnForNewFilter\", \"children\", \"disableAddFilterButton\", \"disableRemoveAllButton\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { GridLogicOperator } from \"../../../models/gridFilterItem.js\";\nimport { useGridApiContext } from \"../../../hooks/utils/useGridApiContext.js\";\nimport { GridPanelContent } from \"../GridPanelContent.js\";\nimport { GridPanelFooter } from \"../GridPanelFooter.js\";\nimport { GridPanelWrapper } from \"../GridPanelWrapper.js\";\nimport { GridFilterForm } from \"./GridFilterForm.js\";\nimport { useGridRootProps } from \"../../../hooks/utils/useGridRootProps.js\";\nimport { useGridSelector } from \"../../../hooks/utils/useGridSelector.js\";\nimport { gridFilterModelSelector } from \"../../../hooks/features/filter/gridFilterSelector.js\";\nimport { gridFilterableColumnDefinitionsSelector, gridFilterableColumnLookupSelector } from \"../../../hooks/features/columns/gridColumnsSelector.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst getGridFilter = col => ({\n  field: col.field,\n  operator: col.filterOperators[0].value,\n  id: Math.round(Math.random() * 1e5)\n});\nconst GridFilterPanel = forwardRef(function GridFilterPanel(props, ref) {\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const filterModel = useGridSelector(apiRef, gridFilterModelSelector);\n  const filterableColumns = useGridSelector(apiRef, gridFilterableColumnDefinitionsSelector);\n  const filterableColumnsLookup = useGridSelector(apiRef, gridFilterableColumnLookupSelector);\n  const lastFilterRef = React.useRef(null);\n  const placeholderFilter = React.useRef(null);\n  const {\n      logicOperators = [GridLogicOperator.And, GridLogicOperator.Or],\n      columnsSort,\n      filterFormProps,\n      getColumnForNewFilter,\n      disableAddFilterButton = false,\n      disableRemoveAllButton = false\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const applyFilter = apiRef.current.upsertFilterItem;\n  const applyFilterLogicOperator = React.useCallback(operator => {\n    apiRef.current.setFilterLogicOperator(operator);\n  }, [apiRef]);\n  const getDefaultFilter = React.useCallback(() => {\n    let nextColumnWithOperator;\n    if (getColumnForNewFilter && typeof getColumnForNewFilter === 'function') {\n      // To allow override the column for default (first) filter\n      const nextFieldName = getColumnForNewFilter({\n        currentFilters: filterModel?.items || [],\n        columns: filterableColumns\n      });\n      if (nextFieldName === null) {\n        return null;\n      }\n      nextColumnWithOperator = filterableColumns.find(({\n        field\n      }) => field === nextFieldName);\n    } else {\n      nextColumnWithOperator = filterableColumns.find(colDef => colDef.filterOperators?.length);\n    }\n    if (!nextColumnWithOperator) {\n      return null;\n    }\n    return getGridFilter(nextColumnWithOperator);\n  }, [filterModel?.items, filterableColumns, getColumnForNewFilter]);\n  const getNewFilter = React.useCallback(() => {\n    if (getColumnForNewFilter === undefined || typeof getColumnForNewFilter !== 'function') {\n      return getDefaultFilter();\n    }\n    const currentFilters = filterModel.items.length ? filterModel.items : [getDefaultFilter()].filter(Boolean);\n\n    // If no items are there in filterModel, we have to pass defaultFilter\n    const nextColumnFieldName = getColumnForNewFilter({\n      currentFilters: currentFilters,\n      columns: filterableColumns\n    });\n    if (nextColumnFieldName === null) {\n      return null;\n    }\n    const nextColumnWithOperator = filterableColumns.find(({\n      field\n    }) => field === nextColumnFieldName);\n    if (!nextColumnWithOperator) {\n      return null;\n    }\n    return getGridFilter(nextColumnWithOperator);\n  }, [filterModel.items, filterableColumns, getColumnForNewFilter, getDefaultFilter]);\n  const items = React.useMemo(() => {\n    if (filterModel.items.length) {\n      return filterModel.items;\n    }\n    if (!placeholderFilter.current) {\n      placeholderFilter.current = getDefaultFilter();\n    }\n    return placeholderFilter.current ? [placeholderFilter.current] : [];\n  }, [filterModel.items, getDefaultFilter]);\n  const hasMultipleFilters = items.length > 1;\n  const {\n    readOnlyFilters,\n    validFilters\n  } = React.useMemo(() => items.reduce((acc, item) => {\n    if (filterableColumnsLookup[item.field]) {\n      acc.validFilters.push(item);\n    } else {\n      acc.readOnlyFilters.push(item);\n    }\n    return acc;\n  }, {\n    readOnlyFilters: [],\n    validFilters: []\n  }), [items, filterableColumnsLookup]);\n  const addNewFilter = React.useCallback(() => {\n    const newFilter = getNewFilter();\n    if (!newFilter) {\n      return;\n    }\n    apiRef.current.upsertFilterItems([...items, newFilter]);\n  }, [apiRef, getNewFilter, items]);\n  const deleteFilter = React.useCallback(item => {\n    const shouldCloseFilterPanel = validFilters.length === 1;\n    apiRef.current.deleteFilterItem(item);\n    if (shouldCloseFilterPanel) {\n      apiRef.current.hideFilterPanel();\n    }\n  }, [apiRef, validFilters.length]);\n  const handleRemoveAll = React.useCallback(() => {\n    if (validFilters.length === 1 && validFilters[0].value === undefined) {\n      apiRef.current.deleteFilterItem(validFilters[0]);\n      return apiRef.current.hideFilterPanel();\n    }\n    return apiRef.current.setFilterModel(_extends({}, filterModel, {\n      items: readOnlyFilters\n    }), 'removeAllFilterItems');\n  }, [apiRef, readOnlyFilters, filterModel, validFilters]);\n  React.useEffect(() => {\n    if (logicOperators.length > 0 && filterModel.logicOperator && !logicOperators.includes(filterModel.logicOperator)) {\n      applyFilterLogicOperator(logicOperators[0]);\n    }\n  }, [logicOperators, applyFilterLogicOperator, filterModel.logicOperator]);\n  React.useEffect(() => {\n    if (validFilters.length > 0) {\n      lastFilterRef.current.focus();\n    }\n  }, [validFilters.length]);\n  return /*#__PURE__*/_jsxs(GridPanelWrapper, _extends({}, other, {\n    ref: ref,\n    children: [/*#__PURE__*/_jsxs(GridPanelContent, {\n      children: [readOnlyFilters.map((item, index) => /*#__PURE__*/_jsx(GridFilterForm, _extends({\n        item: item,\n        applyFilterChanges: applyFilter,\n        deleteFilter: deleteFilter,\n        hasMultipleFilters: hasMultipleFilters,\n        showMultiFilterOperators: index > 0,\n        disableMultiFilterOperator: index !== 1,\n        applyMultiFilterOperatorChanges: applyFilterLogicOperator,\n        focusElementRef: null,\n        readOnly: true,\n        logicOperators: logicOperators,\n        columnsSort: columnsSort\n      }, filterFormProps), item.id == null ? index : item.id)), validFilters.map((item, index) => /*#__PURE__*/_jsx(GridFilterForm, _extends({\n        item: item,\n        applyFilterChanges: applyFilter,\n        deleteFilter: deleteFilter,\n        hasMultipleFilters: hasMultipleFilters,\n        showMultiFilterOperators: readOnlyFilters.length + index > 0,\n        disableMultiFilterOperator: readOnlyFilters.length + index !== 1,\n        applyMultiFilterOperatorChanges: applyFilterLogicOperator,\n        focusElementRef: index === validFilters.length - 1 ? lastFilterRef : null,\n        logicOperators: logicOperators,\n        columnsSort: columnsSort\n      }, filterFormProps), item.id == null ? index + readOnlyFilters.length : item.id))]\n    }), !rootProps.disableMultipleColumnsFiltering && !(disableAddFilterButton && disableRemoveAllButton) ? /*#__PURE__*/_jsxs(GridPanelFooter, {\n      children: [!disableAddFilterButton ? /*#__PURE__*/_jsx(rootProps.slots.baseButton, _extends({\n        onClick: addNewFilter,\n        startIcon: /*#__PURE__*/_jsx(rootProps.slots.filterPanelAddIcon, {})\n      }, rootProps.slotProps?.baseButton, {\n        children: apiRef.current.getLocaleText('filterPanelAddFilter')\n      })) : /*#__PURE__*/_jsx(\"span\", {}), !disableRemoveAllButton && validFilters.length > 0 ? /*#__PURE__*/_jsx(rootProps.slots.baseButton, _extends({\n        onClick: handleRemoveAll,\n        startIcon: /*#__PURE__*/_jsx(rootProps.slots.filterPanelRemoveAllIcon, {})\n      }, rootProps.slotProps?.baseButton, {\n        children: apiRef.current.getLocaleText('filterPanelRemoveAll')\n      })) : null]\n    }) : null]\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridFilterPanel.displayName = \"GridFilterPanel\";\nprocess.env.NODE_ENV !== \"production\" ? GridFilterPanel.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * @ignore - do not document.\n   */\n  children: PropTypes.node,\n  /**\n   * Changes how the options in the columns selector should be ordered.\n   * If not specified, the order is derived from the `columns` prop.\n   */\n  columnsSort: PropTypes.oneOf(['asc', 'desc']),\n  /**\n   * If `true`, the `Add filter` button will not be displayed.\n   * @default false\n   */\n  disableAddFilterButton: PropTypes.bool,\n  /**\n   * If `true`, the `Remove all` button will be disabled\n   * @default false\n   */\n  disableRemoveAllButton: PropTypes.bool,\n  /**\n   * Props passed to each filter form.\n   */\n  filterFormProps: PropTypes.shape({\n    columnInputProps: PropTypes.any,\n    columnsSort: PropTypes.oneOf(['asc', 'desc']),\n    deleteIconProps: PropTypes.any,\n    filterColumns: PropTypes.func,\n    logicOperatorInputProps: PropTypes.any,\n    operatorInputProps: PropTypes.any,\n    valueInputProps: PropTypes.any\n  }),\n  /**\n   * Function that returns the next filter item to be picked as default filter.\n   * @param {GetColumnForNewFilterArgs} args Currently configured filters and columns.\n   * @returns {GridColDef['field']} The field to be used for the next filter or `null` to prevent adding a filter.\n   */\n  getColumnForNewFilter: PropTypes.func,\n  /**\n   * Sets the available logic operators.\n   * @default [GridLogicOperator.And, GridLogicOperator.Or]\n   */\n  logicOperators: PropTypes.arrayOf(PropTypes.oneOf(['and', 'or']).isRequired),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\n\n/**\n * Demos:\n * - [Filtering - overview](https://mui.com/x/react-data-grid/filtering/)\n *\n * API:\n * - [GridFilterPanel API](https://mui.com/x/api/data-grid/grid-filter-panel/)\n */\nexport { GridFilterPanel, getGridFilter };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}