{"ast": null, "code": "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _toPropertyKey from \"@babel/runtime/helpers/esm/toPropertyKey\";\nimport * as React from 'react';\nimport { GridStrategyGroup } from \"./gridStrategyProcessingApi.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nexport const GRID_DEFAULT_STRATEGY = 'none';\nexport const GRID_STRATEGIES_PROCESSORS = {\n  dataSourceRowsUpdate: GridStrategyGroup.DataSource,\n  rowTreeCreation: GridStrategyGroup.RowTree,\n  filtering: GridStrategyGroup.RowTree,\n  sorting: GridStrategyGroup.RowTree,\n  visibleRowsLookupCreation: GridStrategyGroup.RowTree\n};\n/**\n * Implements a variant of the Strategy Pattern (see https://en.wikipedia.org/wiki/Strategy_pattern)\n *\n * More information and detailed example in (TODO add link to technical doc when ready)\n *\n * Some plugins contains custom logic that must only be applied if the right strategy is active.\n * For instance, the row grouping plugin has a custom filtering algorithm.\n * This algorithm must be applied by the filtering plugin if the row grouping is the current way of grouping rows,\n * but not if the tree data is the current way of grouping rows.\n *\n * =====================================================================================================================\n *\n * The plugin containing the custom logic must use:\n *\n * - `useGridRegisterStrategyProcessor` to register their processor.\n *   When the processor of the active strategy changes, it will fire `\"activeStrategyProcessorChange\"` to re-apply the processor.\n *\n * - `apiRef.current.setStrategyAvailability` to tell if their strategy can be used.\n *\n * =====================================================================================================================\n *\n * The plugin or component that needs to apply the custom logic of the current strategy must use:\n *\n * - `apiRef.current.applyStrategyProcessor` to run the processor of the active strategy for a given processor name.\n *\n * - the \"strategyAvailabilityChange\" event to update something when the active strategy changes.\n *    Warning: Be careful not to apply the processor several times.\n *    For instance \"rowsSet\" is fired by `useGridRows` whenever the active strategy changes.\n *    So listening to both would most likely run your logic twice.\n *\n * - The \"activeStrategyProcessorChange\" event to update something when the processor of the active strategy changes.\n *\n * =====================================================================================================================\n *\n * Each processor name is part of a strategy group which can only have one active strategy at the time.\n * There are two active groups named `rowTree` and `dataSource`.\n */\nexport const useGridStrategyProcessing = apiRef => {\n  const availableStrategies = React.useRef(new Map());\n  const strategiesCache = React.useRef({});\n  const registerStrategyProcessor = React.useCallback((strategyName, processorName, processor) => {\n    const cleanup = () => {\n      const _ref = strategiesCache.current[processorName],\n        otherProcessors = _objectWithoutPropertiesLoose(_ref, [strategyName].map(_toPropertyKey));\n      strategiesCache.current[processorName] = otherProcessors;\n    };\n    if (!strategiesCache.current[processorName]) {\n      strategiesCache.current[processorName] = {};\n    }\n    const groupPreProcessors = strategiesCache.current[processorName];\n    const previousProcessor = groupPreProcessors[strategyName];\n    groupPreProcessors[strategyName] = processor;\n    if (!previousProcessor || previousProcessor === processor) {\n      return cleanup;\n    }\n    if (strategyName === apiRef.current.getActiveStrategy(GRID_STRATEGIES_PROCESSORS[processorName])) {\n      apiRef.current.publishEvent('activeStrategyProcessorChange', processorName);\n    }\n    return cleanup;\n  }, [apiRef]);\n  const applyStrategyProcessor = React.useCallback((processorName, params) => {\n    const activeStrategy = apiRef.current.getActiveStrategy(GRID_STRATEGIES_PROCESSORS[processorName]);\n    if (activeStrategy == null) {\n      throw new Error(\"Can't apply a strategy processor before defining an active strategy\");\n    }\n    const groupCache = strategiesCache.current[processorName];\n    if (!groupCache || !groupCache[activeStrategy]) {\n      throw new Error(`No processor found for processor \"${processorName}\" on strategy \"${activeStrategy}\"`);\n    }\n    const processor = groupCache[activeStrategy];\n    return processor(params);\n  }, [apiRef]);\n  const getActiveStrategy = React.useCallback(strategyGroup => {\n    const strategyEntries = Array.from(availableStrategies.current.entries());\n    const availableStrategyEntry = strategyEntries.find(([, strategy]) => {\n      if (strategy.group !== strategyGroup) {\n        return false;\n      }\n      return strategy.isAvailable();\n    });\n    return availableStrategyEntry?.[0] ?? GRID_DEFAULT_STRATEGY;\n  }, []);\n  const setStrategyAvailability = React.useCallback((strategyGroup, strategyName, isAvailable) => {\n    availableStrategies.current.set(strategyName, {\n      group: strategyGroup,\n      isAvailable\n    });\n    apiRef.current.publishEvent('strategyAvailabilityChange');\n  }, [apiRef]);\n  const strategyProcessingApi = {\n    registerStrategyProcessor,\n    applyStrategyProcessor,\n    getActiveStrategy,\n    setStrategyAvailability\n  };\n  useGridApiMethod(apiRef, strategyProcessingApi, 'private');\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}