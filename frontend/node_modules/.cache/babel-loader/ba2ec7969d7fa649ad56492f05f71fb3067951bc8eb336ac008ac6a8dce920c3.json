{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/TailwindDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport apiService from '../services/api';\nimport { toast } from 'react-toastify';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TailwindDashboard = () => {\n  _s();\n  var _user$full_name;\n  const {\n    user\n  } = useAuth();\n  const [stats, setStats] = useState({\n    totalUsers: 0,\n    totalRoles: 0,\n    totalPermissions: 0,\n    activeUsers: 0\n  });\n  const [recentUsers, setRecentUsers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n  const fetchDashboardData = async () => {\n    try {\n      const [usersData, rolesData, permissionsData] = await Promise.all([apiService.getUsers(1, 5), apiService.getAllRoles(), apiService.getAllPermissions()]);\n      setStats({\n        totalUsers: usersData.total,\n        totalRoles: rolesData.length,\n        totalPermissions: permissionsData.length,\n        activeUsers: usersData.items.filter(u => u.is_active).length\n      });\n      setRecentUsers(usersData.items.slice(0, 5));\n    } catch (error) {\n      toast.error('Erreur lors du chargement des données');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const statCards = [{\n    title: 'Utilisateurs',\n    value: stats.totalUsers,\n    icon: '👥',\n    color: 'blue',\n    bgColor: 'bg-blue-100',\n    iconColor: 'text-blue-600'\n  }, {\n    title: 'Rôles',\n    value: stats.totalRoles,\n    icon: '🛡️',\n    color: 'green',\n    bgColor: 'bg-green-100',\n    iconColor: 'text-green-600'\n  }, {\n    title: 'Permissions',\n    value: stats.totalPermissions,\n    icon: '🔑',\n    color: 'yellow',\n    bgColor: 'bg-yellow-100',\n    iconColor: 'text-yellow-600'\n  }, {\n    title: 'Utilisateurs Actifs',\n    value: stats.activeUsers,\n    icon: '📈',\n    color: 'purple',\n    bgColor: 'bg-purple-100',\n    iconColor: 'text-purple-600'\n  }];\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-primary-600 to-primary-700 rounded-lg p-6 text-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold mb-2\",\n            children: [\"Bienvenue, \", user === null || user === void 0 ? void 0 : user.full_name, \" !\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-primary-100\",\n            children: \"Voici un aper\\xE7u de votre syst\\xE8me de gestion des utilisateurs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden md:block\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl font-bold\",\n              children: (user === null || user === void 0 ? void 0 : (_user$full_name = user.full_name) === null || _user$full_name === void 0 ? void 0 : _user$full_name.charAt(0)) || 'U'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n      children: statCards.map((stat, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card hover:shadow-lg transition-shadow duration-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-500 mb-1\",\n              children: stat.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-3xl font-bold text-gray-900\",\n              children: stat.value\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `p-3 rounded-full ${stat.bgColor}`,\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl\",\n              children: stat.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 13\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: \"Utilisateurs R\\xE9cents\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-secondary text-xs\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-4 h-4 mr-1\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this), \"Voir tout\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: recentUsers.length > 0 ? recentUsers.map(recentUser => {\n            var _recentUser$role;\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white text-sm font-medium\",\n                    children: recentUser.full_name.charAt(0)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 161,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-medium text-gray-900\",\n                    children: recentUser.full_name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 166,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500\",\n                    children: recentUser.email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 169,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-right\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${recentUser.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                  children: recentUser.is_active ? 'Actif' : 'Inactif'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500 mt-1\",\n                  children: ((_recentUser$role = recentUser.role) === null || _recentUser$role === void 0 ? void 0 : _recentUser$role.name) || 'Aucun rôle'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 19\n              }, this)]\n            }, recentUser.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 17\n            }, this);\n          }) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-8 text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-12 h-12 mx-auto mb-2 text-gray-300\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Aucun utilisateur trouv\\xE9\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-4\",\n          children: \"Actions Rapides\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"w-full btn-primary justify-start\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-5 h-5 mr-2\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this), \"Cr\\xE9er un nouvel utilisateur\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"w-full btn-secondary justify-start\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mr-2\",\n              children: \"\\uD83D\\uDEE1\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this), \"G\\xE9rer les r\\xF4les\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"w-full btn-secondary justify-start\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mr-2\",\n              children: \"\\uD83D\\uDD11\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this), \"Configurer les permissions\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"w-full btn-secondary justify-start\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mr-2\",\n              children: \"\\uD83D\\uDCCA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this), \"Voir l'activit\\xE9 r\\xE9cente\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-gray-900 mb-4\",\n        children: \"Informations Syst\\xE8me\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center p-4 bg-gray-50 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500\",\n            children: \"Version\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: \"v1.0.0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center p-4 bg-gray-50 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500\",\n            children: \"Base de donn\\xE9es\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: \"MySQL\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center p-4 bg-gray-50 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500\",\n            children: \"Statut\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n            children: \"Op\\xE9rationnel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 96,\n    columnNumber: 5\n  }, this);\n};\n_s(TailwindDashboard, \"UtJVTDZVJvF5jm4uMgtPRi1awNw=\", false, function () {\n  return [useAuth];\n});\n_c = TailwindDashboard;\nexport default TailwindDashboard;\nvar _c;\n$RefreshReg$(_c, \"TailwindDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "apiService", "toast", "jsxDEV", "_jsxDEV", "TailwindDashboard", "_s", "_user$full_name", "user", "stats", "setStats", "totalUsers", "totalRoles", "totalPermissions", "activeUsers", "recentUsers", "setRecentUsers", "loading", "setLoading", "fetchDashboardData", "usersData", "rolesData", "permissionsData", "Promise", "all", "getUsers", "getAllRoles", "getAllPermissions", "total", "length", "items", "filter", "u", "is_active", "slice", "error", "statCards", "title", "value", "icon", "color", "bgColor", "iconColor", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "full_name", "char<PERSON>t", "map", "stat", "index", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "recentUser", "_recentUser$role", "email", "role", "name", "id", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/TailwindDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport apiService from '../services/api';\nimport { toast } from 'react-toastify';\nimport { User } from '../types';\n\ninterface Stats {\n  totalUsers: number;\n  totalRoles: number;\n  totalPermissions: number;\n  activeUsers: number;\n}\n\nconst TailwindDashboard: React.FC = () => {\n  const { user } = useAuth();\n  const [stats, setStats] = useState<Stats>({\n    totalUsers: 0,\n    totalRoles: 0,\n    totalPermissions: 0,\n    activeUsers: 0,\n  });\n  const [recentUsers, setRecentUsers] = useState<User[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n\n  const fetchDashboardData = async () => {\n    try {\n      const [usersData, rolesData, permissionsData] = await Promise.all([\n        apiService.getUsers(1, 5),\n        apiService.getAllRoles(),\n        apiService.getAllPermissions(),\n      ]);\n\n      setStats({\n        totalUsers: usersData.total,\n        totalRoles: rolesData.length,\n        totalPermissions: permissionsData.length,\n        activeUsers: usersData.items.filter((u: User) => u.is_active).length,\n      });\n\n      setRecentUsers(usersData.items.slice(0, 5));\n    } catch (error) {\n      toast.error('Erreur lors du chargement des données');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const statCards = [\n    {\n      title: 'Utilisateurs',\n      value: stats.totalUsers,\n      icon: '👥',\n      color: 'blue',\n      bgColor: 'bg-blue-100',\n      iconColor: 'text-blue-600',\n    },\n    {\n      title: 'Rôles',\n      value: stats.totalRoles,\n      icon: '🛡️',\n      color: 'green',\n      bgColor: 'bg-green-100',\n      iconColor: 'text-green-600',\n    },\n    {\n      title: 'Permissions',\n      value: stats.totalPermissions,\n      icon: '🔑',\n      color: 'yellow',\n      bgColor: 'bg-yellow-100',\n      iconColor: 'text-yellow-600',\n    },\n    {\n      title: 'Utilisateurs Actifs',\n      value: stats.activeUsers,\n      icon: '📈',\n      color: 'purple',\n      bgColor: 'bg-purple-100',\n      iconColor: 'text-purple-600',\n    },\n  ];\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Welcome Section */}\n      <div className=\"bg-gradient-to-r from-primary-600 to-primary-700 rounded-lg p-6 text-white\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold mb-2\">\n              Bienvenue, {user?.full_name} !\n            </h1>\n            <p className=\"text-primary-100\">\n              Voici un aperçu de votre système de gestion des utilisateurs\n            </p>\n          </div>\n          <div className=\"hidden md:block\">\n            <div className=\"w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center\">\n              <span className=\"text-2xl font-bold\">\n                {user?.full_name?.charAt(0) || 'U'}\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        {statCards.map((stat, index) => (\n          <div key={index} className=\"card hover:shadow-lg transition-shadow duration-200\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-500 mb-1\">\n                  {stat.title}\n                </p>\n                <p className=\"text-3xl font-bold text-gray-900\">\n                  {stat.value}\n                </p>\n              </div>\n              <div className={`p-3 rounded-full ${stat.bgColor}`}>\n                <span className=\"text-2xl\">{stat.icon}</span>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Recent Users */}\n        <div className=\"card\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">\n              Utilisateurs Récents\n            </h3>\n            <button className=\"btn-secondary text-xs\">\n              <svg className=\"w-4 h-4 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\n              </svg>\n              Voir tout\n            </button>\n          </div>\n          \n          <div className=\"space-y-3\">\n            {recentUsers.length > 0 ? (\n              recentUsers.map((recentUser) => (\n                <div key={recentUser.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center\">\n                      <span className=\"text-white text-sm font-medium\">\n                        {recentUser.full_name.charAt(0)}\n                      </span>\n                    </div>\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-900\">\n                        {recentUser.full_name}\n                      </p>\n                      <p className=\"text-xs text-gray-500\">\n                        {recentUser.email}\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"text-right\">\n                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                      recentUser.is_active \n                        ? 'bg-green-100 text-green-800' \n                        : 'bg-red-100 text-red-800'\n                    }`}>\n                      {recentUser.is_active ? 'Actif' : 'Inactif'}\n                    </span>\n                    <p className=\"text-xs text-gray-500 mt-1\">\n                      {recentUser.role?.name || 'Aucun rôle'}\n                    </p>\n                  </div>\n                </div>\n              ))\n            ) : (\n              <div className=\"text-center py-8 text-gray-500\">\n                <svg className=\"w-12 h-12 mx-auto mb-2 text-gray-300\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\" />\n                </svg>\n                <p>Aucun utilisateur trouvé</p>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Quick Actions */}\n        <div className=\"card\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n            Actions Rapides\n          </h3>\n          \n          <div className=\"space-y-3\">\n            <button className=\"w-full btn-primary justify-start\">\n              <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z\" />\n              </svg>\n              Créer un nouvel utilisateur\n            </button>\n            \n            <button className=\"w-full btn-secondary justify-start\">\n              <span className=\"mr-2\">🛡️</span>\n              Gérer les rôles\n            </button>\n            \n            <button className=\"w-full btn-secondary justify-start\">\n              <span className=\"mr-2\">🔑</span>\n              Configurer les permissions\n            </button>\n            \n            <button className=\"w-full btn-secondary justify-start\">\n              <span className=\"mr-2\">📊</span>\n              Voir l'activité récente\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* System Info */}\n      <div className=\"card\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n          Informations Système\n        </h3>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <div className=\"text-center p-4 bg-gray-50 rounded-lg\">\n            <p className=\"text-sm text-gray-500\">Version</p>\n            <p className=\"text-lg font-semibold text-gray-900\">v1.0.0</p>\n          </div>\n          \n          <div className=\"text-center p-4 bg-gray-50 rounded-lg\">\n            <p className=\"text-sm text-gray-500\">Base de données</p>\n            <p className=\"text-lg font-semibold text-gray-900\">MySQL</p>\n          </div>\n          \n          <div className=\"text-center p-4 bg-gray-50 rounded-lg\">\n            <p className=\"text-sm text-gray-500\">Statut</p>\n            <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n              Opérationnel\n            </span>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TailwindDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,UAAU,MAAM,iBAAiB;AACxC,SAASC,KAAK,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAUvC,MAAMC,iBAA2B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA;EACxC,MAAM;IAAEC;EAAK,CAAC,GAAGR,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACS,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAQ;IACxCa,UAAU,EAAE,CAAC;IACbC,UAAU,EAAE,CAAC;IACbC,gBAAgB,EAAE,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAS,EAAE,CAAC;EAC1D,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACdoB,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAM,CAACC,SAAS,EAAEC,SAAS,EAAEC,eAAe,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAChEvB,UAAU,CAACwB,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EACzBxB,UAAU,CAACyB,WAAW,CAAC,CAAC,EACxBzB,UAAU,CAAC0B,iBAAiB,CAAC,CAAC,CAC/B,CAAC;MAEFjB,QAAQ,CAAC;QACPC,UAAU,EAAES,SAAS,CAACQ,KAAK;QAC3BhB,UAAU,EAAES,SAAS,CAACQ,MAAM;QAC5BhB,gBAAgB,EAAES,eAAe,CAACO,MAAM;QACxCf,WAAW,EAAEM,SAAS,CAACU,KAAK,CAACC,MAAM,CAAEC,CAAO,IAAKA,CAAC,CAACC,SAAS,CAAC,CAACJ;MAChE,CAAC,CAAC;MAEFb,cAAc,CAACI,SAAS,CAACU,KAAK,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC7C,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdjC,KAAK,CAACiC,KAAK,CAAC,uCAAuC,CAAC;IACtD,CAAC,SAAS;MACRjB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMkB,SAAS,GAAG,CAChB;IACEC,KAAK,EAAE,cAAc;IACrBC,KAAK,EAAE7B,KAAK,CAACE,UAAU;IACvB4B,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,MAAM;IACbC,OAAO,EAAE,aAAa;IACtBC,SAAS,EAAE;EACb,CAAC,EACD;IACEL,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE7B,KAAK,CAACG,UAAU;IACvB2B,IAAI,EAAE,KAAK;IACXC,KAAK,EAAE,OAAO;IACdC,OAAO,EAAE,cAAc;IACvBC,SAAS,EAAE;EACb,CAAC,EACD;IACEL,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE7B,KAAK,CAACI,gBAAgB;IAC7B0B,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,QAAQ;IACfC,OAAO,EAAE,eAAe;IACxBC,SAAS,EAAE;EACb,CAAC,EACD;IACEL,KAAK,EAAE,qBAAqB;IAC5BC,KAAK,EAAE7B,KAAK,CAACK,WAAW;IACxByB,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,QAAQ;IACfC,OAAO,EAAE,eAAe;IACxBC,SAAS,EAAE;EACb,CAAC,CACF;EAED,IAAIzB,OAAO,EAAE;IACX,oBACEb,OAAA;MAAKuC,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpDxC,OAAA;QAAKuC,SAAS,EAAC;MAAmE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtF,CAAC;EAEV;EAEA,oBACE5C,OAAA;IAAKuC,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBxC,OAAA;MAAKuC,SAAS,EAAC,4EAA4E;MAAAC,QAAA,eACzFxC,OAAA;QAAKuC,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDxC,OAAA;UAAAwC,QAAA,gBACExC,OAAA;YAAIuC,SAAS,EAAC,yBAAyB;YAAAC,QAAA,GAAC,aAC3B,EAACpC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyC,SAAS,EAAC,IAC9B;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL5C,OAAA;YAAGuC,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAEhC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACN5C,OAAA;UAAKuC,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BxC,OAAA;YAAKuC,SAAS,EAAC,gFAAgF;YAAAC,QAAA,eAC7FxC,OAAA;cAAMuC,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EACjC,CAAApC,IAAI,aAAJA,IAAI,wBAAAD,eAAA,GAAJC,IAAI,CAAEyC,SAAS,cAAA1C,eAAA,uBAAfA,eAAA,CAAiB2C,MAAM,CAAC,CAAC,CAAC,KAAI;YAAG;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5C,OAAA;MAAKuC,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClER,SAAS,CAACe,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACzBjD,OAAA;QAAiBuC,SAAS,EAAC,qDAAqD;QAAAC,QAAA,eAC9ExC,OAAA;UAAKuC,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDxC,OAAA;YAAAwC,QAAA,gBACExC,OAAA;cAAGuC,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAClDQ,IAAI,CAACf;YAAK;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACJ5C,OAAA;cAAGuC,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAC5CQ,IAAI,CAACd;YAAK;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN5C,OAAA;YAAKuC,SAAS,EAAE,oBAAoBS,IAAI,CAACX,OAAO,EAAG;YAAAG,QAAA,eACjDxC,OAAA;cAAMuC,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAEQ,IAAI,CAACb;YAAI;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GAbEK,KAAK;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAcV,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEN5C,OAAA;MAAKuC,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpDxC,OAAA;QAAKuC,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBxC,OAAA;UAAKuC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDxC,OAAA;YAAIuC,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAEpD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL5C,OAAA;YAAQuC,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACvCxC,OAAA;cAAKuC,SAAS,EAAC,cAAc;cAACW,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAZ,QAAA,gBACjFxC,OAAA;gBAAMqD,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAkC;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1G5C,OAAA;gBAAMqD,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAyH;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9L,CAAC,aAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN5C,OAAA;UAAKuC,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvB7B,WAAW,CAACc,MAAM,GAAG,CAAC,GACrBd,WAAW,CAACoC,GAAG,CAAEU,UAAU;YAAA,IAAAC,gBAAA;YAAA,oBACzB1D,OAAA;cAAyBuC,SAAS,EAAC,6DAA6D;cAAAC,QAAA,gBAC9FxC,OAAA;gBAAKuC,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CxC,OAAA;kBAAKuC,SAAS,EAAC,wEAAwE;kBAAAC,QAAA,eACrFxC,OAAA;oBAAMuC,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,EAC7CiB,UAAU,CAACZ,SAAS,CAACC,MAAM,CAAC,CAAC;kBAAC;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACN5C,OAAA;kBAAAwC,QAAA,gBACExC,OAAA;oBAAGuC,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAC7CiB,UAAU,CAACZ;kBAAS;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC,eACJ5C,OAAA;oBAAGuC,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EACjCiB,UAAU,CAACE;kBAAK;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN5C,OAAA;gBAAKuC,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBxC,OAAA;kBAAMuC,SAAS,EAAE,2EACfkB,UAAU,CAAC5B,SAAS,GAChB,6BAA6B,GAC7B,yBAAyB,EAC5B;kBAAAW,QAAA,EACAiB,UAAU,CAAC5B,SAAS,GAAG,OAAO,GAAG;gBAAS;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC,eACP5C,OAAA;kBAAGuC,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EACtC,EAAAkB,gBAAA,GAAAD,UAAU,CAACG,IAAI,cAAAF,gBAAA,uBAAfA,gBAAA,CAAiBG,IAAI,KAAI;gBAAY;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA,GA3BEa,UAAU,CAACK,EAAE;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA4BlB,CAAC;UAAA,CACP,CAAC,gBAEF5C,OAAA;YAAKuC,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC7CxC,OAAA;cAAKuC,SAAS,EAAC,sCAAsC;cAACW,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAZ,QAAA,eACzGxC,OAAA;gBAAMqD,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAyH;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9L,CAAC,eACN5C,OAAA;cAAAwC,QAAA,EAAG;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN5C,OAAA;QAAKuC,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBxC,OAAA;UAAIuC,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAEzD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEL5C,OAAA;UAAKuC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBxC,OAAA;YAAQuC,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAClDxC,OAAA;cAAKuC,SAAS,EAAC,cAAc;cAACW,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAZ,QAAA,eACjFxC,OAAA;gBAAMqD,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAsF;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3J,CAAC,kCAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAET5C,OAAA;YAAQuC,SAAS,EAAC,oCAAoC;YAAAC,QAAA,gBACpDxC,OAAA;cAAMuC,SAAS,EAAC,MAAM;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,yBAEnC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAET5C,OAAA;YAAQuC,SAAS,EAAC,oCAAoC;YAAAC,QAAA,gBACpDxC,OAAA;cAAMuC,SAAS,EAAC,MAAM;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,8BAElC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAET5C,OAAA;YAAQuC,SAAS,EAAC,oCAAoC;YAAAC,QAAA,gBACpDxC,OAAA;cAAMuC,SAAS,EAAC,MAAM;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,iCAElC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5C,OAAA;MAAKuC,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBxC,OAAA;QAAIuC,SAAS,EAAC,0CAA0C;QAAAC,QAAA,EAAC;MAEzD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEL5C,OAAA;QAAKuC,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpDxC,OAAA;UAAKuC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDxC,OAAA;YAAGuC,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAChD5C,OAAA;YAAGuC,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eAEN5C,OAAA;UAAKuC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDxC,OAAA;YAAGuC,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACxD5C,OAAA;YAAGuC,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eAEN5C,OAAA;UAAKuC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDxC,OAAA;YAAGuC,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC/C5C,OAAA;YAAMuC,SAAS,EAAC,qGAAqG;YAAAC,QAAA,EAAC;UAEtH;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1C,EAAA,CApPID,iBAA2B;EAAA,QACdL,OAAO;AAAA;AAAAmE,EAAA,GADpB9D,iBAA2B;AAsPjC,eAAeA,iBAAiB;AAAC,IAAA8D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}