{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport useId from '@mui/utils/useId';\nimport { fastMemo } from '@mui/x-internals/fastMemo';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { useGridPrivateApiContext } from \"../../hooks/utils/useGridPrivateApiContext.js\";\nimport { ColumnHeaderMenuIcon } from \"./ColumnHeaderMenuIcon.js\";\nimport { GridColumnHeaderMenu } from \"../menu/columnMenu/GridColumnHeaderMenu.js\";\nimport { gridClasses, getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { GridGenericColumnHeaderItem } from \"./GridGenericColumnHeaderItem.js\";\nimport { isEventTargetInPortal } from \"../../utils/domUtils.js\";\nimport { PinnedColumnPosition } from \"../../internals/constants.js\";\nimport { attachPinnedStyle } from \"../../internals/utils/index.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    colDef,\n    classes,\n    isDragging,\n    sortDirection,\n    showRightBorder,\n    showLeftBorder,\n    filterItemsCounter,\n    pinnedPosition,\n    isLastUnpinned,\n    isSiblingFocused\n  } = ownerState;\n  const isColumnSorted = sortDirection != null;\n  const isColumnFiltered = filterItemsCounter != null && filterItemsCounter > 0;\n  // todo refactor to a prop on col isNumeric or ?? ie: coltype===price wont work\n  const isColumnNumeric = colDef.type === 'number';\n  const slots = {\n    root: ['columnHeader', colDef.headerAlign === 'left' && 'columnHeader--alignLeft', colDef.headerAlign === 'center' && 'columnHeader--alignCenter', colDef.headerAlign === 'right' && 'columnHeader--alignRight', colDef.sortable && 'columnHeader--sortable', isDragging && 'columnHeader--moving', isColumnSorted && 'columnHeader--sorted', isColumnFiltered && 'columnHeader--filtered', isColumnNumeric && 'columnHeader--numeric', 'withBorderColor', showRightBorder && 'columnHeader--withRightBorder', showLeftBorder && 'columnHeader--withLeftBorder', pinnedPosition === PinnedColumnPosition.LEFT && 'columnHeader--pinnedLeft', pinnedPosition === PinnedColumnPosition.RIGHT && 'columnHeader--pinnedRight',\n    // TODO: Remove classes below and restore `:has` selectors when they are supported in jsdom\n    // See https://github.com/mui/mui-x/pull/14559\n    isLastUnpinned && 'columnHeader--lastUnpinned', isSiblingFocused && 'columnHeader--siblingFocused'],\n    draggableContainer: ['columnHeaderDraggableContainer'],\n    titleContainer: ['columnHeaderTitleContainer'],\n    titleContainerContent: ['columnHeaderTitleContainerContent']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nfunction GridColumnHeaderItem(props) {\n  const {\n    colDef,\n    columnMenuOpen,\n    colIndex,\n    headerHeight,\n    isResizing,\n    isLast,\n    sortDirection,\n    sortIndex,\n    filterItemsCounter,\n    hasFocus,\n    tabIndex,\n    disableReorder,\n    separatorSide,\n    showLeftBorder,\n    showRightBorder,\n    pinnedPosition,\n    pinnedOffset\n  } = props;\n  const apiRef = useGridPrivateApiContext();\n  const rootProps = useGridRootProps();\n  const isRtl = useRtl();\n  const headerCellRef = React.useRef(null);\n  const columnMenuId = useId();\n  const columnMenuButtonId = useId();\n  const iconButtonRef = React.useRef(null);\n  const [showColumnMenuIcon, setShowColumnMenuIcon] = React.useState(columnMenuOpen);\n  const isDraggable = React.useMemo(() => !rootProps.disableColumnReorder && !disableReorder && !colDef.disableReorder, [rootProps.disableColumnReorder, disableReorder, colDef.disableReorder]);\n  let headerComponent;\n  if (colDef.renderHeader) {\n    headerComponent = colDef.renderHeader(apiRef.current.getColumnHeaderParams(colDef.field));\n  }\n  const ownerState = _extends({}, props, {\n    classes: rootProps.classes,\n    showRightBorder,\n    showLeftBorder\n  });\n  const classes = useUtilityClasses(ownerState);\n  const publish = React.useCallback(eventName => event => {\n    // Ignore portal\n    // See https://github.com/mui/mui-x/issues/1721\n    if (isEventTargetInPortal(event)) {\n      return;\n    }\n    apiRef.current.publishEvent(eventName, apiRef.current.getColumnHeaderParams(colDef.field), event);\n  }, [apiRef, colDef.field]);\n  const mouseEventsHandlers = React.useMemo(() => ({\n    onClick: publish('columnHeaderClick'),\n    onContextMenu: publish('columnHeaderContextMenu'),\n    onDoubleClick: publish('columnHeaderDoubleClick'),\n    onMouseOver: publish('columnHeaderOver'),\n    // TODO remove as it's not used\n    onMouseOut: publish('columnHeaderOut'),\n    // TODO remove as it's not used\n    onMouseEnter: publish('columnHeaderEnter'),\n    // TODO remove as it's not used\n    onMouseLeave: publish('columnHeaderLeave'),\n    // TODO remove as it's not used\n    onKeyDown: publish('columnHeaderKeyDown'),\n    onFocus: publish('columnHeaderFocus'),\n    onBlur: publish('columnHeaderBlur')\n  }), [publish]);\n  const draggableEventHandlers = React.useMemo(() => isDraggable ? {\n    onDragStart: publish('columnHeaderDragStart'),\n    onDragEnter: publish('columnHeaderDragEnter'),\n    onDragOver: publish('columnHeaderDragOver'),\n    onDragEndCapture: publish('columnHeaderDragEnd')\n  } : {}, [isDraggable, publish]);\n  const columnHeaderSeparatorProps = React.useMemo(() => ({\n    onMouseDown: publish('columnSeparatorMouseDown'),\n    onDoubleClick: publish('columnSeparatorDoubleClick')\n  }), [publish]);\n  React.useEffect(() => {\n    if (!showColumnMenuIcon) {\n      setShowColumnMenuIcon(columnMenuOpen);\n    }\n  }, [showColumnMenuIcon, columnMenuOpen]);\n  const handleExited = React.useCallback(() => {\n    setShowColumnMenuIcon(false);\n  }, []);\n  const columnMenuIconButton = !rootProps.disableColumnMenu && !colDef.disableColumnMenu && /*#__PURE__*/_jsx(ColumnHeaderMenuIcon, {\n    colDef: colDef,\n    columnMenuId: columnMenuId,\n    columnMenuButtonId: columnMenuButtonId,\n    open: showColumnMenuIcon,\n    iconButtonRef: iconButtonRef\n  });\n  const columnMenu = /*#__PURE__*/_jsx(GridColumnHeaderMenu, {\n    columnMenuId: columnMenuId,\n    columnMenuButtonId: columnMenuButtonId,\n    field: colDef.field,\n    open: columnMenuOpen,\n    target: iconButtonRef.current,\n    ContentComponent: rootProps.slots.columnMenu,\n    contentComponentProps: rootProps.slotProps?.columnMenu,\n    onExited: handleExited\n  });\n  const sortingOrder = colDef.sortingOrder ?? rootProps.sortingOrder;\n  const showSortIcon = (colDef.sortable || sortDirection != null) && !colDef.hideSortIcons && !rootProps.disableColumnSorting;\n  const columnTitleIconButtons = /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [!rootProps.disableColumnFilter && /*#__PURE__*/_jsx(rootProps.slots.columnHeaderFilterIconButton, _extends({\n      field: colDef.field,\n      counter: filterItemsCounter\n    }, rootProps.slotProps?.columnHeaderFilterIconButton)), showSortIcon && /*#__PURE__*/_jsx(rootProps.slots.columnHeaderSortIcon, _extends({\n      field: colDef.field,\n      direction: sortDirection,\n      index: sortIndex,\n      sortingOrder: sortingOrder,\n      disabled: !colDef.sortable\n    }, rootProps.slotProps?.columnHeaderSortIcon))]\n  });\n  React.useLayoutEffect(() => {\n    const columnMenuState = apiRef.current.state.columnMenu;\n    if (hasFocus && !columnMenuState.open) {\n      const focusableElement = headerCellRef.current.querySelector('[tabindex=\"0\"]');\n      const elementToFocus = focusableElement || headerCellRef.current;\n      elementToFocus?.focus();\n      if (apiRef.current.columnHeadersContainerRef?.current) {\n        apiRef.current.columnHeadersContainerRef.current.scrollLeft = 0;\n      }\n    }\n  }, [apiRef, hasFocus]);\n  const headerClassName = typeof colDef.headerClassName === 'function' ? colDef.headerClassName({\n    field: colDef.field,\n    colDef\n  }) : colDef.headerClassName;\n  const label = colDef.headerName ?? colDef.field;\n  const style = React.useMemo(() => attachPinnedStyle(_extends({}, props.style), isRtl, pinnedPosition, pinnedOffset), [pinnedPosition, pinnedOffset, props.style, isRtl]);\n  return /*#__PURE__*/_jsx(GridGenericColumnHeaderItem, _extends({\n    ref: headerCellRef,\n    classes: classes,\n    columnMenuOpen: columnMenuOpen,\n    colIndex: colIndex,\n    height: headerHeight,\n    isResizing: isResizing,\n    sortDirection: sortDirection,\n    hasFocus: hasFocus,\n    tabIndex: tabIndex,\n    separatorSide: separatorSide,\n    isDraggable: isDraggable,\n    headerComponent: headerComponent,\n    description: colDef.description,\n    elementId: colDef.field,\n    width: colDef.computedWidth,\n    columnMenuIconButton: columnMenuIconButton,\n    columnTitleIconButtons: columnTitleIconButtons,\n    headerClassName: clsx(headerClassName, isLast && gridClasses['columnHeader--last']),\n    label: label,\n    resizable: !rootProps.disableColumnResize && !!colDef.resizable,\n    \"data-field\": colDef.field,\n    columnMenu: columnMenu,\n    draggableContainerProps: draggableEventHandlers,\n    columnHeaderSeparatorProps: columnHeaderSeparatorProps,\n    style: style\n  }, mouseEventsHandlers));\n}\nprocess.env.NODE_ENV !== \"production\" ? GridColumnHeaderItem.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  colDef: PropTypes.object.isRequired,\n  colIndex: PropTypes.number.isRequired,\n  columnMenuOpen: PropTypes.bool.isRequired,\n  disableReorder: PropTypes.bool,\n  filterItemsCounter: PropTypes.number,\n  hasFocus: PropTypes.bool,\n  headerHeight: PropTypes.number.isRequired,\n  isDragging: PropTypes.bool.isRequired,\n  isLast: PropTypes.bool.isRequired,\n  isLastUnpinned: PropTypes.bool.isRequired,\n  isResizing: PropTypes.bool.isRequired,\n  isSiblingFocused: PropTypes.bool.isRequired,\n  pinnedOffset: PropTypes.number,\n  pinnedPosition: PropTypes.oneOf([0, 1, 2, 3]),\n  separatorSide: PropTypes.oneOf(['left', 'right']),\n  showLeftBorder: PropTypes.bool.isRequired,\n  showRightBorder: PropTypes.bool.isRequired,\n  sortDirection: PropTypes.oneOf(['asc', 'desc']),\n  sortIndex: PropTypes.number,\n  style: PropTypes.object,\n  tabIndex: PropTypes.oneOf([-1, 0]).isRequired\n} : void 0;\nconst Memoized = fastMemo(GridColumnHeaderItem);\nexport { Memoized as GridColumnHeaderItem };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}