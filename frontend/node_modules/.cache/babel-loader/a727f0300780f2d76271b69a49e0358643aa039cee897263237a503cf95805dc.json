{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"variant\", \"noRowsVariant\", \"style\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { useGridRootProps } from \"../hooks/utils/useGridRootProps.js\";\nimport { GridOverlay } from \"./containers/GridOverlay.js\";\nimport { GridSkeletonLoadingOverlay } from \"./GridSkeletonLoadingOverlay.js\";\nimport { useGridApiContext } from \"../hooks/utils/useGridApiContext.js\";\nimport { gridRowCountSelector, useGridSelector } from \"../hooks/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst LOADING_VARIANTS = {\n  'circular-progress': {\n    component: rootProps => rootProps.slots.baseCircularProgress,\n    style: {}\n  },\n  'linear-progress': {\n    component: rootProps => rootProps.slots.baseLinearProgress,\n    style: {\n      display: 'block'\n    }\n  },\n  skeleton: {\n    component: () => GridSkeletonLoadingOverlay,\n    style: {\n      display: 'block'\n    }\n  }\n};\nconst GridLoadingOverlay = forwardRef(function GridLoadingOverlay(props, ref) {\n  const {\n      variant = 'linear-progress',\n      noRowsVariant = 'skeleton',\n      style\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const rowsCount = useGridSelector(apiRef, gridRowCountSelector);\n  const activeVariant = LOADING_VARIANTS[rowsCount === 0 ? noRowsVariant : variant];\n  const Component = activeVariant.component(rootProps);\n  return /*#__PURE__*/_jsx(GridOverlay, _extends({\n    style: _extends({}, activeVariant.style, style)\n  }, other, {\n    ref: ref,\n    children: /*#__PURE__*/_jsx(Component, {})\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridLoadingOverlay.displayName = \"GridLoadingOverlay\";\nprocess.env.NODE_ENV !== \"production\" ? GridLoadingOverlay.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * The variant of the overlay when no rows are displayed.\n   * @default 'skeleton'\n   */\n  noRowsVariant: PropTypes.oneOf(['circular-progress', 'linear-progress', 'skeleton']),\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant of the overlay.\n   * @default 'linear-progress'\n   */\n  variant: PropTypes.oneOf(['circular-progress', 'linear-progress', 'skeleton'])\n} : void 0;\nexport { GridLoadingOverlay };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}