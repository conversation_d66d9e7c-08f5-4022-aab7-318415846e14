{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { styled } from '@mui/system';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { vars } from \"../../constants/cssVariables.js\";\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['panelContent']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst GridPanelContentRoot = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'PanelContent'\n})({\n  display: 'flex',\n  flexDirection: 'column',\n  overflow: 'auto',\n  flex: '1 1',\n  maxHeight: 400,\n  padding: vars.spacing(2.5, 1.5, 2, 1),\n  gap: vars.spacing(2.5)\n});\nfunction GridPanelContent(props) {\n  const {\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const rootProps = useGridRootProps();\n  const classes = useUtilityClasses(rootProps);\n  return /*#__PURE__*/_jsx(GridPanelContentRoot, _extends({\n    className: clsx(classes.root, className),\n    ownerState: rootProps\n  }, other));\n}\nprocess.env.NODE_ENV !== \"production\" ? GridPanelContent.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport { GridPanelContent };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}