{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { GRID_DEFAULT_STRATEGY, useGridRegisterStrategyProcessor } from \"../../core/strategyProcessing/index.js\";\nimport { buildRootGroup, GRID_ROOT_GROUP_ID } from \"./gridRowsUtils.js\";\nconst createFlatRowTree = rows => {\n  const tree = {\n    [GRID_ROOT_GROUP_ID]: _extends({}, buildRootGroup(), {\n      children: rows\n    })\n  };\n  for (let i = 0; i < rows.length; i += 1) {\n    const rowId = rows[i];\n    tree[rowId] = {\n      id: rowId,\n      depth: 0,\n      parent: GRID_ROOT_GROUP_ID,\n      type: 'leaf',\n      groupingKey: null\n    };\n  }\n  return {\n    groupingName: GRID_DEFAULT_STRATEGY,\n    tree,\n    treeDepths: {\n      0: rows.length\n    },\n    dataRowIds: rows\n  };\n};\nconst updateFlatRowTree = ({\n  previousTree,\n  actions\n}) => {\n  const tree = _extends({}, previousTree);\n  const idsToRemoveFromRootGroup = {};\n  for (let i = 0; i < actions.remove.length; i += 1) {\n    const idToDelete = actions.remove[i];\n    idsToRemoveFromRootGroup[idToDelete] = true;\n    delete tree[idToDelete];\n  }\n  for (let i = 0; i < actions.insert.length; i += 1) {\n    const idToInsert = actions.insert[i];\n    tree[idToInsert] = {\n      id: idToInsert,\n      depth: 0,\n      parent: GRID_ROOT_GROUP_ID,\n      type: 'leaf',\n      groupingKey: null\n    };\n  }\n\n  // TODO rows v6: Support row unpinning\n\n  const rootGroup = tree[GRID_ROOT_GROUP_ID];\n  let rootGroupChildren = [...rootGroup.children, ...actions.insert];\n  if (Object.values(idsToRemoveFromRootGroup).length) {\n    rootGroupChildren = rootGroupChildren.filter(id => !idsToRemoveFromRootGroup[id]);\n  }\n  tree[GRID_ROOT_GROUP_ID] = _extends({}, rootGroup, {\n    children: rootGroupChildren\n  });\n  return {\n    groupingName: GRID_DEFAULT_STRATEGY,\n    tree,\n    treeDepths: {\n      0: rootGroupChildren.length\n    },\n    dataRowIds: rootGroupChildren\n  };\n};\nconst flatRowTreeCreationMethod = params => {\n  if (params.updates.type === 'full') {\n    return createFlatRowTree(params.updates.rows);\n  }\n  return updateFlatRowTree({\n    previousTree: params.previousTree,\n    actions: params.updates.actions\n  });\n};\nexport const useGridRowsPreProcessors = apiRef => {\n  useGridRegisterStrategyProcessor(apiRef, GRID_DEFAULT_STRATEGY, 'rowTreeCreation', flatRowTreeCreationMethod);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}