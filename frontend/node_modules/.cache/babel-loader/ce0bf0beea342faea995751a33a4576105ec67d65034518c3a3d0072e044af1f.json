{"ast": null, "code": "/**\n * Based on `fast-deep-equal`\n *\n * MIT License\n *\n * Copyright (c) 2017 <PERSON><PERSON><PERSON>\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */\n\n/**\n * Check if two values are deeply equal.\n */\n\nexport function isDeepEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (a && b && typeof a === 'object' && typeof b === 'object') {\n    if (a.constructor !== b.constructor) {\n      return false;\n    }\n    if (Array.isArray(a)) {\n      const length = a.length;\n      if (length !== b.length) {\n        return false;\n      }\n      for (let i = 0; i < length; i += 1) {\n        if (!isDeepEqual(a[i], b[i])) {\n          return false;\n        }\n      }\n      return true;\n    }\n    if (a instanceof Map && b instanceof Map) {\n      if (a.size !== b.size) {\n        return false;\n      }\n      const entriesA = Array.from(a.entries());\n      for (let i = 0; i < entriesA.length; i += 1) {\n        if (!b.has(entriesA[i][0])) {\n          return false;\n        }\n      }\n      for (let i = 0; i < entriesA.length; i += 1) {\n        const entryA = entriesA[i];\n        if (!isDeepEqual(entryA[1], b.get(entryA[0]))) {\n          return false;\n        }\n      }\n      return true;\n    }\n    if (a instanceof Set && b instanceof Set) {\n      if (a.size !== b.size) {\n        return false;\n      }\n      const entries = Array.from(a.entries());\n      for (let i = 0; i < entries.length; i += 1) {\n        if (!b.has(entries[i][0])) {\n          return false;\n        }\n      }\n      return true;\n    }\n    if (ArrayBuffer.isView(a) && ArrayBuffer.isView(b)) {\n      const length = a.length;\n      if (length !== b.length) {\n        return false;\n      }\n      for (let i = 0; i < length; i += 1) {\n        if (a[i] !== b[i]) {\n          return false;\n        }\n      }\n      return true;\n    }\n    if (a.constructor === RegExp) {\n      return a.source === b.source && a.flags === b.flags;\n    }\n    if (a.valueOf !== Object.prototype.valueOf) {\n      return a.valueOf() === b.valueOf();\n    }\n    if (a.toString !== Object.prototype.toString) {\n      return a.toString() === b.toString();\n    }\n    const keys = Object.keys(a);\n    const length = keys.length;\n    if (length !== Object.keys(b).length) {\n      return false;\n    }\n    for (let i = 0; i < length; i += 1) {\n      if (!Object.prototype.hasOwnProperty.call(b, keys[i])) {\n        return false;\n      }\n    }\n    for (let i = 0; i < length; i += 1) {\n      const key = keys[i];\n      if (!isDeepEqual(a[key], b[key])) {\n        return false;\n      }\n    }\n    return true;\n  }\n\n  // true if both NaN, false otherwise\n  // eslint-disable-next-line no-self-compare\n  return a !== a && b !== b;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}