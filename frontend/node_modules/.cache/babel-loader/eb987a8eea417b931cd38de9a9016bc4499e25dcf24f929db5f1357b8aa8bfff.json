{"ast": null, "code": "/**\n * A JSON.stringify that handles circular references safely.\n * Fixes: https://github.com/mui/mui-x/issues/17521\n * Source: https://www.30secondsofcode.org/js/s/stringify-circular-json/\n */\nexport function stringify(input) {\n  const seen = new WeakSet();\n  return JSON.stringify(input, (_, v) => {\n    // https://github.com/mui/mui-x/issues/17855\n    if (typeof window !== 'undefined' && v === window) {\n      return v.toString();\n    }\n    if (v !== null && typeof v === 'object') {\n      if (seen.has(v)) {\n        return null;\n      }\n      seen.add(v);\n    }\n    return v;\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}