{"ast": null, "code": "import * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { GridPreferencePanelsValue } from \"../../../../hooks/features/preferencesPanel/gridPreferencePanelsValue.js\";\nimport { useGridApiContext } from \"../../../../hooks/utils/useGridApiContext.js\";\nimport { useGridRootProps } from \"../../../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction GridColumnMenuManageItem(props) {\n  const {\n    onClick\n  } = props;\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const showColumns = React.useCallback(event => {\n    onClick(event); // hide column menu\n    apiRef.current.showPreferences(GridPreferencePanelsValue.columns);\n  }, [apiRef, onClick]);\n  if (rootProps.disableColumnSelector) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(rootProps.slots.baseMenuItem, {\n    onClick: showColumns,\n    iconStart: /*#__PURE__*/_jsx(rootProps.slots.columnMenuManageColumnsIcon, {\n      fontSize: \"small\"\n    }),\n    children: apiRef.current.getLocaleText('columnMenuManageColumns')\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GridColumnMenuManageItem.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  colDef: PropTypes.object.isRequired,\n  onClick: PropTypes.func.isRequired\n} : void 0;\nexport { GridColumnMenuManageItem };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}