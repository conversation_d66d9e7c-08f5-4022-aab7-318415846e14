{"ast": null, "code": "// Based on https://stackoverflow.com/a/59518678\nlet cachedSupportsPreventScroll;\nexport function doesSupportPreventScroll() {\n  if (cachedSupportsPreventScroll === undefined) {\n    document.createElement('div').focus({\n      get preventScroll() {\n        cachedSupportsPreventScroll = true;\n        return false;\n      }\n    });\n  }\n  return cachedSupportsPreventScroll;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}