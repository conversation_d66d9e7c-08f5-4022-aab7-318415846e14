{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useLazyRef from '@mui/utils/useLazyRef';\nimport { gridFilteredTopLevelRowCountSelector } from \"../filter/index.js\";\nimport { useGridLogger, useGridSelector, useGridApiMethod, useGridEvent } from \"../../utils/index.js\";\nimport { useGridRegisterPipeProcessor } from \"../../core/pipeProcessing/index.js\";\nimport { gridPaginationRowCountSelector, gridPaginationMetaSelector, gridPaginationModelSelector } from \"./gridPaginationSelector.js\";\nexport const useGridRowCount = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridRowCount');\n  const visibleTopLevelRowCount = useGridSelector(apiRef, gridFilteredTopLevelRowCountSelector);\n  const rowCountState = useGridSelector(apiRef, gridPaginationRowCountSelector);\n  const paginationMeta = useGridSelector(apiRef, gridPaginationMetaSelector);\n  const paginationModel = useGridSelector(apiRef, gridPaginationModelSelector);\n  const previousPageSize = useLazyRef(() => gridPaginationModelSelector(apiRef).pageSize);\n  apiRef.current.registerControlState({\n    stateId: 'paginationRowCount',\n    propModel: props.rowCount,\n    propOnChange: props.onRowCountChange,\n    stateSelector: gridPaginationRowCountSelector,\n    changeEvent: 'rowCountChange'\n  });\n\n  /**\n   * API METHODS\n   */\n  const setRowCount = React.useCallback(newRowCount => {\n    if (rowCountState === newRowCount) {\n      return;\n    }\n    logger.debug(\"Setting 'rowCount' to\", newRowCount);\n    apiRef.current.setState(state => _extends({}, state, {\n      pagination: _extends({}, state.pagination, {\n        rowCount: newRowCount\n      })\n    }));\n  }, [apiRef, logger, rowCountState]);\n  const paginationRowCountApi = {\n    setRowCount\n  };\n  useGridApiMethod(apiRef, paginationRowCountApi, 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    const exportedRowCount = gridPaginationRowCountSelector(apiRef);\n    const shouldExportRowCount =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the `rowCount` is controlled\n    props.rowCount != null ||\n    // Always export if the `rowCount` has been initialized\n    props.initialState?.pagination?.rowCount != null;\n    if (!shouldExportRowCount) {\n      return prevState;\n    }\n    return _extends({}, prevState, {\n      pagination: _extends({}, prevState.pagination, {\n        rowCount: exportedRowCount\n      })\n    });\n  }, [apiRef, props.rowCount, props.initialState?.pagination?.rowCount]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    const restoredRowCount = context.stateToRestore.pagination?.rowCount ? context.stateToRestore.pagination.rowCount : gridPaginationRowCountSelector(apiRef);\n    apiRef.current.setState(state => _extends({}, state, {\n      pagination: _extends({}, state.pagination, {\n        rowCount: restoredRowCount\n      })\n    }));\n    return params;\n  }, [apiRef]);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n\n  /**\n   * EVENTS\n   */\n  const handlePaginationModelChange = React.useCallback(model => {\n    if (props.paginationMode === 'client' || !previousPageSize.current) {\n      return;\n    }\n    if (model.pageSize !== previousPageSize.current) {\n      previousPageSize.current = model.pageSize;\n      if (rowCountState === -1) {\n        // Row count unknown and page size changed, reset the page\n        apiRef.current.setPage(0);\n      }\n    }\n  }, [props.paginationMode, previousPageSize, rowCountState, apiRef]);\n  useGridEvent(apiRef, 'paginationModelChange', handlePaginationModelChange);\n\n  /**\n   * EFFECTS\n   */\n  React.useEffect(() => {\n    if (props.paginationMode === 'client') {\n      apiRef.current.setRowCount(visibleTopLevelRowCount);\n    } else if (props.rowCount != null) {\n      apiRef.current.setRowCount(props.rowCount);\n    }\n  }, [apiRef, props.paginationMode, visibleTopLevelRowCount, props.rowCount]);\n  const isLastPage = paginationMeta.hasNextPage === false;\n  React.useEffect(() => {\n    if (isLastPage && rowCountState === -1) {\n      apiRef.current.setRowCount(paginationModel.pageSize * paginationModel.page + visibleTopLevelRowCount);\n    }\n  }, [apiRef, visibleTopLevelRowCount, isLastPage, rowCountState, paginationModel]);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}