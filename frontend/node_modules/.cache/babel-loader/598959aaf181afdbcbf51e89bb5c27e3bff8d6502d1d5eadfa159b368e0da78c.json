{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"id\", \"value\", \"formattedValue\", \"api\", \"field\", \"row\", \"rowNode\", \"colDef\", \"cellMode\", \"isEditable\", \"tabIndex\", \"className\", \"hasFocus\", \"isValidating\", \"isProcessingProps\", \"error\", \"onValueChange\", \"initialOpen\", \"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport { GridCellEditStopReasons } from \"../../models/params/gridEditCellParams.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { GridEditModes } from \"../../models/gridEditRowModel.js\";\nimport { getValueFromValueOptions, getValueOptions, isSingleSelectColDef } from \"../panel/filterPanel/filterPanelUtils.js\";\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { createElement as _createElement } from \"react\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction isKeyboardEvent(event) {\n  return !!event.key;\n}\nfunction GridEditSingleSelectCell(props) {\n  const rootProps = useGridRootProps();\n  const {\n      id,\n      value: valueProp,\n      field,\n      row,\n      colDef,\n      hasFocus,\n      error,\n      onValueChange,\n      initialOpen = rootProps.editMode === GridEditModes.Cell,\n      slotProps\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const apiRef = useGridApiContext();\n  const ref = React.useRef(null);\n  const inputRef = React.useRef(null);\n  const [open, setOpen] = React.useState(initialOpen);\n  const baseSelectProps = rootProps.slotProps?.baseSelect || {};\n  const isSelectNative = baseSelectProps.native ?? false;\n  useEnhancedEffect(() => {\n    if (hasFocus) {\n      inputRef.current?.focus();\n    }\n  }, [hasFocus]);\n  if (!isSingleSelectColDef(colDef)) {\n    return null;\n  }\n  const valueOptions = getValueOptions(colDef, {\n    id,\n    row\n  });\n  if (!valueOptions) {\n    return null;\n  }\n  const getOptionValue = colDef.getOptionValue;\n  const getOptionLabel = colDef.getOptionLabel;\n  const handleChange = async event => {\n    if (!isSingleSelectColDef(colDef) || !valueOptions) {\n      return;\n    }\n    setOpen(false);\n    const target = event.target;\n    // NativeSelect casts the value to a string.\n    const formattedTargetValue = getValueFromValueOptions(target.value, valueOptions, getOptionValue);\n    if (onValueChange) {\n      await onValueChange(event, formattedTargetValue);\n    }\n    await apiRef.current.setEditCellValue({\n      id,\n      field,\n      value: formattedTargetValue\n    }, event);\n  };\n  const handleClose = (event, reason) => {\n    if (rootProps.editMode === GridEditModes.Row) {\n      setOpen(false);\n      return;\n    }\n    if (reason === 'backdropClick' || event.key === 'Escape') {\n      const params = apiRef.current.getCellParams(id, field);\n      apiRef.current.publishEvent('cellEditStop', _extends({}, params, {\n        reason: event.key === 'Escape' ? GridCellEditStopReasons.escapeKeyDown : GridCellEditStopReasons.cellFocusOut\n      }));\n    }\n  };\n  const handleOpen = event => {\n    if (isKeyboardEvent(event) && event.key === 'Enter') {\n      return;\n    }\n    setOpen(true);\n  };\n  if (!valueOptions || !colDef) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(rootProps.slots.baseSelect, _extends({\n    ref: ref,\n    value: valueProp,\n    onChange: handleChange,\n    open: open,\n    onOpen: handleOpen,\n    onClose: handleClose,\n    error: error,\n    native: isSelectNative,\n    fullWidth: true,\n    slotProps: {\n      htmlInput: {\n        ref: inputRef\n      }\n    }\n  }, other, slotProps?.root, rootProps.slotProps?.baseSelect, {\n    children: valueOptions.map(valueOption => {\n      const value = getOptionValue(valueOption);\n      return /*#__PURE__*/_createElement(rootProps.slots.baseSelectOption, _extends({}, rootProps.slotProps?.baseSelectOption || {}, {\n        native: isSelectNative,\n        key: value,\n        value: value\n      }), getOptionLabel(valueOption));\n    })\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? GridEditSingleSelectCell.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * GridApi that let you manipulate the grid.\n   */\n  api: PropTypes.object.isRequired,\n  /**\n   * The mode of the cell.\n   */\n  cellMode: PropTypes.oneOf(['edit', 'view']).isRequired,\n  changeReason: PropTypes.oneOf(['debouncedSetEditCellValue', 'setEditCellValue']),\n  /**\n   * The column of the row that the current cell belongs to.\n   */\n  colDef: PropTypes.object.isRequired,\n  /**\n   * The column field of the cell that triggered the event.\n   */\n  field: PropTypes.string.isRequired,\n  /**\n   * The cell value formatted with the column valueFormatter.\n   */\n  formattedValue: PropTypes.any,\n  /**\n   * If true, the cell is the active element.\n   */\n  hasFocus: PropTypes.bool.isRequired,\n  /**\n   * The grid row id.\n   */\n  id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,\n  /**\n   * If true, the select opens by default.\n   */\n  initialOpen: PropTypes.bool,\n  /**\n   * If true, the cell is editable.\n   */\n  isEditable: PropTypes.bool,\n  isProcessingProps: PropTypes.bool,\n  isValidating: PropTypes.bool,\n  /**\n   * Callback called when the value is changed by the user.\n   * @param {Event<any>} event The event source of the callback.\n   * @param {any} newValue The value that is going to be passed to `apiRef.current.setEditCellValue`.\n   * @returns {Promise<void> | void} A promise to be awaited before calling `apiRef.current.setEditCellValue`\n   */\n  onValueChange: PropTypes.func,\n  /**\n   * The row model of the row that the current cell belongs to.\n   */\n  row: PropTypes.any.isRequired,\n  /**\n   * The node of the row that the current cell belongs to.\n   */\n  rowNode: PropTypes.object.isRequired,\n  /**\n   * the tabIndex value.\n   */\n  tabIndex: PropTypes.oneOf([-1, 0]).isRequired,\n  /**\n   * The cell value.\n   * If the column has `valueGetter`, use `params.row` to directly access the fields.\n   */\n  value: PropTypes.any\n} : void 0;\nexport { GridEditSingleSelectCell };\nexport const renderEditSingleSelectCell = params => /*#__PURE__*/_jsx(GridEditSingleSelectCell, _extends({}, params));\nif (process.env.NODE_ENV !== \"production\") renderEditSingleSelectCell.displayName = \"renderEditSingleSelectCell\";", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}