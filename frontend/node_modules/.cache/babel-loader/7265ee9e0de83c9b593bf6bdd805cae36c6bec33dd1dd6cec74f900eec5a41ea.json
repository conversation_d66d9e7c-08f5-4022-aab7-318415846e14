{"ast": null, "code": "import { gridClasses } from \"../constants/gridClasses.js\";\nexport function isOverflown(element) {\n  return element.scrollHeight > element.clientHeight || element.scrollWidth > element.clientWidth;\n}\nexport function findParentElementFromClassName(elem, className) {\n  return elem.closest(`.${className}`);\n}\n\n// TODO, eventually replaces this function with CSS.escape, once available in jsdom, either added manually or built in\n// https://github.com/jsdom/jsdom/issues/1550#issuecomment-236734471\nexport function escapeOperandAttributeSelector(operand) {\n  return operand.replace(/[\"\\\\]/g, '\\\\$&');\n}\nexport function getGridColumnHeaderElement(root, field) {\n  return root.querySelector(`[role=\"columnheader\"][data-field=\"${escapeOperandAttributeSelector(field)}\"]`);\n}\nfunction getGridRowElementSelector(id) {\n  return `.${gridClasses.row}[data-id=\"${escapeOperandAttributeSelector(String(id))}\"]`;\n}\nexport function getGridRowElement(root, id) {\n  return root.querySelector(getGridRowElementSelector(id));\n}\nexport function getGridCellElement(root, {\n  id,\n  field\n}) {\n  const rowSelector = getGridRowElementSelector(id);\n  const cellSelector = `.${gridClasses.cell}[data-field=\"${escapeOperandAttributeSelector(field)}\"]`;\n  const selector = `${rowSelector} ${cellSelector}`;\n  return root.querySelector(selector);\n}\n\n// https://www.abeautifulsite.net/posts/finding-the-active-element-in-a-shadow-root/\nexport const getActiveElement = (root = document) => {\n  const activeEl = root.activeElement;\n  if (!activeEl) {\n    return null;\n  }\n  if (activeEl.shadowRoot) {\n    return getActiveElement(activeEl.shadowRoot);\n  }\n  return activeEl;\n};\nexport function isEventTargetInPortal(event) {\n  if (\n  // The target is not an element when triggered by a Select inside the cell\n  // See https://github.com/mui/material-ui/issues/10534\n  event.target.nodeType === 1 && !event.currentTarget.contains(event.target)) {\n    return true;\n  }\n  return false;\n}\nexport function getFieldFromHeaderElem(colCellEl) {\n  return colCellEl.getAttribute('data-field');\n}\nexport function findHeaderElementFromField(elem, field) {\n  return elem.querySelector(`[data-field=\"${escapeOperandAttributeSelector(field)}\"]`);\n}\nexport function getFieldsFromGroupHeaderElem(colCellEl) {\n  return colCellEl.getAttribute('data-fields').slice(2, -2).split('-|-');\n}\nexport function findGroupHeaderElementsFromField(elem, field) {\n  return Array.from(elem.querySelectorAll(`[data-fields*=\"|-${escapeOperandAttributeSelector(field)}-|\"]`) ?? []);\n}\nexport function findGridCellElementsFromCol(col, api) {\n  const root = findParentElementFromClassName(col, gridClasses.root);\n  if (!root) {\n    throw new Error('MUI X: The root element is not found.');\n  }\n  const ariaColIndex = col.getAttribute('aria-colindex');\n  if (!ariaColIndex) {\n    return [];\n  }\n  const colIndex = Number(ariaColIndex) - 1;\n  const cells = [];\n  if (!api.virtualScrollerRef?.current) {\n    return [];\n  }\n  queryRows(api).forEach(rowElement => {\n    const rowId = rowElement.getAttribute('data-id');\n    if (!rowId) {\n      return;\n    }\n    let columnIndex = colIndex;\n    const cellColSpanInfo = api.unstable_getCellColSpanInfo(rowId, colIndex);\n    if (cellColSpanInfo && cellColSpanInfo.spannedByColSpan) {\n      columnIndex = cellColSpanInfo.leftVisibleCellIndex;\n    }\n    const cell = rowElement.querySelector(`[data-colindex=\"${columnIndex}\"]`);\n    if (cell) {\n      cells.push(cell);\n    }\n  });\n  return cells;\n}\nexport function findGridElement(api, klass) {\n  return api.rootElementRef.current.querySelector(`.${gridClasses[klass]}`);\n}\nconst findPinnedCells = ({\n  api,\n  colIndex,\n  position,\n  filterFn\n}) => {\n  if (colIndex === null) {\n    return [];\n  }\n  const cells = [];\n  queryRows(api).forEach(rowElement => {\n    const rowId = rowElement.getAttribute('data-id');\n    if (!rowId) {\n      return;\n    }\n    rowElement.querySelectorAll(`.${gridClasses[position === 'left' ? 'cell--pinnedLeft' : 'cell--pinnedRight']}`).forEach(cell => {\n      const currentColIndex = parseCellColIndex(cell);\n      if (currentColIndex !== null && filterFn(currentColIndex)) {\n        cells.push(cell);\n      }\n    });\n  });\n  return cells;\n};\nexport function findLeftPinnedCellsAfterCol(api, col, isRtl) {\n  const colIndex = parseCellColIndex(col);\n  return findPinnedCells({\n    api,\n    colIndex,\n    position: isRtl ? 'right' : 'left',\n    filterFn: index => isRtl ? index < colIndex : index > colIndex\n  });\n}\nexport function findRightPinnedCellsBeforeCol(api, col, isRtl) {\n  const colIndex = parseCellColIndex(col);\n  return findPinnedCells({\n    api,\n    colIndex,\n    position: isRtl ? 'left' : 'right',\n    filterFn: index => isRtl ? index > colIndex : index < colIndex\n  });\n}\nconst findPinnedHeaders = ({\n  api,\n  colIndex,\n  position,\n  filterFn\n}) => {\n  if (!api.columnHeadersContainerRef?.current) {\n    return [];\n  }\n  if (colIndex === null) {\n    return [];\n  }\n  const elements = [];\n  api.columnHeadersContainerRef.current.querySelectorAll(`.${gridClasses[position === 'left' ? 'columnHeader--pinnedLeft' : 'columnHeader--pinnedRight']}`).forEach(element => {\n    const currentColIndex = parseCellColIndex(element);\n    if (currentColIndex !== null && filterFn(currentColIndex, element)) {\n      elements.push(element);\n    }\n  });\n  return elements;\n};\nexport function findLeftPinnedHeadersAfterCol(api, col, isRtl) {\n  const colIndex = parseCellColIndex(col);\n  return findPinnedHeaders({\n    api,\n    position: isRtl ? 'right' : 'left',\n    colIndex,\n    filterFn: index => isRtl ? index < colIndex : index > colIndex\n  });\n}\nexport function findRightPinnedHeadersBeforeCol(api, col, isRtl) {\n  const colIndex = parseCellColIndex(col);\n  return findPinnedHeaders({\n    api,\n    position: isRtl ? 'left' : 'right',\n    colIndex,\n    filterFn: (index, element) => {\n      if (element.classList.contains(gridClasses['columnHeader--last'])) {\n        return false;\n      }\n      return isRtl ? index > colIndex : index < colIndex;\n    }\n  });\n}\nexport function findGridHeader(api, field) {\n  const headers = api.columnHeadersContainerRef.current;\n  return headers.querySelector(`:scope > div > [data-field=\"${escapeOperandAttributeSelector(field)}\"][role=\"columnheader\"]`);\n}\nexport function findGridCells(api, field) {\n  const container = api.virtualScrollerRef.current;\n  return Array.from(container.querySelectorAll(`:scope > div > div > div > [data-field=\"${escapeOperandAttributeSelector(field)}\"][role=\"gridcell\"]`));\n}\nfunction queryRows(api) {\n  return api.virtualScrollerRef.current.querySelectorAll(\n  // Use > to ignore rows from nested Data Grids (for example in detail panel)\n  `:scope > div > div > .${gridClasses.row}`);\n}\nfunction parseCellColIndex(col) {\n  const ariaColIndex = col.getAttribute('aria-colindex');\n  if (!ariaColIndex) {\n    return null;\n  }\n  return Number(ariaColIndex) - 1;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}