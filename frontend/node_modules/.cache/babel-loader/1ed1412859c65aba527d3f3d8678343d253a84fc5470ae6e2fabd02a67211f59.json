{"ast": null, "code": "import * as React from 'react';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { GRID_TREE_DATA_GROUPING_FIELD, GRID_DETAIL_PANEL_TOGGLE_FIELD } from \"../../../internals/constants.js\";\nimport { isGroupingColumn } from \"../../../internals/utils/gridRowGroupingUtils.js\";\nimport { gridVisibleColumnDefinitionsSelector, gridVisibleColumnFieldsSelector } from \"../columns/gridColumnsSelector.js\";\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { useGridEvent } from \"../../utils/useGridEvent.js\";\nimport { gridExpandedSortedRowEntriesSelector } from \"../filter/gridFilterSelector.js\";\nimport { GRID_CHECKBOX_SELECTION_COL_DEF } from \"../../../colDef/gridCheckboxSelectionColDef.js\";\nimport { gridClasses } from \"../../../constants/gridClasses.js\";\nimport { GridCellModes } from \"../../../models/gridEditRowModel.js\";\nimport { isNavigationKey } from \"../../../utils/keyboardUtils.js\";\nimport { gridFocusColumnGroupHeaderSelector } from \"../focus/index.js\";\nimport { gridColumnGroupsHeaderMaxDepthSelector } from \"../columnGrouping/gridColumnGroupsSelector.js\";\nimport { gridHeaderFilteringEditFieldSelector, gridHeaderFilteringMenuSelector } from \"../headerFiltering/gridHeaderFilteringSelectors.js\";\nimport { useGridRegisterPipeProcessor } from \"../../core/pipeProcessing/index.js\";\nimport { isEventTargetInPortal } from \"../../../utils/domUtils.js\";\nimport { getLeftColumnIndex, getRightColumnIndex, findNonRowSpannedCell } from \"./utils.js\";\nimport { gridListColumnSelector } from \"../listView/gridListViewSelectors.js\";\nimport { createSelectorMemoized } from \"../../../utils/createSelector.js\";\nimport { gridVisibleRowsSelector } from \"../pagination/index.js\";\nimport { gridPinnedRowsSelector } from \"../rows/gridRowsSelector.js\";\nconst gridVisibleRowsWithPinnedRowsSelector = createSelectorMemoized(gridVisibleRowsSelector, gridPinnedRowsSelector, (visibleRows, pinnedRows) => {\n  return (pinnedRows.top || []).concat(visibleRows.rows, pinnedRows.bottom || []);\n});\n\n/**\n * @requires useGridSorting (method) - can be after\n * @requires useGridFilter (state) - can be after\n * @requires useGridColumns (state, method) - can be after\n * @requires useGridDimensions (method) - can be after\n * @requires useGridFocus (method) - can be after\n * @requires useGridScroll (method) - can be after\n * @requires useGridColumnSpanning (method) - can be after\n */\nexport const useGridKeyboardNavigation = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridKeyboardNavigation');\n  const isRtl = useRtl();\n  const listView = props.listView;\n  const getCurrentPageRows = React.useCallback(() => {\n    return gridVisibleRowsWithPinnedRowsSelector(apiRef);\n  }, [apiRef]);\n  const headerFilteringEnabled = props.signature !== 'DataGrid' && props.headerFilters;\n\n  /**\n   * @param {number} colIndex Index of the column to focus\n   * @param {GridRowId} rowId index of the row to focus\n   * @param {string} closestColumnToUse Which closest column cell to use when the cell is spanned by `colSpan`.\n   * @param {string} rowSpanScanDirection Which direction to search to find the next cell not hidden by `rowSpan`.\n   * TODO replace with apiRef.current.moveFocusToRelativeCell()\n   */\n  const goToCell = React.useCallback((colIndex, rowId, closestColumnToUse = 'left', rowSpanScanDirection = 'up') => {\n    const visibleSortedRows = gridExpandedSortedRowEntriesSelector(apiRef);\n    const nextCellColSpanInfo = apiRef.current.unstable_getCellColSpanInfo(rowId, colIndex);\n    if (nextCellColSpanInfo && nextCellColSpanInfo.spannedByColSpan) {\n      if (closestColumnToUse === 'left') {\n        colIndex = nextCellColSpanInfo.leftVisibleCellIndex;\n      } else if (closestColumnToUse === 'right') {\n        colIndex = nextCellColSpanInfo.rightVisibleCellIndex;\n      }\n    }\n    const field = listView ? gridListColumnSelector(apiRef).field : gridVisibleColumnFieldsSelector(apiRef)[colIndex];\n    const nonRowSpannedRowId = findNonRowSpannedCell(apiRef, rowId, field, rowSpanScanDirection);\n    // `scrollToIndexes` requires a rowIndex relative to all visible rows.\n    // Those rows do not include pinned rows, but pinned rows do not need scroll anyway.\n    const rowIndexRelativeToAllRows = visibleSortedRows.findIndex(row => row.id === nonRowSpannedRowId);\n    logger.debug(`Navigating to cell row ${rowIndexRelativeToAllRows}, col ${colIndex}`);\n    apiRef.current.scrollToIndexes({\n      colIndex,\n      rowIndex: rowIndexRelativeToAllRows\n    });\n    apiRef.current.setCellFocus(nonRowSpannedRowId, field);\n  }, [apiRef, logger, listView]);\n  const goToHeader = React.useCallback((colIndex, event) => {\n    logger.debug(`Navigating to header col ${colIndex}`);\n    apiRef.current.scrollToIndexes({\n      colIndex\n    });\n    const field = apiRef.current.getVisibleColumns()[colIndex].field;\n    apiRef.current.setColumnHeaderFocus(field, event);\n  }, [apiRef, logger]);\n  const goToHeaderFilter = React.useCallback((colIndex, event) => {\n    logger.debug(`Navigating to header filter col ${colIndex}`);\n    apiRef.current.scrollToIndexes({\n      colIndex\n    });\n    const field = apiRef.current.getVisibleColumns()[colIndex].field;\n    apiRef.current.setColumnHeaderFilterFocus(field, event);\n  }, [apiRef, logger]);\n  const goToGroupHeader = React.useCallback((colIndex, depth, event) => {\n    logger.debug(`Navigating to header col ${colIndex}`);\n    apiRef.current.scrollToIndexes({\n      colIndex\n    });\n    const {\n      field\n    } = apiRef.current.getVisibleColumns()[colIndex];\n    apiRef.current.setColumnGroupHeaderFocus(field, depth, event);\n  }, [apiRef, logger]);\n  const getRowIdFromIndex = React.useCallback(rowIndex => {\n    return getCurrentPageRows()[rowIndex]?.id;\n  }, [getCurrentPageRows]);\n  const handleColumnHeaderKeyDown = React.useCallback((params, event) => {\n    const headerTitleNode = event.currentTarget.querySelector(`.${gridClasses.columnHeaderTitleContainerContent}`);\n    const isFromInsideContent = !!headerTitleNode && headerTitleNode.contains(event.target);\n    if (isFromInsideContent && params.field !== GRID_CHECKBOX_SELECTION_COL_DEF.field) {\n      // When focus is on a nested input, keyboard events have no effect to avoid conflicts with native events.\n      // There is one exception for the checkBoxHeader\n      return;\n    }\n    const currentPageRows = getCurrentPageRows();\n    const viewportPageSize = apiRef.current.getViewportPageSize();\n    const colIndexBefore = params.field ? apiRef.current.getColumnIndex(params.field) : 0;\n    const firstRowIndexInPage = currentPageRows.length > 0 ? 0 : null;\n    const lastRowIndexInPage = currentPageRows.length - 1;\n    const firstColIndex = 0;\n    const lastColIndex = gridVisibleColumnDefinitionsSelector(apiRef).length - 1;\n    const columnGroupMaxDepth = gridColumnGroupsHeaderMaxDepthSelector(apiRef);\n    let shouldPreventDefault = true;\n    switch (event.key) {\n      case 'ArrowDown':\n        {\n          if (headerFilteringEnabled) {\n            goToHeaderFilter(colIndexBefore, event);\n          } else if (firstRowIndexInPage !== null) {\n            goToCell(colIndexBefore, getRowIdFromIndex(firstRowIndexInPage));\n          }\n          break;\n        }\n      case 'ArrowRight':\n        {\n          const rightColIndex = getRightColumnIndex({\n            currentColIndex: colIndexBefore,\n            firstColIndex,\n            lastColIndex,\n            isRtl\n          });\n          if (rightColIndex !== null) {\n            goToHeader(rightColIndex, event);\n          }\n          break;\n        }\n      case 'ArrowLeft':\n        {\n          const leftColIndex = getLeftColumnIndex({\n            currentColIndex: colIndexBefore,\n            firstColIndex,\n            lastColIndex,\n            isRtl\n          });\n          if (leftColIndex !== null) {\n            goToHeader(leftColIndex, event);\n          }\n          break;\n        }\n      case 'ArrowUp':\n        {\n          if (columnGroupMaxDepth > 0) {\n            goToGroupHeader(colIndexBefore, columnGroupMaxDepth - 1, event);\n          }\n          break;\n        }\n      case 'PageDown':\n        {\n          if (firstRowIndexInPage !== null && lastRowIndexInPage !== null) {\n            goToCell(colIndexBefore, getRowIdFromIndex(Math.min(firstRowIndexInPage + viewportPageSize, lastRowIndexInPage)));\n          }\n          break;\n        }\n      case 'Home':\n        {\n          goToHeader(firstColIndex, event);\n          break;\n        }\n      case 'End':\n        {\n          goToHeader(lastColIndex, event);\n          break;\n        }\n      case 'Enter':\n        {\n          if (event.ctrlKey || event.metaKey) {\n            apiRef.current.toggleColumnMenu(params.field);\n          }\n          break;\n        }\n      case ' ':\n        {\n          // prevent Space event from scrolling\n          break;\n        }\n      default:\n        {\n          shouldPreventDefault = false;\n        }\n    }\n    if (shouldPreventDefault) {\n      event.preventDefault();\n    }\n  }, [apiRef, getCurrentPageRows, headerFilteringEnabled, goToHeaderFilter, goToCell, getRowIdFromIndex, isRtl, goToHeader, goToGroupHeader]);\n  const handleHeaderFilterKeyDown = React.useCallback((params, event) => {\n    const isEditing = gridHeaderFilteringEditFieldSelector(apiRef) === params.field;\n    const isHeaderMenuOpen = gridHeaderFilteringMenuSelector(apiRef) === params.field;\n    if (isEditing || isHeaderMenuOpen || !isNavigationKey(event.key)) {\n      return;\n    }\n    const currentPageRows = getCurrentPageRows();\n    const viewportPageSize = apiRef.current.getViewportPageSize();\n    const colIndexBefore = params.field ? apiRef.current.getColumnIndex(params.field) : 0;\n    const firstRowIndexInPage = 0;\n    const lastRowIndexInPage = currentPageRows.length - 1;\n    const firstColIndex = 0;\n    const lastColIndex = gridVisibleColumnDefinitionsSelector(apiRef).length - 1;\n    let shouldPreventDefault = true;\n    switch (event.key) {\n      case 'ArrowDown':\n        {\n          const rowId = getRowIdFromIndex(firstRowIndexInPage);\n          if (firstRowIndexInPage !== null && rowId != null) {\n            goToCell(colIndexBefore, rowId);\n          }\n          break;\n        }\n      case 'ArrowRight':\n        {\n          const rightColIndex = getRightColumnIndex({\n            currentColIndex: colIndexBefore,\n            firstColIndex,\n            lastColIndex,\n            isRtl\n          });\n          if (rightColIndex !== null) {\n            goToHeaderFilter(rightColIndex, event);\n          }\n          break;\n        }\n      case 'ArrowLeft':\n        {\n          const leftColIndex = getLeftColumnIndex({\n            currentColIndex: colIndexBefore,\n            firstColIndex,\n            lastColIndex,\n            isRtl\n          });\n          if (leftColIndex !== null) {\n            goToHeaderFilter(leftColIndex, event);\n          } else {\n            apiRef.current.setColumnHeaderFilterFocus(params.field, event);\n          }\n          break;\n        }\n      case 'ArrowUp':\n        {\n          goToHeader(colIndexBefore, event);\n          break;\n        }\n      case 'PageDown':\n        {\n          if (firstRowIndexInPage !== null && lastRowIndexInPage !== null) {\n            goToCell(colIndexBefore, getRowIdFromIndex(Math.min(firstRowIndexInPage + viewportPageSize, lastRowIndexInPage)));\n          }\n          break;\n        }\n      case 'Home':\n        {\n          goToHeaderFilter(firstColIndex, event);\n          break;\n        }\n      case 'End':\n        {\n          goToHeaderFilter(lastColIndex, event);\n          break;\n        }\n      case ' ':\n        {\n          // prevent Space event from scrolling\n          break;\n        }\n      default:\n        {\n          shouldPreventDefault = false;\n        }\n    }\n    if (shouldPreventDefault) {\n      event.preventDefault();\n    }\n  }, [apiRef, getCurrentPageRows, goToHeaderFilter, isRtl, goToHeader, goToCell, getRowIdFromIndex]);\n  const handleColumnGroupHeaderKeyDown = React.useCallback((params, event) => {\n    const focusedColumnGroup = gridFocusColumnGroupHeaderSelector(apiRef);\n    if (focusedColumnGroup === null) {\n      return;\n    }\n    const {\n      field: currentField,\n      depth: currentDepth\n    } = focusedColumnGroup;\n    const {\n      fields,\n      depth,\n      maxDepth\n    } = params;\n    const currentPageRows = getCurrentPageRows();\n    const viewportPageSize = apiRef.current.getViewportPageSize();\n    const currentColIndex = apiRef.current.getColumnIndex(currentField);\n    const colIndexBefore = currentField ? apiRef.current.getColumnIndex(currentField) : 0;\n    const firstRowIndexInPage = 0;\n    const lastRowIndexInPage = currentPageRows.length - 1;\n    const firstColIndex = 0;\n    const lastColIndex = gridVisibleColumnDefinitionsSelector(apiRef).length - 1;\n    let shouldPreventDefault = true;\n    switch (event.key) {\n      case 'ArrowDown':\n        {\n          if (depth === maxDepth - 1) {\n            goToHeader(currentColIndex, event);\n          } else {\n            goToGroupHeader(currentColIndex, currentDepth + 1, event);\n          }\n          break;\n        }\n      case 'ArrowUp':\n        {\n          if (depth > 0) {\n            goToGroupHeader(currentColIndex, currentDepth - 1, event);\n          }\n          break;\n        }\n      case 'ArrowRight':\n        {\n          const remainingRightColumns = fields.length - fields.indexOf(currentField) - 1;\n          if (currentColIndex + remainingRightColumns + 1 <= lastColIndex) {\n            goToGroupHeader(currentColIndex + remainingRightColumns + 1, currentDepth, event);\n          }\n          break;\n        }\n      case 'ArrowLeft':\n        {\n          const remainingLeftColumns = fields.indexOf(currentField);\n          if (currentColIndex - remainingLeftColumns - 1 >= firstColIndex) {\n            goToGroupHeader(currentColIndex - remainingLeftColumns - 1, currentDepth, event);\n          }\n          break;\n        }\n      case 'PageDown':\n        {\n          if (firstRowIndexInPage !== null && lastRowIndexInPage !== null) {\n            goToCell(colIndexBefore, getRowIdFromIndex(Math.min(firstRowIndexInPage + viewportPageSize, lastRowIndexInPage)));\n          }\n          break;\n        }\n      case 'Home':\n        {\n          goToGroupHeader(firstColIndex, currentDepth, event);\n          break;\n        }\n      case 'End':\n        {\n          goToGroupHeader(lastColIndex, currentDepth, event);\n          break;\n        }\n      case ' ':\n        {\n          // prevent Space event from scrolling\n          break;\n        }\n      default:\n        {\n          shouldPreventDefault = false;\n        }\n    }\n    if (shouldPreventDefault) {\n      event.preventDefault();\n    }\n  }, [apiRef, getCurrentPageRows, goToHeader, goToGroupHeader, goToCell, getRowIdFromIndex]);\n  const handleCellKeyDown = React.useCallback((params, event) => {\n    // Ignore portal\n    if (isEventTargetInPortal(event)) {\n      return;\n    }\n\n    // Get the most recent params because the cell mode may have changed by another listener\n    const cellParams = apiRef.current.getCellParams(params.id, params.field);\n    if (cellParams.cellMode === GridCellModes.Edit || !isNavigationKey(event.key)) {\n      return;\n    }\n    const canUpdateFocus = apiRef.current.unstable_applyPipeProcessors('canUpdateFocus', true, {\n      event,\n      cell: cellParams\n    });\n    if (!canUpdateFocus) {\n      return;\n    }\n    const currentPageRows = getCurrentPageRows();\n    if (currentPageRows.length === 0) {\n      return;\n    }\n    const viewportPageSize = apiRef.current.getViewportPageSize();\n    const getColumnIndexFn = listView ? () => 0 : apiRef.current.getColumnIndex;\n    const colIndexBefore = params.field ? getColumnIndexFn(params.field) : 0;\n    const rowIndexBefore = currentPageRows.findIndex(row => row.id === params.id);\n    const firstRowIndexInPage = 0;\n    const lastRowIndexInPage = currentPageRows.length - 1;\n    const firstColIndex = 0;\n    const visibleColumns = listView ? [gridListColumnSelector(apiRef)] : gridVisibleColumnDefinitionsSelector(apiRef);\n    const lastColIndex = visibleColumns.length - 1;\n    let shouldPreventDefault = true;\n    switch (event.key) {\n      case 'ArrowDown':\n        {\n          // \"Enter\" is only triggered by the row / cell editing feature\n          if (rowIndexBefore < lastRowIndexInPage) {\n            goToCell(colIndexBefore, getRowIdFromIndex(rowIndexBefore + 1), isRtl ? 'right' : 'left', 'down');\n          }\n          break;\n        }\n      case 'ArrowUp':\n        {\n          if (rowIndexBefore > firstRowIndexInPage) {\n            goToCell(colIndexBefore, getRowIdFromIndex(rowIndexBefore - 1));\n          } else if (headerFilteringEnabled) {\n            goToHeaderFilter(colIndexBefore, event);\n          } else {\n            goToHeader(colIndexBefore, event);\n          }\n          break;\n        }\n      case 'ArrowRight':\n        {\n          const rightColIndex = getRightColumnIndex({\n            currentColIndex: colIndexBefore,\n            firstColIndex,\n            lastColIndex,\n            isRtl\n          });\n          if (rightColIndex !== null) {\n            goToCell(rightColIndex, getRowIdFromIndex(rowIndexBefore), isRtl ? 'left' : 'right');\n          }\n          break;\n        }\n      case 'ArrowLeft':\n        {\n          const leftColIndex = getLeftColumnIndex({\n            currentColIndex: colIndexBefore,\n            firstColIndex,\n            lastColIndex,\n            isRtl\n          });\n          if (leftColIndex !== null) {\n            goToCell(leftColIndex, getRowIdFromIndex(rowIndexBefore), isRtl ? 'right' : 'left');\n          }\n          break;\n        }\n      case 'Tab':\n        {\n          // \"Tab\" is only triggered by the row / cell editing feature\n          if (event.shiftKey && colIndexBefore > firstColIndex) {\n            goToCell(colIndexBefore - 1, getRowIdFromIndex(rowIndexBefore), 'left');\n          } else if (!event.shiftKey && colIndexBefore < lastColIndex) {\n            goToCell(colIndexBefore + 1, getRowIdFromIndex(rowIndexBefore), 'right');\n          }\n          break;\n        }\n      case ' ':\n        {\n          const field = params.field;\n          if (field === GRID_DETAIL_PANEL_TOGGLE_FIELD) {\n            break;\n          }\n          const colDef = params.colDef;\n          if (colDef && (colDef.field === GRID_TREE_DATA_GROUPING_FIELD || isGroupingColumn(colDef.field))) {\n            break;\n          }\n          if (!event.shiftKey && rowIndexBefore < lastRowIndexInPage) {\n            goToCell(colIndexBefore, getRowIdFromIndex(Math.min(rowIndexBefore + viewportPageSize, lastRowIndexInPage)));\n          }\n          break;\n        }\n      case 'PageDown':\n        {\n          if (rowIndexBefore < lastRowIndexInPage) {\n            goToCell(colIndexBefore, getRowIdFromIndex(Math.min(rowIndexBefore + viewportPageSize, lastRowIndexInPage)));\n          }\n          break;\n        }\n      case 'PageUp':\n        {\n          // Go to the first row before going to header\n          const nextRowIndex = Math.max(rowIndexBefore - viewportPageSize, firstRowIndexInPage);\n          if (nextRowIndex !== rowIndexBefore && nextRowIndex >= firstRowIndexInPage) {\n            goToCell(colIndexBefore, getRowIdFromIndex(nextRowIndex));\n          } else {\n            goToHeader(colIndexBefore, event);\n          }\n          break;\n        }\n      case 'Home':\n        {\n          if (event.ctrlKey || event.metaKey || event.shiftKey) {\n            goToCell(firstColIndex, getRowIdFromIndex(firstRowIndexInPage));\n          } else {\n            goToCell(firstColIndex, getRowIdFromIndex(rowIndexBefore));\n          }\n          break;\n        }\n      case 'End':\n        {\n          if (event.ctrlKey || event.metaKey || event.shiftKey) {\n            goToCell(lastColIndex, getRowIdFromIndex(lastRowIndexInPage));\n          } else {\n            goToCell(lastColIndex, getRowIdFromIndex(rowIndexBefore));\n          }\n          break;\n        }\n      default:\n        {\n          shouldPreventDefault = false;\n        }\n    }\n    if (shouldPreventDefault) {\n      event.preventDefault();\n    }\n  }, [apiRef, getCurrentPageRows, isRtl, goToCell, getRowIdFromIndex, headerFilteringEnabled, goToHeaderFilter, goToHeader, listView]);\n  const checkIfCanStartEditing = React.useCallback((initialValue, {\n    event\n  }) => {\n    if (event.key === ' ') {\n      // Space scrolls to the last row\n      return false;\n    }\n    return initialValue;\n  }, []);\n  useGridRegisterPipeProcessor(apiRef, 'canStartEditing', checkIfCanStartEditing);\n  useGridEvent(apiRef, 'columnHeaderKeyDown', handleColumnHeaderKeyDown);\n  useGridEvent(apiRef, 'headerFilterKeyDown', handleHeaderFilterKeyDown);\n  useGridEvent(apiRef, 'columnGroupHeaderKeyDown', handleColumnGroupHeaderKeyDown);\n  useGridEvent(apiRef, 'cellKeyDown', handleCellKeyDown);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}