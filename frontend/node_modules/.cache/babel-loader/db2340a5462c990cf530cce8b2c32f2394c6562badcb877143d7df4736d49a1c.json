{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"quickFilterParser\", \"quickFilterFormatter\", \"debounceMs\", \"className\", \"slotProps\"],\n  _excluded2 = [\"ref\", \"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from '@mui/system';\nimport clsx from 'clsx';\nimport { getDataGridUtilityClass } from \"../../constants/index.js\";\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { QuickFilter, QuickFilterClear, QuickFilterControl, QuickFilterTrigger } from \"../quickFilter/index.js\";\nimport { ToolbarButton } from \"../toolbarV8/index.js\";\nimport { vars } from \"../../constants/cssVariables.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['toolbarQuickFilter'],\n    trigger: ['toolbarQuickFilterTrigger'],\n    control: ['toolbarQuickFilterControl']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst GridQuickFilterRoot = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'ToolbarQuickFilter'\n})({\n  display: 'grid',\n  alignItems: 'center'\n});\nconst GridQuickFilterTrigger = styled(ToolbarButton, {\n  name: 'MuiDataGrid',\n  slot: 'ToolbarQuickFilterTrigger'\n})(({\n  ownerState\n}) => ({\n  gridArea: '1 / 1',\n  width: 'min-content',\n  height: 'min-content',\n  zIndex: 1,\n  opacity: ownerState.expanded ? 0 : 1,\n  pointerEvents: ownerState.expanded ? 'none' : 'auto',\n  transition: vars.transition(['opacity'])\n}));\n\n// TODO: Use NotRendered from /utils/assert\n// Currently causes react-docgen to fail\nconst GridQuickFilterTextField = styled(_props => {\n  throw new Error('Failed assertion: should not be rendered');\n}, {\n  name: 'MuiDataGrid',\n  slot: 'ToolbarQuickFilterControl'\n})(({\n  ownerState\n}) => ({\n  gridArea: '1 / 1',\n  overflowX: 'clip',\n  width: ownerState.expanded ? 260 : 'var(--trigger-width)',\n  opacity: ownerState.expanded ? 1 : 0,\n  transition: vars.transition(['width', 'opacity'])\n}));\n\n/**\n * @deprecated Use the {@link https://mui.com/x/react-data-grid/components/quick-filter/ Quick Filter} component instead. This component will be removed in a future major release.\n */\nfunction GridToolbarQuickFilter(props) {\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const ownerState = {\n    classes: rootProps.classes,\n    expanded: false\n  };\n  const classes = useUtilityClasses(ownerState);\n  const {\n      quickFilterParser,\n      quickFilterFormatter,\n      debounceMs,\n      className,\n      slotProps\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  return /*#__PURE__*/_jsx(QuickFilter, {\n    parser: quickFilterParser,\n    formatter: quickFilterFormatter,\n    debounceMs: debounceMs,\n    render: (quickFilterProps, state) => {\n      const currentOwnerState = _extends({}, ownerState, {\n        expanded: state.expanded\n      });\n      return /*#__PURE__*/_jsxs(GridQuickFilterRoot, _extends({}, quickFilterProps, {\n        className: clsx(classes.root, className),\n        children: [/*#__PURE__*/_jsx(QuickFilterTrigger, {\n          render: triggerProps => /*#__PURE__*/_jsx(rootProps.slots.baseTooltip, {\n            title: apiRef.current.getLocaleText('toolbarQuickFilterLabel'),\n            enterDelay: 0 // Prevents tooltip lagging behind transitioning trigger element\n            ,\n\n            children: /*#__PURE__*/_jsx(GridQuickFilterTrigger, _extends({\n              className: classes.trigger\n            }, triggerProps, {\n              ownerState: currentOwnerState,\n              color: \"default\",\n              \"aria-disabled\": state.expanded,\n              children: /*#__PURE__*/_jsx(rootProps.slots.quickFilterIcon, {\n                fontSize: \"small\"\n              })\n            }))\n          })\n        }), /*#__PURE__*/_jsx(QuickFilterControl, {\n          render: _ref => {\n            let {\n                ref,\n                slotProps: controlSlotProps\n              } = _ref,\n              controlProps = _objectWithoutPropertiesLoose(_ref, _excluded2);\n            return /*#__PURE__*/_jsx(GridQuickFilterTextField, _extends({\n              as: rootProps.slots.baseTextField,\n              className: classes.control,\n              ownerState: currentOwnerState,\n              inputRef: ref,\n              \"aria-label\": apiRef.current.getLocaleText('toolbarQuickFilterLabel'),\n              placeholder: apiRef.current.getLocaleText('toolbarQuickFilterPlaceholder'),\n              size: \"small\",\n              slotProps: _extends({\n                input: _extends({\n                  startAdornment: /*#__PURE__*/_jsx(rootProps.slots.quickFilterIcon, {\n                    fontSize: \"small\"\n                  }),\n                  endAdornment: controlProps.value ? /*#__PURE__*/_jsx(QuickFilterClear, {\n                    render: /*#__PURE__*/_jsx(rootProps.slots.baseIconButton, {\n                      size: \"small\",\n                      edge: \"end\",\n                      \"aria-label\": apiRef.current.getLocaleText('toolbarQuickFilterDeleteIconLabel'),\n                      children: /*#__PURE__*/_jsx(rootProps.slots.quickFilterClearIcon, {\n                        fontSize: \"small\"\n                      })\n                    })\n                  }) : null\n                }, controlSlotProps?.input)\n              }, controlSlotProps)\n            }, rootProps.slotProps?.baseTextField, controlProps, slotProps?.root, other));\n          }\n        })]\n      }));\n    }\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GridToolbarQuickFilter.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  className: PropTypes.string,\n  /**\n   * The debounce time in milliseconds.\n   * @default 150\n   */\n  debounceMs: PropTypes.number,\n  /**\n   * Function responsible for formatting values of quick filter in a string when the model is modified\n   * @param {any[]} values The new values passed to the quick filter model\n   * @returns {string} The string to display in the text field\n   * @default (values: string[]) => values.join(' ')\n   */\n  quickFilterFormatter: PropTypes.func,\n  /**\n   * Function responsible for parsing text input in an array of independent values for quick filtering.\n   * @param {string} input The value entered by the user\n   * @returns {any[]} The array of value on which quick filter is applied\n   * @default (searchText: string) => searchText\n   *   .split(' ')\n   *   .filter((word) => word !== '')\n   */\n  quickFilterParser: PropTypes.func,\n  slotProps: PropTypes.object\n} : void 0;\n\n/**\n * Demos:\n * - [Filtering - overview](https://mui.com/x/react-data-grid/filtering/)\n * - [Filtering - quick filter](https://mui.com/x/react-data-grid/filtering/quick-filter/)\n *\n * API:\n * - [GridToolbarQuickFilter API](https://mui.com/x/api/data-grid/grid-toolbar-quick-filter/)\n */\nexport { GridToolbarQuickFilter };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}