{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n/* eslint-disable @typescript-eslint/no-use-before-define */\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from '@mui/material/styles';\nimport { inputBaseClasses } from '@mui/material/InputBase';\nimport { vars } from \"../../constants/cssVariables.js\";\nimport { gridColumnDefinitionsSelector, gridColumnVisibilityModelSelector, gridInitialColumnVisibilityModelSelector } from \"../../hooks/features/columns/gridColumnsSelector.js\";\nimport { useGridSelector } from \"../../hooks/utils/useGridSelector.js\";\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { checkColumnVisibilityModelsSame, defaultSearchPredicate } from \"./utils.js\";\nimport { NotRendered } from \"../../utils/assert.js\";\nimport { GridShadowScrollArea } from \"../GridShadowScrollArea.js\";\nimport { gridPivotActiveSelector, gridPivotInitialColumnsSelector } from \"../../hooks/features/pivoting/gridPivotingSelectors.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['columnsManagement'],\n    header: ['columnsManagementHeader'],\n    searchInput: ['columnsManagementSearchInput'],\n    footer: ['columnsManagementFooter'],\n    row: ['columnsManagementRow']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst collator = new Intl.Collator();\nfunction GridColumnsManagement(props) {\n  const apiRef = useGridApiContext();\n  const searchInputRef = React.useRef(null);\n  const initialColumnVisibilityModel = useGridSelector(apiRef, gridInitialColumnVisibilityModelSelector);\n  const columnVisibilityModel = useGridSelector(apiRef, gridColumnVisibilityModelSelector);\n  const rootProps = useGridRootProps();\n  const [searchValue, setSearchValue] = React.useState('');\n  const classes = useUtilityClasses(rootProps);\n  const columnDefinitions = useGridSelector(apiRef, gridColumnDefinitionsSelector);\n  const pivotActive = useGridSelector(apiRef, gridPivotActiveSelector);\n  const pivotInitialColumns = useGridSelector(apiRef, gridPivotInitialColumnsSelector);\n  const columns = React.useMemo(() => pivotActive ? Array.from(pivotInitialColumns.values()) : columnDefinitions, [pivotActive, pivotInitialColumns, columnDefinitions]);\n  const {\n    sort,\n    searchPredicate = defaultSearchPredicate,\n    autoFocusSearchField = true,\n    disableShowHideToggle = false,\n    disableResetButton = false,\n    toggleAllMode = 'all',\n    getTogglableColumns,\n    searchInputProps\n  } = props;\n  const isResetDisabled = React.useMemo(() => checkColumnVisibilityModelsSame(columnVisibilityModel, initialColumnVisibilityModel), [columnVisibilityModel, initialColumnVisibilityModel]);\n  const sortedColumns = React.useMemo(() => {\n    switch (sort) {\n      case 'asc':\n        return [...columns].sort((a, b) => collator.compare(a.headerName || a.field, b.headerName || b.field));\n      case 'desc':\n        return [...columns].sort((a, b) => -collator.compare(a.headerName || a.field, b.headerName || b.field));\n      default:\n        return columns;\n    }\n  }, [columns, sort]);\n  const toggleColumn = event => {\n    const {\n      name: field\n    } = event.target;\n    apiRef.current.setColumnVisibility(field, columnVisibilityModel[field] === false);\n  };\n  const currentColumns = React.useMemo(() => {\n    const togglableColumns = getTogglableColumns ? getTogglableColumns(sortedColumns) : null;\n    const togglableSortedColumns = togglableColumns ? sortedColumns.filter(({\n      field\n    }) => togglableColumns.includes(field)) : sortedColumns;\n    if (!searchValue) {\n      return togglableSortedColumns;\n    }\n    return togglableSortedColumns.filter(column => searchPredicate(column, searchValue.toLowerCase()));\n  }, [sortedColumns, searchValue, searchPredicate, getTogglableColumns]);\n  const toggleAllColumns = React.useCallback(isVisible => {\n    const currentModel = gridColumnVisibilityModelSelector(apiRef);\n    const newModel = _extends({}, currentModel);\n    const togglableColumns = getTogglableColumns ? getTogglableColumns(columns) : null;\n    (toggleAllMode === 'filteredOnly' ? currentColumns : columns).forEach(col => {\n      if (col.hideable && (togglableColumns == null || togglableColumns.includes(col.field))) {\n        if (isVisible) {\n          // delete the key from the model instead of setting it to `true`\n          delete newModel[col.field];\n        } else {\n          newModel[col.field] = false;\n        }\n      }\n    });\n    return apiRef.current.setColumnVisibilityModel(newModel);\n  }, [apiRef, columns, getTogglableColumns, toggleAllMode, currentColumns]);\n  const handleSearchValueChange = React.useCallback(event => {\n    setSearchValue(event.target.value);\n  }, []);\n  const hideableColumns = React.useMemo(() => currentColumns.filter(col => col.hideable), [currentColumns]);\n  const allHideableColumnsVisible = React.useMemo(() => hideableColumns.every(column => columnVisibilityModel[column.field] == null || columnVisibilityModel[column.field] !== false), [columnVisibilityModel, hideableColumns]);\n  const allHideableColumnsHidden = React.useMemo(() => hideableColumns.every(column => columnVisibilityModel[column.field] === false), [columnVisibilityModel, hideableColumns]);\n  const firstSwitchRef = React.useRef(null);\n  React.useEffect(() => {\n    if (autoFocusSearchField) {\n      searchInputRef.current?.focus();\n    } else if (firstSwitchRef.current && typeof firstSwitchRef.current.focus === 'function') {\n      firstSwitchRef.current.focus();\n    }\n  }, [autoFocusSearchField]);\n  let firstHideableColumnFound = false;\n  const isFirstHideableColumn = column => {\n    if (firstHideableColumnFound === false && column.hideable !== false) {\n      firstHideableColumnFound = true;\n      return true;\n    }\n    return false;\n  };\n  const handleSearchReset = React.useCallback(() => {\n    setSearchValue('');\n    searchInputRef.current?.focus();\n  }, []);\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(GridColumnsManagementHeader, {\n      className: classes.header,\n      ownerState: rootProps,\n      children: /*#__PURE__*/_jsx(SearchInput, _extends({\n        as: rootProps.slots.baseTextField,\n        ownerState: rootProps,\n        placeholder: apiRef.current.getLocaleText('columnsManagementSearchTitle'),\n        inputRef: searchInputRef,\n        className: classes.searchInput,\n        value: searchValue,\n        onChange: handleSearchValueChange,\n        size: \"small\",\n        type: \"search\",\n        slotProps: {\n          input: {\n            startAdornment: /*#__PURE__*/_jsx(rootProps.slots.quickFilterIcon, {\n              fontSize: \"small\"\n            }),\n            endAdornment: /*#__PURE__*/_jsx(rootProps.slots.baseIconButton, _extends({\n              size: \"small\",\n              \"aria-label\": apiRef.current.getLocaleText('columnsManagementDeleteIconLabel'),\n              style: searchValue ? {\n                visibility: 'visible'\n              } : {\n                visibility: 'hidden'\n              },\n              tabIndex: -1,\n              onClick: handleSearchReset,\n              edge: \"end\"\n            }, rootProps.slotProps?.baseIconButton, {\n              children: /*#__PURE__*/_jsx(rootProps.slots.quickFilterClearIcon, {\n                fontSize: \"small\"\n              })\n            }))\n          },\n          htmlInput: {\n            'aria-label': apiRef.current.getLocaleText('columnsManagementSearchTitle')\n          }\n        },\n        autoComplete: \"off\",\n        fullWidth: true\n      }, rootProps.slotProps?.baseTextField, searchInputProps))\n    }), /*#__PURE__*/_jsx(GridColumnsManagementScrollArea, {\n      ownerState: rootProps,\n      children: /*#__PURE__*/_jsxs(GridColumnsManagementBody, {\n        className: classes.root,\n        ownerState: rootProps,\n        children: [currentColumns.map(column => /*#__PURE__*/_jsx(rootProps.slots.baseCheckbox, _extends({\n          className: classes.row,\n          disabled: column.hideable === false || pivotActive,\n          checked: columnVisibilityModel[column.field] !== false,\n          onChange: toggleColumn,\n          name: column.field,\n          inputRef: isFirstHideableColumn(column) ? firstSwitchRef : undefined,\n          label: column.headerName || column.field,\n          density: \"compact\",\n          fullWidth: true\n        }, rootProps.slotProps?.baseCheckbox), column.field)), currentColumns.length === 0 && /*#__PURE__*/_jsx(GridColumnsManagementEmptyText, {\n          ownerState: rootProps,\n          children: apiRef.current.getLocaleText('columnsManagementNoColumns')\n        })]\n      })\n    }), !disableShowHideToggle || !disableResetButton ? /*#__PURE__*/_jsxs(GridColumnsManagementFooter, {\n      ownerState: rootProps,\n      className: classes.footer,\n      children: [!disableShowHideToggle ? /*#__PURE__*/_jsx(rootProps.slots.baseCheckbox, _extends({\n        disabled: hideableColumns.length === 0 || pivotActive,\n        checked: allHideableColumnsVisible,\n        indeterminate: !allHideableColumnsVisible && !allHideableColumnsHidden,\n        onChange: () => toggleAllColumns(!allHideableColumnsVisible),\n        name: apiRef.current.getLocaleText('columnsManagementShowHideAllText'),\n        label: apiRef.current.getLocaleText('columnsManagementShowHideAllText'),\n        density: \"compact\"\n      }, rootProps.slotProps?.baseCheckbox)) : /*#__PURE__*/_jsx(\"span\", {}), !disableResetButton ? /*#__PURE__*/_jsx(rootProps.slots.baseButton, _extends({\n        onClick: () => apiRef.current.setColumnVisibilityModel(initialColumnVisibilityModel),\n        disabled: isResetDisabled || pivotActive\n      }, rootProps.slotProps?.baseButton, {\n        children: apiRef.current.getLocaleText('columnsManagementReset')\n      })) : null]\n    }) : null]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GridColumnsManagement.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * If `true`, the column search field will be focused automatically.\n   * If `false`, the first column switch input will be focused automatically.\n   * This helps to avoid input keyboard panel to popup automatically on touch devices.\n   * @default true\n   */\n  autoFocusSearchField: PropTypes.bool,\n  /**\n   * If `true`, the `Reset` button will not be disabled\n   * @default false\n   */\n  disableResetButton: PropTypes.bool,\n  /**\n   * If `true`, the `Show/Hide all` toggle checkbox will not be displayed.\n   * @default false\n   */\n  disableShowHideToggle: PropTypes.bool,\n  /**\n   * Returns the list of togglable columns.\n   * If used, only those columns will be displayed in the panel\n   * which are passed as the return value of the function.\n   * @param {GridColDef[]} columns The `ColDef` list of all columns.\n   * @returns {GridColDef['field'][]} The list of togglable columns' field names.\n   */\n  getTogglableColumns: PropTypes.func,\n  searchInputProps: PropTypes.object,\n  searchPredicate: PropTypes.func,\n  sort: PropTypes.oneOf(['asc', 'desc']),\n  /**\n   * Changes the behavior of the `Show/Hide All` toggle when the search field is used:\n   * - `all`: Will toggle all columns.\n   * - `filteredOnly`: Will only toggle columns that match the search criteria.\n   * @default 'all'\n   */\n  toggleAllMode: PropTypes.oneOf(['all', 'filteredOnly'])\n} : void 0;\nconst GridColumnsManagementBody = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'ColumnsManagement'\n})({\n  display: 'flex',\n  flexDirection: 'column',\n  padding: vars.spacing(0.5, 1.5)\n});\nconst GridColumnsManagementScrollArea = styled(GridShadowScrollArea, {\n  name: 'MuiDataGrid',\n  slot: 'ColumnsManagementScrollArea'\n})({\n  maxHeight: 300\n});\nconst GridColumnsManagementHeader = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'ColumnsManagementHeader'\n})({\n  padding: vars.spacing(1.5, 2),\n  borderBottom: `1px solid ${vars.colors.border.base}`\n});\nconst SearchInput = styled(NotRendered, {\n  name: 'MuiDataGrid',\n  slot: 'ColumnsManagementSearchInput'\n})({\n  [`& .${inputBaseClasses.input}::-webkit-search-decoration,\n      & .${inputBaseClasses.input}::-webkit-search-cancel-button,\n      & .${inputBaseClasses.input}::-webkit-search-results-button,\n      & .${inputBaseClasses.input}::-webkit-search-results-decoration`]: {\n    /* clears the 'X' icon from Chrome */\n    display: 'none'\n  }\n});\nconst GridColumnsManagementFooter = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'ColumnsManagementFooter'\n})({\n  padding: vars.spacing(1, 1, 1, 1.5),\n  display: 'flex',\n  justifyContent: 'space-between',\n  borderTop: `1px solid ${vars.colors.border.base}`\n});\nconst GridColumnsManagementEmptyText = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'ColumnsManagementEmptyText'\n})({\n  padding: vars.spacing(1, 0),\n  alignSelf: 'center',\n  font: vars.typography.font.body\n});\nexport { GridColumnsManagement };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}