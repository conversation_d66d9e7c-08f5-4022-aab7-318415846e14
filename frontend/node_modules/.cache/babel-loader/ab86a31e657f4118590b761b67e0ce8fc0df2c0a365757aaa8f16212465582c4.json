{"ast": null, "code": "// If no effect ran after this amount of time, we assume that the render was not committed by React\nconst CLEANUP_TIMER_LOOP_MILLIS = 1000;\nexport class TimerBasedCleanupTracking {\n  constructor(timeout = CLEANUP_TIMER_LOOP_MILLIS) {\n    this.timeouts = new Map();\n    this.cleanupTimeout = CLEANUP_TIMER_LOOP_MILLIS;\n    this.cleanupTimeout = timeout;\n  }\n  register(object, unsubscribe, unregisterToken) {\n    if (!this.timeouts) {\n      this.timeouts = new Map();\n    }\n    const timeout = setTimeout(() => {\n      if (typeof unsubscribe === 'function') {\n        unsubscribe();\n      }\n      this.timeouts.delete(unregisterToken.cleanupToken);\n    }, this.cleanupTimeout);\n    this.timeouts.set(unregisterToken.cleanupToken, timeout);\n  }\n  unregister(unregisterToken) {\n    const timeout = this.timeouts.get(unregisterToken.cleanupToken);\n    if (timeout) {\n      this.timeouts.delete(unregisterToken.cleanupToken);\n      clearTimeout(timeout);\n    }\n  }\n  reset() {\n    if (this.timeouts) {\n      this.timeouts.forEach((value, key) => {\n        this.unregister({\n          cleanupToken: key\n        });\n      });\n      this.timeouts = undefined;\n    }\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}