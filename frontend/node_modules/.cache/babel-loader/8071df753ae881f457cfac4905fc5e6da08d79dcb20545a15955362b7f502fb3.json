{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"label\", \"icon\", \"showInMenu\", \"onClick\"],\n  _excluded2 = [\"label\", \"icon\", \"showInMenu\", \"onClick\", \"closeMenuOnClick\", \"closeMenu\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst GridActionsCellItem = forwardRef((props, ref) => {\n  const rootProps = useGridRootProps();\n  if (!props.showInMenu) {\n    const {\n        label,\n        icon,\n        onClick\n      } = props,\n      other = _objectWithoutPropertiesLoose(props, _excluded);\n    const handleClick = event => {\n      onClick?.(event);\n    };\n    return /*#__PURE__*/_jsx(rootProps.slots.baseIconButton, _extends({\n      size: \"small\",\n      role: \"menuitem\",\n      \"aria-label\": label\n    }, other, {\n      onClick: handleClick\n    }, rootProps.slotProps?.baseIconButton, {\n      ref: ref,\n      children: /*#__PURE__*/React.cloneElement(icon, {\n        fontSize: 'small'\n      })\n    }));\n  }\n  const {\n      label,\n      icon,\n      onClick,\n      closeMenuOnClick = true,\n      closeMenu\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded2);\n  const handleClick = event => {\n    onClick?.(event);\n    if (closeMenuOnClick) {\n      closeMenu?.();\n    }\n  };\n  return /*#__PURE__*/_jsx(rootProps.slots.baseMenuItem, _extends({\n    ref: ref\n  }, other, {\n    onClick: handleClick,\n    iconStart: icon,\n    children: label\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridActionsCellItem.displayName = \"GridActionsCellItem\";\nprocess.env.NODE_ENV !== \"production\" ? GridActionsCellItem.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  className: PropTypes.string,\n  /**\n   * from https://mui.com/material-ui/api/button-base/#ButtonBase-prop-component\n   */\n  component: PropTypes.elementType,\n  disabled: PropTypes.bool,\n  icon: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.element, PropTypes.func, PropTypes.number, PropTypes.object, PropTypes.string, PropTypes.bool]),\n  label: PropTypes.node,\n  showInMenu: PropTypes.bool,\n  style: PropTypes.object\n} : void 0;\nexport { GridActionsCellItem };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}