{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"item\", \"applyValue\", \"apiRef\", \"focusElementRef\", \"isFilterActive\", \"headerFilterMenu\", \"clearButton\", \"tabIndex\", \"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport refType from '@mui/utils/refType';\nimport useId from '@mui/utils/useId';\nimport { useGridRootProps } from \"../../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction GridFilterInputBoolean(props) {\n  const {\n      item,\n      applyValue,\n      apiRef,\n      focusElementRef,\n      headerFilterMenu,\n      clearButton,\n      tabIndex,\n      slotProps\n    } = props,\n    others = _objectWithoutPropertiesLoose(props, _excluded);\n  const [filterValueState, setFilterValueState] = React.useState(sanitizeFilterItemValue(item.value));\n  const rootProps = useGridRootProps();\n  const labelId = useId();\n  const selectId = useId();\n  const baseSelectProps = rootProps.slotProps?.baseSelect || {};\n  const isSelectNative = baseSelectProps.native ?? false;\n  const baseSelectOptionProps = rootProps.slotProps?.baseSelectOption || {};\n  const onFilterChange = React.useCallback(event => {\n    const value = sanitizeFilterItemValue(event.target.value);\n    setFilterValueState(value);\n    applyValue(_extends({}, item, {\n      value\n    }));\n  }, [applyValue, item]);\n  React.useEffect(() => {\n    setFilterValueState(sanitizeFilterItemValue(item.value));\n  }, [item.value]);\n  const label = slotProps?.root.label ?? apiRef.current.getLocaleText('filterPanelInputLabel');\n  const rootSlotProps = slotProps?.root.slotProps;\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsxs(rootProps.slots.baseSelect, _extends({\n      fullWidth: true,\n      labelId: labelId,\n      id: selectId,\n      label: label,\n      value: filterValueState === undefined ? '' : String(filterValueState),\n      onChange: onFilterChange,\n      native: isSelectNative,\n      slotProps: {\n        htmlInput: _extends({\n          ref: focusElementRef,\n          tabIndex\n        }, rootSlotProps?.htmlInput)\n      }\n    }, baseSelectProps, others, slotProps?.root, {\n      children: [/*#__PURE__*/_jsx(rootProps.slots.baseSelectOption, _extends({}, baseSelectOptionProps, {\n        native: isSelectNative,\n        value: \"\",\n        children: apiRef.current.getLocaleText('filterValueAny')\n      })), /*#__PURE__*/_jsx(rootProps.slots.baseSelectOption, _extends({}, baseSelectOptionProps, {\n        native: isSelectNative,\n        value: \"true\",\n        children: apiRef.current.getLocaleText('filterValueTrue')\n      })), /*#__PURE__*/_jsx(rootProps.slots.baseSelectOption, _extends({}, baseSelectOptionProps, {\n        native: isSelectNative,\n        value: \"false\",\n        children: apiRef.current.getLocaleText('filterValueFalse')\n      }))]\n    })), headerFilterMenu, clearButton]\n  });\n}\nexport function sanitizeFilterItemValue(value) {\n  if (String(value).toLowerCase() === 'true') {\n    return true;\n  }\n  if (String(value).toLowerCase() === 'false') {\n    return false;\n  }\n  return undefined;\n}\nprocess.env.NODE_ENV !== \"production\" ? GridFilterInputBoolean.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  apiRef: PropTypes.shape({\n    current: PropTypes.object.isRequired\n  }).isRequired,\n  applyValue: PropTypes.func.isRequired,\n  className: PropTypes.string,\n  clearButton: PropTypes.node,\n  disabled: PropTypes.bool,\n  focusElementRef: refType,\n  headerFilterMenu: PropTypes.node,\n  inputRef: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n    current: (props, propName) => {\n      if (props[propName] == null) {\n        return null;\n      }\n      if (typeof props[propName] !== 'object' || props[propName].nodeType !== 1) {\n        return new Error(`Expected prop '${propName}' to be of type Element`);\n      }\n      return null;\n    }\n  })]),\n  /**\n   * It is `true` if the filter either has a value or an operator with no value\n   * required is selected (for example `isEmpty`)\n   */\n  isFilterActive: PropTypes.bool,\n  item: PropTypes.shape({\n    field: PropTypes.string.isRequired,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    operator: PropTypes.string.isRequired,\n    value: PropTypes.any\n  }).isRequired,\n  onBlur: PropTypes.func,\n  onFocus: PropTypes.func,\n  slotProps: PropTypes.object,\n  tabIndex: PropTypes.number\n} : void 0;\nexport { GridFilterInputBoolean };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}