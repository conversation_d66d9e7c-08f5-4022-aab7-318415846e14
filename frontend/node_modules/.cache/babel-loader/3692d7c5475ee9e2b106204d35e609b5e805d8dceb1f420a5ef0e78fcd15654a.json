{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useLazyRef from '@mui/utils/useLazyRef';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport { ResizeObserver } from \"../../../utils/ResizeObserver.js\";\nimport { useGridVisibleRows } from \"../../utils/useGridVisibleRows.js\";\nimport { eslintUseValue } from \"../../../utils/utils.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridSelector } from \"../../utils/useGridSelector.js\";\nimport { gridDensityFactorSelector } from \"../density/densitySelector.js\";\nimport { gridPaginationSelector } from \"../pagination/gridPaginationSelector.js\";\nimport { useGridRegisterPipeApplier } from \"../../core/pipeProcessing/index.js\";\nimport { gridPinnedRowsSelector, gridRowCountSelector } from \"./gridRowsSelector.js\";\nimport { gridDimensionsSelector, gridRowHeightSelector } from \"../dimensions/gridDimensionsSelectors.js\";\nimport { getValidRowHeight, getRowHeightWarning } from \"./gridRowsUtils.js\";\nimport { gridFocusedVirtualCellSelector } from \"../virtualization/gridFocusedVirtualCellSelector.js\";\n/* eslint-disable no-underscore-dangle */\n\nexport const rowsMetaStateInitializer = (state, props, apiRef) => {\n  apiRef.current.caches.rowsMeta = {\n    heights: new Map()\n  };\n  const baseRowHeight = gridRowHeightSelector(apiRef);\n  const dataRowCount = gridRowCountSelector(apiRef);\n  const pagination = gridPaginationSelector(apiRef);\n  const rowCount = Math.min(pagination.enabled ? pagination.paginationModel.pageSize : dataRowCount, dataRowCount);\n  return _extends({}, state, {\n    rowsMeta: {\n      currentPageTotalHeight: rowCount * baseRowHeight,\n      positions: Array.from({\n        length: rowCount\n      }, (_, i) => i * baseRowHeight),\n      pinnedTopRowsTotalHeight: 0,\n      pinnedBottomRowsTotalHeight: 0\n    }\n  });\n};\n\n/**\n * @requires useGridPageSize (method)\n * @requires useGridPage (method)\n */\nexport const useGridRowsMeta = (apiRef, props) => {\n  const {\n    getRowHeight: getRowHeightProp,\n    getRowSpacing,\n    getEstimatedRowHeight\n  } = props;\n  const heightCache = apiRef.current.caches.rowsMeta.heights;\n  const lastMeasuredRowIndex = React.useRef(-1);\n  const hasRowWithAutoHeight = React.useRef(false);\n  const isHeightMetaValid = React.useRef(false);\n  const densityFactor = useGridSelector(apiRef, gridDensityFactorSelector);\n  const currentPage = useGridVisibleRows(apiRef, props);\n  const pinnedRows = useGridSelector(apiRef, gridPinnedRowsSelector);\n  const rowHeight = useGridSelector(apiRef, gridRowHeightSelector);\n  const getRowHeightEntry = rowId => {\n    let entry = heightCache.get(rowId);\n    if (entry === undefined) {\n      entry = {\n        content: rowHeight,\n        spacingTop: 0,\n        spacingBottom: 0,\n        detail: 0,\n        autoHeight: false,\n        needsFirstMeasurement: true\n      };\n      heightCache.set(rowId, entry);\n    }\n    return entry;\n  };\n  const processHeightEntry = React.useCallback(row => {\n    // HACK: rowHeight trails behind the most up-to-date value just enough to\n    // mess the initial rowsMeta hydration :/\n    const baseRowHeight = gridDimensionsSelector(apiRef).rowHeight;\n    eslintUseValue(rowHeight);\n    const entry = apiRef.current.getRowHeightEntry(row.id);\n    if (!getRowHeightProp) {\n      entry.content = baseRowHeight;\n      entry.needsFirstMeasurement = false;\n    } else {\n      const rowHeightFromUser = getRowHeightProp(_extends({}, row, {\n        densityFactor\n      }));\n      if (rowHeightFromUser === 'auto') {\n        if (entry.needsFirstMeasurement) {\n          const estimatedRowHeight = getEstimatedRowHeight ? getEstimatedRowHeight(_extends({}, row, {\n            densityFactor\n          })) : baseRowHeight;\n\n          // If the row was not measured yet use the estimated row height\n          entry.content = estimatedRowHeight ?? baseRowHeight;\n        }\n        hasRowWithAutoHeight.current = true;\n        entry.autoHeight = true;\n      } else {\n        // Default back to base rowHeight if getRowHeight returns invalid value.\n        entry.content = getValidRowHeight(rowHeightFromUser, baseRowHeight, getRowHeightWarning);\n        entry.needsFirstMeasurement = false;\n        entry.autoHeight = false;\n      }\n    }\n    if (getRowSpacing) {\n      const indexRelativeToCurrentPage = apiRef.current.getRowIndexRelativeToVisibleRows(row.id);\n      const spacing = getRowSpacing(_extends({}, row, {\n        isFirstVisible: indexRelativeToCurrentPage === 0,\n        isLastVisible: indexRelativeToCurrentPage === currentPage.rows.length - 1,\n        indexRelativeToCurrentPage\n      }));\n      entry.spacingTop = spacing.top ?? 0;\n      entry.spacingBottom = spacing.bottom ?? 0;\n    } else {\n      entry.spacingTop = 0;\n      entry.spacingBottom = 0;\n    }\n    apiRef.current.unstable_applyPipeProcessors('rowHeight', entry, row);\n    return entry;\n  }, [apiRef, currentPage.rows, getRowHeightProp, getEstimatedRowHeight, rowHeight, getRowSpacing, densityFactor]);\n  const hydrateRowsMeta = React.useCallback(() => {\n    hasRowWithAutoHeight.current = false;\n    const pinnedTopRowsTotalHeight = pinnedRows.top.reduce((acc, row) => {\n      const entry = processHeightEntry(row);\n      return acc + entry.content + entry.spacingTop + entry.spacingBottom + entry.detail;\n    }, 0);\n    const pinnedBottomRowsTotalHeight = pinnedRows.bottom.reduce((acc, row) => {\n      const entry = processHeightEntry(row);\n      return acc + entry.content + entry.spacingTop + entry.spacingBottom + entry.detail;\n    }, 0);\n    const positions = [];\n    const currentPageTotalHeight = currentPage.rows.reduce((acc, row) => {\n      positions.push(acc);\n      const entry = processHeightEntry(row);\n      const total = entry.content + entry.spacingTop + entry.spacingBottom + entry.detail;\n      return acc + total;\n    }, 0);\n    if (!hasRowWithAutoHeight.current) {\n      // No row has height=auto, so all rows are already measured\n      lastMeasuredRowIndex.current = Infinity;\n    }\n    const didHeightsChange = pinnedTopRowsTotalHeight !== apiRef.current.state.rowsMeta.pinnedTopRowsTotalHeight || pinnedBottomRowsTotalHeight !== apiRef.current.state.rowsMeta.pinnedBottomRowsTotalHeight || currentPageTotalHeight !== apiRef.current.state.rowsMeta.currentPageTotalHeight;\n    const rowsMeta = {\n      currentPageTotalHeight,\n      positions,\n      pinnedTopRowsTotalHeight,\n      pinnedBottomRowsTotalHeight\n    };\n    apiRef.current.setState(state => {\n      return _extends({}, state, {\n        rowsMeta\n      });\n    });\n    if (didHeightsChange) {\n      apiRef.current.updateDimensions();\n    }\n    isHeightMetaValid.current = true;\n  }, [apiRef, pinnedRows, currentPage.rows, processHeightEntry]);\n  const getRowHeight = rowId => {\n    return heightCache.get(rowId)?.content ?? rowHeight;\n  };\n  const storeRowHeightMeasurement = (id, height) => {\n    const entry = apiRef.current.getRowHeightEntry(id);\n    const didChange = entry.content !== height;\n    entry.needsFirstMeasurement = false;\n    entry.content = height;\n    isHeightMetaValid.current &&= !didChange;\n  };\n  const rowHasAutoHeight = id => {\n    return heightCache.get(id)?.autoHeight ?? false;\n  };\n  const getLastMeasuredRowIndex = () => {\n    return lastMeasuredRowIndex.current;\n  };\n  const setLastMeasuredRowIndex = index => {\n    if (hasRowWithAutoHeight.current && index > lastMeasuredRowIndex.current) {\n      lastMeasuredRowIndex.current = index;\n    }\n  };\n  const resetRowHeights = () => {\n    heightCache.clear();\n    hydrateRowsMeta();\n  };\n  const resizeObserver = useLazyRef(() => new ResizeObserver(entries => {\n    for (let i = 0; i < entries.length; i += 1) {\n      const entry = entries[i];\n      const height = entry.borderBoxSize && entry.borderBoxSize.length > 0 ? entry.borderBoxSize[0].blockSize : entry.contentRect.height;\n      const rowId = entry.target.__mui_id;\n      const focusedVirtualRowId = gridFocusedVirtualCellSelector(apiRef)?.id;\n      if (focusedVirtualRowId === rowId && height === 0) {\n        // Focused virtual row has 0 height.\n        // We don't want to store it to avoid scroll jumping.\n        // https://github.com/mui/mui-x/issues/14726\n        return;\n      }\n      apiRef.current.unstable_storeRowHeightMeasurement(rowId, height);\n    }\n    if (!isHeightMetaValid.current) {\n      // Avoids \"ResizeObserver loop completed with undelivered notifications\" error\n      requestAnimationFrame(() => {\n        apiRef.current.requestPipeProcessorsApplication('rowHeight');\n      });\n    }\n  })).current;\n  const observeRowHeight = (element, rowId) => {\n    element.__mui_id = rowId;\n    resizeObserver.observe(element);\n    return () => resizeObserver.unobserve(element);\n  };\n  useGridRegisterPipeApplier(apiRef, 'rowHeight', hydrateRowsMeta);\n\n  // The effect is used to build the rows meta data - currentPageTotalHeight and positions.\n  // Because of variable row height this is needed for the virtualization\n  useEnhancedEffect(() => {\n    hydrateRowsMeta();\n  }, [hydrateRowsMeta]);\n  const rowsMetaApi = {\n    unstable_getRowHeight: getRowHeight,\n    unstable_setLastMeasuredRowIndex: setLastMeasuredRowIndex,\n    unstable_storeRowHeightMeasurement: storeRowHeightMeasurement,\n    resetRowHeights\n  };\n  const rowsMetaPrivateApi = {\n    hydrateRowsMeta,\n    observeRowHeight,\n    rowHasAutoHeight,\n    getRowHeightEntry,\n    getLastMeasuredRowIndex\n  };\n  useGridApiMethod(apiRef, rowsMetaApi, 'public');\n  useGridApiMethod(apiRef, rowsMetaPrivateApi, 'private');\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}