{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { lruMemoize } from '@mui/x-internals/lruMemoize';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport { isDeepEqual } from '@mui/x-internals/isDeepEqual';\nimport { useLazyRef } from \"../../utils/useLazyRef.js\";\nimport { useGridEvent } from \"../../utils/useGridEvent.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { gridColumnLookupSelector } from \"../columns/gridColumnsSelector.js\";\nimport { GridPreferencePanelsValue } from \"../preferencesPanel/gridPreferencePanelsValue.js\";\nimport { defaultGridFilterLookup, getDefaultGridFilterModel } from \"./gridFilterState.js\";\nimport { gridFilterModelSelector } from \"./gridFilterSelector.js\";\nimport { useFirstRender } from \"../../utils/useFirstRender.js\";\nimport { gridRowsLookupSelector } from \"../rows/index.js\";\nimport { useGridRegisterPipeProcessor } from \"../../core/pipeProcessing/index.js\";\nimport { GRID_DEFAULT_STRATEGY, useGridRegisterStrategyProcessor } from \"../../core/strategyProcessing/index.js\";\nimport { buildAggregatedFilterApplier, sanitizeFilterModel, mergeStateWithFilterModel, cleanFilterItem, passFilterLogic, shouldQuickFilterExcludeHiddenColumns } from \"./gridFilterUtils.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const filterStateInitializer = (state, props, apiRef) => {\n  const filterModel = props.filterModel ?? props.initialState?.filter?.filterModel ?? getDefaultGridFilterModel();\n  return _extends({}, state, {\n    filter: _extends({\n      filterModel: sanitizeFilterModel(filterModel, props.disableMultipleColumnsFiltering, apiRef)\n    }, defaultGridFilterLookup),\n    visibleRowsLookup: {}\n  });\n};\nconst getVisibleRowsLookup = params => {\n  // For flat tree, the `visibleRowsLookup` and the `filteredRowsLookup` are equals since no row is collapsed.\n  return params.filteredRowsLookup;\n};\nfunction getVisibleRowsLookupState(apiRef, state) {\n  return apiRef.current.applyStrategyProcessor('visibleRowsLookupCreation', {\n    tree: state.rows.tree,\n    filteredRowsLookup: state.filter.filteredRowsLookup\n  });\n}\nfunction createMemoizedValues() {\n  return lruMemoize(Object.values);\n}\n\n/**\n * @requires useGridColumns (method, event)\n * @requires useGridParamsApi (method)\n * @requires useGridRows (event)\n */\nexport const useGridFilter = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridFilter');\n  apiRef.current.registerControlState({\n    stateId: 'filter',\n    propModel: props.filterModel,\n    propOnChange: props.onFilterModelChange,\n    stateSelector: gridFilterModelSelector,\n    changeEvent: 'filterModelChange'\n  });\n  const updateFilteredRows = React.useCallback(() => {\n    apiRef.current.setState(state => {\n      const filterModel = gridFilterModelSelector(apiRef);\n      const filterState = apiRef.current.getFilterState(filterModel);\n      const newState = _extends({}, state, {\n        filter: _extends({}, state.filter, filterState)\n      });\n      const visibleRowsLookupState = getVisibleRowsLookupState(apiRef, newState);\n      return _extends({}, newState, {\n        visibleRowsLookup: visibleRowsLookupState\n      });\n    });\n    apiRef.current.publishEvent('filteredRowsSet');\n  }, [apiRef]);\n  const addColumnMenuItem = React.useCallback((columnMenuItems, colDef) => {\n    if (colDef == null || colDef.filterable === false || props.disableColumnFilter) {\n      return columnMenuItems;\n    }\n    return [...columnMenuItems, 'columnMenuFilterItem'];\n  }, [props.disableColumnFilter]);\n\n  /**\n   * API METHODS\n   */\n  const upsertFilterItem = React.useCallback(item => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    const items = [...filterModel.items];\n    const itemIndex = items.findIndex(filterItem => filterItem.id === item.id);\n    if (itemIndex === -1) {\n      items.push(item);\n    } else {\n      items[itemIndex] = item;\n    }\n    apiRef.current.setFilterModel(_extends({}, filterModel, {\n      items\n    }), 'upsertFilterItem');\n  }, [apiRef]);\n  const upsertFilterItems = React.useCallback(items => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    const existingItems = [...filterModel.items];\n    items.forEach(item => {\n      const itemIndex = existingItems.findIndex(filterItem => filterItem.id === item.id);\n      if (itemIndex === -1) {\n        existingItems.push(item);\n      } else {\n        existingItems[itemIndex] = item;\n      }\n    });\n    apiRef.current.setFilterModel(_extends({}, filterModel, {\n      items: existingItems\n    }), 'upsertFilterItems');\n  }, [apiRef]);\n  const deleteFilterItem = React.useCallback(itemToDelete => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    const items = filterModel.items.filter(item => item.id !== itemToDelete.id);\n    if (items.length === filterModel.items.length) {\n      return;\n    }\n    apiRef.current.setFilterModel(_extends({}, filterModel, {\n      items\n    }), 'deleteFilterItem');\n  }, [apiRef]);\n  const showFilterPanel = React.useCallback((targetColumnField, panelId, labelId) => {\n    logger.debug('Displaying filter panel');\n    if (targetColumnField) {\n      const filterModel = gridFilterModelSelector(apiRef);\n      const filterItemsWithValue = filterModel.items.filter(item => {\n        if (item.value !== undefined) {\n          // Some filters like `isAnyOf` support array as `item.value`.\n          // If array is empty, we want to remove it from the filter model.\n          if (Array.isArray(item.value) && item.value.length === 0) {\n            return false;\n          }\n          return true;\n        }\n        const column = apiRef.current.getColumn(item.field);\n        const filterOperator = column.filterOperators?.find(operator => operator.value === item.operator);\n        const requiresFilterValue = typeof filterOperator?.requiresFilterValue === 'undefined' ? true : filterOperator?.requiresFilterValue;\n\n        // Operators like `isEmpty` don't have and don't require `item.value`.\n        // So we don't want to remove them from the filter model if `item.value === undefined`.\n        // See https://github.com/mui/mui-x/issues/5402\n        if (requiresFilterValue) {\n          return false;\n        }\n        return true;\n      });\n      let newFilterItems;\n      const filterItemOnTarget = filterItemsWithValue.find(item => item.field === targetColumnField);\n      const targetColumn = apiRef.current.getColumn(targetColumnField);\n      if (filterItemOnTarget) {\n        newFilterItems = filterItemsWithValue;\n      } else if (props.disableMultipleColumnsFiltering) {\n        newFilterItems = [cleanFilterItem({\n          field: targetColumnField,\n          operator: targetColumn.filterOperators[0].value\n        }, apiRef)];\n      } else {\n        newFilterItems = [...filterItemsWithValue, cleanFilterItem({\n          field: targetColumnField,\n          operator: targetColumn.filterOperators[0].value\n        }, apiRef)];\n      }\n      apiRef.current.setFilterModel(_extends({}, filterModel, {\n        items: newFilterItems\n      }));\n    }\n    apiRef.current.showPreferences(GridPreferencePanelsValue.filters, panelId, labelId);\n  }, [apiRef, logger, props.disableMultipleColumnsFiltering]);\n  const hideFilterPanel = React.useCallback(() => {\n    logger.debug('Hiding filter panel');\n    apiRef.current.hidePreferences();\n  }, [apiRef, logger]);\n  const setFilterLogicOperator = React.useCallback(logicOperator => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    if (filterModel.logicOperator === logicOperator) {\n      return;\n    }\n    apiRef.current.setFilterModel(_extends({}, filterModel, {\n      logicOperator\n    }), 'changeLogicOperator');\n  }, [apiRef]);\n  const setQuickFilterValues = React.useCallback(values => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    if (isDeepEqual(filterModel.quickFilterValues, values)) {\n      return;\n    }\n    apiRef.current.setFilterModel(_extends({}, filterModel, {\n      quickFilterValues: [...values]\n    }));\n  }, [apiRef]);\n  const setFilterModel = React.useCallback((model, reason) => {\n    const currentModel = gridFilterModelSelector(apiRef);\n    if (currentModel !== model) {\n      logger.debug('Setting filter model');\n      apiRef.current.updateControlState('filter', mergeStateWithFilterModel(model, props.disableMultipleColumnsFiltering, apiRef), reason);\n      apiRef.current.unstable_applyFilters();\n    }\n  }, [apiRef, logger, props.disableMultipleColumnsFiltering]);\n  const getFilterState = React.useCallback(inputFilterModel => {\n    const filterModel = sanitizeFilterModel(inputFilterModel, props.disableMultipleColumnsFiltering, apiRef);\n    const isRowMatchingFilters = props.filterMode === 'client' ? buildAggregatedFilterApplier(filterModel, apiRef, props.disableEval) : null;\n    const filterResult = apiRef.current.applyStrategyProcessor('filtering', {\n      isRowMatchingFilters,\n      filterModel: filterModel ?? getDefaultGridFilterModel()\n    });\n    return _extends({}, filterResult, {\n      filterModel\n    });\n  }, [props.disableMultipleColumnsFiltering, props.filterMode, props.disableEval, apiRef]);\n  const filterApi = {\n    setFilterLogicOperator,\n    unstable_applyFilters: updateFilteredRows,\n    deleteFilterItem,\n    upsertFilterItem,\n    upsertFilterItems,\n    setFilterModel,\n    showFilterPanel,\n    hideFilterPanel,\n    setQuickFilterValues,\n    ignoreDiacritics: props.ignoreDiacritics,\n    getFilterState\n  };\n  useGridApiMethod(apiRef, filterApi, 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    const filterModelToExport = gridFilterModelSelector(apiRef);\n\n    // Remove the additional `fromInput` property from the filter model\n    filterModelToExport.items.forEach(item => {\n      delete item.fromInput;\n    });\n    const shouldExportFilterModel =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the model is controlled\n    props.filterModel != null ||\n    // Always export if the model has been initialized\n    props.initialState?.filter?.filterModel != null ||\n    // Export if the model is not equal to the default value\n    !isDeepEqual(filterModelToExport, getDefaultGridFilterModel());\n    if (!shouldExportFilterModel) {\n      return prevState;\n    }\n    return _extends({}, prevState, {\n      filter: {\n        filterModel: filterModelToExport\n      }\n    });\n  }, [apiRef, props.filterModel, props.initialState?.filter?.filterModel]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    const filterModel = context.stateToRestore.filter?.filterModel;\n    if (filterModel == null) {\n      return params;\n    }\n    apiRef.current.updateControlState('filter', mergeStateWithFilterModel(filterModel, props.disableMultipleColumnsFiltering, apiRef), 'restoreState');\n    return _extends({}, params, {\n      callbacks: [...params.callbacks, apiRef.current.unstable_applyFilters]\n    });\n  }, [apiRef, props.disableMultipleColumnsFiltering]);\n  const preferencePanelPreProcessing = React.useCallback((initialValue, value) => {\n    if (value === GridPreferencePanelsValue.filters) {\n      const FilterPanel = props.slots.filterPanel;\n      return /*#__PURE__*/_jsx(FilterPanel, _extends({}, props.slotProps?.filterPanel));\n    }\n    return initialValue;\n  }, [props.slots.filterPanel, props.slotProps?.filterPanel]);\n  const {\n    getRowId\n  } = props;\n  const getRowsRef = useLazyRef(createMemoizedValues);\n  const flatFilteringMethod = React.useCallback(params => {\n    if (props.filterMode !== 'client' || !params.isRowMatchingFilters || !params.filterModel.items.length && !params.filterModel.quickFilterValues?.length) {\n      return defaultGridFilterLookup;\n    }\n    const dataRowIdToModelLookup = gridRowsLookupSelector(apiRef);\n    const filteredRowsLookup = {};\n    const {\n      isRowMatchingFilters\n    } = params;\n    const filterCache = {};\n    const result = {\n      passingFilterItems: null,\n      passingQuickFilterValues: null\n    };\n    const rows = getRowsRef.current(apiRef.current.state.rows.dataRowIdToModelLookup);\n    for (let i = 0; i < rows.length; i += 1) {\n      const row = rows[i];\n      const id = getRowId ? getRowId(row) : row.id;\n      isRowMatchingFilters(row, undefined, result);\n      const isRowPassing = passFilterLogic([result.passingFilterItems], [result.passingQuickFilterValues], params.filterModel, apiRef, filterCache);\n      if (!isRowPassing) {\n        filteredRowsLookup[id] = isRowPassing;\n      }\n    }\n    const footerId = 'auto-generated-group-footer-root';\n    const footer = dataRowIdToModelLookup[footerId];\n    if (footer) {\n      filteredRowsLookup[footerId] = true;\n    }\n    return {\n      filteredRowsLookup,\n      filteredChildrenCountLookup: {},\n      filteredDescendantCountLookup: {}\n    };\n  }, [apiRef, props.filterMode, getRowId, getRowsRef]);\n  useGridRegisterPipeProcessor(apiRef, 'columnMenu', addColumnMenuItem);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'preferencePanel', preferencePanelPreProcessing);\n  useGridRegisterStrategyProcessor(apiRef, GRID_DEFAULT_STRATEGY, 'filtering', flatFilteringMethod);\n  useGridRegisterStrategyProcessor(apiRef, GRID_DEFAULT_STRATEGY, 'visibleRowsLookupCreation', getVisibleRowsLookup);\n\n  /**\n   * EVENTS\n   */\n  const handleColumnsChange = React.useCallback(() => {\n    logger.debug('onColUpdated - GridColumns changed, applying filters');\n    const filterModel = gridFilterModelSelector(apiRef);\n    const columnsLookup = gridColumnLookupSelector(apiRef);\n    const newFilterItems = filterModel.items.filter(item => item.field && columnsLookup[item.field]);\n    if (newFilterItems.length < filterModel.items.length) {\n      apiRef.current.setFilterModel(_extends({}, filterModel, {\n        items: newFilterItems\n      }));\n    }\n  }, [apiRef, logger]);\n  const handleStrategyProcessorChange = React.useCallback(methodName => {\n    if (methodName === 'filtering') {\n      apiRef.current.unstable_applyFilters();\n    }\n  }, [apiRef]);\n  const updateVisibleRowsLookupState = React.useCallback(() => {\n    apiRef.current.setState(state => {\n      return _extends({}, state, {\n        visibleRowsLookup: getVisibleRowsLookupState(apiRef, state)\n      });\n    });\n  }, [apiRef]);\n  useGridEvent(apiRef, 'rowsSet', updateFilteredRows);\n  useGridEvent(apiRef, 'columnsChange', handleColumnsChange);\n  useGridEvent(apiRef, 'activeStrategyProcessorChange', handleStrategyProcessorChange);\n  useGridEvent(apiRef, 'rowExpansionChange', updateVisibleRowsLookupState);\n  useGridEvent(apiRef, 'columnVisibilityModelChange', () => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    if (filterModel.quickFilterValues && shouldQuickFilterExcludeHiddenColumns(filterModel)) {\n      // re-apply filters because the quick filter results may have changed\n      apiRef.current.unstable_applyFilters();\n    }\n  });\n\n  /**\n   * 1ST RENDER\n   */\n  useFirstRender(() => {\n    apiRef.current.unstable_applyFilters();\n  });\n\n  /**\n   * EFFECTS\n   */\n  useEnhancedEffect(() => {\n    if (props.filterModel !== undefined) {\n      apiRef.current.setFilterModel(props.filterModel);\n    }\n  }, [apiRef, logger, props.filterModel]);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}