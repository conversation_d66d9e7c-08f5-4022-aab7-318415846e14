{"ast": null, "code": "import { PinnedColumnPosition } from \"../internals/constants.js\";\nexport const rtlFlipSide = (position, isRtl) => {\n  if (!position) {\n    return undefined;\n  }\n  if (!isRtl) {\n    if (position === PinnedColumnPosition.LEFT) {\n      return 'left';\n    }\n    if (position === PinnedColumnPosition.RIGHT) {\n      return 'right';\n    }\n  } else {\n    if (position === PinnedColumnPosition.LEFT) {\n      return 'right';\n    }\n    if (position === PinnedColumnPosition.RIGHT) {\n      return 'left';\n    }\n  }\n  return undefined;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}