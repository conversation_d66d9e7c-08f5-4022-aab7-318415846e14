{"ast": null, "code": "import { GridFilterInputSingleSelect } from \"../components/panel/filterPanel/GridFilterInputSingleSelect.js\";\nimport { GridFilterInputMultipleSingleSelect } from \"../components/panel/filterPanel/GridFilterInputMultipleSingleSelect.js\";\nimport { isObject } from \"../utils/utils.js\";\nconst parseObjectValue = value => {\n  if (value == null || !isObject(value)) {\n    return value;\n  }\n  return value.value;\n};\nexport const getGridSingleSelectOperators = () => [{\n  value: 'is',\n  getApplyFilterFn: filterItem => {\n    if (filterItem.value == null || filterItem.value === '') {\n      return null;\n    }\n    return value => parseObjectValue(value) === parseObjectValue(filterItem.value);\n  },\n  InputComponent: GridFilterInputSingleSelect\n}, {\n  value: 'not',\n  getApplyFilterFn: filterItem => {\n    if (filterItem.value == null || filterItem.value === '') {\n      return null;\n    }\n    return value => parseObjectValue(value) !== parseObjectValue(filterItem.value);\n  },\n  InputComponent: GridFilterInputSingleSelect\n}, {\n  value: 'isAnyOf',\n  getApplyFilterFn: filterItem => {\n    if (!Array.isArray(filterItem.value) || filterItem.value.length === 0) {\n      return null;\n    }\n    const filterItemValues = filterItem.value.map(parseObjectValue);\n    return value => filterItemValues.includes(parseObjectValue(value));\n  },\n  InputComponent: GridFilterInputMultipleSingleSelect\n}];", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}