{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"item\", \"applyValue\", \"type\", \"apiRef\", \"focusElementRef\", \"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport useId from '@mui/utils/useId';\nimport { useGridRootProps } from \"../../../hooks/utils/useGridRootProps.js\";\nimport { getValueOptions, isSingleSelectColDef } from \"./filterPanelUtils.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction GridFilterInputMultipleSingleSelect(props) {\n  const {\n      item,\n      applyValue,\n      type,\n      apiRef,\n      focusElementRef,\n      slotProps\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const id = useId();\n  const rootProps = useGridRootProps();\n  let resolvedColumn = null;\n  if (item.field) {\n    const column = apiRef.current.getColumn(item.field);\n    if (isSingleSelectColDef(column)) {\n      resolvedColumn = column;\n    }\n  }\n  const getOptionValue = resolvedColumn?.getOptionValue;\n  const getOptionLabel = resolvedColumn?.getOptionLabel;\n  const isOptionEqualToValue = React.useCallback((option, value) => getOptionValue(option) === getOptionValue(value), [getOptionValue]);\n  const resolvedValueOptions = React.useMemo(() => {\n    return getValueOptions(resolvedColumn) || [];\n  }, [resolvedColumn]);\n\n  // The value is computed from the item.value and used directly\n  // If it was done by a useEffect/useState, the Autocomplete could receive incoherent value and options\n  const filteredValues = React.useMemo(() => {\n    if (!Array.isArray(item.value)) {\n      return [];\n    }\n    return item.value.reduce((acc, value) => {\n      const resolvedValue = resolvedValueOptions.find(v => getOptionValue(v) === value);\n      if (resolvedValue != null) {\n        acc.push(resolvedValue);\n      }\n      return acc;\n    }, []);\n  }, [getOptionValue, item.value, resolvedValueOptions]);\n  const handleChange = React.useCallback((event, value) => {\n    applyValue(_extends({}, item, {\n      value: value.map(getOptionValue)\n    }));\n  }, [applyValue, item, getOptionValue]);\n  const BaseAutocomplete = rootProps.slots.baseAutocomplete;\n  return /*#__PURE__*/_jsx(BaseAutocomplete, _extends({\n    multiple: true,\n    options: resolvedValueOptions,\n    isOptionEqualToValue: isOptionEqualToValue,\n    id: id,\n    value: filteredValues,\n    onChange: handleChange,\n    getOptionLabel: getOptionLabel,\n    label: apiRef.current.getLocaleText('filterPanelInputLabel'),\n    placeholder: apiRef.current.getLocaleText('filterPanelInputPlaceholder'),\n    slotProps: {\n      textField: {\n        type: type || 'text',\n        inputRef: focusElementRef\n      }\n    }\n  }, other, slotProps?.root));\n}\nprocess.env.NODE_ENV !== \"production\" ? GridFilterInputMultipleSingleSelect.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  apiRef: PropTypes.shape({\n    current: PropTypes.object.isRequired\n  }).isRequired,\n  applyValue: PropTypes.func.isRequired,\n  className: PropTypes.string,\n  clearButton: PropTypes.node,\n  disabled: PropTypes.bool,\n  focusElementRef: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.func, PropTypes.object]),\n  headerFilterMenu: PropTypes.node,\n  inputRef: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n    current: (props, propName) => {\n      if (props[propName] == null) {\n        return null;\n      }\n      if (typeof props[propName] !== 'object' || props[propName].nodeType !== 1) {\n        return new Error(`Expected prop '${propName}' to be of type Element`);\n      }\n      return null;\n    }\n  })]),\n  /**\n   * It is `true` if the filter either has a value or an operator with no value\n   * required is selected (for example `isEmpty`)\n   */\n  isFilterActive: PropTypes.bool,\n  item: PropTypes.shape({\n    field: PropTypes.string.isRequired,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    operator: PropTypes.string.isRequired,\n    value: PropTypes.any\n  }).isRequired,\n  onBlur: PropTypes.func,\n  onFocus: PropTypes.func,\n  slotProps: PropTypes.object,\n  tabIndex: PropTypes.number,\n  type: PropTypes.oneOf(['singleSelect'])\n} : void 0;\nexport { GridFilterInputMultipleSingleSelect };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}