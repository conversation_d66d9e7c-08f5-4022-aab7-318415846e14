{"ast": null, "code": "/**\n * Params passed to `apiRef.current.setEditCellValue`.\n */\nvar GridCellEditStartReasons = /*#__PURE__*/function (GridCellEditStartReasons) {\n  GridCellEditStartReasons[\"enterKeyDown\"] = \"enterKeyDown\";\n  GridCellEditStartReasons[\"cellDoubleClick\"] = \"cellDoubleClick\";\n  GridCellEditStartReasons[\"printableKeyDown\"] = \"printableKeyDown\";\n  GridCellEditStartReasons[\"deleteKeyDown\"] = \"deleteKeyDown\";\n  GridCellEditStartReasons[\"pasteKeyDown\"] = \"pasteKeyDown\";\n  return GridCellEditStartReasons;\n}(GridCellEditStartReasons || {});\n/**\n * Params passed to the `cellEditStart` event.\n */\nvar GridCellEditStopReasons = /*#__PURE__*/function (GridCellEditStopReasons) {\n  GridCellEditStopReasons[\"cellFocusOut\"] = \"cellFocusOut\";\n  GridCellEditStopReasons[\"escapeKeyDown\"] = \"escapeKeyDown\";\n  GridCellEditStopReasons[\"enterKeyDown\"] = \"enterKeyDown\";\n  GridCellEditStopReasons[\"tabKeyDown\"] = \"tabKeyDown\";\n  GridCellEditStopReasons[\"shiftTabKeyDown\"] = \"shiftTabKeyDown\";\n  return GridCellEditStopReasons;\n}(GridCellEditStopReasons || {});\n/**\n * Params passed to the `cellEditStop event.\n */\n// https://github.com/mui/mui-x/pull/3738#discussion_r798504277\nexport { GridCellEditStartReasons, GridCellEditStopReasons };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}