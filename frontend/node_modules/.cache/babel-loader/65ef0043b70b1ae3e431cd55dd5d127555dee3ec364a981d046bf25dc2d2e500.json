{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\", \"selectedRowCount\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from '@mui/system';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { vars } from \"../constants/cssVariables.js\";\nimport { useGridApiContext } from \"../hooks/utils/useGridApiContext.js\";\nimport { getDataGridUtilityClass } from \"../constants/gridClasses.js\";\nimport { useGridRootProps } from \"../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['selectedRowCount']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst GridSelectedRowCountRoot = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'SelectedRowCount'\n})({\n  alignItems: 'center',\n  display: 'flex',\n  margin: vars.spacing(0, 2),\n  visibility: 'hidden',\n  width: 0,\n  height: 0,\n  [vars.breakpoints.up('sm')]: {\n    visibility: 'visible',\n    width: 'auto',\n    height: 'auto'\n  }\n});\nconst GridSelectedRowCount = forwardRef(function GridSelectedRowCount(props, ref) {\n  const {\n      className,\n      selectedRowCount\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const apiRef = useGridApiContext();\n  const ownerState = useGridRootProps();\n  const classes = useUtilityClasses(ownerState);\n  const rowSelectedText = apiRef.current.getLocaleText('footerRowSelected')(selectedRowCount);\n  return /*#__PURE__*/_jsx(GridSelectedRowCountRoot, _extends({\n    className: clsx(classes.root, className),\n    ownerState: ownerState\n  }, other, {\n    ref: ref,\n    children: rowSelectedText\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridSelectedRowCount.displayName = \"GridSelectedRowCount\";\nprocess.env.NODE_ENV !== \"production\" ? GridSelectedRowCount.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  selectedRowCount: PropTypes.number.isRequired,\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport { GridSelectedRowCount };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}