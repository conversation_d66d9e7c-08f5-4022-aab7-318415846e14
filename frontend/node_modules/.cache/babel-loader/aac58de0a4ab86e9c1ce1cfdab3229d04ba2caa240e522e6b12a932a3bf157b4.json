{"ast": null, "code": "import * as React from 'react';\nimport { useFirstRender } from \"../../utils/useFirstRender.js\";\nexport const useGridRegisterPipeProcessor = (apiRef, group, callback, enabled = true) => {\n  const cleanup = React.useRef(null);\n  const id = React.useRef(`mui-${Math.round(Math.random() * 1e9)}`);\n  const registerPreProcessor = React.useCallback(() => {\n    cleanup.current = apiRef.current.registerPipeProcessor(group, id.current, callback);\n  }, [apiRef, callback, group]);\n  useFirstRender(() => {\n    if (enabled) {\n      registerPreProcessor();\n    }\n  });\n  const isFirstRender = React.useRef(true);\n  React.useEffect(() => {\n    if (isFirstRender.current) {\n      isFirstRender.current = false;\n    } else if (enabled) {\n      registerPreProcessor();\n    }\n    return () => {\n      if (cleanup.current) {\n        cleanup.current();\n        cleanup.current = null;\n      }\n    };\n  }, [registerPreProcessor, enabled]);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}