{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"item\", \"hasMultipleFilters\", \"deleteFilter\", \"applyFilterChanges\", \"showMultiFilterOperators\", \"disableMultiFilterOperator\", \"applyMultiFilterOperatorChanges\", \"focusElementRef\", \"logicOperators\", \"columnsSort\", \"filterColumns\", \"deleteIconProps\", \"logicOperatorInputProps\", \"operatorInputProps\", \"columnInputProps\", \"valueInputProps\", \"readOnly\", \"children\"],\n  _excluded2 = [\"InputComponentProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport useId from '@mui/utils/useId';\nimport capitalize from '@mui/utils/capitalize';\nimport { styled } from '@mui/material/styles';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { vars } from \"../../../constants/cssVariables.js\";\nimport { gridFilterableColumnDefinitionsSelector, gridColumnLookupSelector } from \"../../../hooks/features/columns/gridColumnsSelector.js\";\nimport { gridFilterModelSelector } from \"../../../hooks/features/filter/gridFilterSelector.js\";\nimport { useGridSelector } from \"../../../hooks/utils/useGridSelector.js\";\nimport { GridLogicOperator } from \"../../../models/gridFilterItem.js\";\nimport { useGridApiContext } from \"../../../hooks/utils/useGridApiContext.js\";\nimport { useGridRootProps } from \"../../../hooks/utils/useGridRootProps.js\";\nimport { getDataGridUtilityClass } from \"../../../constants/gridClasses.js\";\nimport { getValueFromValueOptions, getValueOptions } from \"./filterPanelUtils.js\";\nimport { gridPivotActiveSelector, gridPivotInitialColumnsSelector } from \"../../../hooks/features/pivoting/index.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { createElement as _createElement } from \"react\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['filterForm'],\n    deleteIcon: ['filterFormDeleteIcon'],\n    logicOperatorInput: ['filterFormLogicOperatorInput'],\n    columnInput: ['filterFormColumnInput'],\n    operatorInput: ['filterFormOperatorInput'],\n    valueInput: ['filterFormValueInput']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst GridFilterFormRoot = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'FilterForm'\n})({\n  display: 'flex',\n  gap: vars.spacing(1.5)\n});\nconst FilterFormDeleteIcon = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'FilterFormDeleteIcon'\n})({\n  flexShrink: 0,\n  display: 'flex',\n  justifyContent: 'center',\n  alignItems: 'center'\n});\nconst FilterFormLogicOperatorInput = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'FilterFormLogicOperatorInput'\n})({\n  minWidth: 75,\n  justifyContent: 'end'\n});\nconst FilterFormColumnInput = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'FilterFormColumnInput'\n})({\n  width: 150\n});\nconst FilterFormOperatorInput = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'FilterFormOperatorInput'\n})({\n  width: 150\n});\nconst FilterFormValueInput = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'FilterFormValueInput'\n})({\n  width: 190\n});\nconst getLogicOperatorLocaleKey = logicOperator => {\n  switch (logicOperator) {\n    case GridLogicOperator.And:\n      return 'filterPanelOperatorAnd';\n    case GridLogicOperator.Or:\n      return 'filterPanelOperatorOr';\n    default:\n      throw new Error('MUI X: Invalid `logicOperator` property in the `GridFilterPanel`.');\n  }\n};\nconst getColumnLabel = col => col.headerName || col.field;\nconst collator = new Intl.Collator();\nconst GridFilterForm = forwardRef(function GridFilterForm(props, ref) {\n  const {\n      item,\n      hasMultipleFilters,\n      deleteFilter,\n      applyFilterChanges,\n      showMultiFilterOperators,\n      disableMultiFilterOperator,\n      applyMultiFilterOperatorChanges,\n      focusElementRef,\n      logicOperators = [GridLogicOperator.And, GridLogicOperator.Or],\n      columnsSort,\n      filterColumns,\n      deleteIconProps = {},\n      logicOperatorInputProps = {},\n      operatorInputProps = {},\n      columnInputProps = {},\n      valueInputProps = {},\n      readOnly\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const apiRef = useGridApiContext();\n  const columnLookup = useGridSelector(apiRef, gridColumnLookupSelector);\n  const filterableColumns = useGridSelector(apiRef, gridFilterableColumnDefinitionsSelector);\n  const filterModel = useGridSelector(apiRef, gridFilterModelSelector);\n  const columnSelectId = useId();\n  const columnSelectLabelId = useId();\n  const operatorSelectId = useId();\n  const operatorSelectLabelId = useId();\n  const rootProps = useGridRootProps();\n  const classes = useUtilityClasses(rootProps);\n  const valueRef = React.useRef(null);\n  const filterSelectorRef = React.useRef(null);\n  const multiFilterOperator = filterModel.logicOperator ?? GridLogicOperator.And;\n  const hasLogicOperatorColumn = hasMultipleFilters && logicOperators.length > 0;\n  const baseSelectProps = rootProps.slotProps?.baseSelect || {};\n  const isBaseSelectNative = baseSelectProps.native ?? false;\n  const baseSelectOptionProps = rootProps.slotProps?.baseSelectOption || {};\n  const {\n      InputComponentProps\n    } = valueInputProps,\n    valueInputPropsOther = _objectWithoutPropertiesLoose(valueInputProps, _excluded2);\n  const pivotActive = useGridSelector(apiRef, gridPivotActiveSelector);\n  const initialColumns = useGridSelector(apiRef, gridPivotInitialColumnsSelector);\n  const {\n    filteredColumns,\n    selectedField\n  } = React.useMemo(() => {\n    let itemField = item.field;\n\n    // Yields a valid value if the current filter belongs to a column that is not filterable\n    const selectedNonFilterableColumn = columnLookup[item.field].filterable === false ? columnLookup[item.field] : null;\n    if (selectedNonFilterableColumn) {\n      return {\n        filteredColumns: [selectedNonFilterableColumn],\n        selectedField: itemField\n      };\n    }\n    if (pivotActive) {\n      return {\n        filteredColumns: filterableColumns.filter(column => initialColumns.get(column.field) !== undefined),\n        selectedField: itemField\n      };\n    }\n    if (filterColumns === undefined || typeof filterColumns !== 'function') {\n      return {\n        filteredColumns: filterableColumns,\n        selectedField: itemField\n      };\n    }\n    const filteredFields = filterColumns({\n      field: item.field,\n      columns: filterableColumns,\n      currentFilters: filterModel?.items || []\n    });\n    return {\n      filteredColumns: filterableColumns.filter(column => {\n        const isFieldIncluded = filteredFields.includes(column.field);\n        if (column.field === item.field && !isFieldIncluded) {\n          itemField = undefined;\n        }\n        return isFieldIncluded;\n      }),\n      selectedField: itemField\n    };\n  }, [item.field, columnLookup, pivotActive, filterColumns, filterableColumns, filterModel?.items, initialColumns]);\n  const sortedFilteredColumns = React.useMemo(() => {\n    switch (columnsSort) {\n      case 'asc':\n        return filteredColumns.sort((a, b) => collator.compare(getColumnLabel(a), getColumnLabel(b)));\n      case 'desc':\n        return filteredColumns.sort((a, b) => -collator.compare(getColumnLabel(a), getColumnLabel(b)));\n      default:\n        return filteredColumns;\n    }\n  }, [filteredColumns, columnsSort]);\n  const currentColumn = item.field ? apiRef.current.getColumn(item.field) : null;\n  const currentOperator = React.useMemo(() => {\n    if (!item.operator || !currentColumn) {\n      return null;\n    }\n    return currentColumn.filterOperators?.find(operator => operator.value === item.operator);\n  }, [item, currentColumn]);\n  const changeColumn = React.useCallback(event => {\n    const field = event.target.value;\n    const column = apiRef.current.getColumn(field);\n    if (column.field === currentColumn.field) {\n      // column did not change\n      return;\n    }\n\n    // try to keep the same operator when column change\n    const newOperator = column.filterOperators.find(operator => operator.value === item.operator) || column.filterOperators[0];\n\n    // Erase filter value if the input component or filtered column type is modified\n    const eraseFilterValue = !newOperator.InputComponent || newOperator.InputComponent !== currentOperator?.InputComponent || column.type !== currentColumn.type;\n    let filterValue = eraseFilterValue ? undefined : item.value;\n\n    // Check filter value against the new valueOptions\n    if (column.type === 'singleSelect' && filterValue !== undefined) {\n      const colDef = column;\n      const valueOptions = getValueOptions(colDef);\n      if (Array.isArray(filterValue)) {\n        filterValue = filterValue.filter(val => {\n          return (\n            // Only keep values that are in the new value options\n            getValueFromValueOptions(val, valueOptions, colDef?.getOptionValue) !== undefined\n          );\n        });\n      } else if (getValueFromValueOptions(item.value, valueOptions, colDef?.getOptionValue) === undefined) {\n        // Reset the filter value if it is not in the new value options\n        filterValue = undefined;\n      }\n    }\n    applyFilterChanges(_extends({}, item, {\n      field,\n      operator: newOperator.value,\n      value: filterValue\n    }));\n  }, [apiRef, applyFilterChanges, item, currentColumn, currentOperator]);\n  const changeOperator = React.useCallback(event => {\n    const operator = event.target.value;\n    const newOperator = currentColumn?.filterOperators.find(op => op.value === operator);\n    const eraseItemValue = !newOperator?.InputComponent || newOperator?.InputComponent !== currentOperator?.InputComponent;\n    applyFilterChanges(_extends({}, item, {\n      operator,\n      value: eraseItemValue ? undefined : item.value\n    }));\n  }, [applyFilterChanges, item, currentColumn, currentOperator]);\n  const changeLogicOperator = React.useCallback(event => {\n    const logicOperator = event.target.value === GridLogicOperator.And.toString() ? GridLogicOperator.And : GridLogicOperator.Or;\n    applyMultiFilterOperatorChanges(logicOperator);\n  }, [applyMultiFilterOperatorChanges]);\n  const handleDeleteFilter = () => {\n    deleteFilter(item);\n  };\n  React.useImperativeHandle(focusElementRef, () => ({\n    focus: () => {\n      if (currentOperator?.InputComponent) {\n        valueRef?.current?.focus();\n      } else {\n        filterSelectorRef.current.focus();\n      }\n    }\n  }), [currentOperator]);\n  return /*#__PURE__*/_jsxs(GridFilterFormRoot, _extends({\n    className: classes.root,\n    \"data-id\": item.id,\n    ownerState: rootProps\n  }, other, {\n    ref: ref,\n    children: [/*#__PURE__*/_jsx(FilterFormDeleteIcon, _extends({}, deleteIconProps, {\n      className: clsx(classes.deleteIcon, deleteIconProps.className),\n      ownerState: rootProps,\n      children: /*#__PURE__*/_jsx(rootProps.slots.baseIconButton, _extends({\n        \"aria-label\": apiRef.current.getLocaleText('filterPanelDeleteIconLabel'),\n        title: apiRef.current.getLocaleText('filterPanelDeleteIconLabel'),\n        onClick: handleDeleteFilter,\n        size: \"small\",\n        disabled: readOnly\n      }, rootProps.slotProps?.baseIconButton, {\n        children: /*#__PURE__*/_jsx(rootProps.slots.filterPanelDeleteIcon, {\n          fontSize: \"small\"\n        })\n      }))\n    })), /*#__PURE__*/_jsx(FilterFormLogicOperatorInput, _extends({\n      as: rootProps.slots.baseSelect,\n      sx: [hasLogicOperatorColumn ? {\n        display: 'flex'\n      } : {\n        display: 'none'\n      }, showMultiFilterOperators ? {\n        visibility: 'visible'\n      } : {\n        visibility: 'hidden'\n      }, logicOperatorInputProps.sx],\n      className: clsx(classes.logicOperatorInput, logicOperatorInputProps.className),\n      ownerState: rootProps\n    }, logicOperatorInputProps, {\n      size: \"small\",\n      slotProps: {\n        htmlInput: {\n          'aria-label': apiRef.current.getLocaleText('filterPanelLogicOperator')\n        }\n      },\n      value: multiFilterOperator ?? '',\n      onChange: changeLogicOperator,\n      disabled: !!disableMultiFilterOperator || logicOperators.length === 1,\n      native: isBaseSelectNative\n    }, rootProps.slotProps?.baseSelect, {\n      children: logicOperators.map(logicOperator => /*#__PURE__*/_createElement(rootProps.slots.baseSelectOption, _extends({}, baseSelectOptionProps, {\n        native: isBaseSelectNative,\n        key: logicOperator.toString(),\n        value: logicOperator.toString()\n      }), apiRef.current.getLocaleText(getLogicOperatorLocaleKey(logicOperator))))\n    })), /*#__PURE__*/_jsx(FilterFormColumnInput, _extends({\n      as: rootProps.slots.baseSelect\n    }, columnInputProps, {\n      className: clsx(classes.columnInput, columnInputProps.className),\n      ownerState: rootProps,\n      size: \"small\",\n      labelId: columnSelectLabelId,\n      id: columnSelectId,\n      label: apiRef.current.getLocaleText('filterPanelColumns'),\n      value: selectedField ?? '',\n      onChange: changeColumn,\n      native: isBaseSelectNative,\n      disabled: readOnly\n    }, rootProps.slotProps?.baseSelect, {\n      children: sortedFilteredColumns.map(col => /*#__PURE__*/_createElement(rootProps.slots.baseSelectOption, _extends({}, baseSelectOptionProps, {\n        native: isBaseSelectNative,\n        key: col.field,\n        value: col.field\n      }), getColumnLabel(col)))\n    })), /*#__PURE__*/_jsx(FilterFormOperatorInput, _extends({\n      as: rootProps.slots.baseSelect,\n      size: \"small\"\n    }, operatorInputProps, {\n      className: clsx(classes.operatorInput, operatorInputProps.className),\n      ownerState: rootProps,\n      labelId: operatorSelectLabelId,\n      label: apiRef.current.getLocaleText('filterPanelOperator'),\n      id: operatorSelectId,\n      value: item.operator,\n      onChange: changeOperator,\n      native: isBaseSelectNative,\n      inputRef: filterSelectorRef,\n      disabled: readOnly\n    }, rootProps.slotProps?.baseSelect, {\n      children: currentColumn?.filterOperators?.map(operator => /*#__PURE__*/_createElement(rootProps.slots.baseSelectOption, _extends({}, baseSelectOptionProps, {\n        native: isBaseSelectNative,\n        key: operator.value,\n        value: operator.value\n      }), operator.label || apiRef.current.getLocaleText(`filterOperator${capitalize(operator.value)}`)))\n    })), /*#__PURE__*/_jsx(FilterFormValueInput, _extends({}, valueInputPropsOther, {\n      className: clsx(classes.valueInput, valueInputPropsOther.className),\n      ownerState: rootProps,\n      children: currentOperator?.InputComponent ? /*#__PURE__*/_jsx(currentOperator.InputComponent, _extends({\n        apiRef: apiRef,\n        item: item,\n        applyValue: applyFilterChanges,\n        focusElementRef: valueRef,\n        disabled: readOnly,\n        slotProps: {\n          root: {\n            size: 'small'\n          }\n        }\n      }, currentOperator.InputComponentProps, InputComponentProps), item.field) : null\n    }))]\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridFilterForm.displayName = \"GridFilterForm\";\nprocess.env.NODE_ENV !== \"production\" ? GridFilterForm.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * Callback called when the operator, column field or value is changed.\n   * @param {GridFilterItem} item The updated [[GridFilterItem]].\n   */\n  applyFilterChanges: PropTypes.func.isRequired,\n  /**\n   * Callback called when the logic operator is changed.\n   * @param {GridLogicOperator} operator The new logic operator.\n   */\n  applyMultiFilterOperatorChanges: PropTypes.func.isRequired,\n  /**\n   * @ignore - do not document.\n   */\n  children: PropTypes.node,\n  /**\n   * Props passed to the column input component.\n   * @default {}\n   */\n  columnInputProps: PropTypes.any,\n  /**\n   * Changes how the options in the columns selector should be ordered.\n   * If not specified, the order is derived from the `columns` prop.\n   */\n  columnsSort: PropTypes.oneOf(['asc', 'desc']),\n  /**\n   * Callback called when the delete button is clicked.\n   * @param {GridFilterItem} item The deleted [[GridFilterItem]].\n   */\n  deleteFilter: PropTypes.func.isRequired,\n  /**\n   * Props passed to the delete icon.\n   * @default {}\n   */\n  deleteIconProps: PropTypes.any,\n  /**\n   * If `true`, disables the logic operator field but still renders it.\n   */\n  disableMultiFilterOperator: PropTypes.bool,\n  /**\n   * Allows to filter the columns displayed in the filter form.\n   * @param {FilterColumnsArgs} args The columns of the grid and name of field.\n   * @returns {GridColDef['field'][]} The filtered fields array.\n   */\n  filterColumns: PropTypes.func,\n  /**\n   * A ref allowing to set imperative focus.\n   * It can be passed to the el\n   */\n  focusElementRef: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.func, PropTypes.object]),\n  /**\n   * If `true`, the logic operator field is rendered.\n   * The field will be invisible if `showMultiFilterOperators` is also `true`.\n   */\n  hasMultipleFilters: PropTypes.bool.isRequired,\n  /**\n   * The [[GridFilterItem]] representing this form.\n   */\n  item: PropTypes.shape({\n    field: PropTypes.string.isRequired,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    operator: PropTypes.string.isRequired,\n    value: PropTypes.any\n  }).isRequired,\n  /**\n   * Props passed to the logic operator input component.\n   * @default {}\n   */\n  logicOperatorInputProps: PropTypes.any,\n  /**\n   * Sets the available logic operators.\n   * @default [GridLogicOperator.And, GridLogicOperator.Or]\n   */\n  logicOperators: PropTypes.arrayOf(PropTypes.oneOf(['and', 'or']).isRequired),\n  /**\n   * Props passed to the operator input component.\n   * @default {}\n   */\n  operatorInputProps: PropTypes.any,\n  /**\n   * `true` if the filter is disabled/read only.\n   * i.e. `colDef.fiterable = false` but passed in `filterModel`\n   * @default false\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * If `true`, the logic operator field is visible.\n   */\n  showMultiFilterOperators: PropTypes.bool,\n  /**\n   * Props passed to the value input component.\n   * @default {}\n   */\n  valueInputProps: PropTypes.any\n} : void 0;\n\n/**\n * Demos:\n * - [Filtering - overview](https://mui.com/x/react-data-grid/filtering/)\n *\n * API:\n * - [GridFilterForm API](https://mui.com/x/api/data-grid/grid-filter-form/)\n */\nexport { GridFilterForm };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}