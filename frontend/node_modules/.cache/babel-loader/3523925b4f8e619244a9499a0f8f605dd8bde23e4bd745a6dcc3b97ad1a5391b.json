{"ast": null, "code": "import * as React from 'react';\nexport const useGridRefs = apiRef => {\n  const rootElementRef = React.useRef(null);\n  const mainElementRef = React.useRef(null);\n  const virtualScrollerRef = React.useRef(null);\n  const virtualScrollbarVerticalRef = React.useRef(null);\n  const virtualScrollbarHorizontalRef = React.useRef(null);\n  const columnHeadersContainerRef = React.useRef(null);\n  apiRef.current.register('public', {\n    rootElementRef\n  });\n  apiRef.current.register('private', {\n    mainElementRef,\n    virtualScrollerRef,\n    virtualScrollbarVerticalRef,\n    virtualScrollbarHorizontalRef,\n    columnHeadersContainerRef\n  });\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}