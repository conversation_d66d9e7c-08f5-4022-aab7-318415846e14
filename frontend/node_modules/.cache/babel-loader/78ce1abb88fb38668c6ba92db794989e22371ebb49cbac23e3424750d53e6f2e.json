{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport { styled } from '@mui/system';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { gridClasses, getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = () => {\n  const slots = {\n    root: ['topContainer']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, {});\n};\nconst Element = styled('div')({\n  position: 'sticky',\n  zIndex: 40,\n  top: 0\n});\nexport function GridTopContainer(props) {\n  const classes = useUtilityClasses();\n  return /*#__PURE__*/_jsx(Element, _extends({}, props, {\n    className: clsx(classes.root, gridClasses['container--top']),\n    role: \"presentation\"\n  }));\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}