{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport ownerDocument from '@mui/utils/ownerDocument';\nimport { gridClasses } from \"../../../constants/gridClasses.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { useGridEvent } from \"../../utils/useGridEvent.js\";\nimport { isNavigationKey } from \"../../../utils/keyboardUtils.js\";\nimport { gridFocusCellSelector, gridFocusColumnGroupHeaderSelector } from \"./gridFocusStateSelector.js\";\nimport { gridVisibleColumnDefinitionsSelector } from \"../columns/gridColumnsSelector.js\";\nimport { getVisibleRows } from \"../../utils/useGridVisibleRows.js\";\nimport { clamp } from \"../../../utils/utils.js\";\nimport { gridPinnedRowsSelector } from \"../rows/gridRowsSelector.js\";\nexport const focusStateInitializer = state => _extends({}, state, {\n  focus: {\n    cell: null,\n    columnHeader: null,\n    columnHeaderFilter: null,\n    columnGroupHeader: null\n  },\n  tabIndex: {\n    cell: null,\n    columnHeader: null,\n    columnHeaderFilter: null,\n    columnGroupHeader: null\n  }\n});\n\n/**\n * @requires useGridParamsApi (method)\n * @requires useGridRows (method)\n * @requires useGridEditing (event)\n */\nexport const useGridFocus = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridFocus');\n  const lastClickedCell = React.useRef(null);\n  const hasRootReference = apiRef.current.rootElementRef.current !== null;\n  const publishCellFocusOut = React.useCallback((cell, event) => {\n    if (cell) {\n      // The row might have been deleted\n      if (apiRef.current.getRow(cell.id)) {\n        apiRef.current.publishEvent('cellFocusOut', apiRef.current.getCellParams(cell.id, cell.field), event);\n      }\n    }\n  }, [apiRef]);\n  const setCellFocus = React.useCallback((id, field) => {\n    const focusedCell = gridFocusCellSelector(apiRef);\n    if (focusedCell?.id === id && focusedCell?.field === field) {\n      return;\n    }\n    apiRef.current.setState(state => {\n      logger.debug(`Focusing on cell with id=${id} and field=${field}`);\n      return _extends({}, state, {\n        tabIndex: {\n          cell: {\n            id,\n            field\n          },\n          columnHeader: null,\n          columnHeaderFilter: null,\n          columnGroupHeader: null\n        },\n        focus: {\n          cell: {\n            id,\n            field\n          },\n          columnHeader: null,\n          columnHeaderFilter: null,\n          columnGroupHeader: null\n        }\n      });\n    });\n\n    // The row might have been deleted\n    if (!apiRef.current.getRow(id)) {\n      return;\n    }\n    if (focusedCell) {\n      // There's a focused cell but another cell was clicked\n      // Publishes an event to notify that the focus was lost\n      publishCellFocusOut(focusedCell, {});\n    }\n    apiRef.current.publishEvent('cellFocusIn', apiRef.current.getCellParams(id, field));\n  }, [apiRef, logger, publishCellFocusOut]);\n  const setColumnHeaderFocus = React.useCallback((field, event = {}) => {\n    const cell = gridFocusCellSelector(apiRef);\n    publishCellFocusOut(cell, event);\n    apiRef.current.setState(state => {\n      logger.debug(`Focusing on column header with colIndex=${field}`);\n      return _extends({}, state, {\n        tabIndex: {\n          columnHeader: {\n            field\n          },\n          columnHeaderFilter: null,\n          cell: null,\n          columnGroupHeader: null\n        },\n        focus: {\n          columnHeader: {\n            field\n          },\n          columnHeaderFilter: null,\n          cell: null,\n          columnGroupHeader: null\n        }\n      });\n    });\n  }, [apiRef, logger, publishCellFocusOut]);\n  const setColumnHeaderFilterFocus = React.useCallback((field, event = {}) => {\n    const cell = gridFocusCellSelector(apiRef);\n    publishCellFocusOut(cell, event);\n    apiRef.current.setState(state => {\n      logger.debug(`Focusing on column header filter with colIndex=${field}`);\n      return _extends({}, state, {\n        tabIndex: {\n          columnHeader: null,\n          columnHeaderFilter: {\n            field\n          },\n          cell: null,\n          columnGroupHeader: null\n        },\n        focus: {\n          columnHeader: null,\n          columnHeaderFilter: {\n            field\n          },\n          cell: null,\n          columnGroupHeader: null\n        }\n      });\n    });\n  }, [apiRef, logger, publishCellFocusOut]);\n  const setColumnGroupHeaderFocus = React.useCallback((field, depth, event = {}) => {\n    const cell = gridFocusCellSelector(apiRef);\n    if (cell) {\n      apiRef.current.publishEvent('cellFocusOut', apiRef.current.getCellParams(cell.id, cell.field), event);\n    }\n    apiRef.current.setState(state => {\n      return _extends({}, state, {\n        tabIndex: {\n          columnGroupHeader: {\n            field,\n            depth\n          },\n          columnHeader: null,\n          columnHeaderFilter: null,\n          cell: null\n        },\n        focus: {\n          columnGroupHeader: {\n            field,\n            depth\n          },\n          columnHeader: null,\n          columnHeaderFilter: null,\n          cell: null\n        }\n      });\n    });\n  }, [apiRef]);\n  const getColumnGroupHeaderFocus = React.useCallback(() => gridFocusColumnGroupHeaderSelector(apiRef), [apiRef]);\n  const moveFocusToRelativeCell = React.useCallback((id, field, direction) => {\n    let columnIndexToFocus = apiRef.current.getColumnIndex(field);\n    const visibleColumns = gridVisibleColumnDefinitionsSelector(apiRef);\n    const currentPage = getVisibleRows(apiRef, {\n      pagination: props.pagination,\n      paginationMode: props.paginationMode\n    });\n    const pinnedRows = gridPinnedRowsSelector(apiRef);\n\n    // Include pinned rows as well\n    const currentPageRows = [].concat(pinnedRows.top || [], currentPage.rows, pinnedRows.bottom || []);\n    let rowIndexToFocus = currentPageRows.findIndex(row => row.id === id);\n    if (direction === 'right') {\n      columnIndexToFocus += 1;\n    } else if (direction === 'left') {\n      columnIndexToFocus -= 1;\n    } else {\n      rowIndexToFocus += 1;\n    }\n    if (columnIndexToFocus >= visibleColumns.length) {\n      // Go to next row if we are after the last column\n      rowIndexToFocus += 1;\n      if (rowIndexToFocus < currentPageRows.length) {\n        // Go to first column of the next row if there's one more row\n        columnIndexToFocus = 0;\n      }\n    } else if (columnIndexToFocus < 0) {\n      // Go to previous row if we are before the first column\n      rowIndexToFocus -= 1;\n      if (rowIndexToFocus >= 0) {\n        // Go to last column of the previous if there's one more row\n        columnIndexToFocus = visibleColumns.length - 1;\n      }\n    }\n    rowIndexToFocus = clamp(rowIndexToFocus, 0, currentPageRows.length - 1);\n    const rowToFocus = currentPageRows[rowIndexToFocus];\n    if (!rowToFocus) {\n      return;\n    }\n    const colSpanInfo = apiRef.current.unstable_getCellColSpanInfo(rowToFocus.id, columnIndexToFocus);\n    if (colSpanInfo && colSpanInfo.spannedByColSpan) {\n      if (direction === 'left' || direction === 'below') {\n        columnIndexToFocus = colSpanInfo.leftVisibleCellIndex;\n      } else if (direction === 'right') {\n        columnIndexToFocus = colSpanInfo.rightVisibleCellIndex;\n      }\n    }\n    columnIndexToFocus = clamp(columnIndexToFocus, 0, visibleColumns.length - 1);\n    const columnToFocus = visibleColumns[columnIndexToFocus];\n    apiRef.current.setCellFocus(rowToFocus.id, columnToFocus.field);\n  }, [apiRef, props.pagination, props.paginationMode]);\n  const handleCellDoubleClick = React.useCallback(({\n    id,\n    field\n  }) => {\n    apiRef.current.setCellFocus(id, field);\n  }, [apiRef]);\n  const handleCellKeyDown = React.useCallback((params, event) => {\n    // GRID_CELL_NAVIGATION_KEY_DOWN handles the focus on Enter, Tab and navigation keys\n    if (event.key === 'Enter' || event.key === 'Tab' || event.key === 'Shift' || isNavigationKey(event.key)) {\n      return;\n    }\n    apiRef.current.setCellFocus(params.id, params.field);\n  }, [apiRef]);\n  const handleColumnHeaderFocus = React.useCallback(({\n    field\n  }, event) => {\n    if (event.target !== event.currentTarget) {\n      return;\n    }\n    apiRef.current.setColumnHeaderFocus(field, event);\n  }, [apiRef]);\n  const handleColumnGroupHeaderFocus = React.useCallback(({\n    fields,\n    depth\n  }, event) => {\n    if (event.target !== event.currentTarget) {\n      return;\n    }\n    const focusedColumnGroup = gridFocusColumnGroupHeaderSelector(apiRef);\n    if (focusedColumnGroup !== null && focusedColumnGroup.depth === depth && fields.includes(focusedColumnGroup.field)) {\n      // This group cell has already been focused\n      return;\n    }\n    apiRef.current.setColumnGroupHeaderFocus(fields[0], depth, event);\n  }, [apiRef]);\n  const handleBlur = React.useCallback((_, event) => {\n    if (event.relatedTarget?.getAttribute('class')?.includes(gridClasses.columnHeader)) {\n      return;\n    }\n    logger.debug(`Clearing focus`);\n    apiRef.current.setState(state => _extends({}, state, {\n      focus: {\n        cell: null,\n        columnHeader: null,\n        columnHeaderFilter: null,\n        columnGroupHeader: null\n      }\n    }));\n  }, [logger, apiRef]);\n  const handleCellMouseDown = React.useCallback(params => {\n    lastClickedCell.current = params;\n  }, []);\n  const handleDocumentClick = React.useCallback(event => {\n    const cellParams = lastClickedCell.current;\n    lastClickedCell.current = null;\n    const focusedCell = gridFocusCellSelector(apiRef);\n    const canUpdateFocus = apiRef.current.unstable_applyPipeProcessors('canUpdateFocus', true, {\n      event,\n      cell: cellParams\n    });\n    if (!canUpdateFocus) {\n      return;\n    }\n    if (!focusedCell) {\n      if (cellParams) {\n        apiRef.current.setCellFocus(cellParams.id, cellParams.field);\n      }\n      return;\n    }\n    if (cellParams?.id === focusedCell.id && cellParams?.field === focusedCell.field) {\n      return;\n    }\n    const cellElement = apiRef.current.getCellElement(focusedCell.id, focusedCell.field);\n    if (cellElement?.contains(event.target)) {\n      return;\n    }\n    if (cellParams) {\n      apiRef.current.setCellFocus(cellParams.id, cellParams.field);\n    } else {\n      apiRef.current.setState(state => _extends({}, state, {\n        focus: {\n          cell: null,\n          columnHeader: null,\n          columnHeaderFilter: null,\n          columnGroupHeader: null\n        }\n      }));\n\n      // There's a focused cell but another element (not a cell) was clicked\n      // Publishes an event to notify that the focus was lost\n      publishCellFocusOut(focusedCell, event);\n    }\n  }, [apiRef, publishCellFocusOut]);\n  const handleCellModeChange = React.useCallback(params => {\n    if (params.cellMode === 'view') {\n      return;\n    }\n    const cell = gridFocusCellSelector(apiRef);\n    if (cell?.id !== params.id || cell?.field !== params.field) {\n      apiRef.current.setCellFocus(params.id, params.field);\n    }\n  }, [apiRef]);\n  const handleRowSet = React.useCallback(() => {\n    const cell = gridFocusCellSelector(apiRef);\n\n    // If the focused cell is in a row which does not exist anymore,\n    // focus previous row or remove the focus\n    if (cell && !apiRef.current.getRow(cell.id)) {\n      const lastFocusedRowId = cell.id;\n      let nextRowId = null;\n      if (typeof lastFocusedRowId !== 'undefined') {\n        const rowEl = apiRef.current.getRowElement(lastFocusedRowId);\n        const lastFocusedRowIndex = rowEl?.dataset.rowindex ? Number(rowEl?.dataset.rowindex) : 0;\n        const currentPage = getVisibleRows(apiRef, {\n          pagination: props.pagination,\n          paginationMode: props.paginationMode\n        });\n        const nextRow = currentPage.rows[clamp(lastFocusedRowIndex, 0, currentPage.rows.length - 1)];\n        nextRowId = nextRow?.id ?? null;\n      }\n      apiRef.current.setState(state => _extends({}, state, {\n        focus: {\n          cell: nextRowId === null ? null : {\n            id: nextRowId,\n            field: cell.field\n          },\n          columnHeader: null,\n          columnHeaderFilter: null,\n          columnGroupHeader: null\n        }\n      }));\n    }\n  }, [apiRef, props.pagination, props.paginationMode]);\n  const handlePaginationModelChange = useEventCallback(() => {\n    const currentFocusedCell = gridFocusCellSelector(apiRef);\n    if (!currentFocusedCell) {\n      return;\n    }\n    const currentPage = getVisibleRows(apiRef, {\n      pagination: props.pagination,\n      paginationMode: props.paginationMode\n    });\n    const rowIsInCurrentPage = currentPage.rows.find(row => row.id === currentFocusedCell.id);\n    if (rowIsInCurrentPage || currentPage.rows.length === 0) {\n      return;\n    }\n    const visibleColumns = gridVisibleColumnDefinitionsSelector(apiRef);\n    apiRef.current.setState(state => {\n      return _extends({}, state, {\n        tabIndex: {\n          cell: {\n            id: currentPage.rows[0].id,\n            field: visibleColumns[0].field\n          },\n          columnGroupHeader: null,\n          columnHeader: null,\n          columnHeaderFilter: null\n        }\n      });\n    });\n  });\n  const focusApi = {\n    setCellFocus,\n    setColumnHeaderFocus,\n    setColumnHeaderFilterFocus\n  };\n  const focusPrivateApi = {\n    moveFocusToRelativeCell,\n    setColumnGroupHeaderFocus,\n    getColumnGroupHeaderFocus\n  };\n  useGridApiMethod(apiRef, focusApi, 'public');\n  useGridApiMethod(apiRef, focusPrivateApi, 'private');\n  React.useEffect(() => {\n    const doc = ownerDocument(apiRef.current.rootElementRef.current);\n    doc.addEventListener('mouseup', handleDocumentClick);\n    return () => {\n      doc.removeEventListener('mouseup', handleDocumentClick);\n    };\n  }, [apiRef, hasRootReference, handleDocumentClick]);\n  useGridEvent(apiRef, 'columnHeaderBlur', handleBlur);\n  useGridEvent(apiRef, 'cellDoubleClick', handleCellDoubleClick);\n  useGridEvent(apiRef, 'cellMouseDown', handleCellMouseDown);\n  useGridEvent(apiRef, 'cellKeyDown', handleCellKeyDown);\n  useGridEvent(apiRef, 'cellModeChange', handleCellModeChange);\n  useGridEvent(apiRef, 'columnHeaderFocus', handleColumnHeaderFocus);\n  useGridEvent(apiRef, 'columnGroupHeaderFocus', handleColumnGroupHeaderFocus);\n  useGridEvent(apiRef, 'rowsSet', handleRowSet);\n  useGridEvent(apiRef, 'paginationModelChange', handlePaginationModelChange);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}