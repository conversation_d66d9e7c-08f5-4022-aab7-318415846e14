{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"classes\", \"columnMenuOpen\", \"colIndex\", \"height\", \"isResizing\", \"sortDirection\", \"hasFocus\", \"tabIndex\", \"separatorSide\", \"isDraggable\", \"headerComponent\", \"description\", \"elementId\", \"width\", \"columnMenuIconButton\", \"columnMenu\", \"columnTitleIconButtons\", \"headerClassName\", \"label\", \"resizable\", \"draggableContainerProps\", \"columnHeaderSeparatorProps\", \"style\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport useForkRef from '@mui/utils/useForkRef';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { useGridPrivateApiContext } from \"../../hooks/utils/useGridPrivateApiContext.js\";\nimport { GridColumnHeaderTitle } from \"./GridColumnHeaderTitle.js\";\nimport { GridColumnHeaderSeparator } from \"./GridColumnHeaderSeparator.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst GridGenericColumnHeaderItem = forwardRef(function GridGenericColumnHeaderItem(props, ref) {\n  const {\n      classes,\n      colIndex,\n      height,\n      isResizing,\n      sortDirection,\n      hasFocus,\n      tabIndex,\n      separatorSide,\n      isDraggable,\n      headerComponent,\n      description,\n      width,\n      columnMenuIconButton = null,\n      columnMenu = null,\n      columnTitleIconButtons = null,\n      headerClassName,\n      label,\n      resizable,\n      draggableContainerProps,\n      columnHeaderSeparatorProps,\n      style\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const apiRef = useGridPrivateApiContext();\n  const rootProps = useGridRootProps();\n  const headerCellRef = React.useRef(null);\n  const handleRef = useForkRef(headerCellRef, ref);\n  let ariaSort = 'none';\n  if (sortDirection != null) {\n    ariaSort = sortDirection === 'asc' ? 'ascending' : 'descending';\n  }\n  React.useLayoutEffect(() => {\n    const columnMenuState = apiRef.current.state.columnMenu;\n    if (hasFocus && !columnMenuState.open) {\n      const focusableElement = headerCellRef.current.querySelector('[tabindex=\"0\"]');\n      const elementToFocus = focusableElement || headerCellRef.current;\n      elementToFocus?.focus();\n      if (apiRef.current.columnHeadersContainerRef?.current) {\n        apiRef.current.columnHeadersContainerRef.current.scrollLeft = 0;\n      }\n    }\n  }, [apiRef, hasFocus]);\n  return /*#__PURE__*/_jsxs(\"div\", _extends({\n    className: clsx(classes.root, headerClassName),\n    style: _extends({}, style, {\n      width\n    }),\n    role: \"columnheader\",\n    tabIndex: tabIndex,\n    \"aria-colindex\": colIndex + 1,\n    \"aria-sort\": ariaSort\n  }, other, {\n    ref: handleRef,\n    children: [/*#__PURE__*/_jsxs(\"div\", _extends({\n      className: classes.draggableContainer,\n      draggable: isDraggable,\n      role: \"presentation\"\n    }, draggableContainerProps, {\n      children: [/*#__PURE__*/_jsxs(\"div\", {\n        className: classes.titleContainer,\n        role: \"presentation\",\n        children: [/*#__PURE__*/_jsx(\"div\", {\n          className: classes.titleContainerContent,\n          children: headerComponent !== undefined ? headerComponent : /*#__PURE__*/_jsx(GridColumnHeaderTitle, {\n            label: label,\n            description: description,\n            columnWidth: width\n          })\n        }), columnTitleIconButtons]\n      }), columnMenuIconButton]\n    })), /*#__PURE__*/_jsx(GridColumnHeaderSeparator, _extends({\n      resizable: !rootProps.disableColumnResize && !!resizable,\n      resizing: isResizing,\n      height: height,\n      side: separatorSide\n    }, columnHeaderSeparatorProps)), columnMenu]\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridGenericColumnHeaderItem.displayName = \"GridGenericColumnHeaderItem\";\nexport { GridGenericColumnHeaderItem };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}