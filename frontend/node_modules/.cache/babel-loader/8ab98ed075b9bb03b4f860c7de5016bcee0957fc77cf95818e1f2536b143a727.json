{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/Permissions.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Button, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Dialog, DialogTitle, DialogContent, DialogActions, TextField, IconButton, Tooltip, Chip } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, VpnKey as VpnKeyIcon } from '@mui/icons-material';\nimport { toast } from 'react-toastify';\nimport apiService from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Permissions = () => {\n  _s();\n  const [permissions, setPermissions] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [dialogOpen, setDialogOpen] = useState(false);\n  const [editingPermission, setEditingPermission] = useState(null);\n  const [formData, setFormData] = useState({\n    code: '',\n    name: '',\n    description: ''\n  });\n  useEffect(() => {\n    fetchPermissions();\n  }, []);\n  const fetchPermissions = async () => {\n    try {\n      const permissionsData = await apiService.getAllPermissions();\n      setPermissions(permissionsData);\n      setLoading(false);\n    } catch (error) {\n      toast.error('Erreur lors du chargement des permissions');\n      setLoading(false);\n    }\n  };\n  const handleOpenDialog = permission => {\n    if (permission) {\n      setEditingPermission(permission);\n      setFormData({\n        code: permission.code,\n        name: permission.name,\n        description: permission.description || ''\n      });\n    } else {\n      setEditingPermission(null);\n      setFormData({\n        code: '',\n        name: '',\n        description: ''\n      });\n    }\n    setDialogOpen(true);\n  };\n  const handleCloseDialog = () => {\n    setDialogOpen(false);\n    setEditingPermission(null);\n    setFormData({\n      code: '',\n      name: '',\n      description: ''\n    });\n  };\n  const handleSubmit = async () => {\n    try {\n      if (editingPermission) {\n        await apiService.updatePermission(editingPermission.id, formData);\n        toast.success('Permission modifiée avec succès');\n      } else {\n        await apiService.createPermission(formData);\n        toast.success('Permission créée avec succès');\n      }\n      handleCloseDialog();\n      fetchPermissions();\n    } catch (error) {\n      toast.error('Erreur lors de la sauvegarde de la permission');\n    }\n  };\n  const handleDelete = async permissionId => {\n    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette permission ?')) {\n      try {\n        await apiService.deletePermission(permissionId);\n        toast.success('Permission supprimée avec succès');\n        fetchPermissions();\n      } catch (error) {\n        toast.error('Erreur lors de la suppression de la permission');\n      }\n    }\n  };\n  const getPermissionTypeColor = code => {\n    if (code.includes('create')) return 'success';\n    if (code.includes('read')) return 'info';\n    if (code.includes('update')) return 'warning';\n    if (code.includes('delete')) return 'error';\n    return 'default';\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        mt: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        children: \"Chargement...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(VpnKeyIcon, {\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          component: \"h1\",\n          children: \"Gestion des Permissions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 22\n        }, this),\n        onClick: () => handleOpenDialog(),\n        children: \"Nouvelle Permission\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Code\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Nom\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"center\",\n              children: \"Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: permissions.map(permission => /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                fontFamily: \"monospace\",\n                children: permission.code\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle1\",\n                fontWeight: \"bold\",\n                children: permission.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: permission.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: permission.code.split(':')[1] || 'other',\n                size: \"small\",\n                color: getPermissionTypeColor(permission.code),\n                variant: \"outlined\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"Modifier\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  color: \"primary\",\n                  onClick: () => handleOpenDialog(permission),\n                  children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 188,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"Supprimer\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  color: \"error\",\n                  onClick: () => handleDelete(permission.id),\n                  children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 196,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 17\n            }, this)]\n          }, permission.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: dialogOpen,\n      onClose: handleCloseDialog,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: editingPermission ? 'Modifier la permission' : 'Créer une nouvelle permission'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Code de la permission\",\n            value: formData.code,\n            onChange: e => setFormData(prev => ({\n              ...prev,\n              code: e.target.value\n            })),\n            margin: \"normal\",\n            required: true,\n            placeholder: \"ex: user:create, role:read\",\n            helperText: \"Format recommand\\xE9: ressource:action (ex: user:create)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Nom de la permission\",\n            value: formData.name,\n            onChange: e => setFormData(prev => ({\n              ...prev,\n              name: e.target.value\n            })),\n            margin: \"normal\",\n            required: true,\n            placeholder: \"ex: Cr\\xE9er un utilisateur\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Description\",\n            value: formData.description,\n            onChange: e => setFormData(prev => ({\n              ...prev,\n              description: e.target.value\n            })),\n            margin: \"normal\",\n            multiline: true,\n            rows: 3,\n            placeholder: \"Description d\\xE9taill\\xE9e de la permission\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseDialog,\n          children: \"Annuler\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmit,\n          variant: \"contained\",\n          disabled: !formData.code.trim() || !formData.name.trim(),\n          children: editingPermission ? 'Modifier' : 'Créer'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 207,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 132,\n    columnNumber: 5\n  }, this);\n};\n_s(Permissions, \"V68M/iClpPU4RGVHLhedtCIGi1A=\");\n_c = Permissions;\nexport default Permissions;\nvar _c;\n$RefreshReg$(_c, \"Permissions\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "IconButton", "<PERSON><PERSON><PERSON>", "Chip", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "VpnKey", "VpnKeyIcon", "toast", "apiService", "jsxDEV", "_jsxDEV", "Permissions", "_s", "permissions", "setPermissions", "loading", "setLoading", "dialogOpen", "setDialogOpen", "editingPermission", "setEditingPermission", "formData", "setFormData", "code", "name", "description", "fetchPermissions", "permissionsData", "getAllPermissions", "error", "handleOpenDialog", "permission", "handleCloseDialog", "handleSubmit", "updatePermission", "id", "success", "createPermission", "handleDelete", "permissionId", "window", "confirm", "deletePermission", "getPermissionTypeColor", "includes", "sx", "display", "justifyContent", "mt", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "p", "alignItems", "mb", "gap", "color", "variant", "component", "startIcon", "onClick", "align", "map", "fontFamily", "fontWeight", "label", "split", "size", "title", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "pt", "value", "onChange", "e", "prev", "target", "margin", "required", "placeholder", "helperText", "multiline", "rows", "disabled", "trim", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/Permissions.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  <PERSON>pography,\n  Button,\n  Paper,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  IconButton,\n  Tooltip,\n  Chip,\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Vpn<PERSON>ey as VpnKeyIcon,\n} from '@mui/icons-material';\nimport { toast } from 'react-toastify';\nimport apiService from '../services/api';\nimport { Permission, PermissionCreate } from '../types';\n\nconst Permissions: React.FC = () => {\n  const [permissions, setPermissions] = useState<Permission[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [dialogOpen, setDialogOpen] = useState(false);\n  const [editingPermission, setEditingPermission] = useState<Permission | null>(null);\n  const [formData, setFormData] = useState<PermissionCreate>({\n    code: '',\n    name: '',\n    description: '',\n  });\n\n  useEffect(() => {\n    fetchPermissions();\n  }, []);\n\n  const fetchPermissions = async () => {\n    try {\n      const permissionsData = await apiService.getAllPermissions();\n      setPermissions(permissionsData);\n      setLoading(false);\n    } catch (error) {\n      toast.error('Erreur lors du chargement des permissions');\n      setLoading(false);\n    }\n  };\n\n  const handleOpenDialog = (permission?: Permission) => {\n    if (permission) {\n      setEditingPermission(permission);\n      setFormData({\n        code: permission.code,\n        name: permission.name,\n        description: permission.description || '',\n      });\n    } else {\n      setEditingPermission(null);\n      setFormData({\n        code: '',\n        name: '',\n        description: '',\n      });\n    }\n    setDialogOpen(true);\n  };\n\n  const handleCloseDialog = () => {\n    setDialogOpen(false);\n    setEditingPermission(null);\n    setFormData({\n      code: '',\n      name: '',\n      description: '',\n    });\n  };\n\n  const handleSubmit = async () => {\n    try {\n      if (editingPermission) {\n        await apiService.updatePermission(editingPermission.id, formData);\n        toast.success('Permission modifiée avec succès');\n      } else {\n        await apiService.createPermission(formData);\n        toast.success('Permission créée avec succès');\n      }\n      handleCloseDialog();\n      fetchPermissions();\n    } catch (error) {\n      toast.error('Erreur lors de la sauvegarde de la permission');\n    }\n  };\n\n  const handleDelete = async (permissionId: number) => {\n    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette permission ?')) {\n      try {\n        await apiService.deletePermission(permissionId);\n        toast.success('Permission supprimée avec succès');\n        fetchPermissions();\n      } catch (error) {\n        toast.error('Erreur lors de la suppression de la permission');\n      }\n    }\n  };\n\n  const getPermissionTypeColor = (code: string) => {\n    if (code.includes('create')) return 'success';\n    if (code.includes('read')) return 'info';\n    if (code.includes('update')) return 'warning';\n    if (code.includes('delete')) return 'error';\n    return 'default';\n  };\n\n  if (loading) {\n    return (\n      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>\n        <Typography>Chargement...</Typography>\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ p: 3 }}>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n          <VpnKeyIcon color=\"primary\" />\n          <Typography variant=\"h4\" component=\"h1\">\n            Gestion des Permissions\n          </Typography>\n        </Box>\n        <Button\n          variant=\"contained\"\n          startIcon={<AddIcon />}\n          onClick={() => handleOpenDialog()}\n        >\n          Nouvelle Permission\n        </Button>\n      </Box>\n\n      <TableContainer component={Paper}>\n        <Table>\n          <TableHead>\n            <TableRow>\n              <TableCell>Code</TableCell>\n              <TableCell>Nom</TableCell>\n              <TableCell>Description</TableCell>\n              <TableCell>Type</TableCell>\n              <TableCell align=\"center\">Actions</TableCell>\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {permissions.map((permission) => (\n              <TableRow key={permission.id}>\n                <TableCell>\n                  <Typography variant=\"body2\" fontFamily=\"monospace\">\n                    {permission.code}\n                  </Typography>\n                </TableCell>\n                <TableCell>\n                  <Typography variant=\"subtitle1\" fontWeight=\"bold\">\n                    {permission.name}\n                  </Typography>\n                </TableCell>\n                <TableCell>{permission.description}</TableCell>\n                <TableCell>\n                  <Chip\n                    label={permission.code.split(':')[1] || 'other'}\n                    size=\"small\"\n                    color={getPermissionTypeColor(permission.code) as any}\n                    variant=\"outlined\"\n                  />\n                </TableCell>\n                <TableCell align=\"center\">\n                  <Tooltip title=\"Modifier\">\n                    <IconButton\n                      color=\"primary\"\n                      onClick={() => handleOpenDialog(permission)}\n                    >\n                      <EditIcon />\n                    </IconButton>\n                  </Tooltip>\n                  <Tooltip title=\"Supprimer\">\n                    <IconButton\n                      color=\"error\"\n                      onClick={() => handleDelete(permission.id)}\n                    >\n                      <DeleteIcon />\n                    </IconButton>\n                  </Tooltip>\n                </TableCell>\n              </TableRow>\n            ))}\n          </TableBody>\n        </Table>\n      </TableContainer>\n\n      {/* Dialog pour créer/modifier une permission */}\n      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n        <DialogTitle>\n          {editingPermission ? 'Modifier la permission' : 'Créer une nouvelle permission'}\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 2 }}>\n            <TextField\n              fullWidth\n              label=\"Code de la permission\"\n              value={formData.code}\n              onChange={(e) => setFormData(prev => ({ ...prev, code: e.target.value }))}\n              margin=\"normal\"\n              required\n              placeholder=\"ex: user:create, role:read\"\n              helperText=\"Format recommandé: ressource:action (ex: user:create)\"\n            />\n            <TextField\n              fullWidth\n              label=\"Nom de la permission\"\n              value={formData.name}\n              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}\n              margin=\"normal\"\n              required\n              placeholder=\"ex: Créer un utilisateur\"\n            />\n            <TextField\n              fullWidth\n              label=\"Description\"\n              value={formData.description}\n              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}\n              margin=\"normal\"\n              multiline\n              rows={3}\n              placeholder=\"Description détaillée de la permission\"\n            />\n          </Box>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCloseDialog}>Annuler</Button>\n          <Button\n            onClick={handleSubmit}\n            variant=\"contained\"\n            disabled={!formData.code.trim() || !formData.name.trim()}\n          >\n            {editingPermission ? 'Modifier' : 'Créer'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default Permissions;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,UAAU,EACVC,OAAO,EACPC,IAAI,QACC,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,MAAM,IAAIC,UAAU,QACf,qBAAqB;AAC5B,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,UAAU,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGzC,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGnC,QAAQ,CAAe,EAAE,CAAC;EAChE,MAAM,CAACoC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACwC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzC,QAAQ,CAAoB,IAAI,CAAC;EACnF,MAAM,CAAC0C,QAAQ,EAAEC,WAAW,CAAC,GAAG3C,QAAQ,CAAmB;IACzD4C,IAAI,EAAE,EAAE;IACRC,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE;EACf,CAAC,CAAC;EAEF7C,SAAS,CAAC,MAAM;IACd8C,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMC,eAAe,GAAG,MAAMnB,UAAU,CAACoB,iBAAiB,CAAC,CAAC;MAC5Dd,cAAc,CAACa,eAAe,CAAC;MAC/BX,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdtB,KAAK,CAACsB,KAAK,CAAC,2CAA2C,CAAC;MACxDb,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMc,gBAAgB,GAAIC,UAAuB,IAAK;IACpD,IAAIA,UAAU,EAAE;MACdX,oBAAoB,CAACW,UAAU,CAAC;MAChCT,WAAW,CAAC;QACVC,IAAI,EAAEQ,UAAU,CAACR,IAAI;QACrBC,IAAI,EAAEO,UAAU,CAACP,IAAI;QACrBC,WAAW,EAAEM,UAAU,CAACN,WAAW,IAAI;MACzC,CAAC,CAAC;IACJ,CAAC,MAAM;MACLL,oBAAoB,CAAC,IAAI,CAAC;MAC1BE,WAAW,CAAC;QACVC,IAAI,EAAE,EAAE;QACRC,IAAI,EAAE,EAAE;QACRC,WAAW,EAAE;MACf,CAAC,CAAC;IACJ;IACAP,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMc,iBAAiB,GAAGA,CAAA,KAAM;IAC9Bd,aAAa,CAAC,KAAK,CAAC;IACpBE,oBAAoB,CAAC,IAAI,CAAC;IAC1BE,WAAW,CAAC;MACVC,IAAI,EAAE,EAAE;MACRC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE;IACf,CAAC,CAAC;EACJ,CAAC;EAED,MAAMQ,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,IAAId,iBAAiB,EAAE;QACrB,MAAMX,UAAU,CAAC0B,gBAAgB,CAACf,iBAAiB,CAACgB,EAAE,EAAEd,QAAQ,CAAC;QACjEd,KAAK,CAAC6B,OAAO,CAAC,iCAAiC,CAAC;MAClD,CAAC,MAAM;QACL,MAAM5B,UAAU,CAAC6B,gBAAgB,CAAChB,QAAQ,CAAC;QAC3Cd,KAAK,CAAC6B,OAAO,CAAC,8BAA8B,CAAC;MAC/C;MACAJ,iBAAiB,CAAC,CAAC;MACnBN,gBAAgB,CAAC,CAAC;IACpB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdtB,KAAK,CAACsB,KAAK,CAAC,+CAA+C,CAAC;IAC9D;EACF,CAAC;EAED,MAAMS,YAAY,GAAG,MAAOC,YAAoB,IAAK;IACnD,IAAIC,MAAM,CAACC,OAAO,CAAC,uDAAuD,CAAC,EAAE;MAC3E,IAAI;QACF,MAAMjC,UAAU,CAACkC,gBAAgB,CAACH,YAAY,CAAC;QAC/ChC,KAAK,CAAC6B,OAAO,CAAC,kCAAkC,CAAC;QACjDV,gBAAgB,CAAC,CAAC;MACpB,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdtB,KAAK,CAACsB,KAAK,CAAC,gDAAgD,CAAC;MAC/D;IACF;EACF,CAAC;EAED,MAAMc,sBAAsB,GAAIpB,IAAY,IAAK;IAC/C,IAAIA,IAAI,CAACqB,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,SAAS;IAC7C,IAAIrB,IAAI,CAACqB,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,MAAM;IACxC,IAAIrB,IAAI,CAACqB,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,SAAS;IAC7C,IAAIrB,IAAI,CAACqB,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;IAC3C,OAAO,SAAS;EAClB,CAAC;EAED,IAAI7B,OAAO,EAAE;IACX,oBACEL,OAAA,CAAC7B,GAAG;MAACgE,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,eAC5DvC,OAAA,CAAC5B,UAAU;QAAAmE,QAAA,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CAAC;EAEV;EAEA,oBACE3C,OAAA,CAAC7B,GAAG;IAACgE,EAAE,EAAE;MAAES,CAAC,EAAE;IAAE,CAAE;IAAAL,QAAA,gBAChBvC,OAAA,CAAC7B,GAAG;MAACgE,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEQ,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,gBACzFvC,OAAA,CAAC7B,GAAG;QAACgE,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAES,UAAU,EAAE,QAAQ;UAAEE,GAAG,EAAE;QAAE,CAAE;QAAAR,QAAA,gBACzDvC,OAAA,CAACJ,UAAU;UAACoD,KAAK,EAAC;QAAS;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9B3C,OAAA,CAAC5B,UAAU;UAAC6E,OAAO,EAAC,IAAI;UAACC,SAAS,EAAC,IAAI;UAAAX,QAAA,EAAC;QAExC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACN3C,OAAA,CAAC3B,MAAM;QACL4E,OAAO,EAAC,WAAW;QACnBE,SAAS,eAAEnD,OAAA,CAACV,OAAO;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBS,OAAO,EAAEA,CAAA,KAAMhC,gBAAgB,CAAC,CAAE;QAAAmB,QAAA,EACnC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAEN3C,OAAA,CAACtB,cAAc;MAACwE,SAAS,EAAE5E,KAAM;MAAAiE,QAAA,eAC/BvC,OAAA,CAACzB,KAAK;QAAAgE,QAAA,gBACJvC,OAAA,CAACrB,SAAS;UAAA4D,QAAA,eACRvC,OAAA,CAACpB,QAAQ;YAAA2D,QAAA,gBACPvC,OAAA,CAACvB,SAAS;cAAA8D,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC3B3C,OAAA,CAACvB,SAAS;cAAA8D,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC1B3C,OAAA,CAACvB,SAAS;cAAA8D,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAClC3C,OAAA,CAACvB,SAAS;cAAA8D,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC3B3C,OAAA,CAACvB,SAAS;cAAC4E,KAAK,EAAC,QAAQ;cAAAd,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZ3C,OAAA,CAACxB,SAAS;UAAA+D,QAAA,EACPpC,WAAW,CAACmD,GAAG,CAAEjC,UAAU,iBAC1BrB,OAAA,CAACpB,QAAQ;YAAA2D,QAAA,gBACPvC,OAAA,CAACvB,SAAS;cAAA8D,QAAA,eACRvC,OAAA,CAAC5B,UAAU;gBAAC6E,OAAO,EAAC,OAAO;gBAACM,UAAU,EAAC,WAAW;gBAAAhB,QAAA,EAC/ClB,UAAU,CAACR;cAAI;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACZ3C,OAAA,CAACvB,SAAS;cAAA8D,QAAA,eACRvC,OAAA,CAAC5B,UAAU;gBAAC6E,OAAO,EAAC,WAAW;gBAACO,UAAU,EAAC,MAAM;gBAAAjB,QAAA,EAC9ClB,UAAU,CAACP;cAAI;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACZ3C,OAAA,CAACvB,SAAS;cAAA8D,QAAA,EAAElB,UAAU,CAACN;YAAW;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/C3C,OAAA,CAACvB,SAAS;cAAA8D,QAAA,eACRvC,OAAA,CAACZ,IAAI;gBACHqE,KAAK,EAAEpC,UAAU,CAACR,IAAI,CAAC6C,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,OAAQ;gBAChDC,IAAI,EAAC,OAAO;gBACZX,KAAK,EAAEf,sBAAsB,CAACZ,UAAU,CAACR,IAAI,CAAS;gBACtDoC,OAAO,EAAC;cAAU;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eACZ3C,OAAA,CAACvB,SAAS;cAAC4E,KAAK,EAAC,QAAQ;cAAAd,QAAA,gBACvBvC,OAAA,CAACb,OAAO;gBAACyE,KAAK,EAAC,UAAU;gBAAArB,QAAA,eACvBvC,OAAA,CAACd,UAAU;kBACT8D,KAAK,EAAC,SAAS;kBACfI,OAAO,EAAEA,CAAA,KAAMhC,gBAAgB,CAACC,UAAU,CAAE;kBAAAkB,QAAA,eAE5CvC,OAAA,CAACR,QAAQ;oBAAAgD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACV3C,OAAA,CAACb,OAAO;gBAACyE,KAAK,EAAC,WAAW;gBAAArB,QAAA,eACxBvC,OAAA,CAACd,UAAU;kBACT8D,KAAK,EAAC,OAAO;kBACbI,OAAO,EAAEA,CAAA,KAAMxB,YAAY,CAACP,UAAU,CAACI,EAAE,CAAE;kBAAAc,QAAA,eAE3CvC,OAAA,CAACN,UAAU;oBAAA8C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA,GArCCtB,UAAU,CAACI,EAAE;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAsClB,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAGjB3C,OAAA,CAACnB,MAAM;MAACgF,IAAI,EAAEtD,UAAW;MAACuD,OAAO,EAAExC,iBAAkB;MAACyC,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAAzB,QAAA,gBAC3EvC,OAAA,CAAClB,WAAW;QAAAyD,QAAA,EACT9B,iBAAiB,GAAG,wBAAwB,GAAG;MAA+B;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE,CAAC,eACd3C,OAAA,CAACjB,aAAa;QAAAwD,QAAA,eACZvC,OAAA,CAAC7B,GAAG;UAACgE,EAAE,EAAE;YAAE8B,EAAE,EAAE;UAAE,CAAE;UAAA1B,QAAA,gBACjBvC,OAAA,CAACf,SAAS;YACR+E,SAAS;YACTP,KAAK,EAAC,uBAAuB;YAC7BS,KAAK,EAAEvD,QAAQ,CAACE,IAAK;YACrBsD,QAAQ,EAAGC,CAAC,IAAKxD,WAAW,CAACyD,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAExD,IAAI,EAAEuD,CAAC,CAACE,MAAM,CAACJ;YAAM,CAAC,CAAC,CAAE;YAC1EK,MAAM,EAAC,QAAQ;YACfC,QAAQ;YACRC,WAAW,EAAC,4BAA4B;YACxCC,UAAU,EAAC;UAAuD;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,eACF3C,OAAA,CAACf,SAAS;YACR+E,SAAS;YACTP,KAAK,EAAC,sBAAsB;YAC5BS,KAAK,EAAEvD,QAAQ,CAACG,IAAK;YACrBqD,QAAQ,EAAGC,CAAC,IAAKxD,WAAW,CAACyD,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAEvD,IAAI,EAAEsD,CAAC,CAACE,MAAM,CAACJ;YAAM,CAAC,CAAC,CAAE;YAC1EK,MAAM,EAAC,QAAQ;YACfC,QAAQ;YACRC,WAAW,EAAC;UAA0B;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACF3C,OAAA,CAACf,SAAS;YACR+E,SAAS;YACTP,KAAK,EAAC,aAAa;YACnBS,KAAK,EAAEvD,QAAQ,CAACI,WAAY;YAC5BoD,QAAQ,EAAGC,CAAC,IAAKxD,WAAW,CAACyD,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAEtD,WAAW,EAAEqD,CAAC,CAACE,MAAM,CAACJ;YAAM,CAAC,CAAC,CAAE;YACjFK,MAAM,EAAC,QAAQ;YACfI,SAAS;YACTC,IAAI,EAAE,CAAE;YACRH,WAAW,EAAC;UAAwC;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChB3C,OAAA,CAAChB,aAAa;QAAAuD,QAAA,gBACZvC,OAAA,CAAC3B,MAAM;UAAC+E,OAAO,EAAE9B,iBAAkB;UAAAiB,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACpD3C,OAAA,CAAC3B,MAAM;UACL+E,OAAO,EAAE7B,YAAa;UACtB0B,OAAO,EAAC,WAAW;UACnB4B,QAAQ,EAAE,CAAClE,QAAQ,CAACE,IAAI,CAACiE,IAAI,CAAC,CAAC,IAAI,CAACnE,QAAQ,CAACG,IAAI,CAACgE,IAAI,CAAC,CAAE;UAAAvC,QAAA,EAExD9B,iBAAiB,GAAG,UAAU,GAAG;QAAO;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACzC,EAAA,CAjOID,WAAqB;AAAA8E,EAAA,GAArB9E,WAAqB;AAmO3B,eAAeA,WAAW;AAAC,IAAA8E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}