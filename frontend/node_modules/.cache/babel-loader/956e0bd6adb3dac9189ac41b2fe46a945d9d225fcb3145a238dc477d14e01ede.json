{"ast": null, "code": "import { rtlFlipSide } from \"../../utils/rtlFlipSide.js\";\nexport function attachPinnedStyle(style, isRtl, pinnedPosition, pinnedOffset) {\n  const side = rtlFlipSide(pinnedPosition, isRtl);\n  if (!side || pinnedOffset === undefined) {\n    return style;\n  }\n  style[side] = pinnedOffset;\n  return style;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}