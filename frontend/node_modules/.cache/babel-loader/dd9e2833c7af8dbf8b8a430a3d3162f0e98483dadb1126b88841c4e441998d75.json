{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport { warnOnce } from '@mui/x-internals/warning';\nimport { gridDimensionsSelector } from \"../dimensions/index.js\";\nimport { useGridEvent } from \"../../utils/useGridEvent.js\";\nexport const listViewStateInitializer = (state, props, apiRef) => _extends({}, state, {\n  listViewColumn: props.listViewColumn ? _extends({}, props.listViewColumn, {\n    computedWidth: getListColumnWidth(apiRef)\n  }) : undefined\n});\nexport function useGridListView(apiRef, props) {\n  /*\n   * EVENTS\n   */\n  const updateListColumnWidth = () => {\n    apiRef.current.setState(state => {\n      if (!state.listViewColumn) {\n        return state;\n      }\n      return _extends({}, state, {\n        listViewColumn: _extends({}, state.listViewColumn, {\n          computedWidth: getListColumnWidth(apiRef)\n        })\n      });\n    });\n  };\n  const prevInnerWidth = React.useRef(null);\n  const handleGridSizeChange = viewportInnerSize => {\n    if (prevInnerWidth.current !== viewportInnerSize.width) {\n      prevInnerWidth.current = viewportInnerSize.width;\n      updateListColumnWidth();\n    }\n  };\n  useGridEvent(apiRef, 'viewportInnerSizeChange', handleGridSizeChange);\n  useGridEvent(apiRef, 'columnVisibilityModelChange', updateListColumnWidth);\n\n  /*\n   * EFFECTS\n   */\n  useEnhancedEffect(() => {\n    const listColumn = props.listViewColumn;\n    if (listColumn) {\n      apiRef.current.setState(state => {\n        return _extends({}, state, {\n          listViewColumn: _extends({}, listColumn, {\n            computedWidth: getListColumnWidth(apiRef)\n          })\n        });\n      });\n    }\n  }, [apiRef, props.listViewColumn]);\n  React.useEffect(() => {\n    if (props.listView && !props.listViewColumn) {\n      warnOnce(['MUI X: The `listViewColumn` prop must be set if `listView` is enabled.', 'To fix, pass a column definition to the `listViewColumn` prop, e.g. `{ field: \"example\", renderCell: (params) => <div>{params.row.id}</div> }`.', 'For more details, see https://mui.com/x/react-data-grid/list-view/']);\n    }\n  }, [props.listView, props.listViewColumn]);\n}\nfunction getListColumnWidth(apiRef) {\n  return gridDimensionsSelector(apiRef).viewportInnerSize.width;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}