{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridCellEditing } from \"./useGridCellEditing.js\";\nimport { GridCellModes, GridEditModes } from \"../../../models/gridEditRowModel.js\";\nimport { useGridRowEditing } from \"./useGridRowEditing.js\";\nimport { gridEditRowsStateSelector } from \"./gridEditingSelectors.js\";\nimport { isAutogeneratedRowNode } from \"../rows/gridRowsUtils.js\";\nexport const editingStateInitializer = state => _extends({}, state, {\n  editRows: {}\n});\nexport const useGridEditing = (apiRef, props) => {\n  useGridCellEditing(apiRef, props);\n  useGridRowEditing(apiRef, props);\n  const debounceMap = React.useRef({});\n  const {\n    isCellEditable: isCellEditableProp\n  } = props;\n  const isCellEditable = React.useCallback(params => {\n    if (isAutogeneratedRowNode(params.rowNode)) {\n      return false;\n    }\n    if (!params.colDef.editable) {\n      return false;\n    }\n    if (!params.colDef.renderEditCell) {\n      return false;\n    }\n    if (isCellEditableProp) {\n      return isCellEditableProp(params);\n    }\n    return true;\n  }, [isCellEditableProp]);\n  const maybeDebounce = (id, field, debounceMs, callback) => {\n    if (!debounceMs) {\n      callback();\n      return;\n    }\n    if (!debounceMap.current[id]) {\n      debounceMap.current[id] = {};\n    }\n    if (debounceMap.current[id][field]) {\n      const [timeout] = debounceMap.current[id][field];\n      clearTimeout(timeout);\n    }\n\n    // To run the callback immediately without waiting the timeout\n    const runImmediately = () => {\n      const [timeout] = debounceMap.current[id][field];\n      clearTimeout(timeout);\n      callback();\n      delete debounceMap.current[id][field];\n    };\n    const timeout = setTimeout(() => {\n      callback();\n      delete debounceMap.current[id][field];\n    }, debounceMs);\n    debounceMap.current[id][field] = [timeout, runImmediately];\n  };\n  React.useEffect(() => {\n    const debounces = debounceMap.current;\n    return () => {\n      Object.entries(debounces).forEach(([id, fields]) => {\n        Object.keys(fields).forEach(field => {\n          const [timeout] = debounces[id][field];\n          clearTimeout(timeout);\n          delete debounces[id][field];\n        });\n      });\n    };\n  }, []);\n  const runPendingEditCellValueMutation = React.useCallback((id, field) => {\n    if (!debounceMap.current[id]) {\n      return;\n    }\n    if (!field) {\n      Object.keys(debounceMap.current[id]).forEach(debouncedField => {\n        const [, runCallback] = debounceMap.current[id][debouncedField];\n        runCallback();\n      });\n    } else if (debounceMap.current[id][field]) {\n      const [, runCallback] = debounceMap.current[id][field];\n      runCallback();\n    }\n  }, []);\n  const setEditCellValue = React.useCallback(params => {\n    const {\n      id,\n      field,\n      debounceMs\n    } = params;\n    return new Promise(resolve => {\n      maybeDebounce(id, field, debounceMs, async () => {\n        const setEditCellValueToCall = props.editMode === GridEditModes.Row ? apiRef.current.setRowEditingEditCellValue : apiRef.current.setCellEditingEditCellValue;\n\n        // Check if the cell is in edit mode\n        // By the time this callback runs the user may have cancelled the editing\n        if (apiRef.current.getCellMode(id, field) === GridCellModes.Edit) {\n          const result = await setEditCellValueToCall(params);\n          resolve(result);\n        }\n      });\n    });\n  }, [apiRef, props.editMode]);\n  const getRowWithUpdatedValues = React.useCallback((id, field) => {\n    return props.editMode === GridEditModes.Cell ? apiRef.current.getRowWithUpdatedValuesFromCellEditing(id, field) : apiRef.current.getRowWithUpdatedValuesFromRowEditing(id);\n  }, [apiRef, props.editMode]);\n  const getEditCellMeta = React.useCallback((id, field) => {\n    const editingState = gridEditRowsStateSelector(apiRef);\n    return editingState[id]?.[field] ?? null;\n  }, [apiRef]);\n  const editingSharedApi = {\n    isCellEditable,\n    setEditCellValue,\n    getRowWithUpdatedValues,\n    unstable_getEditCellMeta: getEditCellMeta\n  };\n  const editingSharedPrivateApi = {\n    runPendingEditCellValueMutation\n  };\n  useGridApiMethod(apiRef, editingSharedApi, 'public');\n  useGridApiMethod(apiRef, editingSharedPrivateApi, 'private');\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}