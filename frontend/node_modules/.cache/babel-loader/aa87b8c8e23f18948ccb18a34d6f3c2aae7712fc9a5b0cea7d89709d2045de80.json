{"ast": null, "code": "import { createSelector, createRootSelector } from \"../../../utils/createSelector.js\";\nconst gridPivotingStateSelector = createRootSelector(\n// @ts-ignore\nstate => state.pivoting);\nexport const gridPivotActiveSelector = createSelector(gridPivotingStateSelector, pivoting => pivoting?.active);\nconst emptyColumns = new Map();\nexport const gridPivotInitialColumnsSelector = createSelector(gridPivotingStateSelector, pivoting => pivoting?.initialColumns || emptyColumns);\nexport const gridPivotPanelOpenSelector = createSelector(gridPivotingStateSelector, pivoting => pivoting?.panelOpen);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}