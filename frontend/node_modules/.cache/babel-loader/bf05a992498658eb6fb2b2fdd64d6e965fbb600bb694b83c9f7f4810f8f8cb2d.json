{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/ModernDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport apiService from '../services/api';\nimport { toast } from 'react-toastify';\nimport { Card, Badge, Avatar, Button, Spinner } from 'flowbite-react';\nimport { HiUsers, HiShieldCheck, HiKey, HiTrendingUp, HiEye, HiUserAdd, HiClock } from 'react-icons/hi';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ModernDashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [stats, setStats] = useState({\n    totalUsers: 0,\n    totalRoles: 0,\n    totalPermissions: 0,\n    activeUsers: 0\n  });\n  const [recentUsers, setRecentUsers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n  const fetchDashboardData = async () => {\n    try {\n      const [usersData, rolesData, permissionsData] = await Promise.all([apiService.getUsers(1, 5), apiService.getAllRoles(), apiService.getAllPermissions()]);\n      setStats({\n        totalUsers: usersData.total,\n        totalRoles: rolesData.length,\n        totalPermissions: permissionsData.length,\n        activeUsers: usersData.items.filter(u => u.is_active).length\n      });\n      setRecentUsers(usersData.items.slice(0, 5));\n    } catch (error) {\n      toast.error('Erreur lors du chargement des données');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const statCards = [{\n    title: 'Utilisateurs',\n    value: stats.totalUsers,\n    icon: HiUsers,\n    color: 'blue',\n    bgColor: 'bg-blue-100',\n    iconColor: 'text-blue-600'\n  }, {\n    title: 'Rôles',\n    value: stats.totalRoles,\n    icon: HiShieldCheck,\n    color: 'green',\n    bgColor: 'bg-green-100',\n    iconColor: 'text-green-600'\n  }, {\n    title: 'Permissions',\n    value: stats.totalPermissions,\n    icon: HiKey,\n    color: 'yellow',\n    bgColor: 'bg-yellow-100',\n    iconColor: 'text-yellow-600'\n  }, {\n    title: 'Utilisateurs Actifs',\n    value: stats.activeUsers,\n    icon: HiTrendingUp,\n    color: 'purple',\n    bgColor: 'bg-purple-100',\n    iconColor: 'text-purple-600'\n  }];\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(Spinner, {\n        size: \"xl\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-primary-600 to-primary-700 rounded-lg p-6 text-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold mb-2\",\n            children: [\"Bienvenue, \", user === null || user === void 0 ? void 0 : user.full_name, \" !\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-primary-100\",\n            children: \"Voici un aper\\xE7u de votre syst\\xE8me de gestion des utilisateurs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden md:block\",\n          children: /*#__PURE__*/_jsxDEV(Avatar, {\n            img: `https://ui-avatars.com/api/?name=${encodeURIComponent((user === null || user === void 0 ? void 0 : user.full_name) || 'User')}&background=ffffff&color=3b82f6&size=64`,\n            size: \"lg\",\n            rounded: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n      children: statCards.map((stat, index) => {\n        const Icon = stat.icon;\n        return /*#__PURE__*/_jsxDEV(Card, {\n          className: \"hover:shadow-lg transition-shadow duration-200\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-500 mb-1\",\n                children: stat.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-3xl font-bold text-gray-900\",\n                children: stat.value\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `p-3 rounded-full ${stat.bgColor}`,\n              children: /*#__PURE__*/_jsxDEV(Icon, {\n                className: `w-6 h-6 ${stat.iconColor}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(Card, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: \"Utilisateurs R\\xE9cents\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            size: \"xs\",\n            color: \"gray\",\n            children: [/*#__PURE__*/_jsxDEV(HiEye, {\n              className: \"w-4 h-4 mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this), \"Voir tout\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: recentUsers.length > 0 ? recentUsers.map(recentUser => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                img: `https://ui-avatars.com/api/?name=${encodeURIComponent(recentUser.full_name)}&background=3b82f6&color=fff`,\n                size: \"sm\",\n                rounded: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: recentUser.full_name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500\",\n                  children: recentUser.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-right\",\n              children: [/*#__PURE__*/_jsxDEV(Badge, {\n                color: recentUser.is_active ? 'success' : 'failure',\n                size: \"sm\",\n                children: recentUser.is_active ? 'Actif' : 'Inactif'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500 mt-1\",\n                children: recentUser.role\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 19\n            }, this)]\n          }, recentUser.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 17\n          }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-8 text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(HiUsers, {\n              className: \"w-12 h-12 mx-auto mb-2 text-gray-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Aucun utilisateur trouv\\xE9\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-4\",\n          children: \"Actions Rapides\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            className: \"w-full justify-start\",\n            color: \"blue\",\n            children: [/*#__PURE__*/_jsxDEV(HiUserAdd, {\n              className: \"w-5 h-5 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this), \"Cr\\xE9er un nouvel utilisateur\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            className: \"w-full justify-start\",\n            color: \"green\",\n            children: [/*#__PURE__*/_jsxDEV(HiShieldCheck, {\n              className: \"w-5 h-5 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this), \"G\\xE9rer les r\\xF4les\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            className: \"w-full justify-start\",\n            color: \"yellow\",\n            children: [/*#__PURE__*/_jsxDEV(HiKey, {\n              className: \"w-5 h-5 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 15\n            }, this), \"Configurer les permissions\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            className: \"w-full justify-start\",\n            color: \"purple\",\n            children: [/*#__PURE__*/_jsxDEV(HiClock, {\n              className: \"w-5 h-5 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this), \"Voir l'activit\\xE9 r\\xE9cente\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-gray-900 mb-4\",\n        children: \"Informations Syst\\xE8me\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center p-4 bg-gray-50 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500\",\n            children: \"Version\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: \"v1.0.0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center p-4 bg-gray-50 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500\",\n            children: \"Base de donn\\xE9es\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: \"MySQL\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center p-4 bg-gray-50 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500\",\n            children: \"Statut\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Badge, {\n            color: \"success\",\n            size: \"sm\",\n            children: \"Op\\xE9rationnel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 120,\n    columnNumber: 5\n  }, this);\n};\n_s(ModernDashboard, \"UtJVTDZVJvF5jm4uMgtPRi1awNw=\", false, function () {\n  return [useAuth];\n});\n_c = ModernDashboard;\nexport default ModernDashboard;\nvar _c;\n$RefreshReg$(_c, \"ModernDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "apiService", "toast", "Card", "Badge", "Avatar", "<PERSON><PERSON>", "Spinner", "HiUsers", "HiShieldCheck", "<PERSON><PERSON><PERSON>", "HiTrendingUp", "<PERSON><PERSON><PERSON>", "HiUserAdd", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "ModernDashboard", "_s", "user", "stats", "setStats", "totalUsers", "totalRoles", "totalPermissions", "activeUsers", "recentUsers", "setRecentUsers", "loading", "setLoading", "fetchDashboardData", "usersData", "rolesData", "permissionsData", "Promise", "all", "getUsers", "getAllRoles", "getAllPermissions", "total", "length", "items", "filter", "u", "is_active", "slice", "error", "statCards", "title", "value", "icon", "color", "bgColor", "iconColor", "className", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "full_name", "img", "encodeURIComponent", "rounded", "map", "stat", "index", "Icon", "recentUser", "email", "role", "id", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/ModernDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport apiService from '../services/api';\nimport { toast } from 'react-toastify';\nimport {\n  Card,\n  Badge,\n  <PERSON>tar,\n  <PERSON><PERSON>,\n  Spinner,\n} from 'flowbite-react';\nimport {\n  <PERSON><PERSON><PERSON><PERSON>,\n  HiShieldCheck,\n  Hi<PERSON>ey,\n  HiTrendingUp,\n  <PERSON>Eye,\n  HiUser<PERSON>dd,\n  Hi<PERSON>lock,\n} from 'react-icons/hi';\n\ninterface Stats {\n  totalUsers: number;\n  totalRoles: number;\n  totalPermissions: number;\n  activeUsers: number;\n}\n\ninterface User {\n  id: number;\n  email: string;\n  full_name: string;\n  role: string;\n  is_active: boolean;\n  created_at: string;\n}\n\nconst ModernDashboard: React.FC = () => {\n  const { user } = useAuth();\n  const [stats, setStats] = useState<Stats>({\n    totalUsers: 0,\n    totalRoles: 0,\n    totalPermissions: 0,\n    activeUsers: 0,\n  });\n  const [recentUsers, setRecentUsers] = useState<User[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n\n  const fetchDashboardData = async () => {\n    try {\n      const [usersData, rolesData, permissionsData] = await Promise.all([\n        apiService.getUsers(1, 5),\n        apiService.getAllRoles(),\n        apiService.getAllPermissions(),\n      ]);\n\n      setStats({\n        totalUsers: usersData.total,\n        totalRoles: rolesData.length,\n        totalPermissions: permissionsData.length,\n        activeUsers: usersData.items.filter((u: User) => u.is_active).length,\n      });\n\n      setRecentUsers(usersData.items.slice(0, 5));\n    } catch (error) {\n      toast.error('Erreur lors du chargement des données');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const statCards = [\n    {\n      title: 'Utilisateurs',\n      value: stats.totalUsers,\n      icon: HiUsers,\n      color: 'blue',\n      bgColor: 'bg-blue-100',\n      iconColor: 'text-blue-600',\n    },\n    {\n      title: 'Rôles',\n      value: stats.totalRoles,\n      icon: HiShieldCheck,\n      color: 'green',\n      bgColor: 'bg-green-100',\n      iconColor: 'text-green-600',\n    },\n    {\n      title: 'Permissions',\n      value: stats.totalPermissions,\n      icon: HiKey,\n      color: 'yellow',\n      bgColor: 'bg-yellow-100',\n      iconColor: 'text-yellow-600',\n    },\n    {\n      title: 'Utilisateurs Actifs',\n      value: stats.activeUsers,\n      icon: HiTrendingUp,\n      color: 'purple',\n      bgColor: 'bg-purple-100',\n      iconColor: 'text-purple-600',\n    },\n  ];\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <Spinner size=\"xl\" />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Welcome Section */}\n      <div className=\"bg-gradient-to-r from-primary-600 to-primary-700 rounded-lg p-6 text-white\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold mb-2\">\n              Bienvenue, {user?.full_name} !\n            </h1>\n            <p className=\"text-primary-100\">\n              Voici un aperçu de votre système de gestion des utilisateurs\n            </p>\n          </div>\n          <div className=\"hidden md:block\">\n            <Avatar\n              img={`https://ui-avatars.com/api/?name=${encodeURIComponent(user?.full_name || 'User')}&background=ffffff&color=3b82f6&size=64`}\n              size=\"lg\"\n              rounded\n            />\n          </div>\n        </div>\n      </div>\n\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        {statCards.map((stat, index) => {\n          const Icon = stat.icon;\n          return (\n            <Card key={index} className=\"hover:shadow-lg transition-shadow duration-200\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-500 mb-1\">\n                    {stat.title}\n                  </p>\n                  <p className=\"text-3xl font-bold text-gray-900\">\n                    {stat.value}\n                  </p>\n                </div>\n                <div className={`p-3 rounded-full ${stat.bgColor}`}>\n                  <Icon className={`w-6 h-6 ${stat.iconColor}`} />\n                </div>\n              </div>\n            </Card>\n          );\n        })}\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Recent Users */}\n        <Card>\n          <div className=\"flex items-center justify-between mb-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">\n              Utilisateurs Récents\n            </h3>\n            <Button size=\"xs\" color=\"gray\">\n              <HiEye className=\"w-4 h-4 mr-1\" />\n              Voir tout\n            </Button>\n          </div>\n          \n          <div className=\"space-y-3\">\n            {recentUsers.length > 0 ? (\n              recentUsers.map((recentUser) => (\n                <div key={recentUser.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                  <div className=\"flex items-center space-x-3\">\n                    <Avatar\n                      img={`https://ui-avatars.com/api/?name=${encodeURIComponent(recentUser.full_name)}&background=3b82f6&color=fff`}\n                      size=\"sm\"\n                      rounded\n                    />\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-900\">\n                        {recentUser.full_name}\n                      </p>\n                      <p className=\"text-xs text-gray-500\">\n                        {recentUser.email}\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"text-right\">\n                    <Badge color={recentUser.is_active ? 'success' : 'failure'} size=\"sm\">\n                      {recentUser.is_active ? 'Actif' : 'Inactif'}\n                    </Badge>\n                    <p className=\"text-xs text-gray-500 mt-1\">\n                      {recentUser.role}\n                    </p>\n                  </div>\n                </div>\n              ))\n            ) : (\n              <div className=\"text-center py-8 text-gray-500\">\n                <HiUsers className=\"w-12 h-12 mx-auto mb-2 text-gray-300\" />\n                <p>Aucun utilisateur trouvé</p>\n              </div>\n            )}\n          </div>\n        </Card>\n\n        {/* Quick Actions */}\n        <Card>\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n            Actions Rapides\n          </h3>\n          \n          <div className=\"space-y-3\">\n            <Button className=\"w-full justify-start\" color=\"blue\">\n              <HiUserAdd className=\"w-5 h-5 mr-2\" />\n              Créer un nouvel utilisateur\n            </Button>\n            \n            <Button className=\"w-full justify-start\" color=\"green\">\n              <HiShieldCheck className=\"w-5 h-5 mr-2\" />\n              Gérer les rôles\n            </Button>\n            \n            <Button className=\"w-full justify-start\" color=\"yellow\">\n              <HiKey className=\"w-5 h-5 mr-2\" />\n              Configurer les permissions\n            </Button>\n            \n            <Button className=\"w-full justify-start\" color=\"purple\">\n              <HiClock className=\"w-5 h-5 mr-2\" />\n              Voir l'activité récente\n            </Button>\n          </div>\n        </Card>\n      </div>\n\n      {/* System Info */}\n      <Card>\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n          Informations Système\n        </h3>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <div className=\"text-center p-4 bg-gray-50 rounded-lg\">\n            <p className=\"text-sm text-gray-500\">Version</p>\n            <p className=\"text-lg font-semibold text-gray-900\">v1.0.0</p>\n          </div>\n          \n          <div className=\"text-center p-4 bg-gray-50 rounded-lg\">\n            <p className=\"text-sm text-gray-500\">Base de données</p>\n            <p className=\"text-lg font-semibold text-gray-900\">MySQL</p>\n          </div>\n          \n          <div className=\"text-center p-4 bg-gray-50 rounded-lg\">\n            <p className=\"text-sm text-gray-500\">Statut</p>\n            <Badge color=\"success\" size=\"sm\">\n              Opérationnel\n            </Badge>\n          </div>\n        </div>\n      </Card>\n    </div>\n  );\n};\n\nexport default ModernDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,UAAU,MAAM,iBAAiB;AACxC,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,OAAO,QACF,gBAAgB;AACvB,SACEC,OAAO,EACPC,aAAa,EACbC,KAAK,EACLC,YAAY,EACZC,KAAK,EACLC,SAAS,EACTC,OAAO,QACF,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAkBxB,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM;IAAEC;EAAK,CAAC,GAAGnB,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACoB,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAQ;IACxCwB,UAAU,EAAE,CAAC;IACbC,UAAU,EAAE,CAAC;IACbC,gBAAgB,EAAE,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAAS,EAAE,CAAC;EAC1D,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd+B,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAM,CAACC,SAAS,EAAEC,SAAS,EAAEC,eAAe,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAChElC,UAAU,CAACmC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EACzBnC,UAAU,CAACoC,WAAW,CAAC,CAAC,EACxBpC,UAAU,CAACqC,iBAAiB,CAAC,CAAC,CAC/B,CAAC;MAEFjB,QAAQ,CAAC;QACPC,UAAU,EAAES,SAAS,CAACQ,KAAK;QAC3BhB,UAAU,EAAES,SAAS,CAACQ,MAAM;QAC5BhB,gBAAgB,EAAES,eAAe,CAACO,MAAM;QACxCf,WAAW,EAAEM,SAAS,CAACU,KAAK,CAACC,MAAM,CAAEC,CAAO,IAAKA,CAAC,CAACC,SAAS,CAAC,CAACJ;MAChE,CAAC,CAAC;MAEFb,cAAc,CAACI,SAAS,CAACU,KAAK,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC7C,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd5C,KAAK,CAAC4C,KAAK,CAAC,uCAAuC,CAAC;IACtD,CAAC,SAAS;MACRjB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMkB,SAAS,GAAG,CAChB;IACEC,KAAK,EAAE,cAAc;IACrBC,KAAK,EAAE7B,KAAK,CAACE,UAAU;IACvB4B,IAAI,EAAE1C,OAAO;IACb2C,KAAK,EAAE,MAAM;IACbC,OAAO,EAAE,aAAa;IACtBC,SAAS,EAAE;EACb,CAAC,EACD;IACEL,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE7B,KAAK,CAACG,UAAU;IACvB2B,IAAI,EAAEzC,aAAa;IACnB0C,KAAK,EAAE,OAAO;IACdC,OAAO,EAAE,cAAc;IACvBC,SAAS,EAAE;EACb,CAAC,EACD;IACEL,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE7B,KAAK,CAACI,gBAAgB;IAC7B0B,IAAI,EAAExC,KAAK;IACXyC,KAAK,EAAE,QAAQ;IACfC,OAAO,EAAE,eAAe;IACxBC,SAAS,EAAE;EACb,CAAC,EACD;IACEL,KAAK,EAAE,qBAAqB;IAC5BC,KAAK,EAAE7B,KAAK,CAACK,WAAW;IACxByB,IAAI,EAAEvC,YAAY;IAClBwC,KAAK,EAAE,QAAQ;IACfC,OAAO,EAAE,eAAe;IACxBC,SAAS,EAAE;EACb,CAAC,CACF;EAED,IAAIzB,OAAO,EAAE;IACX,oBACEZ,OAAA;MAAKsC,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpDvC,OAAA,CAACT,OAAO;QAACiD,IAAI,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC;EAEV;EAEA,oBACE5C,OAAA;IAAKsC,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBvC,OAAA;MAAKsC,SAAS,EAAC,4EAA4E;MAAAC,QAAA,eACzFvC,OAAA;QAAKsC,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDvC,OAAA;UAAAuC,QAAA,gBACEvC,OAAA;YAAIsC,SAAS,EAAC,yBAAyB;YAAAC,QAAA,GAAC,aAC3B,EAACpC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0C,SAAS,EAAC,IAC9B;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL5C,OAAA;YAAGsC,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAEhC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACN5C,OAAA;UAAKsC,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BvC,OAAA,CAACX,MAAM;YACLyD,GAAG,EAAE,oCAAoCC,kBAAkB,CAAC,CAAA5C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0C,SAAS,KAAI,MAAM,CAAC,yCAA0C;YAChIL,IAAI,EAAC,IAAI;YACTQ,OAAO;UAAA;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5C,OAAA;MAAKsC,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClER,SAAS,CAACkB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;QAC9B,MAAMC,IAAI,GAAGF,IAAI,CAAChB,IAAI;QACtB,oBACElC,OAAA,CAACb,IAAI;UAAamD,SAAS,EAAC,gDAAgD;UAAAC,QAAA,eAC1EvC,OAAA;YAAKsC,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDvC,OAAA;cAAAuC,QAAA,gBACEvC,OAAA;gBAAGsC,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAClDW,IAAI,CAAClB;cAAK;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACJ5C,OAAA;gBAAGsC,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAC5CW,IAAI,CAACjB;cAAK;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACN5C,OAAA;cAAKsC,SAAS,EAAE,oBAAoBY,IAAI,CAACd,OAAO,EAAG;cAAAG,QAAA,eACjDvC,OAAA,CAACoD,IAAI;gBAACd,SAAS,EAAE,WAAWY,IAAI,CAACb,SAAS;cAAG;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAbGO,KAAK;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAcV,CAAC;MAEX,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEN5C,OAAA;MAAKsC,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpDvC,OAAA,CAACb,IAAI;QAAAoD,QAAA,gBACHvC,OAAA;UAAKsC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDvC,OAAA;YAAIsC,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAEpD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL5C,OAAA,CAACV,MAAM;YAACkD,IAAI,EAAC,IAAI;YAACL,KAAK,EAAC,MAAM;YAAAI,QAAA,gBAC5BvC,OAAA,CAACJ,KAAK;cAAC0C,SAAS,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,aAEpC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN5C,OAAA;UAAKsC,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvB7B,WAAW,CAACc,MAAM,GAAG,CAAC,GACrBd,WAAW,CAACuC,GAAG,CAAEI,UAAU,iBACzBrD,OAAA;YAAyBsC,SAAS,EAAC,6DAA6D;YAAAC,QAAA,gBAC9FvC,OAAA;cAAKsC,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CvC,OAAA,CAACX,MAAM;gBACLyD,GAAG,EAAE,oCAAoCC,kBAAkB,CAACM,UAAU,CAACR,SAAS,CAAC,8BAA+B;gBAChHL,IAAI,EAAC,IAAI;gBACTQ,OAAO;cAAA;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,eACF5C,OAAA;gBAAAuC,QAAA,gBACEvC,OAAA;kBAAGsC,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAC7Cc,UAAU,CAACR;gBAAS;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACJ5C,OAAA;kBAAGsC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EACjCc,UAAU,CAACC;gBAAK;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN5C,OAAA;cAAKsC,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBvC,OAAA,CAACZ,KAAK;gBAAC+C,KAAK,EAAEkB,UAAU,CAACzB,SAAS,GAAG,SAAS,GAAG,SAAU;gBAACY,IAAI,EAAC,IAAI;gBAAAD,QAAA,EAClEc,UAAU,CAACzB,SAAS,GAAG,OAAO,GAAG;cAAS;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACR5C,OAAA;gBAAGsC,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EACtCc,UAAU,CAACE;cAAI;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA,GAvBES,UAAU,CAACG,EAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwBlB,CACN,CAAC,gBAEF5C,OAAA;YAAKsC,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC7CvC,OAAA,CAACR,OAAO;cAAC8C,SAAS,EAAC;YAAsC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5D5C,OAAA;cAAAuC,QAAA,EAAG;YAAwB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGP5C,OAAA,CAACb,IAAI;QAAAoD,QAAA,gBACHvC,OAAA;UAAIsC,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAEzD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEL5C,OAAA;UAAKsC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBvC,OAAA,CAACV,MAAM;YAACgD,SAAS,EAAC,sBAAsB;YAACH,KAAK,EAAC,MAAM;YAAAI,QAAA,gBACnDvC,OAAA,CAACH,SAAS;cAACyC,SAAS,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,kCAExC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAET5C,OAAA,CAACV,MAAM;YAACgD,SAAS,EAAC,sBAAsB;YAACH,KAAK,EAAC,OAAO;YAAAI,QAAA,gBACpDvC,OAAA,CAACP,aAAa;cAAC6C,SAAS,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,yBAE5C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAET5C,OAAA,CAACV,MAAM;YAACgD,SAAS,EAAC,sBAAsB;YAACH,KAAK,EAAC,QAAQ;YAAAI,QAAA,gBACrDvC,OAAA,CAACN,KAAK;cAAC4C,SAAS,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,8BAEpC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAET5C,OAAA,CAACV,MAAM;YAACgD,SAAS,EAAC,sBAAsB;YAACH,KAAK,EAAC,QAAQ;YAAAI,QAAA,gBACrDvC,OAAA,CAACF,OAAO;cAACwC,SAAS,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,iCAEtC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGN5C,OAAA,CAACb,IAAI;MAAAoD,QAAA,gBACHvC,OAAA;QAAIsC,SAAS,EAAC,0CAA0C;QAAAC,QAAA,EAAC;MAEzD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEL5C,OAAA;QAAKsC,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpDvC,OAAA;UAAKsC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDvC,OAAA;YAAGsC,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAChD5C,OAAA;YAAGsC,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eAEN5C,OAAA;UAAKsC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDvC,OAAA;YAAGsC,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACxD5C,OAAA;YAAGsC,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eAEN5C,OAAA;UAAKsC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDvC,OAAA;YAAGsC,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC/C5C,OAAA,CAACZ,KAAK;YAAC+C,KAAK,EAAC,SAAS;YAACK,IAAI,EAAC,IAAI;YAAAD,QAAA,EAAC;UAEjC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC1C,EAAA,CA5OID,eAAyB;EAAA,QACZjB,OAAO;AAAA;AAAAyE,EAAA,GADpBxD,eAAyB;AA8O/B,eAAeA,eAAe;AAAC,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}