{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"render\", \"className\", \"slotProps\", \"onKeyDown\", \"onChange\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport useForkRef from '@mui/utils/useForkRef';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { useComponentRenderer } from '@mui/x-internals/useComponentRenderer';\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { useQuickFilterContext } from \"./QuickFilterContext.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * A component that takes user input and filters row data.\n * It renders the `baseTextField` slot.\n *\n * Demos:\n *\n * - [Quick Filter](https://mui.com/x/react-data-grid/components/quick-filter/)\n *\n * API:\n *\n * - [QuickFilterControl API](https://mui.com/x/api/data-grid/quick-filter-control/)\n */\nconst QuickFilterControl = forwardRef(function QuickFilterControl(props, ref) {\n  const {\n      render,\n      className,\n      slotProps,\n      onKeyDown,\n      onChange\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const rootProps = useGridRootProps();\n  const {\n    state,\n    controlId,\n    controlRef,\n    onValueChange,\n    onExpandedChange,\n    clearValue\n  } = useQuickFilterContext();\n  const resolvedClassName = typeof className === 'function' ? className(state) : className;\n  const handleRef = useForkRef(controlRef, ref);\n  const handleKeyDown = event => {\n    if (event.key === 'Escape') {\n      if (state.value === '') {\n        onExpandedChange(false);\n      } else {\n        clearValue();\n      }\n    }\n    onKeyDown?.(event);\n  };\n  const handleBlur = event => {\n    if (state.value === '') {\n      onExpandedChange(false);\n    }\n    slotProps?.htmlInput?.onBlur?.(event);\n  };\n  const handleChange = event => {\n    if (!state.expanded) {\n      onExpandedChange(true);\n    }\n    onValueChange(event);\n    onChange?.(event);\n  };\n  const element = useComponentRenderer(rootProps.slots.baseTextField, render, _extends({}, rootProps.slotProps?.baseTextField, {\n    slotProps: _extends({\n      htmlInput: _extends({\n        role: 'searchbox',\n        id: controlId,\n        tabIndex: state.expanded ? undefined : -1\n      }, slotProps?.htmlInput, {\n        onBlur: handleBlur\n      })\n    }, slotProps),\n    value: state.value,\n    className: resolvedClassName\n  }, other, {\n    onChange: handleChange,\n    onKeyDown: handleKeyDown,\n    ref: handleRef\n  }), state);\n  return /*#__PURE__*/_jsx(React.Fragment, {\n    children: element\n  });\n});\nif (process.env.NODE_ENV !== \"production\") QuickFilterControl.displayName = \"QuickFilterControl\";\nprocess.env.NODE_ENV !== \"production\" ? QuickFilterControl.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  autoComplete: PropTypes.string,\n  autoFocus: PropTypes.bool,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  className: PropTypes.oneOfType([PropTypes.func, PropTypes.string]),\n  color: PropTypes.oneOf(['error', 'primary']),\n  disabled: PropTypes.bool,\n  error: PropTypes.bool,\n  fullWidth: PropTypes.bool,\n  helperText: PropTypes.string,\n  id: PropTypes.string,\n  inputRef: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n    current: PropTypes.object\n  })]),\n  label: PropTypes.node,\n  multiline: PropTypes.bool,\n  placeholder: PropTypes.string,\n  /**\n   * A function to customize rendering of the component.\n   */\n  render: PropTypes.oneOfType([PropTypes.element, PropTypes.func]),\n  role: PropTypes.string,\n  size: PropTypes.oneOf(['medium', 'small']),\n  slotProps: PropTypes.object,\n  style: PropTypes.object,\n  tabIndex: PropTypes.number,\n  type: PropTypes.oneOfType([PropTypes.oneOf(['button', 'checkbox', 'color', 'date', 'datetime-local', 'email', 'file', 'hidden', 'image', 'month', 'number', 'password', 'radio', 'range', 'reset', 'search', 'submit', 'tel', 'text', 'time', 'url', 'week']), PropTypes.object]),\n  value: PropTypes.string\n} : void 0;\nexport { QuickFilterControl };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}