{"ast": null, "code": "import { createSelector, createRootSelector } from \"../../../utils/createSelector.js\";\nexport const gridPreferencePanelStateSelector = createRootSelector(state => state.preferencePanel);\nexport const gridPreferencePanelSelectorWithLabel = createSelector(gridPreferencePanelStateSelector, (panel, labelId) => {\n  if (panel.open && panel.labelId === labelId) {\n    return true;\n  }\n  return false;\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}