{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/App.tsx\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { ToastContainer } from 'react-toastify';\nimport 'react-toastify/dist/ReactToastify.css';\nimport { AuthProvider } from './contexts/AuthContext';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport TailwindLayout from './components/TailwindLayout';\nimport TailwindLogin from './pages/TailwindLogin';\nimport TailwindDashboard from './pages/TailwindDashboard';\nimport Users from './pages/Users';\nimport Roles from './pages/Roles';\nimport Permissions from './pages/Permissions';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: [/*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/login\",\n          element: /*#__PURE__*/_jsxDEV(TailwindLogin, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n            children: /*#__PURE__*/_jsxDEV(TailwindLayout, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 17\n          }, this),\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            index: true,\n            element: /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/dashboard\",\n              replace: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"dashboard\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              requiredPermissions: ['dashboard:access'],\n              children: /*#__PURE__*/_jsxDEV(TailwindDashboard, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 42,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"users\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              requiredPermissions: ['user:read'],\n              children: /*#__PURE__*/_jsxDEV(Users, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"roles\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              requiredPermissions: ['role:read'],\n              children: /*#__PURE__*/_jsxDEV(Roles, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"permissions\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              requiredPermissions: ['permission:read'],\n              children: /*#__PURE__*/_jsxDEV(Permissions, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"*\",\n          element: /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/dashboard\",\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 38\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ToastContainer, {\n      position: \"top-right\",\n      autoClose: 5000,\n      hideProgressBar: false,\n      newestOnTop: false,\n      closeOnClick: true,\n      rtl: false,\n      pauseOnFocusLoss: true,\n      draggable: true,\n      pauseOnHover: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "ToastContainer", "<PERSON>th<PERSON><PERSON><PERSON>", "ProtectedRoute", "TailwindLayout", "Tailwind<PERSON><PERSON><PERSON>", "TailwindDashboard", "Users", "Roles", "Permissions", "jsxDEV", "_jsxDEV", "App", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "index", "to", "replace", "requiredPermissions", "position", "autoClose", "hideProgressBar", "newestOnTop", "closeOnClick", "rtl", "pauseOnFocusLoss", "draggable", "pauseOnHover", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { ToastContainer } from 'react-toastify';\nimport 'react-toastify/dist/ReactToastify.css';\n\nimport { AuthProvider } from './contexts/AuthContext';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport Layout from './components/Layout';\nimport TailwindLayout from './components/TailwindLayout';\nimport Login from './pages/Login';\nimport TailwindLogin from './pages/TailwindLogin';\nimport Dashboard from './pages/Dashboard';\nimport TailwindDashboard from './pages/TailwindDashboard';\nimport Users from './pages/Users';\nimport Roles from './pages/Roles';\nimport Permissions from './pages/Permissions';\n\n\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <Router>\n          <Routes>\n            {/* Route de connexion */}\n            <Route path=\"/login\" element={<TailwindLogin />} />\n\n            {/* Routes protégées */}\n            <Route\n              path=\"/\"\n              element={\n                <ProtectedRoute>\n                  <TailwindLayout />\n                </ProtectedRoute>\n              }\n            >\n              <Route index element={<Navigate to=\"/dashboard\" replace />} />\n              <Route\n                path=\"dashboard\"\n                element={\n                  <ProtectedRoute requiredPermissions={['dashboard:access']}>\n                    <TailwindDashboard />\n                  </ProtectedRoute>\n                }\n              />\n              <Route\n                path=\"users\"\n                element={\n                  <ProtectedRoute requiredPermissions={['user:read']}>\n                    <Users />\n                  </ProtectedRoute>\n                }\n              />\n              <Route\n                path=\"roles\"\n                element={\n                  <ProtectedRoute requiredPermissions={['role:read']}>\n                    <Roles />\n                  </ProtectedRoute>\n                }\n              />\n              <Route\n                path=\"permissions\"\n                element={\n                  <ProtectedRoute requiredPermissions={['permission:read']}>\n                    <Permissions />\n                  </ProtectedRoute>\n                }\n              />\n            </Route>\n\n            {/* Redirection par défaut */}\n            <Route path=\"*\" element={<Navigate to=\"/dashboard\" replace />} />\n          </Routes>\n        </Router>\n\n        {/* Notifications toast */}\n        <ToastContainer\n          position=\"top-right\"\n          autoClose={5000}\n          hideProgressBar={false}\n          newestOnTop={false}\n          closeOnClick\n          rtl={false}\n          pauseOnFocusLoss\n          draggable\n          pauseOnHover\n        />\n      </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,OAAO,uCAAuC;AAE9C,SAASC,YAAY,QAAQ,wBAAwB;AACrD,OAAOC,cAAc,MAAM,6BAA6B;AAExD,OAAOC,cAAc,MAAM,6BAA6B;AAExD,OAAOC,aAAa,MAAM,uBAAuB;AAEjD,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,WAAW,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAI9C,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACT,YAAY;IAAAW,QAAA,gBACXF,OAAA,CAACd,MAAM;MAAAgB,QAAA,eACHF,OAAA,CAACb,MAAM;QAAAe,QAAA,gBAELF,OAAA,CAACZ,KAAK;UAACe,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAEJ,OAAA,CAACN,aAAa;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGnDR,OAAA,CAACZ,KAAK;UACJe,IAAI,EAAC,GAAG;UACRC,OAAO,eACLJ,OAAA,CAACR,cAAc;YAAAU,QAAA,eACbF,OAAA,CAACP,cAAc;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACjB;UAAAN,QAAA,gBAEDF,OAAA,CAACZ,KAAK;YAACqB,KAAK;YAACL,OAAO,eAAEJ,OAAA,CAACX,QAAQ;cAACqB,EAAE,EAAC,YAAY;cAACC,OAAO;YAAA;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9DR,OAAA,CAACZ,KAAK;YACJe,IAAI,EAAC,WAAW;YAChBC,OAAO,eACLJ,OAAA,CAACR,cAAc;cAACoB,mBAAmB,EAAE,CAAC,kBAAkB,CAAE;cAAAV,QAAA,eACxDF,OAAA,CAACL,iBAAiB;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFR,OAAA,CAACZ,KAAK;YACJe,IAAI,EAAC,OAAO;YACZC,OAAO,eACLJ,OAAA,CAACR,cAAc;cAACoB,mBAAmB,EAAE,CAAC,WAAW,CAAE;cAAAV,QAAA,eACjDF,OAAA,CAACJ,KAAK;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFR,OAAA,CAACZ,KAAK;YACJe,IAAI,EAAC,OAAO;YACZC,OAAO,eACLJ,OAAA,CAACR,cAAc;cAACoB,mBAAmB,EAAE,CAAC,WAAW,CAAE;cAAAV,QAAA,eACjDF,OAAA,CAACH,KAAK;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFR,OAAA,CAACZ,KAAK;YACJe,IAAI,EAAC,aAAa;YAClBC,OAAO,eACLJ,OAAA,CAACR,cAAc;cAACoB,mBAAmB,EAAE,CAAC,iBAAiB,CAAE;cAAAV,QAAA,eACvDF,OAAA,CAACF,WAAW;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eAGRR,OAAA,CAACZ,KAAK;UAACe,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEJ,OAAA,CAACX,QAAQ;YAACqB,EAAE,EAAC,YAAY;YAACC,OAAO;UAAA;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGTR,OAAA,CAACV,cAAc;MACbuB,QAAQ,EAAC,WAAW;MACpBC,SAAS,EAAE,IAAK;MAChBC,eAAe,EAAE,KAAM;MACvBC,WAAW,EAAE,KAAM;MACnBC,YAAY;MACZC,GAAG,EAAE,KAAM;MACXC,gBAAgB;MAChBC,SAAS;MACTC,YAAY;IAAA;MAAAhB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACU,CAAC;AAErB;AAACc,EAAA,GAvEQrB,GAAG;AAyEZ,eAAeA,GAAG;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}