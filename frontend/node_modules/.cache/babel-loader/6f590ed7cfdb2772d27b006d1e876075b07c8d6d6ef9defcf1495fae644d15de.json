{"ast": null, "code": "import * as React from 'react';\nimport { useGridApiMethod } from \"../../utils/index.js\";\nexport const useGridStatePersistence = apiRef => {\n  const exportState = React.useCallback((params = {}) => {\n    const stateToExport = apiRef.current.unstable_applyPipeProcessors('exportState', {}, params);\n    return stateToExport;\n  }, [apiRef]);\n  const restoreState = React.useCallback(stateToRestore => {\n    const response = apiRef.current.unstable_applyPipeProcessors('restoreState', {\n      callbacks: []\n    }, {\n      stateToRestore\n    });\n    response.callbacks.forEach(callback => {\n      callback();\n    });\n  }, [apiRef]);\n  const statePersistenceApi = {\n    exportState,\n    restoreState\n  };\n  useGridApiMethod(apiRef, statePersistenceApi, 'public');\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}