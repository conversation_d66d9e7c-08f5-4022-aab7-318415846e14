{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/Roles.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Button, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormGroup, FormControlLabel, Checkbox, Chip, IconButton, Tooltip } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, Security as SecurityIcon } from '@mui/icons-material';\nimport { toast } from 'react-toastify';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Roles = () => {\n  _s();\n  const [roles, setRoles] = useState([]);\n  const [permissions, setPermissions] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [dialogOpen, setDialogOpen] = useState(false);\n  const [editingRole, setEditingRole] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    permission_ids: []\n  });\n  useEffect(() => {\n    fetchRoles();\n    fetchPermissions();\n  }, []);\n  const fetchRoles = async () => {\n    try {\n      const response = await api.get('/roles/');\n      setRoles(response.data.items || response.data);\n    } catch (error) {\n      toast.error('Erreur lors du chargement des rôles');\n    }\n  };\n  const fetchPermissions = async () => {\n    try {\n      const response = await api.get('/permissions/');\n      setPermissions(response.data.items || response.data);\n      setLoading(false);\n    } catch (error) {\n      toast.error('Erreur lors du chargement des permissions');\n      setLoading(false);\n    }\n  };\n  const handleOpenDialog = role => {\n    if (role) {\n      setEditingRole(role);\n      setFormData({\n        name: role.name,\n        description: role.description,\n        permission_ids: role.permissions.map(p => p.id)\n      });\n    } else {\n      setEditingRole(null);\n      setFormData({\n        name: '',\n        description: '',\n        permission_ids: []\n      });\n    }\n    setDialogOpen(true);\n  };\n  const handleCloseDialog = () => {\n    setDialogOpen(false);\n    setEditingRole(null);\n    setFormData({\n      name: '',\n      description: '',\n      permission_ids: []\n    });\n  };\n  const handlePermissionChange = (permissionId, checked) => {\n    setFormData(prev => ({\n      ...prev,\n      permission_ids: checked ? [...prev.permission_ids, permissionId] : prev.permission_ids.filter(id => id !== permissionId)\n    }));\n  };\n  const handleSubmit = async () => {\n    try {\n      if (editingRole) {\n        await api.put(`/roles/${editingRole.id}`, formData);\n        toast.success('Rôle modifié avec succès');\n      } else {\n        await api.post('/roles/', formData);\n        toast.success('Rôle créé avec succès');\n      }\n      handleCloseDialog();\n      fetchRoles();\n    } catch (error) {\n      toast.error('Erreur lors de la sauvegarde du rôle');\n    }\n  };\n  const handleDelete = async roleId => {\n    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce rôle ?')) {\n      try {\n        await api.delete(`/roles/${roleId}`);\n        toast.success('Rôle supprimé avec succès');\n        fetchRoles();\n      } catch (error) {\n        toast.error('Erreur lors de la suppression du rôle');\n      }\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        mt: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        children: \"Chargement...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(SecurityIcon, {\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          component: \"h1\",\n          children: \"Gestion des R\\xF4les\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 22\n        }, this),\n        onClick: () => handleOpenDialog(),\n        children: \"Nouveau R\\xF4le\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Nom\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Permissions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"center\",\n              children: \"Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: roles.map(role => /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle1\",\n                fontWeight: \"bold\",\n                children: role.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: role.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  flexWrap: 'wrap',\n                  gap: 0.5\n                },\n                children: role.permissions.map(permission => /*#__PURE__*/_jsxDEV(Chip, {\n                  label: permission.name,\n                  size: \"small\",\n                  color: \"primary\",\n                  variant: \"outlined\"\n                }, permission.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"Modifier\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  color: \"primary\",\n                  onClick: () => handleOpenDialog(role),\n                  children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 215,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"Supprimer\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  color: \"error\",\n                  onClick: () => handleDelete(role.id),\n                  children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 223,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 17\n            }, this)]\n          }, role.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: dialogOpen,\n      onClose: handleCloseDialog,\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: editingRole ? 'Modifier le rôle' : 'Créer un nouveau rôle'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Nom du r\\xF4le\",\n            value: formData.name,\n            onChange: e => setFormData(prev => ({\n              ...prev,\n              name: e.target.value\n            })),\n            margin: \"normal\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Description\",\n            value: formData.description,\n            onChange: e => setFormData(prev => ({\n              ...prev,\n              description: e.target.value\n            })),\n            margin: \"normal\",\n            multiline: true,\n            rows: 3\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              mt: 3,\n              mb: 2\n            },\n            children: \"Permissions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n            children: permissions.map(permission => /*#__PURE__*/_jsxDEV(FormControlLabel, {\n              control: /*#__PURE__*/_jsxDEV(Checkbox, {\n                checked: formData.permission_ids.includes(permission.id),\n                onChange: e => handlePermissionChange(permission.id, e.target.checked)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 21\n              }, this),\n              label: /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: permission.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: permission.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 21\n              }, this)\n            }, permission.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseDialog,\n          children: \"Annuler\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmit,\n          variant: \"contained\",\n          disabled: !formData.name.trim(),\n          children: editingRole ? 'Modifier' : 'Créer'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 234,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 160,\n    columnNumber: 5\n  }, this);\n};\n_s(Roles, \"QF01a6wt7phJKHm3wMUO2wTnafk=\");\n_c = Roles;\nexport default Roles;\nvar _c;\n$RefreshReg$(_c, \"Roles\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "FormGroup", "FormControlLabel", "Checkbox", "Chip", "IconButton", "<PERSON><PERSON><PERSON>", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Security", "SecurityIcon", "toast", "jsxDEV", "_jsxDEV", "Roles", "_s", "roles", "setRoles", "permissions", "setPermissions", "loading", "setLoading", "dialogOpen", "setDialogOpen", "editingRole", "setEditingRole", "formData", "setFormData", "name", "description", "permission_ids", "fetchRoles", "fetchPermissions", "response", "api", "get", "data", "items", "error", "handleOpenDialog", "role", "map", "p", "id", "handleCloseDialog", "handlePermissionChange", "permissionId", "checked", "prev", "filter", "handleSubmit", "put", "success", "post", "handleDelete", "roleId", "window", "confirm", "delete", "sx", "display", "justifyContent", "mt", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "alignItems", "mb", "gap", "color", "variant", "component", "startIcon", "onClick", "align", "fontWeight", "flexWrap", "permission", "label", "size", "title", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "pt", "value", "onChange", "e", "target", "margin", "required", "multiline", "rows", "control", "includes", "disabled", "trim", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/Roles.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  <PERSON>pography,\n  Button,\n  Paper,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  FormGroup,\n  FormControlLabel,\n  Checkbox,\n  Chip,\n  IconButton,\n  Tooltip,\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Security as SecurityIcon,\n} from '@mui/icons-material';\nimport { toast } from 'react-toastify';\nimport apiService from '../services/api';\n\ninterface Permission {\n  id: number;\n  code: string;\n  name: string;\n  description: string;\n}\n\ninterface Role {\n  id: number;\n  name: string;\n  description: string;\n  permissions: Permission[];\n}\n\nconst Roles: React.FC = () => {\n  const [roles, setRoles] = useState<Role[]>([]);\n  const [permissions, setPermissions] = useState<Permission[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [dialogOpen, setDialogOpen] = useState(false);\n  const [editingRole, setEditingRole] = useState<Role | null>(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    permission_ids: [] as number[],\n  });\n\n  useEffect(() => {\n    fetchRoles();\n    fetchPermissions();\n  }, []);\n\n  const fetchRoles = async () => {\n    try {\n      const response = await api.get('/roles/');\n      setRoles(response.data.items || response.data);\n    } catch (error) {\n      toast.error('Erreur lors du chargement des rôles');\n    }\n  };\n\n  const fetchPermissions = async () => {\n    try {\n      const response = await api.get('/permissions/');\n      setPermissions(response.data.items || response.data);\n      setLoading(false);\n    } catch (error) {\n      toast.error('Erreur lors du chargement des permissions');\n      setLoading(false);\n    }\n  };\n\n  const handleOpenDialog = (role?: Role) => {\n    if (role) {\n      setEditingRole(role);\n      setFormData({\n        name: role.name,\n        description: role.description,\n        permission_ids: role.permissions.map(p => p.id),\n      });\n    } else {\n      setEditingRole(null);\n      setFormData({\n        name: '',\n        description: '',\n        permission_ids: [],\n      });\n    }\n    setDialogOpen(true);\n  };\n\n  const handleCloseDialog = () => {\n    setDialogOpen(false);\n    setEditingRole(null);\n    setFormData({\n      name: '',\n      description: '',\n      permission_ids: [],\n    });\n  };\n\n  const handlePermissionChange = (permissionId: number, checked: boolean) => {\n    setFormData(prev => ({\n      ...prev,\n      permission_ids: checked\n        ? [...prev.permission_ids, permissionId]\n        : prev.permission_ids.filter(id => id !== permissionId)\n    }));\n  };\n\n  const handleSubmit = async () => {\n    try {\n      if (editingRole) {\n        await api.put(`/roles/${editingRole.id}`, formData);\n        toast.success('Rôle modifié avec succès');\n      } else {\n        await api.post('/roles/', formData);\n        toast.success('Rôle créé avec succès');\n      }\n      handleCloseDialog();\n      fetchRoles();\n    } catch (error) {\n      toast.error('Erreur lors de la sauvegarde du rôle');\n    }\n  };\n\n  const handleDelete = async (roleId: number) => {\n    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce rôle ?')) {\n      try {\n        await api.delete(`/roles/${roleId}`);\n        toast.success('Rôle supprimé avec succès');\n        fetchRoles();\n      } catch (error) {\n        toast.error('Erreur lors de la suppression du rôle');\n      }\n    }\n  };\n\n  if (loading) {\n    return (\n      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>\n        <Typography>Chargement...</Typography>\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ p: 3 }}>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n          <SecurityIcon color=\"primary\" />\n          <Typography variant=\"h4\" component=\"h1\">\n            Gestion des Rôles\n          </Typography>\n        </Box>\n        <Button\n          variant=\"contained\"\n          startIcon={<AddIcon />}\n          onClick={() => handleOpenDialog()}\n        >\n          Nouveau Rôle\n        </Button>\n      </Box>\n\n      <TableContainer component={Paper}>\n        <Table>\n          <TableHead>\n            <TableRow>\n              <TableCell>Nom</TableCell>\n              <TableCell>Description</TableCell>\n              <TableCell>Permissions</TableCell>\n              <TableCell align=\"center\">Actions</TableCell>\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {roles.map((role) => (\n              <TableRow key={role.id}>\n                <TableCell>\n                  <Typography variant=\"subtitle1\" fontWeight=\"bold\">\n                    {role.name}\n                  </Typography>\n                </TableCell>\n                <TableCell>{role.description}</TableCell>\n                <TableCell>\n                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>\n                    {role.permissions.map((permission) => (\n                      <Chip\n                        key={permission.id}\n                        label={permission.name}\n                        size=\"small\"\n                        color=\"primary\"\n                        variant=\"outlined\"\n                      />\n                    ))}\n                  </Box>\n                </TableCell>\n                <TableCell align=\"center\">\n                  <Tooltip title=\"Modifier\">\n                    <IconButton\n                      color=\"primary\"\n                      onClick={() => handleOpenDialog(role)}\n                    >\n                      <EditIcon />\n                    </IconButton>\n                  </Tooltip>\n                  <Tooltip title=\"Supprimer\">\n                    <IconButton\n                      color=\"error\"\n                      onClick={() => handleDelete(role.id)}\n                    >\n                      <DeleteIcon />\n                    </IconButton>\n                  </Tooltip>\n                </TableCell>\n              </TableRow>\n            ))}\n          </TableBody>\n        </Table>\n      </TableContainer>\n\n      {/* Dialog pour créer/modifier un rôle */}\n      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          {editingRole ? 'Modifier le rôle' : 'Créer un nouveau rôle'}\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 2 }}>\n            <TextField\n              fullWidth\n              label=\"Nom du rôle\"\n              value={formData.name}\n              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}\n              margin=\"normal\"\n              required\n            />\n            <TextField\n              fullWidth\n              label=\"Description\"\n              value={formData.description}\n              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}\n              margin=\"normal\"\n              multiline\n              rows={3}\n            />\n            \n            <Typography variant=\"h6\" sx={{ mt: 3, mb: 2 }}>\n              Permissions\n            </Typography>\n            <FormGroup>\n              {permissions.map((permission) => (\n                <FormControlLabel\n                  key={permission.id}\n                  control={\n                    <Checkbox\n                      checked={formData.permission_ids.includes(permission.id)}\n                      onChange={(e) => handlePermissionChange(permission.id, e.target.checked)}\n                    />\n                  }\n                  label={\n                    <Box>\n                      <Typography variant=\"body1\">{permission.name}</Typography>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        {permission.description}\n                      </Typography>\n                    </Box>\n                  }\n                />\n              ))}\n            </FormGroup>\n          </Box>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCloseDialog}>Annuler</Button>\n          <Button\n            onClick={handleSubmit}\n            variant=\"contained\"\n            disabled={!formData.name.trim()}\n          >\n            {editingRole ? 'Modifier' : 'Créer'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default Roles;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,SAAS,EACTC,gBAAgB,EAChBC,QAAQ,EACRC,IAAI,EACJC,UAAU,EACVC,OAAO,QACF,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,QACnB,qBAAqB;AAC5B,SAASC,KAAK,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAiBvC,MAAMC,KAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGrC,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACsC,WAAW,EAAEC,cAAc,CAAC,GAAGvC,QAAQ,CAAe,EAAE,CAAC;EAChE,MAAM,CAACwC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0C,UAAU,EAAEC,aAAa,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC4C,WAAW,EAAEC,cAAc,CAAC,GAAG7C,QAAQ,CAAc,IAAI,CAAC;EACjE,MAAM,CAAC8C,QAAQ,EAAEC,WAAW,CAAC,GAAG/C,QAAQ,CAAC;IACvCgD,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE;EAClB,CAAC,CAAC;EAEFjD,SAAS,CAAC,MAAM;IACdkD,UAAU,CAAC,CAAC;IACZC,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAME,QAAQ,GAAG,MAAMC,GAAG,CAACC,GAAG,CAAC,SAAS,CAAC;MACzClB,QAAQ,CAACgB,QAAQ,CAACG,IAAI,CAACC,KAAK,IAAIJ,QAAQ,CAACG,IAAI,CAAC;IAChD,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd3B,KAAK,CAAC2B,KAAK,CAAC,qCAAqC,CAAC;IACpD;EACF,CAAC;EAED,MAAMN,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMC,GAAG,CAACC,GAAG,CAAC,eAAe,CAAC;MAC/ChB,cAAc,CAACc,QAAQ,CAACG,IAAI,CAACC,KAAK,IAAIJ,QAAQ,CAACG,IAAI,CAAC;MACpDf,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACd3B,KAAK,CAAC2B,KAAK,CAAC,2CAA2C,CAAC;MACxDjB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMkB,gBAAgB,GAAIC,IAAW,IAAK;IACxC,IAAIA,IAAI,EAAE;MACRf,cAAc,CAACe,IAAI,CAAC;MACpBb,WAAW,CAAC;QACVC,IAAI,EAAEY,IAAI,CAACZ,IAAI;QACfC,WAAW,EAAEW,IAAI,CAACX,WAAW;QAC7BC,cAAc,EAAEU,IAAI,CAACtB,WAAW,CAACuB,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE;MAChD,CAAC,CAAC;IACJ,CAAC,MAAM;MACLlB,cAAc,CAAC,IAAI,CAAC;MACpBE,WAAW,CAAC;QACVC,IAAI,EAAE,EAAE;QACRC,WAAW,EAAE,EAAE;QACfC,cAAc,EAAE;MAClB,CAAC,CAAC;IACJ;IACAP,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMqB,iBAAiB,GAAGA,CAAA,KAAM;IAC9BrB,aAAa,CAAC,KAAK,CAAC;IACpBE,cAAc,CAAC,IAAI,CAAC;IACpBE,WAAW,CAAC;MACVC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE;IAClB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMe,sBAAsB,GAAGA,CAACC,YAAoB,EAAEC,OAAgB,KAAK;IACzEpB,WAAW,CAACqB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPlB,cAAc,EAAEiB,OAAO,GACnB,CAAC,GAAGC,IAAI,CAAClB,cAAc,EAAEgB,YAAY,CAAC,GACtCE,IAAI,CAAClB,cAAc,CAACmB,MAAM,CAACN,EAAE,IAAIA,EAAE,KAAKG,YAAY;IAC1D,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMI,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,IAAI1B,WAAW,EAAE;QACf,MAAMU,GAAG,CAACiB,GAAG,CAAC,UAAU3B,WAAW,CAACmB,EAAE,EAAE,EAAEjB,QAAQ,CAAC;QACnDf,KAAK,CAACyC,OAAO,CAAC,0BAA0B,CAAC;MAC3C,CAAC,MAAM;QACL,MAAMlB,GAAG,CAACmB,IAAI,CAAC,SAAS,EAAE3B,QAAQ,CAAC;QACnCf,KAAK,CAACyC,OAAO,CAAC,uBAAuB,CAAC;MACxC;MACAR,iBAAiB,CAAC,CAAC;MACnBb,UAAU,CAAC,CAAC;IACd,CAAC,CAAC,OAAOO,KAAK,EAAE;MACd3B,KAAK,CAAC2B,KAAK,CAAC,sCAAsC,CAAC;IACrD;EACF,CAAC;EAED,MAAMgB,YAAY,GAAG,MAAOC,MAAc,IAAK;IAC7C,IAAIC,MAAM,CAACC,OAAO,CAAC,8CAA8C,CAAC,EAAE;MAClE,IAAI;QACF,MAAMvB,GAAG,CAACwB,MAAM,CAAC,UAAUH,MAAM,EAAE,CAAC;QACpC5C,KAAK,CAACyC,OAAO,CAAC,2BAA2B,CAAC;QAC1CrB,UAAU,CAAC,CAAC;MACd,CAAC,CAAC,OAAOO,KAAK,EAAE;QACd3B,KAAK,CAAC2B,KAAK,CAAC,uCAAuC,CAAC;MACtD;IACF;EACF,CAAC;EAED,IAAIlB,OAAO,EAAE;IACX,oBACEP,OAAA,CAAC/B,GAAG;MAAC6E,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,eAC5DlD,OAAA,CAAC9B,UAAU;QAAAgF,QAAA,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CAAC;EAEV;EAEA,oBACEtD,OAAA,CAAC/B,GAAG;IAAC6E,EAAE,EAAE;MAAEjB,CAAC,EAAE;IAAE,CAAE;IAAAqB,QAAA,gBAChBlD,OAAA,CAAC/B,GAAG;MAAC6E,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEO,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAN,QAAA,gBACzFlD,OAAA,CAAC/B,GAAG;QAAC6E,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEQ,UAAU,EAAE,QAAQ;UAAEE,GAAG,EAAE;QAAE,CAAE;QAAAP,QAAA,gBACzDlD,OAAA,CAACH,YAAY;UAAC6D,KAAK,EAAC;QAAS;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChCtD,OAAA,CAAC9B,UAAU;UAACyF,OAAO,EAAC,IAAI;UAACC,SAAS,EAAC,IAAI;UAAAV,QAAA,EAAC;QAExC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACNtD,OAAA,CAAC7B,MAAM;QACLwF,OAAO,EAAC,WAAW;QACnBE,SAAS,eAAE7D,OAAA,CAACT,OAAO;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBQ,OAAO,EAAEA,CAAA,KAAMpC,gBAAgB,CAAC,CAAE;QAAAwB,QAAA,EACnC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENtD,OAAA,CAACxB,cAAc;MAACoF,SAAS,EAAExF,KAAM;MAAA8E,QAAA,eAC/BlD,OAAA,CAAC3B,KAAK;QAAA6E,QAAA,gBACJlD,OAAA,CAACvB,SAAS;UAAAyE,QAAA,eACRlD,OAAA,CAACtB,QAAQ;YAAAwE,QAAA,gBACPlD,OAAA,CAACzB,SAAS;cAAA2E,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC1BtD,OAAA,CAACzB,SAAS;cAAA2E,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAClCtD,OAAA,CAACzB,SAAS;cAAA2E,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAClCtD,OAAA,CAACzB,SAAS;cAACwF,KAAK,EAAC,QAAQ;cAAAb,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZtD,OAAA,CAAC1B,SAAS;UAAA4E,QAAA,EACP/C,KAAK,CAACyB,GAAG,CAAED,IAAI,iBACd3B,OAAA,CAACtB,QAAQ;YAAAwE,QAAA,gBACPlD,OAAA,CAACzB,SAAS;cAAA2E,QAAA,eACRlD,OAAA,CAAC9B,UAAU;gBAACyF,OAAO,EAAC,WAAW;gBAACK,UAAU,EAAC,MAAM;gBAAAd,QAAA,EAC9CvB,IAAI,CAACZ;cAAI;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACZtD,OAAA,CAACzB,SAAS;cAAA2E,QAAA,EAAEvB,IAAI,CAACX;YAAW;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACzCtD,OAAA,CAACzB,SAAS;cAAA2E,QAAA,eACRlD,OAAA,CAAC/B,GAAG;gBAAC6E,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEkB,QAAQ,EAAE,MAAM;kBAAER,GAAG,EAAE;gBAAI,CAAE;gBAAAP,QAAA,EACtDvB,IAAI,CAACtB,WAAW,CAACuB,GAAG,CAAEsC,UAAU,iBAC/BlE,OAAA,CAACb,IAAI;kBAEHgF,KAAK,EAAED,UAAU,CAACnD,IAAK;kBACvBqD,IAAI,EAAC,OAAO;kBACZV,KAAK,EAAC,SAAS;kBACfC,OAAO,EAAC;gBAAU,GAJbO,UAAU,CAACpC,EAAE;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAKnB,CACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eACZtD,OAAA,CAACzB,SAAS;cAACwF,KAAK,EAAC,QAAQ;cAAAb,QAAA,gBACvBlD,OAAA,CAACX,OAAO;gBAACgF,KAAK,EAAC,UAAU;gBAAAnB,QAAA,eACvBlD,OAAA,CAACZ,UAAU;kBACTsE,KAAK,EAAC,SAAS;kBACfI,OAAO,EAAEA,CAAA,KAAMpC,gBAAgB,CAACC,IAAI,CAAE;kBAAAuB,QAAA,eAEtClD,OAAA,CAACP,QAAQ;oBAAA0D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACVtD,OAAA,CAACX,OAAO;gBAACgF,KAAK,EAAC,WAAW;gBAAAnB,QAAA,eACxBlD,OAAA,CAACZ,UAAU;kBACTsE,KAAK,EAAC,OAAO;kBACbI,OAAO,EAAEA,CAAA,KAAMrB,YAAY,CAACd,IAAI,CAACG,EAAE,CAAE;kBAAAoB,QAAA,eAErClD,OAAA,CAACL,UAAU;oBAAAwD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA,GArCC3B,IAAI,CAACG,EAAE;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAsCZ,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAGjBtD,OAAA,CAACrB,MAAM;MAAC2F,IAAI,EAAE7D,UAAW;MAAC8D,OAAO,EAAExC,iBAAkB;MAACyC,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAAvB,QAAA,gBAC3ElD,OAAA,CAACpB,WAAW;QAAAsE,QAAA,EACTvC,WAAW,GAAG,kBAAkB,GAAG;MAAuB;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC,eACdtD,OAAA,CAACnB,aAAa;QAAAqE,QAAA,eACZlD,OAAA,CAAC/B,GAAG;UAAC6E,EAAE,EAAE;YAAE4B,EAAE,EAAE;UAAE,CAAE;UAAAxB,QAAA,gBACjBlD,OAAA,CAACjB,SAAS;YACR0F,SAAS;YACTN,KAAK,EAAC,gBAAa;YACnBQ,KAAK,EAAE9D,QAAQ,CAACE,IAAK;YACrB6D,QAAQ,EAAGC,CAAC,IAAK/D,WAAW,CAACqB,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAEpB,IAAI,EAAE8D,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAC,CAAE;YAC1EI,MAAM,EAAC,QAAQ;YACfC,QAAQ;UAAA;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACFtD,OAAA,CAACjB,SAAS;YACR0F,SAAS;YACTN,KAAK,EAAC,aAAa;YACnBQ,KAAK,EAAE9D,QAAQ,CAACG,WAAY;YAC5B4D,QAAQ,EAAGC,CAAC,IAAK/D,WAAW,CAACqB,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAEnB,WAAW,EAAE6D,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAC,CAAE;YACjFI,MAAM,EAAC,QAAQ;YACfE,SAAS;YACTC,IAAI,EAAE;UAAE;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEFtD,OAAA,CAAC9B,UAAU;YAACyF,OAAO,EAAC,IAAI;YAACb,EAAE,EAAE;cAAEG,EAAE,EAAE,CAAC;cAAEO,EAAE,EAAE;YAAE,CAAE;YAAAN,QAAA,EAAC;UAE/C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbtD,OAAA,CAAChB,SAAS;YAAAkE,QAAA,EACP7C,WAAW,CAACuB,GAAG,CAAEsC,UAAU,iBAC1BlE,OAAA,CAACf,gBAAgB;cAEfkG,OAAO,eACLnF,OAAA,CAACd,QAAQ;gBACPgD,OAAO,EAAErB,QAAQ,CAACI,cAAc,CAACmE,QAAQ,CAAClB,UAAU,CAACpC,EAAE,CAAE;gBACzD8C,QAAQ,EAAGC,CAAC,IAAK7C,sBAAsB,CAACkC,UAAU,CAACpC,EAAE,EAAE+C,CAAC,CAACC,MAAM,CAAC5C,OAAO;cAAE;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CACF;cACDa,KAAK,eACHnE,OAAA,CAAC/B,GAAG;gBAAAiF,QAAA,gBACFlD,OAAA,CAAC9B,UAAU;kBAACyF,OAAO,EAAC,OAAO;kBAAAT,QAAA,EAAEgB,UAAU,CAACnD;gBAAI;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAC1DtD,OAAA,CAAC9B,UAAU;kBAACyF,OAAO,EAAC,OAAO;kBAACD,KAAK,EAAC,gBAAgB;kBAAAR,QAAA,EAC/CgB,UAAU,CAAClD;gBAAW;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YACN,GAdIY,UAAU,CAACpC,EAAE;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAenB,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChBtD,OAAA,CAAClB,aAAa;QAAAoE,QAAA,gBACZlD,OAAA,CAAC7B,MAAM;UAAC2F,OAAO,EAAE/B,iBAAkB;UAAAmB,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACpDtD,OAAA,CAAC7B,MAAM;UACL2F,OAAO,EAAEzB,YAAa;UACtBsB,OAAO,EAAC,WAAW;UACnB0B,QAAQ,EAAE,CAACxE,QAAQ,CAACE,IAAI,CAACuE,IAAI,CAAC,CAAE;UAAApC,QAAA,EAE/BvC,WAAW,GAAG,UAAU,GAAG;QAAO;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACpD,EAAA,CAzPID,KAAe;AAAAsF,EAAA,GAAftF,KAAe;AA2PrB,eAAeA,KAAK;AAAC,IAAAsF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}