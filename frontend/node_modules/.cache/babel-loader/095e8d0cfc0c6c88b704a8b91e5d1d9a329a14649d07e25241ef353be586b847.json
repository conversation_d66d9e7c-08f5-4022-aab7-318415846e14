{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useGridRegisterPipeProcessor } from \"../../core/pipeProcessing/index.js\";\nimport { getDataGridUtilityClass } from \"../../../constants/index.js\";\nimport { GRID_CHECKBOX_SELECTION_COL_DEF, GRID_CHECKBOX_SELECTION_FIELD } from \"../../../colDef/index.js\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  return React.useMemo(() => {\n    const slots = {\n      cellCheckbox: ['cellCheckbox'],\n      columnHeaderCheckbox: ['columnHeaderCheckbox']\n    };\n    return composeClasses(slots, getDataGridUtilityClass, classes);\n  }, [classes]);\n};\nexport const useGridRowSelectionPreProcessors = (apiRef, props) => {\n  const ownerState = {\n    classes: props.classes\n  };\n  const classes = useUtilityClasses(ownerState);\n  const updateSelectionColumn = React.useCallback(columnsState => {\n    const selectionColumn = _extends({}, GRID_CHECKBOX_SELECTION_COL_DEF, {\n      cellClassName: classes.cellCheckbox,\n      headerClassName: classes.columnHeaderCheckbox,\n      headerName: apiRef.current.getLocaleText('checkboxSelectionHeaderName')\n    });\n    const shouldHaveSelectionColumn = props.checkboxSelection;\n    const hasSelectionColumn = columnsState.lookup[GRID_CHECKBOX_SELECTION_FIELD] != null;\n    if (shouldHaveSelectionColumn && !hasSelectionColumn) {\n      columnsState.lookup[GRID_CHECKBOX_SELECTION_FIELD] = selectionColumn;\n      columnsState.orderedFields = [GRID_CHECKBOX_SELECTION_FIELD, ...columnsState.orderedFields];\n    } else if (!shouldHaveSelectionColumn && hasSelectionColumn) {\n      delete columnsState.lookup[GRID_CHECKBOX_SELECTION_FIELD];\n      columnsState.orderedFields = columnsState.orderedFields.filter(field => field !== GRID_CHECKBOX_SELECTION_FIELD);\n    } else if (shouldHaveSelectionColumn && hasSelectionColumn) {\n      columnsState.lookup[GRID_CHECKBOX_SELECTION_FIELD] = _extends({}, selectionColumn, columnsState.lookup[GRID_CHECKBOX_SELECTION_FIELD]);\n      // If the column is not in the columns array (not a custom selection column), move it to the beginning of the column order\n      if (!props.columns.some(col => col.field === GRID_CHECKBOX_SELECTION_FIELD)) {\n        columnsState.orderedFields = [GRID_CHECKBOX_SELECTION_FIELD, ...columnsState.orderedFields.filter(field => field !== GRID_CHECKBOX_SELECTION_FIELD)];\n      }\n    }\n    return columnsState;\n  }, [apiRef, classes, props.columns, props.checkboxSelection]);\n  useGridRegisterPipeProcessor(apiRef, 'hydrateColumns', updateSelectionColumn);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}