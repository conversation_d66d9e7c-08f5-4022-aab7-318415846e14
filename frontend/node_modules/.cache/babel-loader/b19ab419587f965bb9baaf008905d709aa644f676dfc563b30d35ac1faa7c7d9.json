{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport ownerDocument from '@mui/utils/ownerDocument';\nimport { loadStyleSheets } from '@mui/x-internals/export';\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { gridExpandedRowCountSelector } from \"../filter/gridFilterSelector.js\";\nimport { gridColumnDefinitionsSelector, gridColumnVisibilityModelSelector } from \"../columns/gridColumnsSelector.js\";\nimport { gridClasses } from \"../../../constants/gridClasses.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { gridRowsMetaSelector } from \"../rows/gridRowsMetaSelector.js\";\nimport { GRID_ID_AUTOGENERATED } from \"../rows/gridRowsUtils.js\";\nimport { defaultGetRowsToExport, getColumnsToExport } from \"./utils.js\";\nimport { getDerivedPaginationModel } from \"../pagination/useGridPaginationModel.js\";\nimport { useGridRegisterPipeProcessor } from \"../../core/pipeProcessing/index.js\";\nimport { GridPrintExportMenuItem } from \"../../../components/toolbar/index.js\";\nimport { getTotalHeaderHeight } from \"../columns/gridColumnsUtils.js\";\nimport { GRID_CHECKBOX_SELECTION_COL_DEF } from \"../../../colDef/gridCheckboxSelectionColDef.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction raf() {\n  return new Promise(resolve => {\n    requestAnimationFrame(() => {\n      resolve();\n    });\n  });\n}\nfunction buildPrintWindow(title) {\n  const iframeEl = document.createElement('iframe');\n  iframeEl.style.position = 'absolute';\n  iframeEl.style.width = '0px';\n  iframeEl.style.height = '0px';\n  iframeEl.title = title || document.title;\n  return iframeEl;\n}\n\n/**\n * @requires useGridColumns (state)\n * @requires useGridFilter (state)\n * @requires useGridSorting (state)\n * @requires useGridParamsApi (method)\n */\nexport const useGridPrintExport = (apiRef, props) => {\n  const hasRootReference = apiRef.current.rootElementRef.current !== null;\n  const logger = useGridLogger(apiRef, 'useGridPrintExport');\n  const doc = React.useRef(null);\n  const previousGridState = React.useRef(null);\n  const previousColumnVisibility = React.useRef({});\n  const previousRows = React.useRef([]);\n  const previousVirtualizationState = React.useRef(null);\n  React.useEffect(() => {\n    doc.current = ownerDocument(apiRef.current.rootElementRef.current);\n  }, [apiRef, hasRootReference]);\n\n  // Returns a promise because updateColumns triggers state update and\n  // the new state needs to be in place before the grid can be sized correctly\n  const updateGridColumnsForPrint = React.useCallback((fields, allColumns, includeCheckboxes) => new Promise(resolve => {\n    const exportedColumnFields = getColumnsToExport({\n      apiRef,\n      options: {\n        fields,\n        allColumns\n      }\n    }).map(column => column.field);\n    const columns = gridColumnDefinitionsSelector(apiRef);\n    const newColumnVisibilityModel = {};\n    columns.forEach(column => {\n      newColumnVisibilityModel[column.field] = exportedColumnFields.includes(column.field);\n    });\n    if (includeCheckboxes) {\n      newColumnVisibilityModel[GRID_CHECKBOX_SELECTION_COL_DEF.field] = true;\n    }\n    apiRef.current.setColumnVisibilityModel(newColumnVisibilityModel);\n    resolve();\n  }), [apiRef]);\n  const updateGridRowsForPrint = React.useCallback(getRowsToExport => {\n    const rowsToExportIds = getRowsToExport({\n      apiRef\n    });\n    const newRows = rowsToExportIds.reduce((acc, id) => {\n      const row = apiRef.current.getRow(id);\n      if (!row[GRID_ID_AUTOGENERATED]) {\n        acc.push(row);\n      }\n      return acc;\n    }, []);\n    apiRef.current.setRows(newRows);\n  }, [apiRef]);\n  const handlePrintWindowLoad = React.useCallback((printWindow, options) => {\n    const normalizeOptions = _extends({\n      copyStyles: true,\n      hideToolbar: false,\n      hideFooter: false,\n      includeCheckboxes: false\n    }, options);\n    const printDoc = printWindow.contentDocument;\n    if (!printDoc) {\n      return;\n    }\n    const rowsMeta = gridRowsMetaSelector(apiRef);\n    const gridRootElement = apiRef.current.rootElementRef.current;\n    const gridClone = gridRootElement.cloneNode(true);\n\n    // Allow to overflow to not hide the border of the last row\n    const gridMain = gridClone.querySelector(`.${gridClasses.main}`);\n    gridMain.style.overflow = 'visible';\n\n    // See https://support.google.com/chrome/thread/191619088?hl=en&msgid=193009642\n    gridClone.style.contain = 'size';\n    let gridToolbarElementHeight = gridRootElement.querySelector(`.${gridClasses.toolbarContainer}`)?.offsetHeight || 0;\n    let gridFooterElementHeight = gridRootElement.querySelector(`.${gridClasses.footerContainer}`)?.offsetHeight || 0;\n    const gridFooterElement = gridClone.querySelector(`.${gridClasses.footerContainer}`);\n    if (normalizeOptions.hideToolbar) {\n      gridClone.querySelector(`.${gridClasses.toolbarContainer}`)?.remove();\n      gridToolbarElementHeight = 0;\n    }\n    if (normalizeOptions.hideFooter && gridFooterElement) {\n      gridFooterElement.remove();\n      gridFooterElementHeight = 0;\n    }\n\n    // Expand container height to accommodate all rows\n    const computedTotalHeight = rowsMeta.currentPageTotalHeight + getTotalHeaderHeight(apiRef, props) + gridToolbarElementHeight + gridFooterElementHeight;\n    gridClone.style.height = `${computedTotalHeight}px`;\n    // The height above does not include grid border width, so we need to exclude it\n    gridClone.style.boxSizing = 'content-box';\n    if (!normalizeOptions.hideFooter && gridFooterElement) {\n      // the footer is always being placed at the bottom of the page as if all rows are exported\n      // so if getRowsToExport is being used to only export a subset of rows then we need to\n      // adjust the footer position to be correctly placed at the bottom of the grid\n      gridFooterElement.style.position = 'absolute';\n      gridFooterElement.style.width = '100%';\n      gridFooterElement.style.top = `${computedTotalHeight - gridFooterElementHeight}px`;\n    }\n\n    // printDoc.body.appendChild(gridClone); should be enough but a clone isolation bug in Safari\n    // prevents us to do it\n    const container = document.createElement('div');\n    container.appendChild(gridClone);\n    // To avoid an empty page in start on Chromium based browsers\n    printDoc.body.style.marginTop = '0px';\n    printDoc.body.innerHTML = container.innerHTML;\n    const defaultPageStyle = typeof normalizeOptions.pageStyle === 'function' ? normalizeOptions.pageStyle() : normalizeOptions.pageStyle;\n    if (typeof defaultPageStyle === 'string') {\n      // TODO custom styles should always win\n      const styleElement = printDoc.createElement('style');\n      styleElement.appendChild(printDoc.createTextNode(defaultPageStyle));\n      printDoc.head.appendChild(styleElement);\n    }\n    if (normalizeOptions.bodyClassName) {\n      printDoc.body.classList.add(...normalizeOptions.bodyClassName.split(' '));\n    }\n    let stylesheetLoadPromises = [];\n    if (normalizeOptions.copyStyles) {\n      const rootCandidate = gridRootElement.getRootNode();\n      const root = rootCandidate.constructor.name === 'ShadowRoot' ? rootCandidate : doc.current;\n      stylesheetLoadPromises = loadStyleSheets(printDoc, root);\n    }\n\n    // Trigger print\n    if (process.env.NODE_ENV !== 'test') {\n      // wait for remote stylesheets to load\n      Promise.all(stylesheetLoadPromises).then(() => {\n        printWindow.contentWindow.print();\n      });\n    }\n  }, [apiRef, doc, props]);\n  const handlePrintWindowAfterPrint = React.useCallback(printWindow => {\n    // Remove the print iframe\n    doc.current.body.removeChild(printWindow);\n\n    // Revert grid to previous state\n    apiRef.current.restoreState(previousGridState.current || {});\n    if (!previousGridState.current?.columns?.columnVisibilityModel) {\n      // if the apiRef.current.exportState(); did not exported the column visibility, we update it\n      apiRef.current.setColumnVisibilityModel(previousColumnVisibility.current);\n    }\n    apiRef.current.setState(state => _extends({}, state, {\n      virtualization: previousVirtualizationState.current\n    }));\n    apiRef.current.setRows(previousRows.current);\n\n    // Clear local state\n    previousGridState.current = null;\n    previousColumnVisibility.current = {};\n    previousRows.current = [];\n  }, [apiRef]);\n  const exportDataAsPrint = React.useCallback(async options => {\n    logger.debug(`Export data as Print`);\n    if (!apiRef.current.rootElementRef.current) {\n      throw new Error('MUI X: No grid root element available.');\n    }\n    previousGridState.current = apiRef.current.exportState();\n    // It appends that the visibility model is not exported, especially if columnVisibility is not controlled\n    previousColumnVisibility.current = gridColumnVisibilityModelSelector(apiRef);\n    previousRows.current = apiRef.current.getSortedRows().filter(row => !row[GRID_ID_AUTOGENERATED]);\n    if (props.pagination) {\n      const visibleRowCount = gridExpandedRowCountSelector(apiRef);\n      const paginationModel = {\n        page: 0,\n        pageSize: visibleRowCount\n      };\n      apiRef.current.setState(state => _extends({}, state, {\n        pagination: _extends({}, state.pagination, {\n          paginationModel: getDerivedPaginationModel(state.pagination,\n          // Using signature `DataGridPro` to allow more than 100 rows in the print export\n          'DataGridPro', paginationModel)\n        })\n      }));\n    }\n    previousVirtualizationState.current = apiRef.current.state.virtualization;\n    apiRef.current.setState(state => _extends({}, state, {\n      virtualization: _extends({}, state.virtualization, {\n        enabled: false,\n        enabledForColumns: false\n      })\n    }));\n    await updateGridColumnsForPrint(options?.fields, options?.allColumns, options?.includeCheckboxes);\n    updateGridRowsForPrint(options?.getRowsToExport ?? defaultGetRowsToExport);\n    await raf(); // wait for the state changes to take action\n    const printWindow = buildPrintWindow(options?.fileName);\n    if (process.env.NODE_ENV === 'test') {\n      doc.current.body.appendChild(printWindow);\n      // In test env, run the all pipeline without waiting for loading\n      handlePrintWindowLoad(printWindow, options);\n      handlePrintWindowAfterPrint(printWindow);\n    } else {\n      printWindow.onload = () => {\n        handlePrintWindowLoad(printWindow, options);\n        const mediaQueryList = printWindow.contentWindow.matchMedia('print');\n        mediaQueryList.addEventListener('change', mql => {\n          const isAfterPrint = mql.matches === false;\n          if (isAfterPrint) {\n            handlePrintWindowAfterPrint(printWindow);\n          }\n        });\n      };\n      doc.current.body.appendChild(printWindow);\n    }\n  }, [props, logger, apiRef, handlePrintWindowLoad, handlePrintWindowAfterPrint, updateGridColumnsForPrint, updateGridRowsForPrint]);\n  const printExportApi = {\n    exportDataAsPrint\n  };\n  useGridApiMethod(apiRef, printExportApi, 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const addExportMenuButtons = React.useCallback((initialValue, options) => {\n    if (options.printOptions?.disableToolbarButton) {\n      return initialValue;\n    }\n    return [...initialValue, {\n      component: /*#__PURE__*/_jsx(GridPrintExportMenuItem, {\n        options: options.printOptions\n      }),\n      componentName: 'printExport'\n    }];\n  }, []);\n  useGridRegisterPipeProcessor(apiRef, 'exportMenu', addExportMenuButtons);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}