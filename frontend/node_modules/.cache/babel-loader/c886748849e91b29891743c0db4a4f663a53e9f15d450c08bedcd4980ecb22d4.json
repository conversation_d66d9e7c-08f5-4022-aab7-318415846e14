{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\", \"children\", \"sidePanel\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport useForkRef from '@mui/utils/useForkRef';\nimport capitalize from '@mui/utils/capitalize';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { fastMemo } from '@mui/x-internals/fastMemo';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { GridRootStyles } from \"./GridRootStyles.js\";\nimport { useCSSVariablesContext } from \"../../utils/css/context.js\";\nimport { useGridSelector } from \"../../hooks/utils/useGridSelector.js\";\nimport { useGridPrivateApiContext } from \"../../hooks/utils/useGridPrivateApiContext.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { getDataGridUtilityClass, gridClasses } from \"../../constants/gridClasses.js\";\nimport { gridDensitySelector } from \"../../hooks/features/density/densitySelector.js\";\nimport { useIsSSR } from \"../../hooks/utils/useIsSSR.js\";\nimport { GridHeader } from \"../GridHeader.js\";\nimport { GridBody, GridFooterPlaceholder } from \"../base/index.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = (ownerState, density) => {\n  const {\n    autoHeight,\n    classes,\n    showCellVerticalBorder\n  } = ownerState;\n  const slots = {\n    root: ['root', autoHeight && 'autoHeight', `root--density${capitalize(density)}`, ownerState.slots.toolbar === null && 'root--noToolbar', 'withBorderColor', showCellVerticalBorder && 'withVerticalBorder']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst GridRoot = forwardRef(function GridRoot(props, ref) {\n  const rootProps = useGridRootProps();\n  const {\n      className,\n      children,\n      sidePanel\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const apiRef = useGridPrivateApiContext();\n  const density = useGridSelector(apiRef, gridDensitySelector);\n  const rootElementRef = apiRef.current.rootElementRef;\n  const rootMountCallback = React.useCallback(node => {\n    if (node === null) {\n      return;\n    }\n    apiRef.current.publishEvent('rootMount', node);\n  }, [apiRef]);\n  const handleRef = useForkRef(rootElementRef, ref, rootMountCallback);\n  const ownerState = rootProps;\n  const classes = useUtilityClasses(ownerState, density);\n  const cssVariables = useCSSVariablesContext();\n  const isSSR = useIsSSR();\n  if (isSSR) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxs(GridRootStyles, _extends({\n    className: clsx(classes.root, className, cssVariables.className, sidePanel && gridClasses.withSidePanel),\n    ownerState: ownerState\n  }, other, {\n    ref: handleRef,\n    children: [/*#__PURE__*/_jsxs(\"div\", {\n      className: gridClasses.mainContent,\n      role: \"presentation\",\n      children: [/*#__PURE__*/_jsx(GridHeader, {}), /*#__PURE__*/_jsx(GridBody, {\n        children: children\n      }), /*#__PURE__*/_jsx(GridFooterPlaceholder, {})]\n    }), sidePanel, cssVariables.tag]\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridRoot.displayName = \"GridRoot\";\nprocess.env.NODE_ENV !== \"production\" ? GridRoot.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  sidePanel: PropTypes.node,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nconst MemoizedGridRoot = fastMemo(GridRoot);\nexport { MemoizedGridRoot as GridRoot };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}