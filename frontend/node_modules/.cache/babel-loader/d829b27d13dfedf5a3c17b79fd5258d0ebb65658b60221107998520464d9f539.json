{"ast": null, "code": "import * as React from 'react';\nimport { useFirstRender } from \"../../utils/useFirstRender.js\";\nexport const useGridRegisterStrategyProcessor = (apiRef, strategyName, group, processor) => {\n  const registerPreProcessor = React.useCallback(() => {\n    apiRef.current.registerStrategyProcessor(strategyName, group, processor);\n  }, [apiRef, processor, group, strategyName]);\n  useFirstRender(() => {\n    registerPreProcessor();\n  });\n  const isFirstRender = React.useRef(true);\n  React.useEffect(() => {\n    if (isFirstRender.current) {\n      isFirstRender.current = false;\n    } else {\n      registerPreProcessor();\n    }\n  }, [registerPreProcessor]);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}