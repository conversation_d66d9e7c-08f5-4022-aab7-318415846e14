{"ast": null, "code": "export const checkColumnVisibilityModelsSame = (a, b) => {\n  // Filter `false` values only, as `true` and not having a key are the same\n  const aFalseValues = new Set(Object.keys(a).filter(key => a[key] === false));\n  const bFalseValues = new Set(Object.keys(b).filter(key => b[key] === false));\n  if (aFalseValues.size !== bFalseValues.size) {\n    return false;\n  }\n  let result = true;\n  aFalseValues.forEach(key => {\n    if (!bFalseValues.has(key)) {\n      result = false;\n    }\n  });\n  return result;\n};\nexport const defaultSearchPredicate = (column, searchValue) => (column.headerName || column.field).toLowerCase().indexOf(searchValue) > -1;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}