{"ast": null, "code": "import { useGridLogger } from \"./useGridLogger.js\";\nimport { useGridEventPriority } from \"./useGridEvent.js\";\nexport const useGridNativeEventListener = (apiRef, ref, eventName, handler, options) => {\n  const logger = useGridLogger(apiRef, 'useNativeEventListener');\n  useGridEventPriority(apiRef, 'rootMount', () => {\n    const targetElement = ref();\n    if (!targetElement || !eventName) {\n      return undefined;\n    }\n    logger.debug(`Binding native ${eventName} event`);\n    targetElement.addEventListener(eventName, handler, options);\n    return () => {\n      logger.debug(`Clearing native ${eventName} event`);\n      targetElement.removeEventListener(eventName, handler, options);\n    };\n  });\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}