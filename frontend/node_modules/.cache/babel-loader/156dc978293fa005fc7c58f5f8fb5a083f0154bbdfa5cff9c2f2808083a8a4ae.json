{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"id\", \"value\", \"formattedValue\", \"api\", \"field\", \"row\", \"rowNode\", \"colDef\", \"cellMode\", \"isEditable\", \"tabIndex\", \"hasFocus\", \"inputProps\", \"isValidating\", \"isProcessingProps\", \"onValueChange\", \"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport composeClasses from '@mui/utils/composeClasses';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport { styled } from '@mui/material/styles';\nimport { NotRendered } from \"../../utils/assert.js\";\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst StyledInputBase = styled(NotRendered)({\n  fontSize: 'inherit'\n});\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['editInputCell']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nfunction GridEditDateCell(props) {\n  const {\n      id,\n      value: valueProp,\n      field,\n      colDef,\n      hasFocus,\n      onValueChange,\n      slotProps\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const isDateTime = colDef.type === 'dateTime';\n  const apiRef = useGridApiContext();\n  const inputRef = React.useRef(null);\n  const valueTransformed = React.useMemo(() => {\n    let parsedDate;\n    if (valueProp == null) {\n      parsedDate = null;\n    } else if (valueProp instanceof Date) {\n      parsedDate = valueProp;\n    } else {\n      parsedDate = new Date((valueProp ?? '').toString());\n    }\n    let formattedDate;\n    if (parsedDate == null || Number.isNaN(parsedDate.getTime())) {\n      formattedDate = '';\n    } else {\n      const localDate = new Date(parsedDate.getTime() - parsedDate.getTimezoneOffset() * 60 * 1000);\n      formattedDate = localDate.toISOString().substr(0, isDateTime ? 16 : 10);\n    }\n    return {\n      parsed: parsedDate,\n      formatted: formattedDate\n    };\n  }, [valueProp, isDateTime]);\n  const [valueState, setValueState] = React.useState(valueTransformed);\n  const rootProps = useGridRootProps();\n  const ownerState = {\n    classes: rootProps.classes\n  };\n  const classes = useUtilityClasses(ownerState);\n  const parseValueToDate = React.useCallback(value => {\n    if (value === '') {\n      return null;\n    }\n    const [date, time] = value.split('T');\n    const [year, month, day] = date.split('-');\n    const parsedDate = new Date();\n    parsedDate.setFullYear(Number(year), Number(month) - 1, Number(day));\n    parsedDate.setHours(0, 0, 0, 0);\n    if (time) {\n      const [hours, minutes] = time.split(':');\n      parsedDate.setHours(Number(hours), Number(minutes), 0, 0);\n    }\n    return parsedDate;\n  }, []);\n  const handleChange = React.useCallback(async event => {\n    const newFormattedDate = event.target.value;\n    const newParsedDate = parseValueToDate(newFormattedDate);\n    if (onValueChange) {\n      await onValueChange(event, newParsedDate);\n    }\n    setValueState({\n      parsed: newParsedDate,\n      formatted: newFormattedDate\n    });\n    apiRef.current.setEditCellValue({\n      id,\n      field,\n      value: newParsedDate\n    }, event);\n  }, [apiRef, field, id, onValueChange, parseValueToDate]);\n  React.useEffect(() => {\n    setValueState(state => {\n      if (valueTransformed.parsed !== state.parsed && valueTransformed.parsed?.getTime() !== state.parsed?.getTime()) {\n        return valueTransformed;\n      }\n      return state;\n    });\n  }, [valueTransformed]);\n  useEnhancedEffect(() => {\n    if (hasFocus) {\n      inputRef.current.focus();\n    }\n  }, [hasFocus]);\n  return /*#__PURE__*/_jsx(StyledInputBase, _extends({\n    as: rootProps.slots.baseInput,\n    inputRef: inputRef,\n    fullWidth: true,\n    className: classes.root,\n    type: isDateTime ? 'datetime-local' : 'date',\n    value: valueState.formatted,\n    onChange: handleChange\n  }, other, slotProps?.root, {\n    slotProps: {\n      htmlInput: _extends({\n        max: isDateTime ? '9999-12-31T23:59' : '9999-12-31'\n      }, slotProps?.root?.slotProps?.htmlInput)\n    }\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? GridEditDateCell.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * GridApi that let you manipulate the grid.\n   */\n  api: PropTypes.object.isRequired,\n  /**\n   * The mode of the cell.\n   */\n  cellMode: PropTypes.oneOf(['edit', 'view']).isRequired,\n  changeReason: PropTypes.oneOf(['debouncedSetEditCellValue', 'setEditCellValue']),\n  /**\n   * The column of the row that the current cell belongs to.\n   */\n  colDef: PropTypes.object.isRequired,\n  /**\n   * The column field of the cell that triggered the event.\n   */\n  field: PropTypes.string.isRequired,\n  /**\n   * The cell value formatted with the column valueFormatter.\n   */\n  formattedValue: PropTypes.any,\n  /**\n   * If true, the cell is the active element.\n   */\n  hasFocus: PropTypes.bool.isRequired,\n  /**\n   * The grid row id.\n   */\n  id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,\n  /**\n   * If true, the cell is editable.\n   */\n  isEditable: PropTypes.bool,\n  isProcessingProps: PropTypes.bool,\n  isValidating: PropTypes.bool,\n  /**\n   * Callback called when the value is changed by the user.\n   * @param {React.ChangeEvent<HTMLInputElement>} event The event source of the callback.\n   * @param {Date | null} newValue The value that is going to be passed to `apiRef.current.setEditCellValue`.\n   * @returns {Promise<void> | void} A promise to be awaited before calling `apiRef.current.setEditCellValue`\n   */\n  onValueChange: PropTypes.func,\n  /**\n   * The row model of the row that the current cell belongs to.\n   */\n  row: PropTypes.any.isRequired,\n  /**\n   * The node of the row that the current cell belongs to.\n   */\n  rowNode: PropTypes.object.isRequired,\n  slotProps: PropTypes.object,\n  /**\n   * the tabIndex value.\n   */\n  tabIndex: PropTypes.oneOf([-1, 0]).isRequired,\n  /**\n   * The cell value.\n   * If the column has `valueGetter`, use `params.row` to directly access the fields.\n   */\n  value: PropTypes.any\n} : void 0;\nexport { GridEditDateCell };\nexport const renderEditDateCell = params => /*#__PURE__*/_jsx(GridEditDateCell, _extends({}, params));\nif (process.env.NODE_ENV !== \"production\") renderEditDateCell.displayName = \"renderEditDateCell\";", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}