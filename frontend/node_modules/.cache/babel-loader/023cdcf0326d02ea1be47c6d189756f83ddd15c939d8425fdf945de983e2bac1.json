{"ast": null, "code": "import { createRootSelector, createSelector, createSelectorMemoized } from \"../../../utils/createSelector.js\";\nexport const gridRowsStateSelector = createRootSelector(state => state.rows);\nexport const gridRowCountSelector = createSelector(gridRowsStateSelector, rows => rows.totalRowCount);\nexport const gridRowsLoadingSelector = createSelector(gridRowsStateSelector, rows => rows.loading);\nexport const gridTopLevelRowCountSelector = createSelector(gridRowsStateSelector, rows => rows.totalTopLevelRowCount);\n\n// TODO rows v6: Rename\nexport const gridRowsLookupSelector = createSelector(gridRowsStateSelector, rows => rows.dataRowIdToModelLookup);\nexport const gridRowSelector = createSelector(gridRowsLookupSelector, (rows, id) => rows[id]);\nexport const gridRowTreeSelector = createSelector(gridRowsStateSelector, rows => rows.tree);\nexport const gridRowNodeSelector = createSelector(gridRowTreeSelector, (rowTree, rowId) => rowTree[rowId]);\nexport const gridRowGroupsToFetchSelector = createSelector(gridRowsStateSelector, rows => rows.groupsToFetch);\nexport const gridRowGroupingNameSelector = createSelector(gridRowsStateSelector, rows => rows.groupingName);\nexport const gridRowTreeDepthsSelector = createSelector(gridRowsStateSelector, rows => rows.treeDepths);\nexport const gridRowMaximumTreeDepthSelector = createSelectorMemoized(gridRowsStateSelector, rows => {\n  const entries = Object.entries(rows.treeDepths);\n  if (entries.length === 0) {\n    return 1;\n  }\n  return (entries.filter(([, nodeCount]) => nodeCount > 0).map(([depth]) => Number(depth)).sort((a, b) => b - a)[0] ?? 0) + 1;\n});\nexport const gridDataRowIdsSelector = createSelector(gridRowsStateSelector, rows => rows.dataRowIds);\nexport const gridDataRowsSelector = createSelectorMemoized(gridDataRowIdsSelector, gridRowsLookupSelector, (dataRowIds, rowsLookup) => dataRowIds.reduce((acc, id) => {\n  if (!rowsLookup[id]) {\n    return acc;\n  }\n  acc.push(rowsLookup[id]);\n  return acc;\n}, []));\n\n/**\n * @ignore - do not document.\n */\nexport const gridAdditionalRowGroupsSelector = createSelector(gridRowsStateSelector, rows => rows?.additionalRowGroups);\n\n/**\n * @ignore - do not document.\n */\nexport const gridPinnedRowsSelector = createSelectorMemoized(gridAdditionalRowGroupsSelector, additionalRowGroups => {\n  const rawPinnedRows = additionalRowGroups?.pinnedRows;\n  return {\n    bottom: rawPinnedRows?.bottom?.map(rowEntry => ({\n      id: rowEntry.id,\n      model: rowEntry.model ?? {}\n    })) ?? [],\n    top: rawPinnedRows?.top?.map(rowEntry => ({\n      id: rowEntry.id,\n      model: rowEntry.model ?? {}\n    })) ?? []\n  };\n});\n\n/**\n * @ignore - do not document.\n */\nexport const gridPinnedRowsCountSelector = createSelector(gridPinnedRowsSelector, pinnedRows => {\n  return (pinnedRows?.top?.length || 0) + (pinnedRows?.bottom?.length || 0);\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}