{"ast": null, "code": "import { GridSignature } from \"../../../constants/signature.js\";\nimport { GRID_ROOT_GROUP_ID } from \"../rows/gridRowsUtils.js\";\nimport { gridFilteredRowsLookupSelector } from \"../filter/gridFilterSelector.js\";\nimport { gridSortedRowIdsSelector } from \"../sorting/gridSortingSelector.js\";\nimport { gridRowSelectionManagerSelector } from \"./gridRowSelectionSelector.js\";\nimport { gridRowTreeSelector } from \"../rows/gridRowsSelector.js\";\nimport { createSelector } from \"../../../utils/createSelector.js\";\nexport const ROW_SELECTION_PROPAGATION_DEFAULT = {\n  parents: true,\n  descendants: true\n};\nfunction getGridRowGroupSelectableDescendants(apiRef, groupId) {\n  const rowTree = gridRowTreeSelector(apiRef);\n  const sortedRowIds = gridSortedRowIdsSelector(apiRef);\n  const filteredRowsLookup = gridFilteredRowsLookupSelector(apiRef);\n  const groupNode = rowTree[groupId];\n  if (!groupNode || groupNode.type !== 'group') {\n    return [];\n  }\n  const descendants = [];\n  const startIndex = sortedRowIds.findIndex(id => id === groupId) + 1;\n  for (let index = startIndex; index < sortedRowIds.length && rowTree[sortedRowIds[index]]?.depth > groupNode.depth; index += 1) {\n    const id = sortedRowIds[index];\n    if (filteredRowsLookup[id] !== false && apiRef.current.isRowSelectable(id)) {\n      descendants.push(id);\n    }\n  }\n  return descendants;\n}\nexport const checkboxPropsSelector = createSelector(gridRowTreeSelector, gridFilteredRowsLookupSelector, gridRowSelectionManagerSelector, (rowTree, filteredRowsLookup, rowSelectionManager, {\n  groupId,\n  autoSelectParents\n}) => {\n  const groupNode = rowTree[groupId];\n  if (!groupNode || groupNode.type !== 'group' || rowSelectionManager.has(groupId)) {\n    return {\n      isIndeterminate: false,\n      isChecked: rowSelectionManager.has(groupId)\n    };\n  }\n  let hasSelectedDescendant = false;\n  let hasUnSelectedDescendant = false;\n  const traverseDescendants = itemToTraverseId => {\n    if (filteredRowsLookup[itemToTraverseId] === false ||\n    // Perf: Skip checking the rest of the descendants if we already\n    // know that there is a selected and an unselected descendant\n    hasSelectedDescendant && hasUnSelectedDescendant) {\n      return;\n    }\n    const node = rowTree[itemToTraverseId];\n    if (node?.type === 'group') {\n      node.children.forEach(traverseDescendants);\n    }\n    if (rowSelectionManager.has(itemToTraverseId)) {\n      hasSelectedDescendant = true;\n    } else {\n      hasUnSelectedDescendant = true;\n    }\n  };\n  traverseDescendants(groupId);\n  return {\n    isIndeterminate: hasSelectedDescendant && hasUnSelectedDescendant,\n    isChecked: autoSelectParents ? hasSelectedDescendant && !hasUnSelectedDescendant : false\n  };\n});\nexport function isMultipleRowSelectionEnabled(props) {\n  if (props.signature === GridSignature.DataGrid) {\n    // DataGrid Community has multiple row selection enabled only if checkbox selection is enabled.\n    return props.checkboxSelection && props.disableMultipleRowSelection !== true;\n  }\n  return !props.disableMultipleRowSelection;\n}\nconst getRowNodeParents = (tree, id) => {\n  const parents = [];\n  let parent = id;\n  while (parent != null && parent !== GRID_ROOT_GROUP_ID) {\n    const node = tree[parent];\n    if (!node) {\n      return parents;\n    }\n    parents.push(parent);\n    parent = node.parent;\n  }\n  return parents;\n};\nconst getFilteredRowNodeSiblings = (tree, filteredRows, id) => {\n  const node = tree[id];\n  if (!node) {\n    return [];\n  }\n  const parent = node.parent;\n  if (parent == null) {\n    return [];\n  }\n  const parentNode = tree[parent];\n  return parentNode.children.filter(childId => childId !== id && filteredRows[childId] !== false);\n};\nexport const findRowsToSelect = (apiRef, tree, selectedRow, autoSelectDescendants, autoSelectParents, addRow, rowSelectionManager = gridRowSelectionManagerSelector(apiRef)) => {\n  const filteredRows = gridFilteredRowsLookupSelector(apiRef);\n  const selectedDescendants = new Set([]);\n  if (!autoSelectDescendants && !autoSelectParents || filteredRows[selectedRow] === false) {\n    return;\n  }\n  if (autoSelectDescendants) {\n    const rowNode = tree[selectedRow];\n    if (rowNode?.type === 'group') {\n      const descendants = getGridRowGroupSelectableDescendants(apiRef, selectedRow);\n      descendants.forEach(rowId => {\n        addRow(rowId);\n        selectedDescendants.add(rowId);\n      });\n    }\n  }\n  if (autoSelectParents) {\n    const checkAllDescendantsSelected = rowId => {\n      if (!rowSelectionManager.has(rowId) && !selectedDescendants.has(rowId)) {\n        return false;\n      }\n      const node = tree[rowId];\n      if (!node) {\n        return false;\n      }\n      if (node.type !== 'group') {\n        return true;\n      }\n      return node.children.every(checkAllDescendantsSelected);\n    };\n    const traverseParents = rowId => {\n      const siblings = getFilteredRowNodeSiblings(tree, filteredRows, rowId);\n      if (siblings.length === 0 || siblings.every(checkAllDescendantsSelected)) {\n        const rowNode = tree[rowId];\n        const parent = rowNode?.parent;\n        if (parent != null && parent !== GRID_ROOT_GROUP_ID && apiRef.current.isRowSelectable(parent)) {\n          addRow(parent);\n          selectedDescendants.add(parent);\n          traverseParents(parent);\n        }\n      }\n    };\n    traverseParents(selectedRow);\n  }\n};\nexport const findRowsToDeselect = (apiRef, tree, deselectedRow, autoSelectDescendants, autoSelectParents, removeRow) => {\n  const rowSelectionManager = gridRowSelectionManagerSelector(apiRef);\n  if (!autoSelectParents && !autoSelectDescendants) {\n    return;\n  }\n  if (autoSelectParents) {\n    const allParents = getRowNodeParents(tree, deselectedRow);\n    allParents.forEach(parent => {\n      const isSelected = rowSelectionManager.has(parent);\n      if (isSelected) {\n        removeRow(parent);\n      }\n    });\n  }\n  if (autoSelectDescendants) {\n    const rowNode = tree[deselectedRow];\n    if (rowNode?.type === 'group') {\n      const descendants = getGridRowGroupSelectableDescendants(apiRef, deselectedRow);\n      descendants.forEach(descendant => {\n        removeRow(descendant);\n      });\n    }\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}