{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from '@mui/system';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['iconButtonContainer']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst GridIconButtonContainerRoot = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'IconButtonContainer'\n})(() => ({\n  display: 'flex',\n  visibility: 'hidden',\n  width: 0\n}));\nexport const GridIconButtonContainer = forwardRef(function GridIconButtonContainer(props, ref) {\n  const {\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const rootProps = useGridRootProps();\n  const classes = useUtilityClasses(rootProps);\n  return /*#__PURE__*/_jsx(GridIconButtonContainerRoot, _extends({\n    className: clsx(classes.root, className),\n    ownerState: rootProps\n  }, other, {\n    ref: ref\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridIconButtonContainer.displayName = \"GridIconButtonContainer\";", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}