{"ast": null, "code": "/**\n * Filter item definition interface.\n * @demos\n *   - [Custom filter operator](/x/react-data-grid/filtering/customization/#create-a-custom-operator)\n */\nvar GridLogicOperator = /*#__PURE__*/function (GridLogicOperator) {\n  GridLogicOperator[\"And\"] = \"and\";\n  GridLogicOperator[\"Or\"] = \"or\";\n  return GridLogicOperator;\n}(GridLogicOperator || {});\nexport { GridLogicOperator };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}