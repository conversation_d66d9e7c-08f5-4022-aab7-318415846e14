{"ast": null, "code": "import * as React from 'react';\nimport { alpha, darken, lighten } from '@mui/material/styles';\nimport { useTheme } from '@mui/material/styles';\nimport { hash, stringify } from '@mui/x-internals/hash';\nimport { vars } from \"../constants/cssVariables.js\";\nexport function useMaterialCSSVariables() {\n  const theme = useTheme();\n  return React.useMemo(() => {\n    const id = hash(stringify(theme));\n    const variables = transformTheme(theme);\n    return {\n      id,\n      variables\n    };\n  }, [theme]);\n}\nfunction transformTheme(t) {\n  const borderColor = getBorderColor(t);\n  const dataGridPalette = (t.vars || t).palette.DataGrid;\n  const backgroundBase = dataGridPalette?.bg ?? (t.vars || t).palette.background.default;\n  const backgroundHeader = dataGridPalette?.headerBg ?? backgroundBase;\n  const backgroundPinned = dataGridPalette?.pinnedBg ?? backgroundBase;\n  const backgroundBackdrop = t.vars ? `rgba(${t.vars.palette.background.defaultChannel} / ${t.vars.palette.action.disabledOpacity})` : alpha(t.palette.background.default, t.palette.action.disabledOpacity);\n  const backgroundOverlay = t.palette.mode === 'dark' ? `color-mix(in srgb, ${(t.vars || t).palette.background.paper} 95%, #fff)` : (t.vars || t).palette.background.paper;\n  const selectedColor = t.vars ? `rgb(${t.vars.palette.primary.mainChannel})` : t.palette.primary.main;\n  const radius = getRadius(t);\n  const fontBody = t.vars?.font?.body2 ?? formatFont(t.typography.body2);\n  const fontSmall = t.vars?.font?.caption ?? formatFont(t.typography.caption);\n  const fontLarge = t.vars?.font?.body1 ?? formatFont(t.typography.body1);\n  const k = vars.keys;\n  return {\n    [k.spacingUnit]: t.vars ? t.vars.spacing ?? t.spacing(1) : t.spacing(1),\n    [k.colors.border.base]: borderColor,\n    [k.colors.background.base]: backgroundBase,\n    [k.colors.background.overlay]: backgroundOverlay,\n    [k.colors.background.backdrop]: backgroundBackdrop,\n    [k.colors.foreground.base]: (t.vars || t).palette.text.primary,\n    [k.colors.foreground.muted]: (t.vars || t).palette.text.secondary,\n    [k.colors.foreground.accent]: (t.vars || t).palette.primary.dark,\n    [k.colors.foreground.disabled]: (t.vars || t).palette.text.disabled,\n    [k.colors.foreground.error]: (t.vars || t).palette.error.dark,\n    [k.colors.interactive.hover]: (t.vars || t).palette.action.hover,\n    [k.colors.interactive.hoverOpacity]: (t.vars || t).palette.action.hoverOpacity,\n    [k.colors.interactive.focus]: removeOpacity((t.vars || t).palette.primary.main),\n    [k.colors.interactive.focusOpacity]: (t.vars || t).palette.action.focusOpacity,\n    [k.colors.interactive.disabled]: removeOpacity((t.vars || t).palette.action.disabled),\n    [k.colors.interactive.disabledOpacity]: (t.vars || t).palette.action.disabledOpacity,\n    [k.colors.interactive.selected]: selectedColor,\n    [k.colors.interactive.selectedOpacity]: (t.vars || t).palette.action.selectedOpacity,\n    [k.header.background.base]: backgroundHeader,\n    [k.cell.background.pinned]: backgroundPinned,\n    [k.radius.base]: radius,\n    [k.typography.fontFamily.base]: t.typography.fontFamily,\n    [k.typography.fontWeight.light]: t.typography.fontWeightLight,\n    [k.typography.fontWeight.regular]: t.typography.fontWeightRegular,\n    [k.typography.fontWeight.medium]: t.typography.fontWeightMedium,\n    [k.typography.fontWeight.bold]: t.typography.fontWeightBold,\n    [k.typography.font.body]: fontBody,\n    [k.typography.font.small]: fontSmall,\n    [k.typography.font.large]: fontLarge,\n    [k.transitions.easing.easeIn]: t.transitions.easing.easeIn,\n    [k.transitions.easing.easeOut]: t.transitions.easing.easeOut,\n    [k.transitions.easing.easeInOut]: t.transitions.easing.easeInOut,\n    [k.transitions.duration.short]: `${t.transitions.duration.shorter}ms`,\n    [k.transitions.duration.base]: `${t.transitions.duration.short}ms`,\n    [k.transitions.duration.long]: `${t.transitions.duration.standard}ms`,\n    [k.shadows.base]: (t.vars || t).shadows[2],\n    [k.shadows.overlay]: (t.vars || t).shadows[8],\n    [k.zIndex.panel]: (t.vars || t).zIndex.modal,\n    [k.zIndex.menu]: (t.vars || t).zIndex.modal\n  };\n}\nfunction getRadius(theme) {\n  if (theme.vars) {\n    return theme.vars.shape.borderRadius;\n  }\n  return typeof theme.shape.borderRadius === 'number' ? `${theme.shape.borderRadius}px` : theme.shape.borderRadius;\n}\nfunction getBorderColor(theme) {\n  if (theme.vars) {\n    return theme.vars.palette.TableCell.border;\n  }\n  if (theme.palette.mode === 'light') {\n    return lighten(alpha(theme.palette.divider, 1), 0.88);\n  }\n  return darken(alpha(theme.palette.divider, 1), 0.68);\n}\nfunction setOpacity(color, opacity) {\n  return `rgba(from ${color} r g b / ${opacity})`;\n}\nfunction removeOpacity(color) {\n  return setOpacity(color, 1);\n}\nfunction formatFont(font) {\n  // Accounts for disabled typography variants\n  // See: https://github.com/mui/mui-x/issues/17812\n  if (!font) {\n    return undefined;\n  }\n  return `${font.fontWeight} ${font.fontSize} / ${font.lineHeight} ${font.fontFamily}`;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}