{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { gridColumnDefinitionsSelector } from \"../../hooks/features/columns/gridColumnsSelector.js\";\nimport { useGridSelector } from \"../../hooks/utils/useGridSelector.js\";\nimport { gridPreferencePanelStateSelector } from \"../../hooks/features/preferencesPanel/gridPreferencePanelSelector.js\";\nimport { GridPreferencePanelsValue } from \"../../hooks/features/preferencesPanel/gridPreferencePanelsValue.js\";\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { useGridPanelContext } from \"./GridPanelContext.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function GridPreferencesPanel() {\n  const apiRef = useGridApiContext();\n  const columns = useGridSelector(apiRef, gridColumnDefinitionsSelector);\n  const rootProps = useGridRootProps();\n  const preferencePanelState = useGridSelector(apiRef, gridPreferencePanelStateSelector);\n  const {\n    columnsPanelTriggerRef,\n    filterPanelTriggerRef,\n    aiAssistantPanelTriggerRef\n  } = useGridPanelContext();\n  const panelContent = apiRef.current.unstable_applyPipeProcessors('preferencePanel', null, preferencePanelState.openedPanelValue ?? GridPreferencePanelsValue.filters);\n  let target = null;\n  switch (preferencePanelState.openedPanelValue) {\n    case GridPreferencePanelsValue.filters:\n      target = filterPanelTriggerRef.current;\n      break;\n    case GridPreferencePanelsValue.columns:\n      target = columnsPanelTriggerRef.current;\n      break;\n    case GridPreferencePanelsValue.aiAssistant:\n      target = aiAssistantPanelTriggerRef.current;\n      break;\n    default:\n  }\n  return /*#__PURE__*/_jsx(rootProps.slots.panel, _extends({\n    id: preferencePanelState.panelId,\n    open: columns.length > 0 && preferencePanelState.open,\n    \"aria-labelledby\": preferencePanelState.labelId,\n    target: target,\n    onClose: () => apiRef.current.hidePreferences()\n  }, rootProps.slotProps?.panel, {\n    children: panelContent\n  }));\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}