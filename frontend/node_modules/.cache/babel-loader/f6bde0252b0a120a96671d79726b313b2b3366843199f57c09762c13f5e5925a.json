{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\n/**\n * Resolves the rendering logic for a component.\n * Handles three scenarios:\n * 1. A render function that receives props and state\n * 2. A React element\n * 3. A default element\n *\n * @ignore - internal hook.\n */\nexport function useComponentRenderer(defaultElement, render, props, state = {}) {\n  if (typeof render === 'function') {\n    return render(props, state);\n  }\n  if (render) {\n    if (render.props.className) {\n      props.className = mergeClassNames(render.props.className, props.className);\n    }\n    if (render.props.style || props.style) {\n      props.style = _extends({}, props.style, render.props.style);\n    }\n    return /*#__PURE__*/React.cloneElement(render, props);\n  }\n  return /*#__PURE__*/React.createElement(defaultElement, props);\n}\nfunction mergeClassNames(className, otherClassName) {\n  if (!className || !otherClassName) {\n    return className || otherClassName;\n  }\n  return `${className} ${otherClassName}`;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}