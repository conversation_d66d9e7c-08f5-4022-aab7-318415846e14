{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"open\", \"target\", \"onClose\", \"children\", \"position\", \"className\", \"onExited\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport HTMLElementType from '@mui/utils/HTMLElementType';\nimport { styled } from '@mui/material/styles';\nimport { isHideMenuKey } from \"../../utils/keyboardUtils.js\";\nimport { vars } from \"../../constants/cssVariables.js\";\nimport { useCSSVariablesClass } from \"../../utils/css/context.js\";\nimport { getDataGridUtilityClass, gridClasses } from \"../../constants/gridClasses.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { NotRendered } from \"../../utils/assert.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['menu']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst GridMenuRoot = styled(NotRendered, {\n  name: 'MuiDataGrid',\n  slot: 'Menu'\n})({\n  zIndex: vars.zIndex.menu,\n  [`& .${gridClasses.menuList}`]: {\n    outline: 0\n  }\n});\nfunction GridMenu(props) {\n  const {\n      open,\n      target,\n      onClose,\n      children,\n      position,\n      className,\n      onExited\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const classes = useUtilityClasses(rootProps);\n  const variablesClass = useCSSVariablesClass();\n  const savedFocusRef = React.useRef(null);\n  useEnhancedEffect(() => {\n    if (open) {\n      savedFocusRef.current = document.activeElement instanceof HTMLElement ? document.activeElement : null;\n    } else {\n      savedFocusRef.current?.focus?.();\n      savedFocusRef.current = null;\n    }\n  }, [open]);\n  React.useEffect(() => {\n    // Emit menuOpen or menuClose events\n    const eventName = open ? 'menuOpen' : 'menuClose';\n    apiRef.current.publishEvent(eventName, {\n      target\n    });\n  }, [apiRef, open, target]);\n  const handleClickAway = event => {\n    if (event.target && (target === event.target || target?.contains(event.target))) {\n      return;\n    }\n    onClose(event);\n  };\n  const handleKeyDown = event => {\n    if (isHideMenuKey(event.key)) {\n      onClose(event);\n    }\n  };\n  return /*#__PURE__*/_jsx(GridMenuRoot, _extends({\n    as: rootProps.slots.basePopper,\n    className: clsx(classes.root, className, variablesClass),\n    ownerState: rootProps,\n    open: open,\n    target: target,\n    transition: true,\n    placement: position,\n    onClickAway: handleClickAway,\n    onExited: onExited,\n    clickAwayMouseEvent: \"onMouseDown\",\n    onKeyDown: handleKeyDown\n  }, other, rootProps.slotProps?.basePopper, {\n    children: children\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? GridMenu.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  children: PropTypes.node,\n  className: PropTypes.string,\n  onClose: PropTypes.func.isRequired,\n  onExited: PropTypes.func,\n  open: PropTypes.bool.isRequired,\n  position: PropTypes.oneOf(['bottom-end', 'bottom-start', 'bottom', 'left-end', 'left-start', 'left', 'right-end', 'right-start', 'right', 'top-end', 'top-start', 'top']),\n  target: HTMLElementType\n} : void 0;\nexport { GridMenu };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}