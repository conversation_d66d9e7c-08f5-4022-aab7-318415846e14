{"ast": null, "code": "import * as React from 'react';\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { exportAs } from \"../../../utils/exportAs.js\";\nimport { buildCSV } from \"./serializers/csvSerializer.js\";\nimport { getColumnsToExport, defaultGetRowsToExport } from \"./utils.js\";\nimport { useGridRegisterPipeProcessor } from \"../../core/pipeProcessing/index.js\";\nimport { GridCsvExportMenuItem } from \"../../../components/toolbar/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * @requires useGridColumns (state)\n * @requires useGridFilter (state)\n * @requires useGridSorting (state)\n * @requires useGridSelection (state)\n * @requires useGridParamsApi (method)\n */\nexport const useGridCsvExport = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridCsvExport');\n  const ignoreValueFormatterProp = props.ignoreValueFormatterDuringExport;\n  const ignoreValueFormatter = (typeof ignoreValueFormatterProp === 'object' ? ignoreValueFormatterProp?.csvExport : ignoreValueFormatterProp) || false;\n  const getDataAsCsv = React.useCallback((options = {}) => {\n    logger.debug(`Get data as CSV`);\n    const exportedColumns = getColumnsToExport({\n      apiRef,\n      options\n    });\n    const getRowsToExport = options.getRowsToExport ?? defaultGetRowsToExport;\n    const exportedRowIds = getRowsToExport({\n      apiRef\n    });\n    return buildCSV({\n      columns: exportedColumns,\n      rowIds: exportedRowIds,\n      csvOptions: {\n        delimiter: options.delimiter || ',',\n        shouldAppendQuotes: options.shouldAppendQuotes ?? true,\n        includeHeaders: options.includeHeaders ?? true,\n        includeColumnGroupsHeaders: options.includeColumnGroupsHeaders ?? true,\n        escapeFormulas: options.escapeFormulas ?? true\n      },\n      ignoreValueFormatter,\n      apiRef\n    });\n  }, [logger, apiRef, ignoreValueFormatter]);\n  const exportDataAsCsv = React.useCallback(options => {\n    logger.debug(`Export data as CSV`);\n    const csv = getDataAsCsv(options);\n    const blob = new Blob([options?.utf8WithBom ? new Uint8Array([0xef, 0xbb, 0xbf]) : '', csv], {\n      type: 'text/csv'\n    });\n    exportAs(blob, 'csv', options?.fileName);\n  }, [logger, getDataAsCsv]);\n  const csvExportApi = {\n    getDataAsCsv,\n    exportDataAsCsv\n  };\n  useGridApiMethod(apiRef, csvExportApi, 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const addExportMenuButtons = React.useCallback((initialValue, options) => {\n    if (options.csvOptions?.disableToolbarButton) {\n      return initialValue;\n    }\n    return [...initialValue, {\n      component: /*#__PURE__*/_jsx(GridCsvExportMenuItem, {\n        options: options.csvOptions\n      }),\n      componentName: 'csvExport'\n    }];\n  }, []);\n  useGridRegisterPipeProcessor(apiRef, 'exportMenu', addExportMenuButtons);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}