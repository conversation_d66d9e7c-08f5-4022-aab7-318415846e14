{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/components/TailwindLayout.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Outlet, useNavigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TailwindLayout = () => {\n  _s();\n  var _user$full_name, _filteredMenuItems$fi, _user$full_name2;\n  const {\n    user,\n    logout,\n    hasPermission\n  } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const handleLogout = () => {\n    logout();\n    navigate('/login');\n  };\n  const menuItems = [{\n    title: 'Dashboard',\n    path: '/dashboard',\n    icon: '📊',\n    permission: 'dashboard:access'\n  }, {\n    title: 'Utilisateurs',\n    path: '/users',\n    icon: '👥',\n    permission: 'user:read'\n  }, {\n    title: 'Rô<PERSON>',\n    path: '/roles',\n    icon: '🛡️',\n    permission: 'role:read'\n  }, {\n    title: 'Permissions',\n    path: '/permissions',\n    icon: '🔑',\n    permission: 'permission:read'\n  }];\n  const filteredMenuItems = menuItems.filter(item => hasPermission(item.permission));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"lg:hidden\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between p-4 bg-white border-b border-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-xl font-semibold text-gray-900\",\n          children: \"Gestion Utilisateurs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setSidebarOpen(!sidebarOpen),\n          className: \"p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"sr-only\",\n            children: \"Ouvrir le menu\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this), sidebarOpen ? /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"h-6 w-6\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M6 18L18 6M6 6l12 12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"h-6 w-6\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M4 6h16M4 12h16M4 18h16\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `\n          fixed inset-y-0 left-0 z-50 w-64 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0\n          ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}\n        `,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col h-full\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center h-16 px-4 bg-primary-600\",\n            children: /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-xl font-bold text-white\",\n              children: \"Admin Panel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n            className: \"flex-1 px-4 py-6 space-y-2\",\n            children: filteredMenuItems.map(item => {\n              const isActive = location.pathname === item.path;\n              return /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  navigate(item.path);\n                  setSidebarOpen(false);\n                },\n                className: `\n                      w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200\n                      ${isActive ? 'bg-primary-100 text-primary-700 border-r-2 border-primary-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'}\n                    `,\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-lg mr-3\",\n                  children: item.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 107,\n                  columnNumber: 21\n                }, this), item.title]\n              }, item.path, true, {\n                fileName: _jsxFileName,\n                lineNumber: 93,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 border-t border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white text-sm font-medium\",\n                  children: (user === null || user === void 0 ? void 0 : (_user$full_name = user.full_name) === null || _user$full_name === void 0 ? void 0 : _user$full_name.charAt(0)) || 'U'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 118,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1 min-w-0\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-900 truncate\",\n                  children: user === null || user === void 0 ? void 0 : user.full_name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 123,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500 truncate\",\n                  children: user === null || user === void 0 ? void 0 : user.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 126,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleLogout,\n                className: \"p-1 text-gray-400 hover:text-gray-500 rounded-md hover:bg-gray-100\",\n                title: \"D\\xE9connexion\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-4 h-4\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 136,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this), sidebarOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\",\n        onClick: () => setSidebarOpen(false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 lg:ml-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden lg:block\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white border-b border-gray-200 px-6 py-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-2xl font-semibold text-gray-900\",\n                  children: ((_filteredMenuItems$fi = filteredMenuItems.find(item => item.path === location.pathname)) === null || _filteredMenuItems$fi === void 0 ? void 0 : _filteredMenuItems$fi.title) || 'Dashboard'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-500 mt-1\",\n                  children: [\"Bienvenue, \", user === null || user === void 0 ? void 0 : user.full_name]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white font-medium\",\n                    children: (user === null || user === void 0 ? void 0 : (_user$full_name2 = user.full_name) === null || _user$full_name2 === void 0 ? void 0 : _user$full_name2.charAt(0)) || 'U'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 169,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-right\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-medium text-gray-700\",\n                    children: user === null || user === void 0 ? void 0 : user.full_name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 174,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500\",\n                    children: user === null || user === void 0 ? void 0 : user.email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 175,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleLogout,\n                  className: \"p-2 text-gray-400 hover:text-gray-500 rounded-md hover:bg-gray-100\",\n                  title: \"D\\xE9connexion\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-5 h-5\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 183,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 182,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n          className: \"p-6\",\n          children: /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 5\n  }, this);\n};\n_s(TailwindLayout, \"/wLYle2orWEQahPnKwc4pu7MfsM=\", false, function () {\n  return [useAuth, useNavigate, useLocation];\n});\n_c = TailwindLayout;\nexport default TailwindLayout;\nvar _c;\n$RefreshReg$(_c, \"TailwindLayout\");", "map": {"version": 3, "names": ["React", "useState", "Outlet", "useNavigate", "useLocation", "useAuth", "jsxDEV", "_jsxDEV", "TailwindLayout", "_s", "_user$full_name", "_filteredMenuItems$fi", "_user$full_name2", "user", "logout", "hasPermission", "navigate", "location", "sidebarOpen", "setSidebarOpen", "handleLogout", "menuItems", "title", "path", "icon", "permission", "filteredMenuItems", "filter", "item", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "map", "isActive", "pathname", "full_name", "char<PERSON>t", "email", "find", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/components/TailwindLayout.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Outlet, useNavigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst TailwindLayout: React.FC = () => {\n  const { user, logout, hasPermission } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  const handleLogout = () => {\n    logout();\n    navigate('/login');\n  };\n\n  const menuItems = [\n    {\n      title: 'Dashboard',\n      path: '/dashboard',\n      icon: '📊',\n      permission: 'dashboard:access',\n    },\n    {\n      title: 'Utilisateurs',\n      path: '/users',\n      icon: '👥',\n      permission: 'user:read',\n    },\n    {\n      title: 'Rôles',\n      path: '/roles',\n      icon: '🛡️',\n      permission: 'role:read',\n    },\n    {\n      title: 'Permissions',\n      path: '/permissions',\n      icon: '🔑',\n      permission: 'permission:read',\n    },\n  ];\n\n  const filteredMenuItems = menuItems.filter(item => \n    hasPermission(item.permission)\n  );\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Mobile menu button */}\n      <div className=\"lg:hidden\">\n        <div className=\"flex items-center justify-between p-4 bg-white border-b border-gray-200\">\n          <h1 className=\"text-xl font-semibold text-gray-900\">\n            Gestion Utilisateurs\n          </h1>\n          <button\n            onClick={() => setSidebarOpen(!sidebarOpen)}\n            className=\"p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500\"\n          >\n            <span className=\"sr-only\">Ouvrir le menu</span>\n            {sidebarOpen ? (\n              <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            ) : (\n              <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n              </svg>\n            )}\n          </button>\n        </div>\n      </div>\n\n      <div className=\"flex\">\n        {/* Sidebar */}\n        <div className={`\n          fixed inset-y-0 left-0 z-50 w-64 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0\n          ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}\n        `}>\n          <div className=\"flex flex-col h-full\">\n            {/* Logo */}\n            <div className=\"flex items-center justify-center h-16 px-4 bg-primary-600\">\n              <h1 className=\"text-xl font-bold text-white\">\n                Admin Panel\n              </h1>\n            </div>\n\n            {/* Navigation */}\n            <nav className=\"flex-1 px-4 py-6 space-y-2\">\n              {filteredMenuItems.map((item) => {\n                const isActive = location.pathname === item.path;\n                \n                return (\n                  <button\n                    key={item.path}\n                    onClick={() => {\n                      navigate(item.path);\n                      setSidebarOpen(false);\n                    }}\n                    className={`\n                      w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200\n                      ${isActive \n                        ? 'bg-primary-100 text-primary-700 border-r-2 border-primary-600' \n                        : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'\n                      }\n                    `}\n                  >\n                    <span className=\"text-lg mr-3\">{item.icon}</span>\n                    {item.title}\n                  </button>\n                );\n              })}\n            </nav>\n\n            {/* User section */}\n            <div className=\"p-4 border-t border-gray-200\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center\">\n                  <span className=\"text-white text-sm font-medium\">\n                    {user?.full_name?.charAt(0) || 'U'}\n                  </span>\n                </div>\n                <div className=\"flex-1 min-w-0\">\n                  <p className=\"text-sm font-medium text-gray-900 truncate\">\n                    {user?.full_name}\n                  </p>\n                  <p className=\"text-xs text-gray-500 truncate\">\n                    {user?.email}\n                  </p>\n                </div>\n                <button\n                  onClick={handleLogout}\n                  className=\"p-1 text-gray-400 hover:text-gray-500 rounded-md hover:bg-gray-100\"\n                  title=\"Déconnexion\"\n                >\n                  <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\n                  </svg>\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Overlay for mobile */}\n        {sidebarOpen && (\n          <div\n            className=\"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\"\n            onClick={() => setSidebarOpen(false)}\n          />\n        )}\n\n        {/* Main content */}\n        <div className=\"flex-1 lg:ml-0\">\n          {/* Top bar for desktop */}\n          <div className=\"hidden lg:block\">\n            <div className=\"bg-white border-b border-gray-200 px-6 py-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <h1 className=\"text-2xl font-semibold text-gray-900\">\n                    {filteredMenuItems.find(item => item.path === location.pathname)?.title || 'Dashboard'}\n                  </h1>\n                  <p className=\"text-sm text-gray-500 mt-1\">\n                    Bienvenue, {user?.full_name}\n                  </p>\n                </div>\n                \n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white font-medium\">\n                      {user?.full_name?.charAt(0) || 'U'}\n                    </span>\n                  </div>\n                  <div className=\"text-right\">\n                    <p className=\"text-sm font-medium text-gray-700\">{user?.full_name}</p>\n                    <p className=\"text-xs text-gray-500\">{user?.email}</p>\n                  </div>\n                  <button\n                    onClick={handleLogout}\n                    className=\"p-2 text-gray-400 hover:text-gray-500 rounded-md hover:bg-gray-100\"\n                    title=\"Déconnexion\"\n                  >\n                    <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\n                    </svg>\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Page content */}\n          <main className=\"p-6\">\n            <Outlet />\n          </main>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TailwindLayout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACnE,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,qBAAA,EAAAC,gBAAA;EACrC,MAAM;IAAEC,IAAI;IAAEC,MAAM;IAAEC;EAAc,CAAC,GAAGV,OAAO,CAAC,CAAC;EACjD,MAAMW,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAMc,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACc,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAMmB,YAAY,GAAGA,CAAA,KAAM;IACzBN,MAAM,CAAC,CAAC;IACRE,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAMK,SAAS,GAAG,CAChB;IACEC,KAAK,EAAE,WAAW;IAClBC,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,IAAI;IACVC,UAAU,EAAE;EACd,CAAC,EACD;IACEH,KAAK,EAAE,cAAc;IACrBC,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,IAAI;IACVC,UAAU,EAAE;EACd,CAAC,EACD;IACEH,KAAK,EAAE,OAAO;IACdC,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,KAAK;IACXC,UAAU,EAAE;EACd,CAAC,EACD;IACEH,KAAK,EAAE,aAAa;IACpBC,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,IAAI;IACVC,UAAU,EAAE;EACd,CAAC,CACF;EAED,MAAMC,iBAAiB,GAAGL,SAAS,CAACM,MAAM,CAACC,IAAI,IAC7Cb,aAAa,CAACa,IAAI,CAACH,UAAU,CAC/B,CAAC;EAED,oBACElB,OAAA;IAAKsB,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBAEtCvB,OAAA;MAAKsB,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBvB,OAAA;QAAKsB,SAAS,EAAC,yEAAyE;QAAAC,QAAA,gBACtFvB,OAAA;UAAIsB,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAEpD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL3B,OAAA;UACE4B,OAAO,EAAEA,CAAA,KAAMhB,cAAc,CAAC,CAACD,WAAW,CAAE;UAC5CW,SAAS,EAAC,4IAA4I;UAAAC,QAAA,gBAEtJvB,OAAA;YAAMsB,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EAC9ChB,WAAW,gBACVX,OAAA;YAAKsB,SAAS,EAAC,SAAS;YAACO,IAAI,EAAC,MAAM;YAACC,OAAO,EAAC,WAAW;YAACC,MAAM,EAAC,cAAc;YAAAR,QAAA,eAC5EvB,OAAA;cAAMgC,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAsB;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F,CAAC,gBAEN3B,OAAA;YAAKsB,SAAS,EAAC,SAAS;YAACO,IAAI,EAAC,MAAM;YAACC,OAAO,EAAC,WAAW;YAACC,MAAM,EAAC,cAAc;YAAAR,QAAA,eAC5EvB,OAAA;cAAMgC,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAyB;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9F,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN3B,OAAA;MAAKsB,SAAS,EAAC,MAAM;MAAAC,QAAA,gBAEnBvB,OAAA;QAAKsB,SAAS,EAAE;AACxB;AACA,YAAYX,WAAW,GAAG,eAAe,GAAG,mBAAmB;AAC/D,SAAU;QAAAY,QAAA,eACAvB,OAAA;UAAKsB,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBAEnCvB,OAAA;YAAKsB,SAAS,EAAC,2DAA2D;YAAAC,QAAA,eACxEvB,OAAA;cAAIsB,SAAS,EAAC,8BAA8B;cAAAC,QAAA,EAAC;YAE7C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGN3B,OAAA;YAAKsB,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EACxCJ,iBAAiB,CAACiB,GAAG,CAAEf,IAAI,IAAK;cAC/B,MAAMgB,QAAQ,GAAG3B,QAAQ,CAAC4B,QAAQ,KAAKjB,IAAI,CAACL,IAAI;cAEhD,oBACEhB,OAAA;gBAEE4B,OAAO,EAAEA,CAAA,KAAM;kBACbnB,QAAQ,CAACY,IAAI,CAACL,IAAI,CAAC;kBACnBJ,cAAc,CAAC,KAAK,CAAC;gBACvB,CAAE;gBACFU,SAAS,EAAE;AAC/B;AACA,wBAAwBe,QAAQ,GACN,+DAA+D,GAC/D,qDAAqD;AAC/E,qBACsB;gBAAAd,QAAA,gBAEFvB,OAAA;kBAAMsB,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAEF,IAAI,CAACJ;gBAAI;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EAChDN,IAAI,CAACN,KAAK;cAAA,GAdNM,IAAI,CAACL,IAAI;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAeR,CAAC;YAEb,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN3B,OAAA;YAAKsB,SAAS,EAAC,8BAA8B;YAAAC,QAAA,eAC3CvB,OAAA;cAAKsB,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CvB,OAAA;gBAAKsB,SAAS,EAAC,sEAAsE;gBAAAC,QAAA,eACnFvB,OAAA;kBAAMsB,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAC7C,CAAAjB,IAAI,aAAJA,IAAI,wBAAAH,eAAA,GAAJG,IAAI,CAAEiC,SAAS,cAAApC,eAAA,uBAAfA,eAAA,CAAiBqC,MAAM,CAAC,CAAC,CAAC,KAAI;gBAAG;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACN3B,OAAA;gBAAKsB,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BvB,OAAA;kBAAGsB,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EACtDjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiC;gBAAS;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eACJ3B,OAAA;kBAAGsB,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAC1CjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmC;gBAAK;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACN3B,OAAA;gBACE4B,OAAO,EAAEf,YAAa;gBACtBS,SAAS,EAAC,oEAAoE;gBAC9EP,KAAK,EAAC,gBAAa;gBAAAQ,QAAA,eAEnBvB,OAAA;kBAAKsB,SAAS,EAAC,SAAS;kBAACO,IAAI,EAAC,MAAM;kBAACE,MAAM,EAAC,cAAc;kBAACD,OAAO,EAAC,WAAW;kBAAAP,QAAA,eAC5EvB,OAAA;oBAAMgC,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAA2F;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLhB,WAAW,iBACVX,OAAA;QACEsB,SAAS,EAAC,qDAAqD;QAC/DM,OAAO,EAAEA,CAAA,KAAMhB,cAAc,CAAC,KAAK;MAAE;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CACF,eAGD3B,OAAA;QAAKsB,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAE7BvB,OAAA;UAAKsB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BvB,OAAA;YAAKsB,SAAS,EAAC,6CAA6C;YAAAC,QAAA,eAC1DvB,OAAA;cAAKsB,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDvB,OAAA;gBAAAuB,QAAA,gBACEvB,OAAA;kBAAIsB,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,EACjD,EAAAnB,qBAAA,GAAAe,iBAAiB,CAACuB,IAAI,CAACrB,IAAI,IAAIA,IAAI,CAACL,IAAI,KAAKN,QAAQ,CAAC4B,QAAQ,CAAC,cAAAlC,qBAAA,uBAA/DA,qBAAA,CAAiEW,KAAK,KAAI;gBAAW;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpF,CAAC,eACL3B,OAAA;kBAAGsB,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,GAAC,aAC7B,EAACjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiC,SAAS;gBAAA;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAEN3B,OAAA;gBAAKsB,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CvB,OAAA;kBAAKsB,SAAS,EAAC,wEAAwE;kBAAAC,QAAA,eACrFvB,OAAA;oBAAMsB,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,EACrC,CAAAjB,IAAI,aAAJA,IAAI,wBAAAD,gBAAA,GAAJC,IAAI,CAAEiC,SAAS,cAAAlC,gBAAA,uBAAfA,gBAAA,CAAiBmC,MAAM,CAAC,CAAC,CAAC,KAAI;kBAAG;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACN3B,OAAA;kBAAKsB,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBvB,OAAA;oBAAGsB,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAEjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiC;kBAAS;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtE3B,OAAA;oBAAGsB,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAEjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmC;kBAAK;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC,eACN3B,OAAA;kBACE4B,OAAO,EAAEf,YAAa;kBACtBS,SAAS,EAAC,oEAAoE;kBAC9EP,KAAK,EAAC,gBAAa;kBAAAQ,QAAA,eAEnBvB,OAAA;oBAAKsB,SAAS,EAAC,SAAS;oBAACO,IAAI,EAAC,MAAM;oBAACE,MAAM,EAAC,cAAc;oBAACD,OAAO,EAAC,WAAW;oBAAAP,QAAA,eAC5EvB,OAAA;sBAAMgC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAA2F;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN3B,OAAA;UAAMsB,SAAS,EAAC,KAAK;UAAAC,QAAA,eACnBvB,OAAA,CAACL,MAAM;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzB,EAAA,CAlMID,cAAwB;EAAA,QACYH,OAAO,EAC9BF,WAAW,EACXC,WAAW;AAAA;AAAA8C,EAAA,GAHxB1C,cAAwB;AAoM9B,eAAeA,cAAc;AAAC,IAAA0C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}