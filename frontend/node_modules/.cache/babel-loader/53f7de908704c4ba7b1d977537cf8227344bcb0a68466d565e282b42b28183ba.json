{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { gridRowNodeSelector } from \"./gridRowsSelector.js\";\nexport const GRID_ROOT_GROUP_ID = `auto-generated-group-node-root`;\nexport const GRID_ID_AUTOGENERATED = Symbol('mui.id_autogenerated');\nexport const buildRootGroup = () => ({\n  type: 'group',\n  id: GRID_ROOT_GROUP_ID,\n  depth: -1,\n  groupingField: null,\n  groupingKey: null,\n  isAutoGenerated: true,\n  children: [],\n  childrenFromPath: {},\n  childrenExpanded: true,\n  parent: null\n});\n\n/**\n * A helper function to check if the id provided is valid.\n * @param {GridRowId} id Id as [[GridRowId]].\n * @param {GridRowModel | Partial<GridRowModel>} row Row as [[GridRowModel]].\n * @param {string} detailErrorMessage A custom error message to display for invalid IDs\n */\nexport function checkGridRowIdIsValid(id, row, detailErrorMessage = 'A row was provided without id in the rows prop:') {\n  if (id == null) {\n    throw new Error(['MUI X: The Data Grid component requires all rows to have a unique `id` property.', 'Alternatively, you can use the `getRowId` prop to specify a custom id for each row.', detailErrorMessage, JSON.stringify(row)].join('\\n'));\n  }\n}\nexport const getRowIdFromRowModel = (rowModel, getRowId, detailErrorMessage) => {\n  const id = getRowId ? getRowId(rowModel) : rowModel.id;\n  checkGridRowIdIsValid(id, rowModel, detailErrorMessage);\n  return id;\n};\nexport const getRowValue = (row, colDef, apiRef) => {\n  const field = colDef.field;\n  if (!colDef || !colDef.valueGetter) {\n    return row[field];\n  }\n  const value = row[colDef.field];\n  return colDef.valueGetter(value, row, colDef, apiRef);\n};\nexport const createRowsInternalCache = ({\n  rows,\n  getRowId,\n  loading,\n  rowCount\n}) => {\n  const updates = {\n    type: 'full',\n    rows: []\n  };\n  const dataRowIdToModelLookup = {};\n  for (let i = 0; i < rows.length; i += 1) {\n    const model = rows[i];\n    const id = getRowIdFromRowModel(model, getRowId);\n    dataRowIdToModelLookup[id] = model;\n    updates.rows.push(id);\n  }\n  return {\n    rowsBeforePartialUpdates: rows,\n    loadingPropBeforePartialUpdates: loading,\n    rowCountPropBeforePartialUpdates: rowCount,\n    updates,\n    dataRowIdToModelLookup\n  };\n};\nexport const getTopLevelRowCount = ({\n  tree,\n  rowCountProp = 0\n}) => {\n  const rootGroupNode = tree[GRID_ROOT_GROUP_ID];\n  return Math.max(rowCountProp, rootGroupNode.children.length + (rootGroupNode.footerId == null ? 0 : 1));\n};\nexport const getRowsStateFromCache = ({\n  apiRef,\n  rowCountProp = 0,\n  loadingProp,\n  previousTree,\n  previousTreeDepths,\n  previousGroupsToFetch\n}) => {\n  const cache = apiRef.current.caches.rows;\n\n  // 1. Apply the \"rowTreeCreation\" family processing.\n  const {\n    tree: unProcessedTree,\n    treeDepths: unProcessedTreeDepths,\n    dataRowIds: unProcessedDataRowIds,\n    groupingName,\n    groupsToFetch = []\n  } = apiRef.current.applyStrategyProcessor('rowTreeCreation', {\n    previousTree,\n    previousTreeDepths,\n    updates: cache.updates,\n    dataRowIdToModelLookup: cache.dataRowIdToModelLookup,\n    previousGroupsToFetch\n  });\n\n  // 2. Apply the \"hydrateRows\" pipe-processing.\n  const groupingParamsWithHydrateRows = apiRef.current.unstable_applyPipeProcessors('hydrateRows', {\n    tree: unProcessedTree,\n    treeDepths: unProcessedTreeDepths,\n    dataRowIds: unProcessedDataRowIds,\n    dataRowIdToModelLookup: cache.dataRowIdToModelLookup\n  });\n\n  // 3. Reset the cache updates\n  apiRef.current.caches.rows.updates = {\n    type: 'partial',\n    actions: {\n      insert: [],\n      modify: [],\n      remove: []\n    },\n    idToActionLookup: {}\n  };\n  return _extends({}, groupingParamsWithHydrateRows, {\n    totalRowCount: Math.max(rowCountProp, groupingParamsWithHydrateRows.dataRowIds.length),\n    totalTopLevelRowCount: getTopLevelRowCount({\n      tree: groupingParamsWithHydrateRows.tree,\n      rowCountProp\n    }),\n    groupingName,\n    loading: loadingProp,\n    groupsToFetch\n  });\n};\nexport const isAutogeneratedRow = row => GRID_ID_AUTOGENERATED in row;\nexport const isAutogeneratedRowNode = rowNode => rowNode.type === 'skeletonRow' || rowNode.type === 'footer' || rowNode.type === 'group' && rowNode.isAutoGenerated || rowNode.type === 'pinnedRow' && rowNode.isAutoGenerated;\nexport const getTreeNodeDescendants = (tree, parentId, skipAutoGeneratedRows) => {\n  const node = tree[parentId];\n  if (node.type !== 'group') {\n    return [];\n  }\n  const validDescendants = [];\n  for (let i = 0; i < node.children.length; i += 1) {\n    const child = node.children[i];\n    if (!skipAutoGeneratedRows || !isAutogeneratedRowNode(tree[child])) {\n      validDescendants.push(child);\n    }\n    const childDescendants = getTreeNodeDescendants(tree, child, skipAutoGeneratedRows);\n    for (let j = 0; j < childDescendants.length; j += 1) {\n      validDescendants.push(childDescendants[j]);\n    }\n  }\n  if (!skipAutoGeneratedRows && node.footerId != null) {\n    validDescendants.push(node.footerId);\n  }\n  return validDescendants;\n};\nexport const updateCacheWithNewRows = ({\n  previousCache,\n  getRowId,\n  updates,\n  groupKeys\n}) => {\n  if (previousCache.updates.type === 'full') {\n    throw new Error('MUI X: Unable to prepare a partial update if a full update is not applied yet.');\n  }\n\n  // Remove duplicate updates.\n  // A server can batch updates, and send several updates for the same row in one fn call.\n  const uniqueUpdates = new Map();\n  updates.forEach(update => {\n    const id = getRowIdFromRowModel(update, getRowId, 'A row was provided without id when calling updateRows():');\n    if (uniqueUpdates.has(id)) {\n      uniqueUpdates.set(id, _extends({}, uniqueUpdates.get(id), update));\n    } else {\n      uniqueUpdates.set(id, update);\n    }\n  });\n  const partialUpdates = {\n    type: 'partial',\n    actions: {\n      insert: [...(previousCache.updates.actions.insert ?? [])],\n      modify: [...(previousCache.updates.actions.modify ?? [])],\n      remove: [...(previousCache.updates.actions.remove ?? [])]\n    },\n    idToActionLookup: _extends({}, previousCache.updates.idToActionLookup),\n    groupKeys\n  };\n  const dataRowIdToModelLookup = _extends({}, previousCache.dataRowIdToModelLookup);\n  const alreadyAppliedActionsToRemove = {\n    insert: {},\n    modify: {},\n    remove: {}\n  };\n\n  // Depending on the action already applied to the data row,\n  // We might want drop the already-applied-update.\n  // For instance:\n  // - if you delete then insert, then you don't want to apply the deletion in the tree.\n  // - if you insert, then modify, then you just want to apply the insertion in the tree.\n  uniqueUpdates.forEach((partialRow, id) => {\n    const actionAlreadyAppliedToRow = partialUpdates.idToActionLookup[id];\n\n    // Action === \"delete\"\n    // eslint-disable-next-line no-underscore-dangle\n    if (partialRow._action === 'delete') {\n      // If the data row has been removed since the last state update,\n      // Then do nothing.\n      if (actionAlreadyAppliedToRow === 'remove' || !dataRowIdToModelLookup[id]) {\n        return;\n      }\n\n      // If the data row has been inserted / modified since the last state update,\n      // Then drop this \"insert\" / \"modify\" update.\n      if (actionAlreadyAppliedToRow != null) {\n        alreadyAppliedActionsToRemove[actionAlreadyAppliedToRow][id] = true;\n      }\n\n      // Remove the data row from the lookups and add it to the \"delete\" update.\n      partialUpdates.actions.remove.push(id);\n      delete dataRowIdToModelLookup[id];\n      return;\n    }\n    const oldRow = dataRowIdToModelLookup[id];\n\n    // Action === \"modify\"\n    if (oldRow) {\n      // If the data row has been removed since the last state update,\n      // Then drop this \"remove\" update and add it to the \"modify\" update instead.\n      if (actionAlreadyAppliedToRow === 'remove') {\n        alreadyAppliedActionsToRemove.remove[id] = true;\n        partialUpdates.actions.modify.push(id);\n      }\n      // If the date has not been inserted / modified since the last state update,\n      // Then add it to the \"modify\" update (if it has been inserted it should just remain \"inserted\").\n      else if (actionAlreadyAppliedToRow == null) {\n        partialUpdates.actions.modify.push(id);\n      }\n\n      // Update the data row lookups.\n      dataRowIdToModelLookup[id] = _extends({}, oldRow, partialRow);\n      return;\n    }\n\n    // Action === \"insert\"\n    // If the data row has been removed since the last state update,\n    // Then drop the \"remove\" update and add it to the \"insert\" update instead.\n    if (actionAlreadyAppliedToRow === 'remove') {\n      alreadyAppliedActionsToRemove.remove[id] = true;\n      partialUpdates.actions.insert.push(id);\n    }\n    // If the data row has not been inserted since the last state update,\n    // Then add it to the \"insert\" update.\n    // `actionAlreadyAppliedToRow` can't be equal to \"modify\", otherwise we would have an `oldRow` above.\n    else if (actionAlreadyAppliedToRow == null) {\n      partialUpdates.actions.insert.push(id);\n    }\n\n    // Update the data row lookups.\n    dataRowIdToModelLookup[id] = partialRow;\n  });\n  const actionTypeWithActionsToRemove = Object.keys(alreadyAppliedActionsToRemove);\n  for (let i = 0; i < actionTypeWithActionsToRemove.length; i += 1) {\n    const actionType = actionTypeWithActionsToRemove[i];\n    const idsToRemove = alreadyAppliedActionsToRemove[actionType];\n    if (Object.keys(idsToRemove).length > 0) {\n      partialUpdates.actions[actionType] = partialUpdates.actions[actionType].filter(id => !idsToRemove[id]);\n    }\n  }\n  return {\n    dataRowIdToModelLookup,\n    updates: partialUpdates,\n    rowsBeforePartialUpdates: previousCache.rowsBeforePartialUpdates,\n    loadingPropBeforePartialUpdates: previousCache.loadingPropBeforePartialUpdates,\n    rowCountPropBeforePartialUpdates: previousCache.rowCountPropBeforePartialUpdates\n  };\n};\nexport const minimalContentHeight = 'var(--DataGrid-overlayHeight, calc(var(--height) * 2))';\nexport function computeRowsUpdates(apiRef, updates, getRowId) {\n  const nonPinnedRowsUpdates = [];\n  updates.forEach(update => {\n    const id = getRowIdFromRowModel(update, getRowId, 'A row was provided without id when calling updateRows():');\n    const rowNode = gridRowNodeSelector(apiRef, id);\n    if (rowNode?.type === 'pinnedRow') {\n      // @ts-ignore because otherwise `release:build` doesn't work\n      const pinnedRowsCache = apiRef.current.caches.pinnedRows;\n      const prevModel = pinnedRowsCache.idLookup[id];\n      if (prevModel) {\n        pinnedRowsCache.idLookup[id] = _extends({}, prevModel, update);\n      }\n    } else {\n      nonPinnedRowsUpdates.push(update);\n    }\n  });\n  return nonPinnedRowsUpdates;\n}\nlet warnedOnceInvalidRowHeight = false;\nexport const getValidRowHeight = (rowHeightProp, defaultRowHeight, warningMessage) => {\n  if (typeof rowHeightProp === 'number' && rowHeightProp > 0) {\n    return rowHeightProp;\n  }\n  if (process.env.NODE_ENV !== 'production' && !warnedOnceInvalidRowHeight && typeof rowHeightProp !== 'undefined' && rowHeightProp !== null) {\n    console.warn(warningMessage);\n    warnedOnceInvalidRowHeight = true;\n  }\n  return defaultRowHeight;\n};\nexport const rowHeightWarning = [`MUI X: The \\`rowHeight\\` prop should be a number greater than 0.`, `The default value will be used instead.`].join('\\n');\nexport const getRowHeightWarning = [`MUI X: The \\`getRowHeight\\` prop should return a number greater than 0 or 'auto'.`, `The default value will be used instead.`].join('\\n');", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}