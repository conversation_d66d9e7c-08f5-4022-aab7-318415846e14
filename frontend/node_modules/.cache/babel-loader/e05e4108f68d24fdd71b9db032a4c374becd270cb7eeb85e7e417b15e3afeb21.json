{"ast": null, "code": "import { createSelector, createRootSelector } from \"../../../utils/createSelector.js\";\nexport const COMPACT_DENSITY_FACTOR = 0.7;\nexport const COMFORTABLE_DENSITY_FACTOR = 1.3;\nconst DENSITY_FACTORS = {\n  compact: COMPACT_DENSITY_FACTOR,\n  comfortable: COMFORTABLE_DENSITY_FACTOR,\n  standard: 1\n};\nexport const gridDensitySelector = createRootSelector(state => state.density);\nexport const gridDensityFactorSelector = createSelector(gridDensitySelector, density => DENSITY_FACTORS[density]);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}