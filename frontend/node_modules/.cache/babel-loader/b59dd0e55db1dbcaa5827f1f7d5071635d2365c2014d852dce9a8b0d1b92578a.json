{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { GridSignature } from \"../../../constants/signature.js\";\nimport { useGridEvent } from \"../../utils/useGridEvent.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { useGridSelector } from \"../../utils/useGridSelector.js\";\nimport { gridRowsLookupSelector, gridRowMaximumTreeDepthSelector, gridRowNodeSelector, gridRowTreeSelector } from \"../rows/gridRowsSelector.js\";\nimport { gridRowSelectionManagerSelector, gridRowSelectionStateSelector, gridRowSelectionCountSelector, gridRowSelectionIdsSelector } from \"./gridRowSelectionSelector.js\";\nimport { gridFocusCellSelector } from \"../focus/gridFocusStateSelector.js\";\nimport { gridExpandedSortedRowIdsSelector, gridFilteredRowsLookupSelector, gridFilterModelSelector, gridQuickFilterValuesSelector } from \"../filter/gridFilterSelector.js\";\nimport { GRID_CHECKBOX_SELECTION_COL_DEF, GRID_ACTIONS_COLUMN_TYPE } from \"../../../colDef/index.js\";\nimport { GridCellModes } from \"../../../models/gridEditRowModel.js\";\nimport { isKeyboardEvent, isNavigationKey } from \"../../../utils/keyboardUtils.js\";\nimport { getVisibleRows } from \"../../utils/useGridVisibleRows.js\";\nimport { GRID_DETAIL_PANEL_TOGGLE_FIELD } from \"../../../internals/constants.js\";\nimport { gridClasses } from \"../../../constants/gridClasses.js\";\nimport { isEventTargetInPortal } from \"../../../utils/domUtils.js\";\nimport { isMultipleRowSelectionEnabled, findRowsToSelect, findRowsToDeselect } from \"./utils.js\";\nimport { createRowSelectionManager } from \"../../../models/gridRowSelectionManager.js\";\nimport { gridPaginatedVisibleSortedGridRowIdsSelector } from \"../pagination/index.js\";\nconst emptyModel = {\n  type: 'include',\n  ids: new Set()\n};\nexport const rowSelectionStateInitializer = (state, props) => _extends({}, state, {\n  rowSelection: props.rowSelection ? props.rowSelectionModel ?? emptyModel : emptyModel\n});\n\n/**\n * @requires useGridRows (state, method) - can be after\n * @requires useGridParamsApi (method) - can be after\n * @requires useGridFocus (state) - can be after\n * @requires useGridKeyboardNavigation (`cellKeyDown` event must first be consumed by it)\n */\nexport const useGridRowSelection = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridSelection');\n  const runIfRowSelectionIsEnabled = React.useCallback(callback => (...args) => {\n    if (props.rowSelection) {\n      callback(...args);\n    }\n  }, [props.rowSelection]);\n  const applyAutoSelection = props.signature !== GridSignature.DataGrid && (props.rowSelectionPropagation?.parents || props.rowSelectionPropagation?.descendants);\n  const propRowSelectionModel = React.useMemo(() => {\n    return props.rowSelectionModel;\n  }, [props.rowSelectionModel]);\n  const lastRowToggled = React.useRef(null);\n  apiRef.current.registerControlState({\n    stateId: 'rowSelection',\n    propModel: propRowSelectionModel,\n    propOnChange: props.onRowSelectionModelChange,\n    stateSelector: gridRowSelectionStateSelector,\n    changeEvent: 'rowSelectionChange'\n  });\n  const {\n    checkboxSelection,\n    disableRowSelectionOnClick,\n    isRowSelectable: propIsRowSelectable\n  } = props;\n  const canHaveMultipleSelection = isMultipleRowSelectionEnabled(props);\n  const tree = useGridSelector(apiRef, gridRowTreeSelector);\n  const isNestedData = useGridSelector(apiRef, gridRowMaximumTreeDepthSelector) > 1;\n  const expandMouseRowRangeSelection = React.useCallback(id => {\n    let endId = id;\n    const startId = lastRowToggled.current ?? id;\n    const isSelected = apiRef.current.isRowSelected(id);\n    if (isSelected) {\n      const visibleRowIds = gridExpandedSortedRowIdsSelector(apiRef);\n      const startIndex = visibleRowIds.findIndex(rowId => rowId === startId);\n      const endIndex = visibleRowIds.findIndex(rowId => rowId === endId);\n      if (startIndex === endIndex) {\n        return;\n      }\n      if (startIndex > endIndex) {\n        endId = visibleRowIds[endIndex + 1];\n      } else {\n        endId = visibleRowIds[endIndex - 1];\n      }\n    }\n    lastRowToggled.current = id;\n    apiRef.current.selectRowRange({\n      startId,\n      endId\n    }, !isSelected);\n  }, [apiRef]);\n  const getRowsToBeSelected = useEventCallback(() => {\n    const rowsToBeSelected = props.pagination && props.checkboxSelectionVisibleOnly && props.paginationMode === 'client' ? gridPaginatedVisibleSortedGridRowIdsSelector(apiRef) : gridExpandedSortedRowIdsSelector(apiRef);\n    return rowsToBeSelected;\n  });\n\n  /*\n   * API METHODS\n   */\n  const setRowSelectionModel = React.useCallback((model, reason) => {\n    if (props.signature === GridSignature.DataGrid && !canHaveMultipleSelection && (model.type !== 'include' || model.ids.size > 1)) {\n      throw new Error(['MUI X: `rowSelectionModel` can only contain 1 item in DataGrid.', 'You need to upgrade to DataGridPro or DataGridPremium component to unlock multiple selection.'].join('\\n'));\n    }\n    const currentModel = gridRowSelectionStateSelector(apiRef);\n    if (currentModel !== model) {\n      logger.debug(`Setting selection model`);\n      apiRef.current.setState(state => _extends({}, state, {\n        rowSelection: props.rowSelection ? model : emptyModel\n      }), reason);\n    }\n  }, [apiRef, logger, props.rowSelection, props.signature, canHaveMultipleSelection]);\n  const isRowSelected = React.useCallback(id => {\n    const selectionManager = gridRowSelectionManagerSelector(apiRef);\n    return selectionManager.has(id);\n  }, [apiRef]);\n  const isRowSelectable = React.useCallback(id => {\n    if (props.rowSelection === false) {\n      return false;\n    }\n    if (propIsRowSelectable && !propIsRowSelectable(apiRef.current.getRowParams(id))) {\n      return false;\n    }\n    const rowNode = gridRowNodeSelector(apiRef, id);\n    if (rowNode?.type === 'footer' || rowNode?.type === 'pinnedRow') {\n      return false;\n    }\n    return true;\n  }, [apiRef, props.rowSelection, propIsRowSelectable]);\n  const getSelectedRows = React.useCallback(() => gridRowSelectionIdsSelector(apiRef), [apiRef]);\n  const selectRow = React.useCallback((id, isSelected = true, resetSelection = false) => {\n    if (!apiRef.current.isRowSelectable(id)) {\n      return;\n    }\n    lastRowToggled.current = id;\n    if (resetSelection) {\n      logger.debug(`Setting selection for row ${id}`);\n      const newSelectionModel = {\n        type: 'include',\n        ids: new Set()\n      };\n      const addRow = rowId => {\n        newSelectionModel.ids.add(rowId);\n      };\n      if (isSelected) {\n        addRow(id);\n        if (applyAutoSelection) {\n          findRowsToSelect(apiRef, tree, id, props.rowSelectionPropagation?.descendants ?? false, props.rowSelectionPropagation?.parents ?? false, addRow);\n        }\n      }\n      apiRef.current.setRowSelectionModel(newSelectionModel, 'singleRowSelection');\n    } else {\n      logger.debug(`Toggling selection for row ${id}`);\n      const selectionModel = gridRowSelectionStateSelector(apiRef);\n      const newSelectionModel = {\n        type: selectionModel.type,\n        ids: new Set(selectionModel.ids)\n      };\n      const selectionManager = createRowSelectionManager(newSelectionModel);\n      selectionManager.unselect(id);\n      const addRow = rowId => {\n        selectionManager.select(rowId);\n      };\n      const removeRow = rowId => {\n        selectionManager.unselect(rowId);\n      };\n      if (isSelected) {\n        addRow(id);\n        if (applyAutoSelection) {\n          findRowsToSelect(apiRef, tree, id, props.rowSelectionPropagation?.descendants ?? false, props.rowSelectionPropagation?.parents ?? false, addRow);\n        }\n      } else if (applyAutoSelection) {\n        findRowsToDeselect(apiRef, tree, id, props.rowSelectionPropagation?.descendants ?? false, props.rowSelectionPropagation?.parents ?? false, removeRow);\n      }\n      const isSelectionValid = newSelectionModel.type === 'include' && newSelectionModel.ids.size < 2 || canHaveMultipleSelection;\n      if (isSelectionValid) {\n        apiRef.current.setRowSelectionModel(newSelectionModel, 'singleRowSelection');\n      }\n    }\n  }, [apiRef, logger, applyAutoSelection, tree, props.rowSelectionPropagation?.descendants, props.rowSelectionPropagation?.parents, canHaveMultipleSelection]);\n  const selectRows = React.useCallback((ids, isSelected = true, resetSelection = false) => {\n    logger.debug(`Setting selection for several rows`);\n    if (props.rowSelection === false) {\n      return;\n    }\n    const selectableIds = new Set();\n    for (let i = 0; i < ids.length; i += 1) {\n      const id = ids[i];\n      if (apiRef.current.isRowSelectable(id)) {\n        selectableIds.add(id);\n      }\n    }\n    const currentSelectionModel = gridRowSelectionStateSelector(apiRef);\n    let newSelectionModel;\n    if (resetSelection) {\n      newSelectionModel = {\n        type: 'include',\n        ids: selectableIds\n      };\n      if (isSelected) {\n        const selectionManager = createRowSelectionManager(newSelectionModel);\n        if (applyAutoSelection) {\n          const addRow = rowId => {\n            selectionManager.select(rowId);\n          };\n          for (const id of selectableIds) {\n            findRowsToSelect(apiRef, tree, id, props.rowSelectionPropagation?.descendants ?? false, props.rowSelectionPropagation?.parents ?? false, addRow);\n          }\n        }\n      } else {\n        newSelectionModel.ids = new Set();\n      }\n      if (currentSelectionModel.type === newSelectionModel.type && newSelectionModel.ids.size === currentSelectionModel.ids.size && Array.from(newSelectionModel.ids).every(id => currentSelectionModel.ids.has(id))) {\n        return;\n      }\n    } else {\n      newSelectionModel = {\n        type: currentSelectionModel.type,\n        ids: new Set(currentSelectionModel.ids)\n      };\n      const selectionManager = createRowSelectionManager(newSelectionModel);\n      const addRow = rowId => {\n        selectionManager.select(rowId);\n      };\n      const removeRow = rowId => {\n        selectionManager.unselect(rowId);\n      };\n      for (const id of selectableIds) {\n        if (isSelected) {\n          selectionManager.select(id);\n          if (applyAutoSelection) {\n            findRowsToSelect(apiRef, tree, id, props.rowSelectionPropagation?.descendants ?? false, props.rowSelectionPropagation?.parents ?? false, addRow);\n          }\n        } else {\n          removeRow(id);\n          if (applyAutoSelection) {\n            findRowsToDeselect(apiRef, tree, id, props.rowSelectionPropagation?.descendants ?? false, props.rowSelectionPropagation?.parents ?? false, removeRow);\n          }\n        }\n      }\n    }\n    const isSelectionValid = newSelectionModel.type === 'include' && newSelectionModel.ids.size < 2 || canHaveMultipleSelection;\n    if (isSelectionValid) {\n      apiRef.current.setRowSelectionModel(newSelectionModel, 'multipleRowsSelection');\n    }\n  }, [logger, applyAutoSelection, canHaveMultipleSelection, apiRef, tree, props.rowSelectionPropagation?.descendants, props.rowSelectionPropagation?.parents, props.rowSelection]);\n  const getPropagatedRowSelectionModel = React.useCallback(inputSelectionModel => {\n    if (!isNestedData || !applyAutoSelection || inputSelectionModel.ids.size === 0 && inputSelectionModel.type === 'include') {\n      return inputSelectionModel;\n    }\n    const propagatedSelectionModel = {\n      type: inputSelectionModel.type,\n      ids: new Set(inputSelectionModel.ids)\n    };\n    const selectionManager = createRowSelectionManager(propagatedSelectionModel);\n    const addRow = rowId => {\n      selectionManager.select(rowId);\n    };\n    for (const id of inputSelectionModel.ids) {\n      findRowsToSelect(apiRef, tree, id, props.rowSelectionPropagation?.descendants ?? false, props.rowSelectionPropagation?.parents ?? false, addRow, selectionManager);\n    }\n    return propagatedSelectionModel;\n  }, [apiRef, tree, props.rowSelectionPropagation?.descendants, props.rowSelectionPropagation?.parents, isNestedData, applyAutoSelection]);\n  const selectRowRange = React.useCallback(({\n    startId,\n    endId\n  }, isSelected = true, resetSelection = false) => {\n    if (!apiRef.current.getRow(startId) || !apiRef.current.getRow(endId)) {\n      return;\n    }\n    logger.debug(`Expanding selection from row ${startId} to row ${endId}`);\n\n    // Using rows from all pages allow to select a range across several pages\n    const allPagesRowIds = gridExpandedSortedRowIdsSelector(apiRef);\n    const startIndex = allPagesRowIds.indexOf(startId);\n    const endIndex = allPagesRowIds.indexOf(endId);\n    const [start, end] = startIndex > endIndex ? [endIndex, startIndex] : [startIndex, endIndex];\n    const rowsBetweenStartAndEnd = allPagesRowIds.slice(start, end + 1);\n    apiRef.current.selectRows(rowsBetweenStartAndEnd, isSelected, resetSelection);\n  }, [apiRef, logger]);\n  const selectionPublicApi = {\n    selectRow,\n    setRowSelectionModel,\n    getSelectedRows,\n    isRowSelected,\n    isRowSelectable\n  };\n  const selectionPrivateApi = {\n    selectRows,\n    selectRowRange,\n    getPropagatedRowSelectionModel\n  };\n  useGridApiMethod(apiRef, selectionPublicApi, 'public');\n  useGridApiMethod(apiRef, selectionPrivateApi, props.signature === GridSignature.DataGrid ? 'private' : 'public');\n\n  /*\n   * EVENTS\n   */\n  const isFirstRender = React.useRef(true);\n  const removeOutdatedSelection = React.useCallback((sortModelUpdated = false) => {\n    if (isFirstRender.current) {\n      return;\n    }\n    const currentSelection = gridRowSelectionStateSelector(apiRef);\n    const rowsLookup = gridRowsLookupSelector(apiRef);\n    const filteredRowsLookup = gridFilteredRowsLookupSelector(apiRef);\n    const isNonExistent = id => {\n      if (props.filterMode === 'server') {\n        return !rowsLookup[id];\n      }\n      return !rowsLookup[id] || filteredRowsLookup[id] === false;\n    };\n    const newSelectionModel = {\n      type: currentSelection.type,\n      ids: new Set(currentSelection.ids)\n    };\n    const selectionManager = createRowSelectionManager(newSelectionModel);\n    let hasChanged = false;\n    for (const id of currentSelection.ids) {\n      if (isNonExistent(id)) {\n        if (props.keepNonExistentRowsSelected) {\n          continue;\n        }\n        selectionManager.unselect(id);\n        hasChanged = true;\n        continue;\n      }\n      if (!props.rowSelectionPropagation?.parents) {\n        continue;\n      }\n      const node = tree[id];\n      if (node?.type === 'group') {\n        const isAutoGenerated = node.isAutoGenerated;\n        if (isAutoGenerated) {\n          selectionManager.unselect(id);\n          hasChanged = true;\n          continue;\n        }\n        // Keep previously selected tree data parents selected if all their children are filtered out\n        if (!node.children.every(childId => filteredRowsLookup[childId] === false)) {\n          selectionManager.unselect(id);\n          hasChanged = true;\n        }\n      }\n    }\n\n    // For nested data, on row tree updation (filtering, adding rows, etc.) when the selection is\n    // not empty, we need to re-run scanning of the tree to propagate the selection changes\n    // Example: A parent whose de-selected children are filtered out should now be selected\n    const shouldReapplyPropagation = isNestedData && props.rowSelectionPropagation?.parents && (newSelectionModel.ids.size > 0 ||\n    // In case of exclude selection, newSelectionModel.ids.size === 0 means all rows are selected\n    newSelectionModel.type === 'exclude');\n    if (hasChanged || shouldReapplyPropagation && !sortModelUpdated) {\n      if (shouldReapplyPropagation) {\n        if (newSelectionModel.type === 'exclude') {\n          const unfilteredSelectedRowIds = getRowsToBeSelected();\n          const selectedRowIds = [];\n          for (let i = 0; i < unfilteredSelectedRowIds.length; i += 1) {\n            const rowId = unfilteredSelectedRowIds[i];\n            if ((props.keepNonExistentRowsSelected || !isNonExistent(rowId)) && selectionManager.has(rowId)) {\n              selectedRowIds.push(rowId);\n            }\n          }\n          apiRef.current.selectRows(selectedRowIds, true, true);\n        } else {\n          apiRef.current.selectRows(Array.from(newSelectionModel.ids), true, true);\n        }\n      } else {\n        apiRef.current.setRowSelectionModel(newSelectionModel, 'multipleRowsSelection');\n      }\n    }\n  }, [apiRef, isNestedData, props.rowSelectionPropagation?.parents, props.keepNonExistentRowsSelected, props.filterMode, tree, getRowsToBeSelected]);\n  const handleSingleRowSelection = React.useCallback((id, event) => {\n    const hasCtrlKey = event.metaKey || event.ctrlKey;\n\n    // multiple selection is only allowed if:\n    // - it is a checkboxSelection\n    // - it is a keyboard selection\n    // - Ctrl is pressed\n\n    const isMultipleSelectionDisabled = !checkboxSelection && !hasCtrlKey && !isKeyboardEvent(event);\n    const resetSelection = !canHaveMultipleSelection || isMultipleSelectionDisabled;\n    const isSelected = apiRef.current.isRowSelected(id);\n    const selectedRowsCount = gridRowSelectionCountSelector(apiRef);\n\n    // Clicking on a row should toggle the selection except when a range of rows is already selected and the selection should reset\n    // In that case, we want to keep the current row selected (https://github.com/mui/mui-x/pull/15509#discussion_r1878082687)\n    const shouldStaySelected = selectedRowsCount > 1 && resetSelection;\n    const newSelectionState = shouldStaySelected || !isSelected;\n    apiRef.current.selectRow(id, newSelectionState, resetSelection);\n  }, [apiRef, canHaveMultipleSelection, checkboxSelection]);\n  const handleRowClick = React.useCallback((params, event) => {\n    if (disableRowSelectionOnClick) {\n      return;\n    }\n    const field = event.target.closest(`.${gridClasses.cell}`)?.getAttribute('data-field');\n    if (field === GRID_CHECKBOX_SELECTION_COL_DEF.field) {\n      // click on checkbox should not trigger row selection\n      return;\n    }\n    if (field === GRID_DETAIL_PANEL_TOGGLE_FIELD) {\n      // click to open the detail panel should not select the row\n      return;\n    }\n    if (field) {\n      const column = apiRef.current.getColumn(field);\n      if (column?.type === GRID_ACTIONS_COLUMN_TYPE) {\n        return;\n      }\n    }\n    const rowNode = gridRowNodeSelector(apiRef, params.id);\n    if (rowNode.type === 'pinnedRow') {\n      return;\n    }\n    if (event.shiftKey && canHaveMultipleSelection) {\n      expandMouseRowRangeSelection(params.id);\n    } else {\n      handleSingleRowSelection(params.id, event);\n    }\n  }, [disableRowSelectionOnClick, canHaveMultipleSelection, apiRef, expandMouseRowRangeSelection, handleSingleRowSelection]);\n  const preventSelectionOnShift = React.useCallback((params, event) => {\n    if (canHaveMultipleSelection && event.shiftKey) {\n      window.getSelection()?.removeAllRanges();\n    }\n  }, [canHaveMultipleSelection]);\n  const handleRowSelectionCheckboxChange = React.useCallback((params, event) => {\n    if (canHaveMultipleSelection && event.nativeEvent.shiftKey) {\n      expandMouseRowRangeSelection(params.id);\n    } else {\n      apiRef.current.selectRow(params.id, params.value, !canHaveMultipleSelection);\n    }\n  }, [apiRef, expandMouseRowRangeSelection, canHaveMultipleSelection]);\n  const toggleAllRows = React.useCallback(value => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    const quickFilterModel = gridQuickFilterValuesSelector(apiRef);\n    const hasFilters = filterModel.items.length > 0 || quickFilterModel?.some(val => val.length);\n    if (!props.isRowSelectable && !props.checkboxSelectionVisibleOnly && applyAutoSelection && !hasFilters) {\n      apiRef.current.setRowSelectionModel({\n        type: value ? 'exclude' : 'include',\n        ids: new Set()\n      });\n    } else {\n      apiRef.current.selectRows(getRowsToBeSelected(), value);\n    }\n  }, [apiRef, applyAutoSelection, getRowsToBeSelected, props.checkboxSelectionVisibleOnly, props.isRowSelectable]);\n  const handleHeaderSelectionCheckboxChange = React.useCallback(params => {\n    toggleAllRows(params.value);\n  }, [toggleAllRows]);\n  const handleCellKeyDown = React.useCallback((params, event) => {\n    // Get the most recent cell mode because it may have been changed by another listener\n    if (apiRef.current.getCellMode(params.id, params.field) === GridCellModes.Edit) {\n      return;\n    }\n\n    // Ignore portal\n    // Do not apply shortcuts if the focus is not on the cell root component\n    if (isEventTargetInPortal(event)) {\n      return;\n    }\n    if (isNavigationKey(event.key) && event.shiftKey) {\n      // The cell that has focus after the keyboard navigation\n      const focusCell = gridFocusCellSelector(apiRef);\n      if (focusCell && focusCell.id !== params.id) {\n        event.preventDefault();\n        const isNextRowSelected = apiRef.current.isRowSelected(focusCell.id);\n        if (!canHaveMultipleSelection) {\n          apiRef.current.selectRow(focusCell.id, !isNextRowSelected, true);\n          return;\n        }\n        const newRowIndex = apiRef.current.getRowIndexRelativeToVisibleRows(focusCell.id);\n        const previousRowIndex = apiRef.current.getRowIndexRelativeToVisibleRows(params.id);\n        let start;\n        let end;\n        if (newRowIndex > previousRowIndex) {\n          if (isNextRowSelected) {\n            // We are navigating to the bottom of the page and adding selected rows\n            start = previousRowIndex;\n            end = newRowIndex - 1;\n          } else {\n            // We are navigating to the bottom of the page and removing selected rows\n            start = previousRowIndex;\n            end = newRowIndex;\n          }\n        } else {\n          // eslint-disable-next-line no-lonely-if\n          if (isNextRowSelected) {\n            // We are navigating to the top of the page and removing selected rows\n            start = newRowIndex + 1;\n            end = previousRowIndex;\n          } else {\n            // We are navigating to the top of the page and adding selected rows\n            start = newRowIndex;\n            end = previousRowIndex;\n          }\n        }\n        const visibleRows = getVisibleRows(apiRef);\n        const rowsBetweenStartAndEnd = [];\n        for (let i = start; i <= end; i += 1) {\n          rowsBetweenStartAndEnd.push(visibleRows.rows[i].id);\n        }\n        apiRef.current.selectRows(rowsBetweenStartAndEnd, !isNextRowSelected);\n        return;\n      }\n    }\n    if (event.key === ' ' && event.shiftKey) {\n      event.preventDefault();\n      handleSingleRowSelection(params.id, event);\n      return;\n    }\n    if (String.fromCharCode(event.keyCode) === 'A' && (event.ctrlKey || event.metaKey)) {\n      event.preventDefault();\n      toggleAllRows(true);\n    }\n  }, [apiRef, canHaveMultipleSelection, handleSingleRowSelection, toggleAllRows]);\n  const syncControlledState = useEventCallback(() => {\n    if (!props.rowSelection) {\n      apiRef.current.setRowSelectionModel(emptyModel);\n      return;\n    }\n    if (propRowSelectionModel === undefined) {\n      return;\n    }\n    if (!applyAutoSelection || !isNestedData || propRowSelectionModel.type === 'include' && propRowSelectionModel.ids.size === 0) {\n      apiRef.current.setRowSelectionModel(propRowSelectionModel);\n      return;\n    }\n    const newSelectionModel = apiRef.current.getPropagatedRowSelectionModel(propRowSelectionModel);\n    if (newSelectionModel.type !== propRowSelectionModel.type || newSelectionModel.ids.size !== propRowSelectionModel.ids.size || !Array.from(propRowSelectionModel.ids).every(id => newSelectionModel.ids.has(id))) {\n      apiRef.current.setRowSelectionModel(newSelectionModel);\n      return;\n    }\n    apiRef.current.setRowSelectionModel(propRowSelectionModel);\n  });\n  useGridEvent(apiRef, 'sortedRowsSet', runIfRowSelectionIsEnabled(() => removeOutdatedSelection(true)));\n  useGridEvent(apiRef, 'filteredRowsSet', runIfRowSelectionIsEnabled(() => removeOutdatedSelection()));\n  useGridEvent(apiRef, 'rowClick', runIfRowSelectionIsEnabled(handleRowClick));\n  useGridEvent(apiRef, 'rowSelectionCheckboxChange', runIfRowSelectionIsEnabled(handleRowSelectionCheckboxChange));\n  useGridEvent(apiRef, 'headerSelectionCheckboxChange', handleHeaderSelectionCheckboxChange);\n  useGridEvent(apiRef, 'cellMouseDown', runIfRowSelectionIsEnabled(preventSelectionOnShift));\n  useGridEvent(apiRef, 'cellKeyDown', runIfRowSelectionIsEnabled(handleCellKeyDown));\n\n  /*\n   * EFFECTS\n   */\n  React.useEffect(() => {\n    syncControlledState();\n  }, [apiRef, propRowSelectionModel, props.rowSelection, syncControlledState]);\n  const isStateControlled = propRowSelectionModel != null;\n  React.useEffect(() => {\n    if (isStateControlled || !props.rowSelection || typeof isRowSelectable !== 'function') {\n      return;\n    }\n\n    // props.isRowSelectable changed\n    const currentSelection = gridRowSelectionStateSelector(apiRef);\n    if (currentSelection.type !== 'include') {\n      return;\n    }\n    const selectableIds = new Set();\n    for (const id of currentSelection.ids) {\n      if (isRowSelectable(id)) {\n        selectableIds.add(id);\n      }\n    }\n    if (selectableIds.size < currentSelection.ids.size) {\n      apiRef.current.setRowSelectionModel({\n        type: currentSelection.type,\n        ids: selectableIds\n      });\n    }\n  }, [apiRef, isRowSelectable, isStateControlled, props.rowSelection]);\n  React.useEffect(() => {\n    if (!props.rowSelection || isStateControlled) {\n      return;\n    }\n    const currentSelection = gridRowSelectionStateSelector(apiRef);\n    if (!canHaveMultipleSelection && (currentSelection.type === 'include' && currentSelection.ids.size > 1 || currentSelection.type === 'exclude')) {\n      // See https://github.com/mui/mui-x/issues/8455\n      apiRef.current.setRowSelectionModel(emptyModel);\n    }\n  }, [apiRef, canHaveMultipleSelection, checkboxSelection, isStateControlled, props.rowSelection]);\n  React.useEffect(() => {\n    runIfRowSelectionIsEnabled(removeOutdatedSelection);\n  }, [removeOutdatedSelection, runIfRowSelectionIsEnabled]);\n  React.useEffect(() => {\n    if (isFirstRender.current) {\n      isFirstRender.current = false;\n    }\n  }, []);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}