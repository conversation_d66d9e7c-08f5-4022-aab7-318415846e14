{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { useGridApiContext } from \"../hooks/utils/useGridApiContext.js\";\nimport { GridOverlay } from \"./containers/GridOverlay.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const GridNoResultsOverlay = forwardRef(function GridNoResultsOverlay(props, ref) {\n  const apiRef = useGridApiContext();\n  const noResultsOverlayLabel = apiRef.current.getLocaleText('noResultsOverlayLabel');\n  return /*#__PURE__*/_jsx(GridOverlay, _extends({}, props, {\n    ref: ref,\n    children: noResultsOverlayLabel\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridNoResultsOverlay.displayName = \"GridNoResultsOverlay\";", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}