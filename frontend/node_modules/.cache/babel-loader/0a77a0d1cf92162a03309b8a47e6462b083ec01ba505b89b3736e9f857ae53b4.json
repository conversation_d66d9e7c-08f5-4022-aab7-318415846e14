{"ast": null, "code": "import { GridFilterInputValue } from \"../components/panel/filterPanel/GridFilterInputValue.js\";\nimport { GridFilterInputMultipleValue } from \"../components/panel/filterPanel/GridFilterInputMultipleValue.js\";\nconst parseNumericValue = value => {\n  if (value == null) {\n    return null;\n  }\n  return Number(value);\n};\nexport const getGridNumericQuickFilterFn = value => {\n  if (value == null || Number.isNaN(value) || value === '') {\n    return null;\n  }\n  return columnValue => {\n    return parseNumericValue(columnValue) === parseNumericValue(value);\n  };\n};\nexport const getGridNumericOperators = () => [{\n  value: '=',\n  getApplyFilterFn: filterItem => {\n    if (filterItem.value == null || Number.isNaN(filterItem.value)) {\n      return null;\n    }\n    return value => {\n      return parseNumericValue(value) === filterItem.value;\n    };\n  },\n  InputComponent: GridFilterInputValue,\n  InputComponentProps: {\n    type: 'number'\n  }\n}, {\n  value: '!=',\n  getApplyFilterFn: filterItem => {\n    if (filterItem.value == null || Number.isNaN(filterItem.value)) {\n      return null;\n    }\n    return value => {\n      return parseNumericValue(value) !== filterItem.value;\n    };\n  },\n  InputComponent: GridFilterInputValue,\n  InputComponentProps: {\n    type: 'number'\n  }\n}, {\n  value: '>',\n  getApplyFilterFn: filterItem => {\n    if (filterItem.value == null || Number.isNaN(filterItem.value)) {\n      return null;\n    }\n    return value => {\n      if (value == null) {\n        return false;\n      }\n      return parseNumericValue(value) > filterItem.value;\n    };\n  },\n  InputComponent: GridFilterInputValue,\n  InputComponentProps: {\n    type: 'number'\n  }\n}, {\n  value: '>=',\n  getApplyFilterFn: filterItem => {\n    if (filterItem.value == null || Number.isNaN(filterItem.value)) {\n      return null;\n    }\n    return value => {\n      if (value == null) {\n        return false;\n      }\n      return parseNumericValue(value) >= filterItem.value;\n    };\n  },\n  InputComponent: GridFilterInputValue,\n  InputComponentProps: {\n    type: 'number'\n  }\n}, {\n  value: '<',\n  getApplyFilterFn: filterItem => {\n    if (filterItem.value == null || Number.isNaN(filterItem.value)) {\n      return null;\n    }\n    return value => {\n      if (value == null) {\n        return false;\n      }\n      return parseNumericValue(value) < filterItem.value;\n    };\n  },\n  InputComponent: GridFilterInputValue,\n  InputComponentProps: {\n    type: 'number'\n  }\n}, {\n  value: '<=',\n  getApplyFilterFn: filterItem => {\n    if (filterItem.value == null || Number.isNaN(filterItem.value)) {\n      return null;\n    }\n    return value => {\n      if (value == null) {\n        return false;\n      }\n      return parseNumericValue(value) <= filterItem.value;\n    };\n  },\n  InputComponent: GridFilterInputValue,\n  InputComponentProps: {\n    type: 'number'\n  }\n}, {\n  value: 'isEmpty',\n  getApplyFilterFn: () => {\n    return value => {\n      return value == null;\n    };\n  },\n  requiresFilterValue: false\n}, {\n  value: 'isNotEmpty',\n  getApplyFilterFn: () => {\n    return value => {\n      return value != null;\n    };\n  },\n  requiresFilterValue: false\n}, {\n  value: 'isAnyOf',\n  getApplyFilterFn: filterItem => {\n    if (!Array.isArray(filterItem.value) || filterItem.value.length === 0) {\n      return null;\n    }\n    return value => {\n      return value != null && filterItem.value.includes(Number(value));\n    };\n  },\n  InputComponent: GridFilterInputMultipleValue,\n  InputComponentProps: {\n    type: 'number'\n  }\n}];", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}