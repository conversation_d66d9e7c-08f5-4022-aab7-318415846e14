{"ast": null, "code": "import * as React from 'react';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const GridPanelContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== \"production\") GridPanelContext.displayName = \"GridPanelContext\";\nexport function useGridPanelContext() {\n  const context = React.useContext(GridPanelContext);\n  if (context === undefined) {\n    throw new Error('MUI X: Missing context.');\n  }\n  return context;\n}\nexport function GridPanelContextProvider({\n  children\n}) {\n  const columnsPanelTriggerRef = React.useRef(null);\n  const filterPanelTriggerRef = React.useRef(null);\n  const aiAssistantPanelTriggerRef = React.useRef(null);\n  const value = React.useMemo(() => ({\n    columnsPanelTriggerRef,\n    filterPanelTriggerRef,\n    aiAssistantPanelTriggerRef\n  }), []);\n  return /*#__PURE__*/_jsx(GridPanelContext.Provider, {\n    value: value,\n    children: children\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}