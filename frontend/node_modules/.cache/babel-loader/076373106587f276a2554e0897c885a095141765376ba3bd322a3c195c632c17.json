{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"sortingOrder\"];\nimport * as React from 'react';\nimport { useGridRootProps } from \"../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const GridColumnUnsortedIcon = /*#__PURE__*/React.memo(function GridColumnHeaderSortIcon(props) {\n  const {\n      sortingOrder\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const rootProps = useGridRootProps();\n  const [nextSortDirection] = sortingOrder;\n  const Icon = nextSortDirection === 'asc' ? rootProps.slots.columnSortedAscendingIcon : rootProps.slots.columnSortedDescendingIcon;\n  return Icon ? /*#__PURE__*/_jsx(Icon, _extends({}, other)) : null;\n});\nif (process.env.NODE_ENV !== \"production\") GridColumnUnsortedIcon.displayName = \"GridColumnUnsortedIcon\";", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}