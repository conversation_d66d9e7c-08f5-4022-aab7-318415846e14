{"ast": null, "code": "import { styled } from '@mui/material/styles';\nimport { gridClasses as c } from \"../../constants/gridClasses.js\";\nimport { vars } from \"../../constants/cssVariables.js\";\nimport { useGridSelector } from \"../../hooks/utils/useGridSelector.js\";\nimport { useGridPrivateApiContext } from \"../../hooks/utils/useGridPrivateApiContext.js\";\nconst columnSeparatorTargetSize = 10;\nconst columnSeparatorOffset = -5;\nconst focusOutlineWidth = 1;\nconst separatorIconDragStyles = {\n  width: 3,\n  rx: 1.5,\n  x: 10.5\n};\n\n// Emotion thinks it knows better than us which selector we should use.\n// https://github.com/emotion-js/emotion/issues/1105#issuecomment-1722524968\nconst ignoreSsrWarning = '/* emotion-disable-server-rendering-unsafe-selector-warning-please-do-not-use-this-the-warning-exists-for-a-reason */';\nconst shouldShowBorderTopRightRadiusSelector = apiRef => apiRef.current.state.dimensions.hasScrollX && (!apiRef.current.state.dimensions.hasScrollY || apiRef.current.state.dimensions.scrollbarSize === 0);\nexport const GridRootStyles = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'Root',\n  overridesResolver: (props, styles) => [\n  // Root overrides\n  styles.root, {\n    [`&.${c.autoHeight}`]: styles.autoHeight\n  }, {\n    [`&.${c.autosizing}`]: styles.autosizing\n  }, {\n    [`&.${c['root--densityStandard']}`]: styles['root--densityStandard']\n  }, {\n    [`&.${c['root--densityComfortable']}`]: styles['root--densityComfortable']\n  }, {\n    [`&.${c['root--densityCompact']}`]: styles['root--densityCompact']\n  }, {\n    [`&.${c['root--disableUserSelection']}`]: styles['root--disableUserSelection']\n  }, {\n    [`&.${c['root--noToolbar']}`]: styles['root--noToolbar']\n  }, {\n    [`&.${c.withVerticalBorder}`]: styles.withVerticalBorder\n  },\n  // Child element overrides\n  // - Only declare overrides here for class names that are not applied to `styled` components.\n  // - For `styled` components, declare overrides in the component itself.\n  {\n    [`& .${c.actionsCell}`]: styles.actionsCell\n  }, {\n    [`& .${c.booleanCell}`]: styles.booleanCell\n  }, {\n    [`& .${c.cell}`]: styles.cell\n  }, {\n    [`& .${c['cell--editable']}`]: styles['cell--editable']\n  }, {\n    [`& .${c['cell--editing']}`]: styles['cell--editing']\n  }, {\n    [`& .${c['cell--flex']}`]: styles['cell--flex']\n  }, {\n    [`& .${c['cell--pinnedLeft']}`]: styles['cell--pinnedLeft']\n  }, {\n    [`& .${c['cell--pinnedRight']}`]: styles['cell--pinnedRight']\n  }, {\n    [`& .${c['cell--rangeBottom']}`]: styles['cell--rangeBottom']\n  }, {\n    [`& .${c['cell--rangeLeft']}`]: styles['cell--rangeLeft']\n  }, {\n    [`& .${c['cell--rangeRight']}`]: styles['cell--rangeRight']\n  }, {\n    [`& .${c['cell--rangeTop']}`]: styles['cell--rangeTop']\n  }, {\n    [`& .${c['cell--selectionMode']}`]: styles['cell--selectionMode']\n  }, {\n    [`& .${c['cell--textCenter']}`]: styles['cell--textCenter']\n  }, {\n    [`& .${c['cell--textLeft']}`]: styles['cell--textLeft']\n  }, {\n    [`& .${c['cell--textRight']}`]: styles['cell--textRight']\n  }, {\n    [`& .${c['cell--withLeftBorder']}`]: styles['cell--withLeftBorder']\n  }, {\n    [`& .${c['cell--withRightBorder']}`]: styles['cell--withRightBorder']\n  }, {\n    [`& .${c.cellCheckbox}`]: styles.cellCheckbox\n  }, {\n    [`& .${c.cellEmpty}`]: styles.cellEmpty\n  }, {\n    [`& .${c.cellOffsetLeft}`]: styles.cellOffsetLeft\n  }, {\n    [`& .${c.cellSkeleton}`]: styles.cellSkeleton\n  }, {\n    [`& .${c.checkboxInput}`]: styles.checkboxInput\n  }, {\n    [`& .${c.columnHeader}`]: styles.columnHeader\n  }, {\n    [`& .${c['columnHeader--alignCenter']}`]: styles['columnHeader--alignCenter']\n  }, {\n    [`& .${c['columnHeader--alignLeft']}`]: styles['columnHeader--alignLeft']\n  }, {\n    [`& .${c['columnHeader--alignRight']}`]: styles['columnHeader--alignRight']\n  }, {\n    [`& .${c['columnHeader--dragging']}`]: styles['columnHeader--dragging']\n  }, {\n    [`& .${c['columnHeader--emptyGroup']}`]: styles['columnHeader--emptyGroup']\n  }, {\n    [`& .${c['columnHeader--filledGroup']}`]: styles['columnHeader--filledGroup']\n  }, {\n    [`& .${c['columnHeader--filtered']}`]: styles['columnHeader--filtered']\n  }, {\n    [`& .${c['columnHeader--last']}`]: styles['columnHeader--last']\n  }, {\n    [`& .${c['columnHeader--lastUnpinned']}`]: styles['columnHeader--lastUnpinned']\n  }, {\n    [`& .${c['columnHeader--moving']}`]: styles['columnHeader--moving']\n  }, {\n    [`& .${c['columnHeader--numeric']}`]: styles['columnHeader--numeric']\n  }, {\n    [`& .${c['columnHeader--pinnedLeft']}`]: styles['columnHeader--pinnedLeft']\n  }, {\n    [`& .${c['columnHeader--pinnedRight']}`]: styles['columnHeader--pinnedRight']\n  }, {\n    [`& .${c['columnHeader--siblingFocused']}`]: styles['columnHeader--siblingFocused']\n  }, {\n    [`& .${c['columnHeader--sortable']}`]: styles['columnHeader--sortable']\n  }, {\n    [`& .${c['columnHeader--sorted']}`]: styles['columnHeader--sorted']\n  }, {\n    [`& .${c['columnHeader--withLeftBorder']}`]: styles['columnHeader--withLeftBorder']\n  }, {\n    [`& .${c['columnHeader--withRightBorder']}`]: styles['columnHeader--withRightBorder']\n  }, {\n    [`& .${c.columnHeaderCheckbox}`]: styles.columnHeaderCheckbox\n  }, {\n    [`& .${c.columnHeaderDraggableContainer}`]: styles.columnHeaderDraggableContainer\n  }, {\n    [`& .${c.columnHeaderTitleContainer}`]: styles.columnHeaderTitleContainer\n  }, {\n    [`& .${c.columnHeaderTitleContainerContent}`]: styles.columnHeaderTitleContainerContent\n  }, {\n    [`& .${c.columnSeparator}`]: styles.columnSeparator\n  }, {\n    [`& .${c['columnSeparator--resizable']}`]: styles['columnSeparator--resizable']\n  }, {\n    [`& .${c['columnSeparator--resizing']}`]: styles['columnSeparator--resizing']\n  }, {\n    [`& .${c['columnSeparator--sideLeft']}`]: styles['columnSeparator--sideLeft']\n  }, {\n    [`& .${c['columnSeparator--sideRight']}`]: styles['columnSeparator--sideRight']\n  }, {\n    [`& .${c['container--bottom']}`]: styles['container--bottom']\n  }, {\n    [`& .${c['container--top']}`]: styles['container--top']\n  }, {\n    [`& .${c.detailPanelToggleCell}`]: styles.detailPanelToggleCell\n  }, {\n    [`& .${c['detailPanelToggleCell--expanded']}`]: styles['detailPanelToggleCell--expanded']\n  }, {\n    [`& .${c.editBooleanCell}`]: styles.editBooleanCell\n  }, {\n    [`& .${c.filterIcon}`]: styles.filterIcon\n  }, {\n    [`& .${c['filler--borderBottom']}`]: styles['filler--borderBottom']\n  }, {\n    [`& .${c['filler--pinnedLeft']}`]: styles['filler--pinnedLeft']\n  }, {\n    [`& .${c['filler--pinnedRight']}`]: styles['filler--pinnedRight']\n  }, {\n    [`& .${c.groupingCriteriaCell}`]: styles.groupingCriteriaCell\n  }, {\n    [`& .${c.groupingCriteriaCellLoadingContainer}`]: styles.groupingCriteriaCellLoadingContainer\n  }, {\n    [`& .${c.groupingCriteriaCellToggle}`]: styles.groupingCriteriaCellToggle\n  }, {\n    [`& .${c.headerFilterRow}`]: styles.headerFilterRow\n  }, {\n    [`& .${c.iconSeparator}`]: styles.iconSeparator\n  }, {\n    [`& .${c.menuIcon}`]: styles.menuIcon\n  }, {\n    [`& .${c.menuIconButton}`]: styles.menuIconButton\n  }, {\n    [`& .${c.menuList}`]: styles.menuList\n  }, {\n    [`& .${c.menuOpen}`]: styles.menuOpen\n  }, {\n    [`& .${c.overlayWrapperInner}`]: styles.overlayWrapperInner\n  }, {\n    [`& .${c.pinnedRows}`]: styles.pinnedRows\n  }, {\n    [`& .${c['pinnedRows--bottom']}`]: styles['pinnedRows--bottom']\n  }, {\n    [`& .${c['pinnedRows--top']}`]: styles['pinnedRows--top']\n  }, {\n    [`& .${c.row}`]: styles.row\n  }, {\n    [`& .${c['row--borderBottom']}`]: styles['row--borderBottom']\n  }, {\n    [`& .${c['row--detailPanelExpanded']}`]: styles['row--detailPanelExpanded']\n  }, {\n    [`& .${c['row--dragging']}`]: styles['row--dragging']\n  }, {\n    [`& .${c['row--dynamicHeight']}`]: styles['row--dynamicHeight']\n  }, {\n    [`& .${c['row--editable']}`]: styles['row--editable']\n  }, {\n    [`& .${c['row--editing']}`]: styles['row--editing']\n  }, {\n    [`& .${c['row--firstVisible']}`]: styles['row--firstVisible']\n  }, {\n    [`& .${c['row--lastVisible']}`]: styles['row--lastVisible']\n  }, {\n    [`& .${c.rowReorderCell}`]: styles.rowReorderCell\n  }, {\n    [`& .${c['rowReorderCell--draggable']}`]: styles['rowReorderCell--draggable']\n  }, {\n    [`& .${c.rowReorderCellContainer}`]: styles.rowReorderCellContainer\n  }, {\n    [`& .${c.rowReorderCellPlaceholder}`]: styles.rowReorderCellPlaceholder\n  }, {\n    [`& .${c.rowSkeleton}`]: styles.rowSkeleton\n  }, {\n    [`& .${c.scrollbar}`]: styles.scrollbar\n  }, {\n    [`& .${c['scrollbar--horizontal']}`]: styles['scrollbar--horizontal']\n  }, {\n    [`& .${c['scrollbar--vertical']}`]: styles['scrollbar--vertical']\n  }, {\n    [`& .${c.scrollbarFiller}`]: styles.scrollbarFiller\n  }, {\n    [`& .${c['scrollbarFiller--borderBottom']}`]: styles['scrollbarFiller--borderBottom']\n  }, {\n    [`& .${c['scrollbarFiller--borderTop']}`]: styles['scrollbarFiller--borderTop']\n  }, {\n    [`& .${c['scrollbarFiller--header']}`]: styles['scrollbarFiller--header']\n  }, {\n    [`& .${c['scrollbarFiller--pinnedRight']}`]: styles['scrollbarFiller--pinnedRight']\n  }, {\n    [`& .${c.sortIcon}`]: styles.sortIcon\n  }, {\n    [`& .${c.treeDataGroupingCell}`]: styles.treeDataGroupingCell\n  }, {\n    [`& .${c.treeDataGroupingCellLoadingContainer}`]: styles.treeDataGroupingCellLoadingContainer\n  }, {\n    [`& .${c.treeDataGroupingCellToggle}`]: styles.treeDataGroupingCellToggle\n  }, {\n    [`& .${c.withBorderColor}`]: styles.withBorderColor\n  }]\n})(() => {\n  const apiRef = useGridPrivateApiContext();\n  const shouldShowBorderTopRightRadius = useGridSelector(apiRef, shouldShowBorderTopRightRadiusSelector);\n  const baseBackground = vars.colors.background.base;\n  const headerBackground = vars.header.background.base;\n  const pinnedBackground = vars.cell.background.pinned;\n  const hoverColor = removeOpacity(vars.colors.interactive.hover);\n  const hoverOpacity = vars.colors.interactive.hoverOpacity;\n  const selectedColor = vars.colors.interactive.selected;\n  const selectedOpacity = vars.colors.interactive.selectedOpacity;\n  const selectedHoverColor = selectedColor;\n  const selectedHoverOpacity = `calc(${selectedOpacity} + ${hoverOpacity})`;\n  const hoverBackground = mix(baseBackground, hoverColor, hoverOpacity);\n  const selectedBackground = mix(baseBackground, selectedColor, selectedOpacity);\n  const selectedHoverBackground = mix(baseBackground, selectedHoverColor, selectedHoverOpacity);\n  const pinnedHoverBackground = mix(pinnedBackground, hoverColor, hoverOpacity);\n  const pinnedSelectedBackground = mix(pinnedBackground, selectedColor, selectedOpacity);\n  const pinnedSelectedHoverBackground = mix(pinnedBackground, selectedHoverColor, selectedHoverOpacity);\n  const getPinnedBackgroundStyles = backgroundColor => ({\n    [`& .${c['cell--pinnedLeft']}, & .${c['cell--pinnedRight']}`]: {\n      backgroundColor,\n      '&.Mui-selected': {\n        backgroundColor: mix(backgroundColor, selectedBackground, selectedOpacity),\n        '&:hover': {\n          backgroundColor: mix(backgroundColor, selectedHoverBackground, selectedHoverOpacity)\n        }\n      }\n    }\n  });\n  const pinnedHoverStyles = getPinnedBackgroundStyles(pinnedHoverBackground);\n  const pinnedSelectedStyles = getPinnedBackgroundStyles(pinnedSelectedBackground);\n  const pinnedSelectedHoverStyles = getPinnedBackgroundStyles(pinnedSelectedHoverBackground);\n  const selectedStyles = {\n    backgroundColor: selectedBackground,\n    '&:hover': {\n      backgroundColor: selectedHoverBackground,\n      // Reset on touch devices, it doesn't add specificity\n      '@media (hover: none)': {\n        backgroundColor: selectedBackground\n      }\n    }\n  };\n  const gridStyle = {\n    '--unstable_DataGrid-radius': vars.radius.base,\n    '--unstable_DataGrid-headWeight': vars.typography.fontWeight.medium,\n    '--DataGrid-rowBorderColor': vars.colors.border.base,\n    '--DataGrid-cellOffsetMultiplier': 2,\n    '--DataGrid-width': '0px',\n    '--DataGrid-hasScrollX': '0',\n    '--DataGrid-hasScrollY': '0',\n    '--DataGrid-scrollbarSize': '10px',\n    '--DataGrid-rowWidth': '0px',\n    '--DataGrid-columnsTotalWidth': '0px',\n    '--DataGrid-leftPinnedWidth': '0px',\n    '--DataGrid-rightPinnedWidth': '0px',\n    '--DataGrid-headerHeight': '0px',\n    '--DataGrid-headersTotalHeight': '0px',\n    '--DataGrid-topContainerHeight': '0px',\n    '--DataGrid-bottomContainerHeight': '0px',\n    flex: 1,\n    boxSizing: 'border-box',\n    position: 'relative',\n    borderWidth: '1px',\n    borderStyle: 'solid',\n    borderColor: vars.colors.border.base,\n    borderRadius: 'var(--unstable_DataGrid-radius)',\n    backgroundColor: vars.colors.background.base,\n    color: vars.colors.foreground.base,\n    font: vars.typography.font.body,\n    outline: 'none',\n    height: '100%',\n    display: 'flex',\n    minWidth: 0,\n    // See https://github.com/mui/mui-x/issues/8547\n    minHeight: 0,\n    flexDirection: 'column',\n    overflow: 'hidden',\n    overflowAnchor: 'none',\n    // Keep the same scrolling position\n    transform: 'translate(0, 0)',\n    // Create a stacking context to keep scrollbars from showing on top\n\n    [`.${c.main} > *:first-child${ignoreSsrWarning}`]: {\n      borderTopLeftRadius: 'var(--unstable_DataGrid-radius)',\n      borderTopRightRadius: 'var(--unstable_DataGrid-radius)'\n    },\n    [`&.${c.autoHeight}`]: {\n      height: 'auto'\n    },\n    [`&.${c.autosizing}`]: {\n      [`& .${c.columnHeaderTitleContainerContent} > *`]: {\n        overflow: 'visible !important'\n      },\n      '@media (hover: hover)': {\n        [`& .${c.menuIcon}`]: {\n          width: '0 !important',\n          visibility: 'hidden !important'\n        }\n      },\n      [`& .${c.cell}`]: {\n        overflow: 'visible !important',\n        whiteSpace: 'nowrap',\n        minWidth: 'max-content !important',\n        maxWidth: 'max-content !important'\n      },\n      [`& .${c.groupingCriteriaCell}`]: {\n        width: 'unset'\n      },\n      [`& .${c.treeDataGroupingCell}`]: {\n        width: 'unset'\n      }\n    },\n    [`&.${c.withSidePanel}`]: {\n      flexDirection: 'row'\n    },\n    [`& .${c.mainContent}`]: {\n      display: 'flex',\n      flexDirection: 'column',\n      overflow: 'hidden',\n      flex: 1\n    },\n    [`& .${c.columnHeader}, & .${c.cell}`]: {\n      WebkitTapHighlightColor: 'transparent',\n      padding: '0 10px',\n      boxSizing: 'border-box'\n    },\n    [`& .${c.columnHeader}:focus-within, & .${c.cell}:focus-within`]: {\n      outline: `solid ${setOpacity(vars.colors.interactive.focus, 0.5)} ${focusOutlineWidth}px`,\n      outlineOffset: focusOutlineWidth * -1\n    },\n    [`& .${c.columnHeader}:focus, & .${c.cell}:focus`]: {\n      outline: `solid ${vars.colors.interactive.focus} ${focusOutlineWidth}px`,\n      outlineOffset: focusOutlineWidth * -1\n    },\n    // Hide the column separator when:\n    // - the column is focused and has an outline\n    // - the next column is focused and has an outline\n    // - the column has a left or right border\n    // - the next column is pinned right and has a left border\n    [`& .${c.columnHeader}:focus,\n      & .${c['columnHeader--withLeftBorder']},\n      & .${c['columnHeader--withRightBorder']},\n      & .${c['columnHeader--siblingFocused']},\n      & .${c['virtualScroller--hasScrollX']} .${c['columnHeader--lastUnpinned']},\n      & .${c['virtualScroller--hasScrollX']} .${c['columnHeader--last']}\n      `]: {\n      [`& .${c.columnSeparator}`]: {\n        opacity: 0\n      },\n      // Show resizable separators at all times on touch devices\n      '@media (hover: none)': {\n        [`& .${c['columnSeparator--resizable']}`]: {\n          opacity: 1\n        }\n      },\n      [`& .${c['columnSeparator--resizable']}:hover`]: {\n        opacity: 1\n      }\n    },\n    [`&.${c['root--noToolbar']} [aria-rowindex=\"1\"] [aria-colindex=\"1\"]`]: {\n      borderTopLeftRadius: 'calc(var(--unstable_DataGrid-radius) - 1px)'\n    },\n    [`&.${c['root--noToolbar']} [aria-rowindex=\"1\"] .${c['columnHeader--last']}`]: {\n      borderTopRightRadius: shouldShowBorderTopRightRadius ? 'calc(var(--unstable_DataGrid-radius) - 1px)' : undefined\n    },\n    [`& .${c.columnHeaderCheckbox}, & .${c.cellCheckbox}`]: {\n      padding: 0,\n      justifyContent: 'center',\n      alignItems: 'center'\n    },\n    [`& .${c.columnHeader}`]: {\n      position: 'relative',\n      display: 'flex',\n      alignItems: 'center',\n      backgroundColor: headerBackground\n    },\n    [`& .${c['columnHeader--filter']}`]: {\n      paddingTop: 8,\n      paddingBottom: 8,\n      paddingRight: 5,\n      minHeight: 'min-content',\n      overflow: 'hidden'\n    },\n    [`& .${c['virtualScroller--hasScrollX']} .${c['columnHeader--last']}`]: {\n      overflow: 'hidden'\n    },\n    [`& .${c['pivotPanelField--sorted']} .${c.iconButtonContainer},\n      & .${c['columnHeader--sorted']} .${c.iconButtonContainer},\n      & .${c['columnHeader--filtered']} .${c.iconButtonContainer}`]: {\n      visibility: 'visible',\n      width: 'auto'\n    },\n    [`& .${c.pivotPanelField}:not(.${c['pivotPanelField--sorted']}) .${c.sortButton},\n      & .${c.columnHeader}:not(.${c['columnHeader--sorted']}) .${c.sortButton}`]: {\n      opacity: 0,\n      transition: vars.transition(['opacity'], {\n        duration: vars.transitions.duration.short\n      })\n    },\n    [`& .${c.columnHeaderTitleContainer}`]: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: vars.spacing(0.25),\n      minWidth: 0,\n      flex: 1,\n      whiteSpace: 'nowrap',\n      overflow: 'hidden'\n    },\n    [`& .${c.columnHeaderTitleContainerContent}`]: {\n      overflow: 'hidden',\n      display: 'flex',\n      alignItems: 'center'\n    },\n    [`& .${c['columnHeader--filledGroup']} .${c.columnHeaderTitleContainer}`]: {\n      borderBottomWidth: '1px',\n      borderBottomStyle: 'solid',\n      boxSizing: 'border-box'\n    },\n    [`& .${c.sortIcon}, & .${c.filterIcon}`]: {\n      fontSize: 'inherit'\n    },\n    [`& .${c['columnHeader--sortable']}`]: {\n      cursor: 'pointer'\n    },\n    [`& .${c['columnHeader--alignCenter']} .${c.columnHeaderTitleContainer}`]: {\n      justifyContent: 'center'\n    },\n    [`& .${c['columnHeader--alignRight']} .${c.columnHeaderDraggableContainer}, & .${c['columnHeader--alignRight']} .${c.columnHeaderTitleContainer}`]: {\n      flexDirection: 'row-reverse'\n    },\n    [`& .${c['columnHeader--alignCenter']} .${c.menuIcon}`]: {\n      marginLeft: 'auto'\n    },\n    [`& .${c['columnHeader--alignRight']} .${c.menuIcon}`]: {\n      marginRight: 'auto',\n      marginLeft: -5\n    },\n    [`& .${c['columnHeader--moving']}`]: {\n      backgroundColor: hoverBackground\n    },\n    [`& .${c['columnHeader--pinnedLeft']}, & .${c['columnHeader--pinnedRight']}`]: {\n      position: 'sticky',\n      zIndex: 40,\n      // Should be above the column separator\n      background: vars.header.background.base\n    },\n    [`& .${c.columnSeparator}`]: {\n      position: 'absolute',\n      overflow: 'hidden',\n      zIndex: 30,\n      display: 'flex',\n      flexDirection: 'column',\n      justifyContent: 'center',\n      alignItems: 'center',\n      maxWidth: columnSeparatorTargetSize,\n      color: vars.colors.border.base\n    },\n    [`& .${c.columnHeaders}`]: {\n      width: 'var(--DataGrid-rowWidth)',\n      backgroundColor: headerBackground\n    },\n    '@media (hover: hover)': {\n      [`& .${c.columnHeader}:hover`]: {\n        [`& .${c.menuIcon}`]: {\n          width: 'auto',\n          visibility: 'visible'\n        },\n        [`& .${c.iconButtonContainer}`]: {\n          visibility: 'visible',\n          width: 'auto'\n        }\n      },\n      [`& .${c.columnHeader}:not(.${c['columnHeader--sorted']}):hover .${c.sortButton},\n        & .${c.pivotPanelField}:not(.${c['pivotPanelField--sorted']}):hover .${c.sortButton},\n        & .${c.pivotPanelField}:not(.${c['pivotPanelField--sorted']}) .${c.sortButton}:focus-visible`]: {\n        opacity: 0.5\n      }\n    },\n    '@media (hover: none)': {\n      [`& .${c.columnHeader} .${c.menuIcon}`]: {\n        width: 'auto',\n        visibility: 'visible'\n      },\n      [`& .${c.columnHeader}:focus,\n        & .${c['columnHeader--siblingFocused']}`]: {\n        [`.${c['columnSeparator--resizable']}`]: {\n          color: vars.colors.foreground.accent\n        }\n      },\n      [`& .${c.pivotPanelField}:not(.${c['pivotPanelField--sorted']}) .${c.sortButton}`]: {\n        opacity: 0.5\n      }\n    },\n    [`& .${c['columnSeparator--sideLeft']}`]: {\n      left: columnSeparatorOffset\n    },\n    [`& .${c['columnSeparator--sideRight']}`]: {\n      right: columnSeparatorOffset\n    },\n    [`& .${c['columnHeader--withRightBorder']} .${c['columnSeparator--sideLeft']}`]: {\n      left: columnSeparatorOffset - 0.5\n    },\n    [`& .${c['columnHeader--withRightBorder']} .${c['columnSeparator--sideRight']}`]: {\n      right: columnSeparatorOffset - 0.5\n    },\n    [`& .${c['columnSeparator--resizable']}`]: {\n      cursor: 'col-resize',\n      touchAction: 'none',\n      [`&.${c['columnSeparator--resizing']}`]: {\n        color: vars.colors.foreground.accent\n      },\n      // Always appear as draggable on touch devices\n      '@media (hover: none)': {\n        [`& .${c.iconSeparator} rect`]: separatorIconDragStyles\n      },\n      '@media (hover: hover)': {\n        '&:hover': {\n          color: vars.colors.foreground.accent,\n          [`& .${c.iconSeparator} rect`]: separatorIconDragStyles\n        }\n      },\n      '& svg': {\n        pointerEvents: 'none'\n      }\n    },\n    [`& .${c.iconSeparator}`]: {\n      color: 'inherit',\n      transition: vars.transition(['color', 'width'], {\n        duration: vars.transitions.duration.short\n      })\n    },\n    [`& .${c.menuIcon}`]: {\n      width: 0,\n      visibility: 'hidden',\n      fontSize: 20,\n      marginRight: -5,\n      display: 'flex',\n      alignItems: 'center'\n    },\n    [`.${c.menuOpen}`]: {\n      visibility: 'visible',\n      width: 'auto'\n    },\n    [`& .${c.headerFilterRow}`]: {\n      [`& .${c.columnHeader}`]: {\n        boxSizing: 'border-box',\n        borderBottom: '1px solid var(--DataGrid-rowBorderColor)'\n      }\n    },\n    /* Bottom border of the top-container */\n    [`& .${c['row--borderBottom']} .${c.columnHeader},\n      & .${c['row--borderBottom']} .${c.filler},\n      & .${c['row--borderBottom']} .${c.scrollbarFiller}`]: {\n      borderBottom: `1px solid var(--DataGrid-rowBorderColor)`\n    },\n    [`& .${c['row--borderBottom']} .${c.cell}`]: {\n      borderBottom: `1px solid var(--rowBorderColor)`\n    },\n    /* Row styles */\n    [`.${c.row}`]: {\n      display: 'flex',\n      width: 'var(--DataGrid-rowWidth)',\n      breakInside: 'avoid',\n      // Avoid the row to be broken in two different print pages.\n\n      '--rowBorderColor': 'var(--DataGrid-rowBorderColor)',\n      [`&.${c['row--firstVisible']}`]: {\n        '--rowBorderColor': 'transparent'\n      },\n      '&:hover': {\n        backgroundColor: hoverBackground,\n        // Reset on touch devices, it doesn't add specificity\n        '@media (hover: none)': {\n          backgroundColor: 'transparent'\n        }\n      },\n      [`&.${c.rowSkeleton}:hover`]: {\n        backgroundColor: 'transparent'\n      },\n      '&.Mui-selected': selectedStyles\n    },\n    [`& .${c['container--top']}, & .${c['container--bottom']}`]: {\n      '[role=row]': {\n        background: vars.colors.background.base\n      }\n    },\n    /* Cell styles */\n    [`& .${c.cell}`]: {\n      flex: '0 0 auto',\n      height: 'var(--height)',\n      width: 'var(--width)',\n      lineHeight: 'calc(var(--height) - 1px)',\n      // -1px for the border\n\n      boxSizing: 'border-box',\n      borderTop: `1px solid var(--rowBorderColor)`,\n      overflow: 'hidden',\n      whiteSpace: 'nowrap',\n      textOverflow: 'ellipsis',\n      '&.Mui-selected': selectedStyles\n    },\n    [`& .${c['virtualScrollerContent--overflowed']} .${c['row--lastVisible']} .${c.cell}`]: {\n      borderTopColor: 'transparent'\n    },\n    [`& .${c.pinnedRows} .${c.row}, .${c.aggregationRowOverlayWrapper} .${c.row}`]: {\n      backgroundColor: pinnedBackground,\n      '&:hover': {\n        backgroundColor: pinnedHoverBackground\n      }\n    },\n    [`& .${c['pinnedRows--top']} :first-of-type`]: {\n      [`& .${c.cell}, .${c.scrollbarFiller}`]: {\n        borderTop: 'none'\n      }\n    },\n    [`&.${c['root--disableUserSelection']}`]: {\n      userSelect: 'none'\n    },\n    [`& .${c['row--dynamicHeight']} > .${c.cell}`]: {\n      whiteSpace: 'initial',\n      lineHeight: 'inherit'\n    },\n    [`& .${c.cellEmpty}`]: {\n      flex: 1,\n      padding: 0,\n      height: 'unset'\n    },\n    [`& .${c.cell}.${c['cell--selectionMode']}`]: {\n      cursor: 'default'\n    },\n    [`& .${c.cell}.${c['cell--editing']}`]: {\n      padding: 1,\n      display: 'flex',\n      boxShadow: vars.shadows.base,\n      backgroundColor: vars.colors.background.overlay,\n      '&:focus-within': {\n        outline: `${focusOutlineWidth}px solid ${vars.colors.interactive.focus}`,\n        outlineOffset: focusOutlineWidth * -1\n      }\n    },\n    [`& .${c['row--editing']}`]: {\n      boxShadow: vars.shadows.base\n    },\n    [`& .${c['row--editing']} .${c.cell}`]: {\n      boxShadow: 'none',\n      backgroundColor: vars.colors.background.overlay\n    },\n    [`& .${c.editBooleanCell}`]: {\n      display: 'flex',\n      height: '100%',\n      width: '100%',\n      alignItems: 'center',\n      justifyContent: 'center'\n    },\n    [`& .${c.booleanCell}[data-value=\"true\"]`]: {\n      color: vars.colors.foreground.muted\n    },\n    [`& .${c.booleanCell}[data-value=\"false\"]`]: {\n      color: vars.colors.foreground.disabled\n    },\n    [`& .${c.actionsCell}`]: {\n      display: 'inline-flex',\n      alignItems: 'center',\n      gridGap: vars.spacing(1)\n    },\n    [`& .${c.rowReorderCell}`]: {\n      display: 'inline-flex',\n      flex: 1,\n      alignItems: 'center',\n      justifyContent: 'center',\n      opacity: vars.colors.interactive.disabledOpacity\n    },\n    [`& .${c['rowReorderCell--draggable']}`]: {\n      cursor: 'grab',\n      opacity: 1\n    },\n    [`& .${c.rowReorderCellContainer}`]: {\n      padding: 0,\n      display: 'flex',\n      alignItems: 'stretch'\n    },\n    [`.${c.withBorderColor}`]: {\n      borderColor: vars.colors.border.base\n    },\n    [`& .${c['cell--withLeftBorder']}, & .${c['columnHeader--withLeftBorder']}`]: {\n      borderLeftColor: 'var(--DataGrid-rowBorderColor)',\n      borderLeftWidth: '1px',\n      borderLeftStyle: 'solid'\n    },\n    [`& .${c['cell--withRightBorder']}, & .${c['columnHeader--withRightBorder']}`]: {\n      borderRightColor: 'var(--DataGrid-rowBorderColor)',\n      borderRightWidth: '1px',\n      borderRightStyle: 'solid'\n    },\n    [`& .${c['cell--flex']}`]: {\n      display: 'flex',\n      alignItems: 'center',\n      lineHeight: 'inherit'\n    },\n    [`& .${c['cell--textLeft']}`]: {\n      textAlign: 'left',\n      justifyContent: 'flex-start'\n    },\n    [`& .${c['cell--textRight']}`]: {\n      textAlign: 'right',\n      justifyContent: 'flex-end'\n    },\n    [`& .${c['cell--textCenter']}`]: {\n      textAlign: 'center',\n      justifyContent: 'center'\n    },\n    [`& .${c['cell--pinnedLeft']}, & .${c['cell--pinnedRight']}`]: {\n      position: 'sticky',\n      zIndex: 30,\n      background: vars.cell.background.pinned,\n      '&.Mui-selected': {\n        backgroundColor: pinnedSelectedBackground\n      }\n    },\n    [`& .${c.row}`]: {\n      '&:hover': pinnedHoverStyles,\n      '&.Mui-selected': pinnedSelectedStyles,\n      '&.Mui-selected:hover': pinnedSelectedHoverStyles\n    },\n    [`& .${c.cellOffsetLeft}`]: {\n      flex: '0 0 auto',\n      display: 'inline-block'\n    },\n    [`& .${c.cellSkeleton}`]: {\n      flex: '0 0 auto',\n      height: '100%',\n      display: 'inline-flex',\n      alignItems: 'center'\n    },\n    [`& .${c.columnHeaderDraggableContainer}`]: {\n      display: 'flex',\n      width: '100%',\n      height: '100%'\n    },\n    [`& .${c.rowReorderCellPlaceholder}`]: {\n      display: 'none'\n    },\n    [`& .${c['columnHeader--dragging']}, & .${c['row--dragging']}`]: {\n      background: vars.colors.background.overlay,\n      padding: '0 12px',\n      borderRadius: 'var(--unstable_DataGrid-radius)',\n      opacity: vars.colors.interactive.disabledOpacity\n    },\n    [`& .${c['row--dragging']}`]: {\n      background: vars.colors.background.overlay,\n      padding: '0 12px',\n      borderRadius: 'var(--unstable_DataGrid-radius)',\n      opacity: vars.colors.interactive.disabledOpacity,\n      [`& .${c.rowReorderCellPlaceholder}`]: {\n        display: 'flex'\n      }\n    },\n    [`& .${c.treeDataGroupingCell}`]: {\n      display: 'flex',\n      alignItems: 'center',\n      width: '100%'\n    },\n    [`& .${c.treeDataGroupingCellToggle}`]: {\n      flex: '0 0 28px',\n      alignSelf: 'stretch',\n      marginRight: vars.spacing(2)\n    },\n    [`& .${c.treeDataGroupingCellLoadingContainer}, .${c.groupingCriteriaCellLoadingContainer}`]: {\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      height: '100%'\n    },\n    [`& .${c.groupingCriteriaCell}`]: {\n      display: 'flex',\n      alignItems: 'center',\n      width: '100%'\n    },\n    [`& .${c.groupingCriteriaCellToggle}`]: {\n      flex: '0 0 28px',\n      alignSelf: 'stretch',\n      marginRight: vars.spacing(2)\n    },\n    /* ScrollbarFiller styles */\n    [`& .${c.columnHeaders} .${c.scrollbarFiller}`]: {\n      backgroundColor: headerBackground\n    },\n    [`.${c.scrollbarFiller}`]: {\n      minWidth: 'calc(var(--DataGrid-hasScrollY) * var(--DataGrid-scrollbarSize))',\n      alignSelf: 'stretch',\n      [`&.${c['scrollbarFiller--borderTop']}`]: {\n        borderTop: '1px solid var(--DataGrid-rowBorderColor)'\n      },\n      [`&.${c['scrollbarFiller--borderBottom']}`]: {\n        borderBottom: '1px solid var(--DataGrid-rowBorderColor)'\n      },\n      [`&.${c['scrollbarFiller--pinnedRight']}`]: {\n        backgroundColor: vars.cell.background.pinned,\n        position: 'sticky',\n        right: 0\n      }\n    },\n    [`& .${c.filler}`]: {\n      flex: '1 0 auto'\n    },\n    [`& .${c['filler--borderBottom']}`]: {\n      borderBottom: '1px solid var(--DataGrid-rowBorderColor)'\n    },\n    [`& .${c.columnHeaders} .${c.filler}`]: {\n      backgroundColor: headerBackground\n    },\n    /* Hide grid rows, row filler, and vertical scrollbar. Used when skeleton/no columns overlay is visible */\n    [`& .${c['main--hiddenContent']}`]: {\n      [`& .${c.virtualScrollerContent}`]: {\n        // We use visibility hidden so that the virtual scroller content retains its height.\n        // Position fixed is used to remove the virtual scroller content from the flow.\n        // https://github.com/mui/mui-x/issues/14061\n        position: 'fixed',\n        visibility: 'hidden'\n      },\n      [`& .${c['scrollbar--vertical']}, & .${c.pinnedRows}, & .${c.virtualScroller} > .${c.filler}`]: {\n        display: 'none'\n      }\n    }\n  };\n  return gridStyle;\n});\nfunction setOpacity(color, opacity) {\n  return `rgba(from ${color} r g b / ${opacity})`;\n}\nfunction removeOpacity(color) {\n  return setOpacity(color, 1);\n}\nfunction mix(background, overlay, opacity) {\n  return `color-mix(in srgb,${background}, ${overlay} calc(${opacity} * 100%))`;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}