{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { GridRoot } from \"../components/index.js\";\nimport { useGridAriaAttributes } from \"../hooks/utils/useGridAriaAttributes.js\";\nimport { useGridRowAriaAttributes } from \"../hooks/features/rows/useGridRowAriaAttributes.js\";\nimport { GridContextProvider } from \"../context/GridContextProvider.js\";\nimport { useDataGridComponent } from \"./useDataGridComponent.js\";\nimport { useDataGridProps } from \"./useDataGridProps.js\";\nimport { propValidatorsDataGrid, validateProps } from \"../internals/utils/propValidation.js\";\nimport { useMaterialCSSVariables } from \"../material/variables.js\";\nimport { useGridApiInitialization } from \"../hooks/core/useGridApiInitialization.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst configuration = {\n  hooks: {\n    useCSSVariables: useMaterialCSSVariables,\n    useGridAriaAttributes,\n    useGridRowAriaAttributes,\n    useCellAggregationResult: () => null\n  }\n};\nconst DataGridRaw = function DataGrid(inProps, ref) {\n  const props = useDataGridProps(inProps);\n  const privateApiRef = useGridApiInitialization(props.apiRef, props);\n  useDataGridComponent(privateApiRef, props);\n  if (process.env.NODE_ENV !== 'production') {\n    validateProps(props, propValidatorsDataGrid);\n  }\n  return /*#__PURE__*/_jsx(GridContextProvider, {\n    privateApiRef: privateApiRef,\n    configuration: configuration,\n    props: props,\n    children: /*#__PURE__*/_jsx(GridRoot, _extends({\n      className: props.className,\n      style: props.style,\n      sx: props.sx\n    }, props.slotProps?.root, {\n      ref: ref\n    }))\n  });\n};\nif (process.env.NODE_ENV !== \"production\") DataGridRaw.displayName = \"DataGridRaw\";\n/**\n * Features:\n * - [DataGrid](https://mui.com/x/react-data-grid/features/)\n *\n * API:\n * - [DataGrid API](https://mui.com/x/api/data-grid/data-grid/)\n */\nexport const DataGrid = /*#__PURE__*/React.memo(forwardRef(DataGridRaw));\nif (process.env.NODE_ENV !== \"production\") DataGrid.displayName = \"DataGrid\";\nDataGridRaw.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * The ref object that allows Data Grid manipulation. Can be instantiated with `useGridApiRef()`.\n   */\n  apiRef: PropTypes.shape({\n    current: PropTypes.object\n  }),\n  /**\n   * The `aria-label` of the Data Grid.\n   */\n  'aria-label': PropTypes.string,\n  /**\n   * The `id` of the element containing a label for the Data Grid.\n   */\n  'aria-labelledby': PropTypes.string,\n  /**\n   * If `true`, the Data Grid height is dynamic and follows the number of rows in the Data Grid.\n   * @default false\n   * @deprecated Use flex parent container instead: https://mui.com/x/react-data-grid/layout/#flex-parent-container\n   * @example\n   * <div style={{ display: 'flex', flexDirection: 'column' }}>\n   *   <DataGrid />\n   * </div>\n   */\n  autoHeight: PropTypes.bool,\n  /**\n   * If `true`, the pageSize is calculated according to the container size and the max number of rows to avoid rendering a vertical scroll bar.\n   * @default false\n   */\n  autoPageSize: PropTypes.bool,\n  /**\n   * If `true`, columns are autosized after the datagrid is mounted.\n   * @default false\n   */\n  autosizeOnMount: PropTypes.bool,\n  /**\n   * The options for autosize when user-initiated.\n   */\n  autosizeOptions: PropTypes.shape({\n    columns: PropTypes.arrayOf(PropTypes.string),\n    disableColumnVirtualization: PropTypes.bool,\n    expand: PropTypes.bool,\n    includeHeaders: PropTypes.bool,\n    includeOutliers: PropTypes.bool,\n    outliersFactor: PropTypes.number\n  }),\n  /**\n   * Controls the modes of the cells.\n   */\n  cellModesModel: PropTypes.object,\n  /**\n   * If `true`, the Data Grid will display an extra column with checkboxes for selecting rows.\n   * @default false\n   */\n  checkboxSelection: PropTypes.bool,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  className: PropTypes.string,\n  /**\n   * The character used to separate cell values when copying to the clipboard.\n   * @default '\\t'\n   */\n  clipboardCopyCellDelimiter: PropTypes.string,\n  /**\n   * Column region in pixels to render before/after the viewport\n   * @default 150\n   */\n  columnBufferPx: PropTypes.number,\n  /**\n   * Sets the height in pixels of the column group headers in the Data Grid.\n   * Inherits the `columnHeaderHeight` value if not set.\n   */\n  columnGroupHeaderHeight: PropTypes.number,\n  columnGroupingModel: PropTypes.arrayOf(PropTypes.object),\n  /**\n   * Sets the height in pixel of the column headers in the Data Grid.\n   * @default 56\n   */\n  columnHeaderHeight: PropTypes.number,\n  /**\n   * Set of columns of type [[GridColDef]][].\n   */\n  columns: PropTypes.arrayOf(PropTypes.object).isRequired,\n  /**\n   * Set the column visibility model of the Data Grid.\n   * If defined, the Data Grid will ignore the `hide` property in [[GridColDef]].\n   */\n  columnVisibilityModel: PropTypes.object,\n  /**\n   * The data source object.\n   */\n  dataSource: PropTypes.shape({\n    getRows: PropTypes.func.isRequired,\n    updateRow: PropTypes.func\n  }),\n  /**\n   * Data source cache object.\n   */\n  dataSourceCache: PropTypes.shape({\n    clear: PropTypes.func.isRequired,\n    get: PropTypes.func.isRequired,\n    set: PropTypes.func.isRequired\n  }),\n  /**\n   * Set the density of the Data Grid.\n   * @default \"standard\"\n   */\n  density: PropTypes.oneOf(['comfortable', 'compact', 'standard']),\n  /**\n   * If `true`, column autosizing on header separator double-click is disabled.\n   * @default false\n   */\n  disableAutosize: PropTypes.bool,\n  /**\n   * If `true`, column filters are disabled.\n   * @default false\n   */\n  disableColumnFilter: PropTypes.bool,\n  /**\n   * If `true`, the column menu is disabled.\n   * @default false\n   */\n  disableColumnMenu: PropTypes.bool,\n  /**\n   * If `true`, resizing columns is disabled.\n   * @default false\n   */\n  disableColumnResize: PropTypes.bool,\n  /**\n   * If `true`, hiding/showing columns is disabled.\n   * @default false\n   */\n  disableColumnSelector: PropTypes.bool,\n  /**\n   * If `true`, the column sorting feature will be disabled.\n   * @default false\n   */\n  disableColumnSorting: PropTypes.bool,\n  /**\n   * If `true`, the density selector is disabled.\n   * @default false\n   */\n  disableDensitySelector: PropTypes.bool,\n  /**\n   * If `true`, `eval()` is not used for performance optimization.\n   * @default false\n   */\n  disableEval: PropTypes.bool,\n  /**\n   * If `true`, multiple selection using the Ctrl/CMD or Shift key is disabled.\n   * The MIT DataGrid will ignore this prop, unless `checkboxSelection` is enabled.\n   * @default false (`!props.checkboxSelection` for MIT Data Grid)\n   */\n  disableMultipleRowSelection: PropTypes.bool,\n  /**\n   * If `true`, the selection on click on a row or cell is disabled.\n   * @default false\n   */\n  disableRowSelectionOnClick: PropTypes.bool,\n  /**\n   * If `true`, the virtualization is disabled.\n   * @default false\n   */\n  disableVirtualization: PropTypes.bool,\n  /**\n   * Controls whether to use the cell or row editing.\n   * @default \"cell\"\n   */\n  editMode: PropTypes.oneOf(['cell', 'row']),\n  /**\n   * Use if the actual rowCount is not known upfront, but an estimation is available.\n   * If some rows have children (for instance in the tree data), this number represents the amount of top level rows.\n   * Applicable only with `paginationMode=\"server\"` and when `rowCount=\"-1\"`\n   */\n  estimatedRowCount: PropTypes.number,\n  /**\n   * Unstable features, breaking changes might be introduced.\n   * For each feature, if the flag is not explicitly set to `true`, the feature will be fully disabled and any property / method call will not have any effect.\n   */\n  experimentalFeatures: PropTypes.shape({\n    warnIfFocusStateIsNotSynced: PropTypes.bool\n  }),\n  /**\n   * The milliseconds delay to wait after a keystroke before triggering filtering.\n   * @default 150\n   */\n  filterDebounceMs: PropTypes.number,\n  /**\n   * Filtering can be processed on the server or client-side.\n   * Set it to 'server' if you would like to handle filtering on the server-side.\n   * @default \"client\"\n   */\n  filterMode: PropTypes.oneOf(['client', 'server']),\n  /**\n   * Set the filter model of the Data Grid.\n   */\n  filterModel: PropTypes.shape({\n    items: PropTypes.arrayOf(PropTypes.shape({\n      field: PropTypes.string.isRequired,\n      id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n      operator: PropTypes.string.isRequired,\n      value: PropTypes.any\n    })).isRequired,\n    logicOperator: PropTypes.oneOf(['and', 'or']),\n    quickFilterExcludeHiddenColumns: PropTypes.bool,\n    quickFilterLogicOperator: PropTypes.oneOf(['and', 'or']),\n    quickFilterValues: PropTypes.array\n  }),\n  /**\n   * Function that applies CSS classes dynamically on cells.\n   * @param {GridCellParams} params With all properties from [[GridCellParams]].\n   * @returns {string} The CSS class to apply to the cell.\n   */\n  getCellClassName: PropTypes.func,\n  /**\n   * Function that returns the element to render in row detail.\n   * @param {GridRowParams} params With all properties from [[GridRowParams]].\n   * @returns {React.JSX.Element} The row detail element.\n   */\n  getDetailPanelContent: PropTypes.func,\n  /**\n   * Function that returns the estimated height for a row.\n   * Only works if dynamic row height is used.\n   * Once the row height is measured this value is discarded.\n   * @param {GridRowHeightParams} params With all properties from [[GridRowHeightParams]].\n   * @returns {number | null} The estimated row height value. If `null` or `undefined` then the default row height, based on the density, is applied.\n   */\n  getEstimatedRowHeight: PropTypes.func,\n  /**\n   * Function that applies CSS classes dynamically on rows.\n   * @param {GridRowClassNameParams} params With all properties from [[GridRowClassNameParams]].\n   * @returns {string} The CSS class to apply to the row.\n   */\n  getRowClassName: PropTypes.func,\n  /**\n   * Function that sets the row height per row.\n   * @param {GridRowHeightParams} params With all properties from [[GridRowHeightParams]].\n   * @returns {GridRowHeightReturnValue} The row height value. If `null` or `undefined` then the default row height is applied. If \"auto\" then the row height is calculated based on the content.\n   */\n  getRowHeight: PropTypes.func,\n  /**\n   * Return the id of a given [[GridRowModel]].\n   * Ensure the reference of this prop is stable to avoid performance implications.\n   * It could be done by either defining the prop outside of the component or by memoizing it.\n   */\n  getRowId: PropTypes.func,\n  /**\n   * Function that allows to specify the spacing between rows.\n   * @param {GridRowSpacingParams} params With all properties from [[GridRowSpacingParams]].\n   * @returns {GridRowSpacing} The row spacing values.\n   */\n  getRowSpacing: PropTypes.func,\n  /**\n   * If `true`, the footer component is hidden.\n   * @default false\n   */\n  hideFooter: PropTypes.bool,\n  /**\n   * If `true`, the pagination component in the footer is hidden.\n   * @default false\n   */\n  hideFooterPagination: PropTypes.bool,\n  /**\n   * If `true`, the selected row count in the footer is hidden.\n   * @default false\n   */\n  hideFooterSelectedRowCount: PropTypes.bool,\n  /**\n   * If `true`, the diacritics (accents) are ignored when filtering or quick filtering.\n   * E.g. when filter value is `cafe`, the rows with `café` will be visible.\n   * @default false\n   */\n  ignoreDiacritics: PropTypes.bool,\n  /**\n   * If `true`, the Data Grid will not use `valueFormatter` when exporting to CSV or copying to clipboard.\n   * If an object is provided, you can choose to ignore the `valueFormatter` for CSV export or clipboard export.\n   * @default false\n   */\n  ignoreValueFormatterDuringExport: PropTypes.oneOfType([PropTypes.shape({\n    clipboardExport: PropTypes.bool,\n    csvExport: PropTypes.bool\n  }), PropTypes.bool]),\n  /**\n   * The initial state of the DataGrid.\n   * The data in it will be set in the state on initialization but will not be controlled.\n   * If one of the data in `initialState` is also being controlled, then the control state wins.\n   */\n  initialState: PropTypes.object,\n  /**\n   * Callback fired when a cell is rendered, returns true if the cell is editable.\n   * @param {GridCellParams} params With all properties from [[GridCellParams]].\n   * @returns {boolean} A boolean indicating if the cell is editable.\n   */\n  isCellEditable: PropTypes.func,\n  /**\n   * Determines if a row can be selected.\n   * @param {GridRowParams} params With all properties from [[GridRowParams]].\n   * @returns {boolean} A boolean indicating if the row is selectable.\n   */\n  isRowSelectable: PropTypes.func,\n  /**\n   * If `true`, the selection model will retain selected rows that do not exist.\n   * Useful when using server side pagination and row selections need to be retained\n   * when changing pages.\n   * @default false\n   */\n  keepNonExistentRowsSelected: PropTypes.bool,\n  /**\n   * The label of the Data Grid.\n   * If the `showToolbar` prop is `true`, the label will be displayed in the toolbar and applied to the `aria-label` attribute of the grid.\n   * If the `showToolbar` prop is `false`, the label will not be visible but will be applied to the `aria-label` attribute of the grid.\n   */\n  label: PropTypes.string,\n  /**\n   * If `true`, a loading overlay is displayed.\n   * @default false\n   */\n  loading: PropTypes.bool,\n  /**\n   * Set the locale text of the Data Grid.\n   * You can find all the translation keys supported in [the source](https://github.com/mui/mui-x/blob/HEAD/packages/x-data-grid/src/constants/localeTextConstants.ts) in the GitHub repository.\n   */\n  localeText: PropTypes.object,\n  /**\n   * Pass a custom logger in the components that implements the [[Logger]] interface.\n   * @default console\n   */\n  logger: PropTypes.shape({\n    debug: PropTypes.func.isRequired,\n    error: PropTypes.func.isRequired,\n    info: PropTypes.func.isRequired,\n    warn: PropTypes.func.isRequired\n  }),\n  /**\n   * Allows to pass the logging level or false to turn off logging.\n   * @default \"error\" (\"warn\" in dev mode)\n   */\n  logLevel: PropTypes.oneOf(['debug', 'error', 'info', 'warn', false]),\n  /**\n   * Nonce of the inline styles for [Content Security Policy](https://www.w3.org/TR/2016/REC-CSP2-20161215/#script-src-the-nonce-attribute).\n   */\n  nonce: PropTypes.string,\n  /**\n   * Callback fired when any cell is clicked.\n   * @param {GridCellParams} params With all properties from [[GridCellParams]].\n   * @param {MuiEvent<React.MouseEvent>} event The event object.\n   * @param {GridCallbackDetails} details Additional details for this callback.\n   */\n  onCellClick: PropTypes.func,\n  /**\n   * Callback fired when a double click event comes from a cell element.\n   * @param {GridCellParams} params With all properties from [[GridCellParams]].\n   * @param {MuiEvent<React.MouseEvent>} event The event object.\n   * @param {GridCallbackDetails} details Additional details for this callback.\n   */\n  onCellDoubleClick: PropTypes.func,\n  /**\n   * Callback fired when the cell turns to edit mode.\n   * @param {GridCellParams} params With all properties from [[GridCellParams]].\n   * @param {MuiEvent<React.KeyboardEvent | React.MouseEvent>} event The event that caused this prop to be called.\n   */\n  onCellEditStart: PropTypes.func,\n  /**\n   * Callback fired when the cell turns to view mode.\n   * @param {GridCellParams} params With all properties from [[GridCellParams]].\n   * @param {MuiEvent<MuiBaseEvent>} event The event that caused this prop to be called.\n   */\n  onCellEditStop: PropTypes.func,\n  /**\n   * Callback fired when a keydown event comes from a cell element.\n   * @param {GridCellParams} params With all properties from [[GridCellParams]].\n   * @param {MuiEvent<React.KeyboardEvent>} event The event object.\n   * @param {GridCallbackDetails} details Additional details for this callback.\n   */\n  onCellKeyDown: PropTypes.func,\n  /**\n   * Callback fired when the `cellModesModel` prop changes.\n   * @param {GridCellModesModel} cellModesModel Object containing which cells are in \"edit\" mode.\n   * @param {GridCallbackDetails} details Additional details for this callback.\n   */\n  onCellModesModelChange: PropTypes.func,\n  /**\n   * Callback called when the data is copied to the clipboard.\n   * @param {string} data The data copied to the clipboard.\n   */\n  onClipboardCopy: PropTypes.func,\n  /**\n   * Callback fired when a click event comes from a column header element.\n   * @param {GridColumnHeaderParams} params With all properties from [[GridColumnHeaderParams]].\n   * @param {MuiEvent<React.MouseEvent>} event The event object.\n   * @param {GridCallbackDetails} details Additional details for this callback.\n   */\n  onColumnHeaderClick: PropTypes.func,\n  /**\n   * Callback fired when a contextmenu event comes from a column header element.\n   * @param {GridColumnHeaderParams} params With all properties from [[GridColumnHeaderParams]].\n   * @param {MuiEvent<React.MouseEvent>} event The event object.\n   */\n  onColumnHeaderContextMenu: PropTypes.func,\n  /**\n   * Callback fired when a double click event comes from a column header element.\n   * @param {GridColumnHeaderParams} params With all properties from [[GridColumnHeaderParams]].\n   * @param {MuiEvent<React.MouseEvent>} event The event object.\n   * @param {GridCallbackDetails} details Additional details for this callback.\n   */\n  onColumnHeaderDoubleClick: PropTypes.func,\n  /**\n   * Callback fired when a mouse enter event comes from a column header element.\n   * @param {GridColumnHeaderParams} params With all properties from [[GridColumnHeaderParams]].\n   * @param {MuiEvent<React.MouseEvent>} event The event object.\n   * @param {GridCallbackDetails} details Additional details for this callback.\n   */\n  onColumnHeaderEnter: PropTypes.func,\n  /**\n   * Callback fired when a mouse leave event comes from a column header element.\n   * @param {GridColumnHeaderParams} params With all properties from [[GridColumnHeaderParams]].\n   * @param {MuiEvent<React.MouseEvent>} event The event object.\n   * @param {GridCallbackDetails} details Additional details for this callback.\n   */\n  onColumnHeaderLeave: PropTypes.func,\n  /**\n   * Callback fired when a mouseout event comes from a column header element.\n   * @param {GridColumnHeaderParams} params With all properties from [[GridColumnHeaderParams]].\n   * @param {MuiEvent<React.MouseEvent>} event The event object.\n   * @param {GridCallbackDetails} details Additional details for this callback.\n   */\n  onColumnHeaderOut: PropTypes.func,\n  /**\n   * Callback fired when a mouseover event comes from a column header element.\n   * @param {GridColumnHeaderParams} params With all properties from [[GridColumnHeaderParams]].\n   * @param {MuiEvent<React.MouseEvent>} event The event object.\n   * @param {GridCallbackDetails} details Additional details for this callback.\n   */\n  onColumnHeaderOver: PropTypes.func,\n  /**\n   * Callback fired when a column is reordered.\n   * @param {GridColumnOrderChangeParams} params With all properties from [[GridColumnOrderChangeParams]].\n   * @param {MuiEvent<{}>} event The event object.\n   * @param {GridCallbackDetails} details Additional details for this callback.\n   */\n  onColumnOrderChange: PropTypes.func,\n  /**\n   * Callback fired while a column is being resized.\n   * @param {GridColumnResizeParams} params With all properties from [[GridColumnResizeParams]].\n   * @param {MuiEvent<React.MouseEvent>} event The event object.\n   * @param {GridCallbackDetails} details Additional details for this callback.\n   */\n  onColumnResize: PropTypes.func,\n  /**\n   * Callback fired when the column visibility model changes.\n   * @param {GridColumnVisibilityModel} model The new model.\n   * @param {GridCallbackDetails} details Additional details for this callback.\n   */\n  onColumnVisibilityModelChange: PropTypes.func,\n  /**\n   * Callback fired when the width of a column is changed.\n   * @param {GridColumnResizeParams} params With all properties from [[GridColumnResizeParams]].\n   * @param {MuiEvent<React.MouseEvent>} event The event object.\n   * @param {GridCallbackDetails} details Additional details for this callback.\n   */\n  onColumnWidthChange: PropTypes.func,\n  /**\n   * Callback fired when a data source request fails.\n   * @param {GridGetRowsError | GridUpdateRowError} error The data source error object.\n   */\n  onDataSourceError: PropTypes.func,\n  /**\n   * Callback fired when the density changes.\n   * @param {GridDensity} density New density value.\n   */\n  onDensityChange: PropTypes.func,\n  /**\n   * Callback fired when the Filter model changes before the filters are applied.\n   * @param {GridFilterModel} model With all properties from [[GridFilterModel]].\n   * @param {GridCallbackDetails} details Additional details for this callback.\n   */\n  onFilterModelChange: PropTypes.func,\n  /**\n   * Callback fired when the menu is closed.\n   * @param {GridMenuParams} params With all properties from [[GridMenuParams]].\n   * @param {MuiEvent<{}>} event The event object.\n   * @param {GridCallbackDetails} details Additional details for this callback.\n   */\n  onMenuClose: PropTypes.func,\n  /**\n   * Callback fired when the menu is opened.\n   * @param {GridMenuParams} params With all properties from [[GridMenuParams]].\n   * @param {MuiEvent<{}>} event The event object.\n   * @param {GridCallbackDetails} details Additional details for this callback.\n   */\n  onMenuOpen: PropTypes.func,\n  /**\n   * Callback fired when the pagination meta has changed.\n   * @param {GridPaginationMeta} paginationMeta Updated pagination meta.\n   */\n  onPaginationMetaChange: PropTypes.func,\n  /**\n   * Callback fired when the pagination model has changed.\n   * @param {GridPaginationModel} model Updated pagination model.\n   * @param {GridCallbackDetails} details Additional details for this callback.\n   */\n  onPaginationModelChange: PropTypes.func,\n  /**\n   * Callback fired when the preferences panel is closed.\n   * @param {GridPreferencePanelParams} params With all properties from [[GridPreferencePanelParams]].\n   * @param {MuiEvent<{}>} event The event object.\n   * @param {GridCallbackDetails} details Additional details for this callback.\n   */\n  onPreferencePanelClose: PropTypes.func,\n  /**\n   * Callback fired when the preferences panel is opened.\n   * @param {GridPreferencePanelParams} params With all properties from [[GridPreferencePanelParams]].\n   * @param {MuiEvent<{}>} event The event object.\n   * @param {GridCallbackDetails} details Additional details for this callback.\n   */\n  onPreferencePanelOpen: PropTypes.func,\n  /**\n   * Callback called when `processRowUpdate` throws an error or rejects.\n   * @param {any} error The error thrown.\n   */\n  onProcessRowUpdateError: PropTypes.func,\n  /**\n   * Callback fired when the Data Grid is resized.\n   * @param {ElementSize} containerSize With all properties from [[ElementSize]].\n   * @param {MuiEvent<{}>} event The event object.\n   * @param {GridCallbackDetails} details Additional details for this callback.\n   */\n  onResize: PropTypes.func,\n  /**\n   * Callback fired when a row is clicked.\n   * Not called if the target clicked is an interactive element added by the built-in columns.\n   * @param {GridRowParams} params With all properties from [[GridRowParams]].\n   * @param {MuiEvent<React.MouseEvent>} event The event object.\n   * @param {GridCallbackDetails} details Additional details for this callback.\n   */\n  onRowClick: PropTypes.func,\n  /**\n   * Callback fired when the row count has changed.\n   * @param {number} count Updated row count.\n   */\n  onRowCountChange: PropTypes.func,\n  /**\n   * Callback fired when a double click event comes from a row container element.\n   * @param {GridRowParams} params With all properties from [[RowParams]].\n   * @param {MuiEvent<React.MouseEvent>} event The event object.\n   * @param {GridCallbackDetails} details Additional details for this callback.\n   */\n  onRowDoubleClick: PropTypes.func,\n  /**\n   * Callback fired when the row turns to edit mode.\n   * @param {GridRowParams} params With all properties from [[GridRowParams]].\n   * @param {MuiEvent<React.KeyboardEvent | React.MouseEvent>} event The event that caused this prop to be called.\n   */\n  onRowEditStart: PropTypes.func,\n  /**\n   * Callback fired when the row turns to view mode.\n   * @param {GridRowParams} params With all properties from [[GridRowParams]].\n   * @param {MuiEvent<MuiBaseEvent>} event The event that caused this prop to be called.\n   */\n  onRowEditStop: PropTypes.func,\n  /**\n   * Callback fired when the `rowModesModel` prop changes.\n   * @param {GridRowModesModel} rowModesModel Object containing which rows are in \"edit\" mode.\n   * @param {GridCallbackDetails} details Additional details for this callback.\n   */\n  onRowModesModelChange: PropTypes.func,\n  /**\n   * Callback fired when the selection state of one or multiple rows changes.\n   * @param {GridRowSelectionModel} rowSelectionModel With all the row ids [[GridSelectionModel]].\n   * @param {GridCallbackDetails} details Additional details for this callback.\n   */\n  onRowSelectionModelChange: PropTypes.func,\n  /**\n   * Callback fired when the sort model changes before a column is sorted.\n   * @param {GridSortModel} model With all properties from [[GridSortModel]].\n   * @param {GridCallbackDetails} details Additional details for this callback.\n   */\n  onSortModelChange: PropTypes.func,\n  /**\n   * Callback fired when the state of the Data Grid is updated.\n   * @param {GridState} state The new state.\n   * @param {MuiEvent<{}>} event The event object.\n   * @param {GridCallbackDetails} details Additional details for this callback.\n   * @ignore - do not document.\n   */\n  onStateChange: PropTypes.func,\n  /**\n   * Select the pageSize dynamically using the component UI.\n   * @default [25, 50, 100]\n   */\n  pageSizeOptions: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    label: PropTypes.string.isRequired,\n    value: PropTypes.number.isRequired\n  })]).isRequired),\n  pagination: PropTypes.oneOf([true]),\n  /**\n   * The extra information about the pagination state of the Data Grid.\n   * Only applicable with `paginationMode=\"server\"`.\n   */\n  paginationMeta: PropTypes.shape({\n    hasNextPage: PropTypes.bool\n  }),\n  /**\n   * Pagination can be processed on the server or client-side.\n   * Set it to 'client' if you would like to handle the pagination on the client-side.\n   * Set it to 'server' if you would like to handle the pagination on the server-side.\n   * @default \"client\"\n   */\n  paginationMode: PropTypes.oneOf(['client', 'server']),\n  /**\n   * The pagination model of type [[GridPaginationModel]] which refers to current `page` and `pageSize`.\n   */\n  paginationModel: PropTypes.shape({\n    page: PropTypes.number.isRequired,\n    pageSize: PropTypes.number.isRequired\n  }),\n  /**\n   * Callback called before updating a row with new values in the row and cell editing.\n   * @template R\n   * @param {R} newRow Row object with the new values.\n   * @param {R} oldRow Row object with the old values.\n   * @param {{ rowId: GridRowId }} params Additional parameters.\n   * @returns {Promise<R> | R} The final values to update the row.\n   */\n  processRowUpdate: PropTypes.func,\n  /**\n   * The milliseconds throttle delay for resizing the grid.\n   * @default 60\n   */\n  resizeThrottleMs: PropTypes.number,\n  /**\n   * Row region in pixels to render before/after the viewport\n   * @default 150\n   */\n  rowBufferPx: PropTypes.number,\n  /**\n   * Set the total number of rows, if it is different from the length of the value `rows` prop.\n   * If some rows have children (for instance in the tree data), this number represents the amount of top level rows.\n   * Only works with `paginationMode=\"server\"`, ignored when `paginationMode=\"client\"`.\n   */\n  rowCount: PropTypes.number,\n  /**\n   * Sets the height in pixel of a row in the Data Grid.\n   * @default 52\n   */\n  rowHeight: PropTypes.number,\n  /**\n   * Controls the modes of the rows.\n   */\n  rowModesModel: PropTypes.object,\n  /**\n   * Set of rows of type [[GridRowsProp]].\n   * @default []\n   */\n  rows: PropTypes.arrayOf(PropTypes.object),\n  /**\n   * If `false`, the row selection mode is disabled.\n   * @default true\n   */\n  rowSelection: PropTypes.bool,\n  /**\n   * Sets the row selection model of the Data Grid.\n   */\n  rowSelectionModel: PropTypes /* @typescript-to-proptypes-ignore */.shape({\n    ids: PropTypes.instanceOf(Set).isRequired,\n    type: PropTypes.oneOf(['exclude', 'include']).isRequired\n  }),\n  /**\n   * Sets the type of space between rows added by `getRowSpacing`.\n   * @default \"margin\"\n   */\n  rowSpacingType: PropTypes.oneOf(['border', 'margin']),\n  /**\n   * If `true`, the Data Grid will auto span the cells over the rows having the same value.\n   * @default false\n   */\n  rowSpanning: PropTypes.bool,\n  /**\n   * Override the height/width of the Data Grid inner scrollbar.\n   */\n  scrollbarSize: PropTypes.number,\n  /**\n   * If `true`, vertical borders will be displayed between cells.\n   * @default false\n   */\n  showCellVerticalBorder: PropTypes.bool,\n  /**\n   * If `true`, vertical borders will be displayed between column header items.\n   * @default false\n   */\n  showColumnVerticalBorder: PropTypes.bool,\n  /**\n   * If `true`, the toolbar is displayed.\n   * @default false\n   */\n  showToolbar: PropTypes.bool,\n  /**\n   * Overridable components props dynamically passed to the component at rendering.\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable components.\n   */\n  slots: PropTypes.object,\n  /**\n   * Sorting can be processed on the server or client-side.\n   * Set it to 'client' if you would like to handle sorting on the client-side.\n   * Set it to 'server' if you would like to handle sorting on the server-side.\n   * @default \"client\"\n   */\n  sortingMode: PropTypes.oneOf(['client', 'server']),\n  /**\n   * The order of the sorting sequence.\n   * @default ['asc', 'desc', null]\n   */\n  sortingOrder: PropTypes.arrayOf(PropTypes.oneOf(['asc', 'desc'])),\n  /**\n   * Set the sort model of the Data Grid.\n   */\n  sortModel: PropTypes.arrayOf(PropTypes.shape({\n    field: PropTypes.string.isRequired,\n    sort: PropTypes.oneOf(['asc', 'desc'])\n  })),\n  style: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * If `true`, the Data Grid enables column virtualization when `getRowHeight` is set to `() => 'auto'`.\n   * By default, column virtualization is disabled when dynamic row height is enabled to measure the row height correctly.\n   * For datasets with a large number of columns, this can cause performance issues.\n   * The downside of enabling this prop is that the row height will be estimated based the cells that are currently rendered, which can cause row height change when scrolling horizontally.\n   * @default false\n   */\n  virtualizeColumnsWithAutoRowHeight: PropTypes.bool\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}