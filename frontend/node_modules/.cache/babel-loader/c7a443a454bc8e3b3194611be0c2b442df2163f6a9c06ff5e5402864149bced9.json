{"ast": null, "code": "export function isLeaf(node) {\n  return node.field !== undefined;\n}\n\n/**\n * A function used to process headerClassName params.\n * @param {GridColumnGroupHeaderParams} params The parameters of the column group header.\n * @returns {string} The class name to be added to the column group header cell.\n */\n\n/**\n * The union type representing the [[GridColDef]] column header class type.\n */", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}