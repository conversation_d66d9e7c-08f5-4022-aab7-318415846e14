{"ast": null, "code": "export const getDefaultCellValue = colDef => {\n  switch (colDef.type) {\n    case 'boolean':\n      return false;\n    case 'date':\n    case 'dateTime':\n    case 'number':\n      return undefined;\n    case 'singleSelect':\n      return null;\n    case 'string':\n    default:\n      return '';\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}