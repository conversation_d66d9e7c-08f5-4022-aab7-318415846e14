{"ast": null, "code": "import { renderEditInputCell } from \"../components/cell/GridEditInputCell.js\";\nimport { gridStringOrNumberComparator } from \"../hooks/features/sorting/gridSortingUtils.js\";\nimport { getGridStringOperators, getGridStringQuickFilterFn } from \"./gridStringOperators.js\";\n\n/**\n * TODO: Move pro and premium properties outside of this Community file\n */\nexport const GRID_STRING_COL_DEF = {\n  width: 100,\n  minWidth: 50,\n  maxWidth: Infinity,\n  hideable: true,\n  sortable: true,\n  resizable: true,\n  filterable: true,\n  groupable: true,\n  pinnable: true,\n  // @ts-ignore\n  aggregable: true,\n  editable: false,\n  sortComparator: gridStringOrNumberComparator,\n  type: 'string',\n  align: 'left',\n  filterOperators: getGridStringOperators(),\n  renderEditCell: renderEditInputCell,\n  getApplyQuickFilterFn: getGridStringQuickFilterFn\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}