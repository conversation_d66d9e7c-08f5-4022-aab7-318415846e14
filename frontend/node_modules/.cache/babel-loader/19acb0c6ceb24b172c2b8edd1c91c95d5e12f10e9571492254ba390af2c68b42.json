{"ast": null, "code": "import { createSelector, createRootSelector, createSelectorMemoized } from \"../../../utils/createSelector.js\";\nimport { gridExpandedSortedRowEntriesSelector, gridExpandedSortedRowIdsSelector, gridFilteredSortedTopLevelRowEntriesSelector } from \"../filter/gridFilterSelector.js\";\nimport { gridRowMaximumTreeDepthSelector, gridRowTreeSelector } from \"../rows/gridRowsSelector.js\";\nimport { getPageCount } from \"./gridPaginationUtils.js\";\nconst ALL_RESULTS_PAGE_VALUE = -1;\n\n/**\n * @category Pagination\n * @ignore - do not document.\n */\nexport const gridPaginationSelector = createRootSelector(state => state.pagination);\n\n/**\n * @category Pagination\n * @ignore - do not document.\n */\nexport const gridPaginationEnabledClientSideSelector = createSelector(gridPaginationSelector, pagination => pagination.enabled && pagination.paginationMode === 'client');\n\n/**\n * Get the pagination model\n * @category Pagination\n */\nexport const gridPaginationModelSelector = createSelector(gridPaginationSelector, pagination => pagination.paginationModel);\n\n/**\n * Get the row count\n * @category Pagination\n */\nexport const gridPaginationRowCountSelector = createSelector(gridPaginationSelector, pagination => pagination.rowCount);\n\n/**\n * Get the pagination meta\n * @category Pagination\n */\nexport const gridPaginationMetaSelector = createSelector(gridPaginationSelector, pagination => pagination.meta);\n\n/**\n * Get the index of the page to render if the pagination is enabled\n * @category Pagination\n */\nexport const gridPageSelector = createSelector(gridPaginationModelSelector, paginationModel => paginationModel.page);\n\n/**\n * Get the maximum amount of rows to display on a single page if the pagination is enabled\n * @category Pagination\n */\nexport const gridPageSizeSelector = createSelector(gridPaginationModelSelector, paginationModel => paginationModel.pageSize);\n\n/**\n * Get the amount of pages needed to display all the rows if the pagination is enabled\n * @category Pagination\n */\nexport const gridPageCountSelector = createSelector(gridPaginationModelSelector, gridPaginationRowCountSelector, (paginationModel, rowCount) => getPageCount(rowCount, paginationModel.pageSize, paginationModel.page));\n\n/**\n * Get the index of the first and the last row to include in the current page if the pagination is enabled.\n * @category Pagination\n */\nexport const gridPaginationRowRangeSelector = createSelectorMemoized(gridPaginationEnabledClientSideSelector, gridPaginationModelSelector, gridRowTreeSelector, gridRowMaximumTreeDepthSelector, gridExpandedSortedRowEntriesSelector, gridFilteredSortedTopLevelRowEntriesSelector, (clientSidePaginationEnabled, paginationModel, rowTree, rowTreeDepth, visibleSortedRowEntries, visibleSortedTopLevelRowEntries) => {\n  if (!clientSidePaginationEnabled) {\n    return null;\n  }\n  const visibleTopLevelRowCount = visibleSortedTopLevelRowEntries.length;\n  const topLevelFirstRowIndex = Math.min(paginationModel.pageSize * paginationModel.page, visibleTopLevelRowCount - 1);\n  const topLevelLastRowIndex = paginationModel.pageSize === ALL_RESULTS_PAGE_VALUE ? visibleTopLevelRowCount - 1 : Math.min(topLevelFirstRowIndex + paginationModel.pageSize - 1, visibleTopLevelRowCount - 1);\n\n  // The range contains no element\n  if (topLevelFirstRowIndex === -1 || topLevelLastRowIndex === -1) {\n    return null;\n  }\n\n  // The tree is flat, there is no need to look for children\n  if (rowTreeDepth < 2) {\n    return {\n      firstRowIndex: topLevelFirstRowIndex,\n      lastRowIndex: topLevelLastRowIndex\n    };\n  }\n  const topLevelFirstRow = visibleSortedTopLevelRowEntries[topLevelFirstRowIndex];\n  const topLevelRowsInCurrentPageCount = topLevelLastRowIndex - topLevelFirstRowIndex + 1;\n  const firstRowIndex = visibleSortedRowEntries.findIndex(row => row.id === topLevelFirstRow.id);\n  let lastRowIndex = firstRowIndex;\n  let topLevelRowAdded = 0;\n  while (lastRowIndex < visibleSortedRowEntries.length && topLevelRowAdded <= topLevelRowsInCurrentPageCount) {\n    const row = visibleSortedRowEntries[lastRowIndex];\n    const depth = rowTree[row.id]?.depth;\n    if (depth === undefined) {\n      lastRowIndex += 1;\n    } else {\n      if (topLevelRowAdded < topLevelRowsInCurrentPageCount || depth > 0) {\n        lastRowIndex += 1;\n      }\n      if (depth === 0) {\n        topLevelRowAdded += 1;\n      }\n    }\n  }\n  return {\n    firstRowIndex,\n    lastRowIndex: lastRowIndex - 1\n  };\n});\n\n/**\n * Get the id and the model of each row to include in the current page if the pagination is enabled.\n * @category Pagination\n */\nexport const gridPaginatedVisibleSortedGridRowEntriesSelector = createSelectorMemoized(gridExpandedSortedRowEntriesSelector, gridPaginationRowRangeSelector, (visibleSortedRowEntries, paginationRange) => {\n  if (!paginationRange) {\n    return [];\n  }\n  return visibleSortedRowEntries.slice(paginationRange.firstRowIndex, paginationRange.lastRowIndex + 1);\n});\n\n/**\n * Get the id of each row to include in the current page if the pagination is enabled.\n * @category Pagination\n */\nexport const gridPaginatedVisibleSortedGridRowIdsSelector = createSelectorMemoized(gridExpandedSortedRowIdsSelector, gridPaginationRowRangeSelector, (visibleSortedRowIds, paginationRange) => {\n  if (!paginationRange) {\n    return [];\n  }\n  return visibleSortedRowIds.slice(paginationRange.firstRowIndex, paginationRange.lastRowIndex + 1);\n});\n\n/**\n * Get the rows, range and rowIndex lookup map after filtering and sorting.\n * Does not contain the collapsed children.\n * @category Pagination\n */\nexport const gridVisibleRowsSelector = createSelectorMemoized(gridPaginationEnabledClientSideSelector, gridPaginationRowRangeSelector, gridPaginatedVisibleSortedGridRowEntriesSelector, gridExpandedSortedRowEntriesSelector, (clientPaginationEnabled, paginationRowRange, paginationRows, expandedSortedRowEntries) => {\n  if (clientPaginationEnabled) {\n    return {\n      rows: paginationRows,\n      range: paginationRowRange,\n      rowIdToIndexMap: paginationRows.reduce((lookup, row, index) => {\n        lookup.set(row.id, index);\n        return lookup;\n      }, new Map())\n    };\n  }\n  return {\n    rows: expandedSortedRowEntries,\n    range: expandedSortedRowEntries.length === 0 ? null : {\n      firstRowIndex: 0,\n      lastRowIndex: expandedSortedRowEntries.length - 1\n    },\n    rowIdToIndexMap: expandedSortedRowEntries.reduce((lookup, row, index) => {\n      lookup.set(row.id, index);\n      return lookup;\n    }, new Map())\n  };\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}