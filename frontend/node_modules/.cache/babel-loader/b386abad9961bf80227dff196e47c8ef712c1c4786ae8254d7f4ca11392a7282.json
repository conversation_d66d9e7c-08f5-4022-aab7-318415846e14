{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { styled, keyframes } from '@mui/system';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst reveal = keyframes({\n  from: {\n    opacity: 0\n  },\n  to: {\n    opacity: 1\n  }\n});\nconst detectScroll = keyframes({\n  'from, to': {\n    '--scrollable': '\" \"'\n  }\n});\n\n// This `styled()` function invokes keyframes. `styled-components` only supports keyframes\n// in string templates. Do not convert these styles in JS object as it will break.\nconst ShadowScrollArea = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'ShadowScrollArea'\n})`\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  animation: ${detectScroll};\n  animation-timeline: --scroll-timeline;\n  animation-fill-mode: none;\n  box-sizing: border-box;\n  overflow: auto;\n  scrollbar-width: thin;\n  scroll-timeline: --scroll-timeline block;\n\n  &::before,\n  &::after {\n    content: '';\n    flex-shrink: 0;\n    display: block;\n    position: sticky;\n    left: 0;\n    width: 100%;\n    height: 4px;\n    animation: ${reveal} linear both;\n    animation-timeline: --scroll-timeline;\n\n    // Custom property toggle trick:\n    // - Detects if the element is scrollable\n    // - https://css-tricks.com/the-css-custom-property-toggle-trick/\n    --visibility-scrollable: var(--scrollable) visible;\n    --visibility-not-scrollable: hidden;\n    visibility: var(--visibility-scrollable, var(--visibility-not-scrollable));\n  }\n\n  &::before {\n    top: 0;\n    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.05) 0, transparent 100%);\n    animation-range: 0 4px;\n  }\n\n  &::after {\n    bottom: 0;\n    background: linear-gradient(to top, rgba(0, 0, 0, 0.05) 0, transparent 100%);\n    animation-direction: reverse;\n    animation-range: calc(100% - 4px) 100%;\n  }\n`;\n\n/**\n * Adds scroll shadows above and below content in a scrollable container.\n */\nconst GridShadowScrollArea = forwardRef(function GridShadowScrollArea(props, ref) {\n  const {\n      children\n    } = props,\n    rest = _objectWithoutPropertiesLoose(props, _excluded);\n  return /*#__PURE__*/_jsx(ShadowScrollArea, _extends({}, rest, {\n    ref: ref,\n    children: children\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridShadowScrollArea.displayName = \"GridShadowScrollArea\";\nprocess.env.NODE_ENV !== \"production\" ? GridShadowScrollArea.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  children: PropTypes.node\n} : void 0;\nexport { GridShadowScrollArea };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}