{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"id\", \"value\", \"formattedValue\", \"api\", \"field\", \"row\", \"rowNode\", \"colDef\", \"cellMode\", \"isEditable\", \"hasFocus\", \"tabIndex\", \"hideDescendantCount\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useGridSelector } from \"../../hooks/utils/useGridSelector.js\";\nimport { gridRowMaximumTreeDepthSelector } from \"../../hooks/features/rows/gridRowsSelector.js\";\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { isAutogeneratedRowNode } from \"../../hooks/features/rows/gridRowsUtils.js\";\nimport { GRID_ROW_GROUPING_SINGLE_GROUPING_FIELD } from \"../../internals/constants.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['booleanCell']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nfunction GridBooleanCellRaw(props) {\n  const {\n      value,\n      rowNode\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const ownerState = {\n    classes: rootProps.classes\n  };\n  const classes = useUtilityClasses(ownerState);\n  const maxDepth = useGridSelector(apiRef, gridRowMaximumTreeDepthSelector);\n  const isServerSideRowGroupingRow =\n  // @ts-expect-error - Access tree data prop\n  maxDepth > 0 && rowNode.type === 'group' && rootProps.treeData === false;\n  const Icon = React.useMemo(() => value ? rootProps.slots.booleanCellTrueIcon : rootProps.slots.booleanCellFalseIcon, [rootProps.slots.booleanCellFalseIcon, rootProps.slots.booleanCellTrueIcon, value]);\n  if (isServerSideRowGroupingRow && value === undefined) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(Icon, _extends({\n    fontSize: \"small\",\n    className: classes.root,\n    titleAccess: apiRef.current.getLocaleText(value ? 'booleanCellTrueLabel' : 'booleanCellFalseLabel'),\n    \"data-value\": Boolean(value)\n  }, other));\n}\nprocess.env.NODE_ENV !== \"production\" ? GridBooleanCellRaw.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * GridApi that let you manipulate the grid.\n   */\n  api: PropTypes.object.isRequired,\n  /**\n   * The mode of the cell.\n   */\n  cellMode: PropTypes.oneOf(['edit', 'view']).isRequired,\n  /**\n   * The column of the row that the current cell belongs to.\n   */\n  colDef: PropTypes.object.isRequired,\n  /**\n   * The column field of the cell that triggered the event.\n   */\n  field: PropTypes.string.isRequired,\n  /**\n   * A ref allowing to set imperative focus.\n   * It can be passed to the element that should receive focus.\n   * @ignore - do not document.\n   */\n  focusElementRef: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n    current: PropTypes.shape({\n      focus: PropTypes.func.isRequired\n    })\n  })]),\n  /**\n   * The cell value formatted with the column valueFormatter.\n   */\n  formattedValue: PropTypes.any,\n  /**\n   * If true, the cell is the active element.\n   */\n  hasFocus: PropTypes.bool.isRequired,\n  hideDescendantCount: PropTypes.bool,\n  /**\n   * The grid row id.\n   */\n  id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,\n  /**\n   * If true, the cell is editable.\n   */\n  isEditable: PropTypes.bool,\n  /**\n   * The row model of the row that the current cell belongs to.\n   */\n  row: PropTypes.any.isRequired,\n  /**\n   * The node of the row that the current cell belongs to.\n   */\n  rowNode: PropTypes.object.isRequired,\n  /**\n   * the tabIndex value.\n   */\n  tabIndex: PropTypes.oneOf([-1, 0]).isRequired,\n  /**\n   * The cell value.\n   * If the column has `valueGetter`, use `params.row` to directly access the fields.\n   */\n  value: PropTypes.any\n} : void 0;\nconst GridBooleanCell = /*#__PURE__*/React.memo(GridBooleanCellRaw);\nif (process.env.NODE_ENV !== \"production\") GridBooleanCell.displayName = \"GridBooleanCell\";\nexport { GridBooleanCell };\nexport const renderBooleanCell = params => {\n  if (params.field !== GRID_ROW_GROUPING_SINGLE_GROUPING_FIELD && isAutogeneratedRowNode(params.rowNode)) {\n    return '';\n  }\n  return /*#__PURE__*/_jsx(GridBooleanCell, _extends({}, params));\n};\nif (process.env.NODE_ENV !== \"production\") renderBooleanCell.displayName = \"renderBooleanCell\";", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}