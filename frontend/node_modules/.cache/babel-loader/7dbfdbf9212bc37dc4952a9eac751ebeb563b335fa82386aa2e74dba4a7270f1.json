{"ast": null, "code": "import { PinnedColumnPosition } from \"../internals/constants.js\";\nexport const shouldCellShowRightBorder = (pinnedPosition, indexInSection, sectionLength, showCellVerticalBorderRootProp, gridHasFiller) => {\n  const isSectionLastCell = indexInSection === sectionLength - 1;\n  if (pinnedPosition === PinnedColumnPosition.LEFT && isSectionLastCell) {\n    return true;\n  }\n  if (showCellVerticalBorderRootProp) {\n    if (pinnedPosition === PinnedColumnPosition.LEFT) {\n      return true;\n    }\n    if (pinnedPosition === PinnedColumnPosition.RIGHT) {\n      return !isSectionLastCell;\n    }\n    // pinnedPosition === undefined, middle section\n    return !isSectionLastCell || gridHasFiller;\n  }\n  return false;\n};\nexport const shouldCellShowLeftBorder = (pinnedPosition, indexInSection) => {\n  return pinnedPosition === PinnedColumnPosition.RIGHT && indexInSection === 0;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}