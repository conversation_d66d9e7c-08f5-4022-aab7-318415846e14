{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nexport function isSingleSelectColDef(colDef) {\n  return colDef?.type === 'singleSelect';\n}\nexport function getValueOptions(column, additionalParams) {\n  if (!column) {\n    return undefined;\n  }\n  return typeof column.valueOptions === 'function' ? column.valueOptions(_extends({\n    field: column.field\n  }, additionalParams)) : column.valueOptions;\n}\nexport function getValueFromValueOptions(value, valueOptions, getOptionValue) {\n  if (valueOptions === undefined) {\n    return undefined;\n  }\n  const result = valueOptions.find(option => {\n    const optionValue = getOptionValue(option);\n    return String(optionValue) === String(value);\n  });\n  return getOptionValue(result);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}