{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { warnOnce } from '@mui/x-internals/warning';\nimport { GridLogicOperator } from \"../../../models/index.js\";\nimport { getDefaultGridFilterModel } from \"./gridFilterState.js\";\nimport { getPublicApiRef } from \"../../../utils/getPublicApiRef.js\";\nimport { gridColumnFieldsSelector, gridColumnLookupSelector, gridVisibleColumnFieldsSelector } from \"../columns/index.js\";\nlet hasEval;\nfunction getHasEval() {\n  if (hasEval !== undefined) {\n    return hasEval;\n  }\n  try {\n    // eslint-disable-next-line no-new-func\n    hasEval = new Function('return true')();\n  } catch (_) {\n    hasEval = false;\n  }\n  return hasEval;\n}\n/**\n * Adds default values to the optional fields of a filter items.\n * @param {GridFilterItem} item The raw filter item.\n * @param {RefObject<GridPrivateApiCommunity>} apiRef The API of the grid.\n * @return {GridFilterItem} The clean filter item with an uniq ID and an always-defined operator.\n * TODO: Make the typing reflect the different between GridFilterInputItem and GridFilterItem.\n */\nexport const cleanFilterItem = (item, apiRef) => {\n  const cleanItem = _extends({}, item);\n  if (cleanItem.id == null) {\n    cleanItem.id = Math.round(Math.random() * 1e5);\n  }\n  if (cleanItem.operator == null) {\n    // Selects a default operator\n    // We don't use `apiRef.current.getColumn` because it is not ready during state initialization\n    const column = gridColumnLookupSelector(apiRef)[cleanItem.field];\n    cleanItem.operator = column && column.filterOperators[0].value;\n  }\n  return cleanItem;\n};\nexport const sanitizeFilterModel = (model, disableMultipleColumnsFiltering, apiRef) => {\n  const hasSeveralItems = model.items.length > 1;\n  let items;\n  if (hasSeveralItems && disableMultipleColumnsFiltering) {\n    if (process.env.NODE_ENV !== 'production') {\n      warnOnce(['MUI X: The `filterModel` can only contain a single item when the `disableMultipleColumnsFiltering` prop is set to `true`.', 'If you are using the community version of the Data Grid, this prop is always `true`.'], 'error');\n    }\n    items = [model.items[0]];\n  } else {\n    items = model.items;\n  }\n  const hasItemsWithoutIds = hasSeveralItems && items.some(item => item.id == null);\n  const hasItemWithoutOperator = items.some(item => item.operator == null);\n  if (process.env.NODE_ENV !== 'production') {\n    if (hasItemsWithoutIds) {\n      warnOnce('MUI X: The `id` field is required on `filterModel.items` when you use multiple filters.', 'error');\n    }\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    if (hasItemWithoutOperator) {\n      warnOnce('MUI X: The `operator` field is required on `filterModel.items`, one or more of your filtering item has no `operator` provided.', 'error');\n    }\n  }\n  if (hasItemWithoutOperator || hasItemsWithoutIds) {\n    return _extends({}, model, {\n      items: items.map(item => cleanFilterItem(item, apiRef))\n    });\n  }\n  if (model.items !== items) {\n    return _extends({}, model, {\n      items\n    });\n  }\n  return model;\n};\nexport const mergeStateWithFilterModel = (filterModel, disableMultipleColumnsFiltering, apiRef) => filteringState => _extends({}, filteringState, {\n  filterModel: sanitizeFilterModel(filterModel, disableMultipleColumnsFiltering, apiRef)\n});\nexport const removeDiacritics = value => {\n  if (typeof value === 'string') {\n    return value.normalize('NFD').replace(/[\\u0300-\\u036f]/g, '');\n  }\n  return value;\n};\nconst getFilterCallbackFromItem = (filterItem, apiRef) => {\n  if (!filterItem.field || !filterItem.operator) {\n    return null;\n  }\n  const column = apiRef.current.getColumn(filterItem.field);\n  if (!column) {\n    return null;\n  }\n  let parsedValue;\n  if (column.valueParser) {\n    const parser = column.valueParser;\n    parsedValue = Array.isArray(filterItem.value) ? filterItem.value?.map(x => parser(x, undefined, column, apiRef)) : parser(filterItem.value, undefined, column, apiRef);\n  } else {\n    parsedValue = filterItem.value;\n  }\n  const {\n    ignoreDiacritics\n  } = apiRef.current.rootProps;\n  if (ignoreDiacritics) {\n    parsedValue = removeDiacritics(parsedValue);\n  }\n  const newFilterItem = _extends({}, filterItem, {\n    value: parsedValue\n  });\n  const filterOperators = column.filterOperators;\n  if (!filterOperators?.length) {\n    throw new Error(`MUI X: No filter operators found for column '${column.field}'.`);\n  }\n  const filterOperator = filterOperators.find(operator => operator.value === newFilterItem.operator);\n  if (!filterOperator) {\n    throw new Error(`MUI X: No filter operator found for column '${column.field}' and operator value '${newFilterItem.operator}'.`);\n  }\n  const publicApiRef = getPublicApiRef(apiRef);\n  const applyFilterOnRow = filterOperator.getApplyFilterFn(newFilterItem, column);\n  if (typeof applyFilterOnRow !== 'function') {\n    return null;\n  }\n  return {\n    item: newFilterItem,\n    fn: row => {\n      let value = apiRef.current.getRowValue(row, column);\n      if (ignoreDiacritics) {\n        value = removeDiacritics(value);\n      }\n      return applyFilterOnRow(value, row, column, publicApiRef);\n    }\n  };\n};\nlet filterItemsApplierId = 1;\n\n/**\n * Generates a method to easily check if a row is matching the current filter model.\n * @param {GridFilterModel} filterModel The model with which we want to filter the rows.\n * @param {RefObject<GridPrivateApiCommunity>} apiRef The API of the grid.\n * @returns {GridAggregatedFilterItemApplier | null} A method that checks if a row is matching the current filter model. If `null`, we consider that all the rows are matching the filters.\n */\nconst buildAggregatedFilterItemsApplier = (filterModel, apiRef, disableEval) => {\n  const {\n    items\n  } = filterModel;\n  const appliers = items.map(item => getFilterCallbackFromItem(item, apiRef)).filter(callback => !!callback);\n  if (appliers.length === 0) {\n    return null;\n  }\n  if (disableEval || !getHasEval()) {\n    // This is the original logic, which is used if `eval()` is not supported (aka prevented by CSP).\n    return (row, shouldApplyFilter) => {\n      const resultPerItemId = {};\n      for (let i = 0; i < appliers.length; i += 1) {\n        const applier = appliers[i];\n        if (!shouldApplyFilter || shouldApplyFilter(applier.item.field)) {\n          resultPerItemId[applier.item.id] = applier.fn(row);\n        }\n      }\n      return resultPerItemId;\n    };\n  }\n\n  // We generate a new function with `new Function()` to avoid expensive patterns for JS engines\n  // such as a dynamic object assignment, for example `{ [dynamicKey]: value }`.\n  // eslint-disable-next-line no-new-func\n  const filterItemCore = new Function('appliers', 'row', 'shouldApplyFilter', `\"use strict\";\n${appliers.map((applier, i) => `const shouldApply${i} = !shouldApplyFilter || shouldApplyFilter(${JSON.stringify(applier.item.field)});`).join('\\n')}\n\nconst result$$ = {\n${appliers.map((applier, i) => `  ${JSON.stringify(String(applier.item.id))}: !shouldApply${i} ? false : appliers[${i}].fn(row),`).join('\\n')}\n};\n\nreturn result$$;`.replaceAll('$$', String(filterItemsApplierId)));\n  filterItemsApplierId += 1;\n\n  // Assign to the arrow function a name to help debugging\n  const filterItem = (row, shouldApplyItem) => filterItemCore(appliers, row, shouldApplyItem);\n  return filterItem;\n};\nexport const shouldQuickFilterExcludeHiddenColumns = filterModel => {\n  return filterModel.quickFilterExcludeHiddenColumns ?? true;\n};\n\n/**\n * Generates a method to easily check if a row is matching the current quick filter.\n * @param {any[]} filterModel The model with which we want to filter the rows.\n * @param {RefObject<GridPrivateApiCommunity>} apiRef The API of the grid.\n * @returns {GridAggregatedFilterItemApplier | null} A method that checks if a row is matching the current filter model. If `null`, we consider that all the rows are matching the filters.\n */\nconst buildAggregatedQuickFilterApplier = (filterModel, apiRef) => {\n  const quickFilterValues = filterModel.quickFilterValues?.filter(Boolean) ?? [];\n  if (quickFilterValues.length === 0) {\n    return null;\n  }\n  const columnFields = shouldQuickFilterExcludeHiddenColumns(filterModel) ? gridVisibleColumnFieldsSelector(apiRef) : gridColumnFieldsSelector(apiRef);\n  const appliersPerField = [];\n  const {\n    ignoreDiacritics\n  } = apiRef.current.rootProps;\n  const publicApiRef = getPublicApiRef(apiRef);\n  columnFields.forEach(field => {\n    const column = apiRef.current.getColumn(field);\n    const getApplyQuickFilterFn = column?.getApplyQuickFilterFn;\n    if (getApplyQuickFilterFn) {\n      appliersPerField.push({\n        column,\n        appliers: quickFilterValues.map(quickFilterValue => {\n          const value = ignoreDiacritics ? removeDiacritics(quickFilterValue) : quickFilterValue;\n          return {\n            fn: getApplyQuickFilterFn(value, column, publicApiRef)\n          };\n        })\n      });\n    }\n  });\n  return function isRowMatchingQuickFilter(row, shouldApplyFilter) {\n    const result = {};\n\n    /* eslint-disable no-labels */\n    outer: for (let v = 0; v < quickFilterValues.length; v += 1) {\n      const filterValue = quickFilterValues[v];\n      for (let i = 0; i < appliersPerField.length; i += 1) {\n        const {\n          column,\n          appliers\n        } = appliersPerField[i];\n        const {\n          field\n        } = column;\n        if (shouldApplyFilter && !shouldApplyFilter(field)) {\n          continue;\n        }\n        const applier = appliers[v];\n        let value = apiRef.current.getRowValue(row, column);\n        if (applier.fn === null) {\n          continue;\n        }\n        if (ignoreDiacritics) {\n          value = removeDiacritics(value);\n        }\n        const isMatching = applier.fn(value, row, column, publicApiRef);\n        if (isMatching) {\n          result[filterValue] = true;\n          continue outer;\n        }\n      }\n      result[filterValue] = false;\n    }\n    return result;\n  };\n};\nexport const buildAggregatedFilterApplier = (filterModel, apiRef, disableEval) => {\n  const isRowMatchingFilterItems = buildAggregatedFilterItemsApplier(filterModel, apiRef, disableEval);\n  const isRowMatchingQuickFilter = buildAggregatedQuickFilterApplier(filterModel, apiRef);\n  return function isRowMatchingFilters(row, shouldApplyFilter, result) {\n    result.passingFilterItems = isRowMatchingFilterItems?.(row, shouldApplyFilter) ?? null;\n    result.passingQuickFilterValues = isRowMatchingQuickFilter?.(row, shouldApplyFilter) ?? null;\n  };\n};\nconst isNotNull = result => result != null;\nconst filterModelItems = (cache, apiRef, items) => {\n  if (!cache.cleanedFilterItems) {\n    cache.cleanedFilterItems = items.filter(item => getFilterCallbackFromItem(item, apiRef) !== null);\n  }\n  return cache.cleanedFilterItems;\n};\nexport const passFilterLogic = (allFilterItemResults, allQuickFilterResults, filterModel, apiRef, cache) => {\n  const cleanedFilterItems = filterModelItems(cache, apiRef, filterModel.items);\n  const cleanedFilterItemResults = allFilterItemResults.filter(isNotNull);\n  const cleanedQuickFilterResults = allQuickFilterResults.filter(isNotNull);\n\n  // get result for filter items model\n  if (cleanedFilterItemResults.length > 0) {\n    // Return true if the item pass with one of the rows\n    const filterItemPredicate = item => {\n      return cleanedFilterItemResults.some(filterItemResult => filterItemResult[item.id]);\n    };\n    const logicOperator = filterModel.logicOperator ?? getDefaultGridFilterModel().logicOperator;\n    if (logicOperator === GridLogicOperator.And) {\n      const passesAllFilters = cleanedFilterItems.every(filterItemPredicate);\n      if (!passesAllFilters) {\n        return false;\n      }\n    } else {\n      const passesSomeFilters = cleanedFilterItems.some(filterItemPredicate);\n      if (!passesSomeFilters) {\n        return false;\n      }\n    }\n  }\n\n  // get result for quick filter model\n  if (cleanedQuickFilterResults.length > 0 && filterModel.quickFilterValues != null) {\n    // Return true if the item pass with one of the rows\n    const quickFilterValuePredicate = value => {\n      return cleanedQuickFilterResults.some(quickFilterValueResult => quickFilterValueResult[value]);\n    };\n    const quickFilterLogicOperator = filterModel.quickFilterLogicOperator ?? getDefaultGridFilterModel().quickFilterLogicOperator;\n    if (quickFilterLogicOperator === GridLogicOperator.And) {\n      const passesAllQuickFilterValues = filterModel.quickFilterValues.every(quickFilterValuePredicate);\n      if (!passesAllQuickFilterValues) {\n        return false;\n      }\n    } else {\n      const passesSomeQuickFilterValues = filterModel.quickFilterValues.some(quickFilterValuePredicate);\n      if (!passesSomeQuickFilterValues) {\n        return false;\n      }\n    }\n  }\n  return true;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}