{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridLogger, useGridApiMethod, useGridEvent } from \"../../utils/index.js\";\nimport { gridColumnMenuSelector } from \"./columnMenuSelector.js\";\nimport { gridColumnLookupSelector, gridColumnVisibilityModelSelector, gridColumnFieldsSelector } from \"../columns/gridColumnsSelector.js\";\nexport const columnMenuStateInitializer = state => _extends({}, state, {\n  columnMenu: {\n    open: false\n  }\n});\n\n/**\n * @requires useGridColumnResize (event)\n * @requires useGridInfiniteLoader (event)\n */\nexport const useGridColumnMenu = apiRef => {\n  const logger = useGridLogger(apiRef, 'useGridColumnMenu');\n\n  /**\n   * API METHODS\n   */\n  const showColumnMenu = React.useCallback(field => {\n    const columnMenuState = gridColumnMenuSelector(apiRef);\n    const newState = {\n      open: true,\n      field\n    };\n    const shouldUpdate = newState.open !== columnMenuState.open || newState.field !== columnMenuState.field;\n    if (shouldUpdate) {\n      apiRef.current.setState(state => {\n        if (state.columnMenu.open && state.columnMenu.field === field) {\n          return state;\n        }\n        logger.debug('Opening Column Menu');\n        return _extends({}, state, {\n          columnMenu: {\n            open: true,\n            field\n          }\n        });\n      });\n      apiRef.current.hidePreferences();\n    }\n  }, [apiRef, logger]);\n  const hideColumnMenu = React.useCallback(() => {\n    const columnMenuState = gridColumnMenuSelector(apiRef);\n    if (columnMenuState.field) {\n      const columnLookup = gridColumnLookupSelector(apiRef);\n      const columnVisibilityModel = gridColumnVisibilityModelSelector(apiRef);\n      const orderedFields = gridColumnFieldsSelector(apiRef);\n      let fieldToFocus = columnMenuState.field;\n\n      // If the column was removed from the grid, we need to find the closest visible field\n      if (!columnLookup[fieldToFocus]) {\n        fieldToFocus = orderedFields[0];\n      }\n\n      // If the field to focus is hidden, we need to find the closest visible field\n      if (columnVisibilityModel[fieldToFocus] === false) {\n        // contains visible column fields + the field that was just hidden\n        const visibleOrderedFields = orderedFields.filter(field => {\n          if (field === fieldToFocus) {\n            return true;\n          }\n          return columnVisibilityModel[field] !== false;\n        });\n        const fieldIndex = visibleOrderedFields.indexOf(fieldToFocus);\n        fieldToFocus = visibleOrderedFields[fieldIndex + 1] || visibleOrderedFields[fieldIndex - 1];\n      }\n      apiRef.current.setColumnHeaderFocus(fieldToFocus);\n    }\n    const newState = {\n      open: false,\n      field: undefined\n    };\n    const shouldUpdate = newState.open !== columnMenuState.open || newState.field !== columnMenuState.field;\n    if (shouldUpdate) {\n      apiRef.current.setState(state => {\n        logger.debug('Hiding Column Menu');\n        return _extends({}, state, {\n          columnMenu: newState\n        });\n      });\n    }\n  }, [apiRef, logger]);\n  const toggleColumnMenu = React.useCallback(field => {\n    logger.debug('Toggle Column Menu');\n    const columnMenu = gridColumnMenuSelector(apiRef);\n    if (!columnMenu.open || columnMenu.field !== field) {\n      showColumnMenu(field);\n    } else {\n      hideColumnMenu();\n    }\n  }, [apiRef, logger, showColumnMenu, hideColumnMenu]);\n  const columnMenuApi = {\n    showColumnMenu,\n    hideColumnMenu,\n    toggleColumnMenu\n  };\n  useGridApiMethod(apiRef, columnMenuApi, 'public');\n  useGridEvent(apiRef, 'columnResizeStart', hideColumnMenu);\n  useGridEvent(apiRef, 'virtualScrollerWheel', apiRef.current.hideColumnMenu);\n  useGridEvent(apiRef, 'virtualScrollerTouchMove', apiRef.current.hideColumnMenu);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}