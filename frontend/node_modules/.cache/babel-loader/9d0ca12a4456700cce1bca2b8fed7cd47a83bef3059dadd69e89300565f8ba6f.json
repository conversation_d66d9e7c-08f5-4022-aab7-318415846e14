{"ast": null, "code": "import * as React from 'react';\nimport { styled } from '@mui/material/styles';\nimport PropTypes from 'prop-types';\nimport { NotRendered } from \"../utils/assert.js\";\nimport { useGridSelector } from \"../hooks/utils/useGridSelector.js\";\nimport { useGridApiContext } from \"../hooks/utils/useGridApiContext.js\";\nimport { useGridRootProps } from \"../hooks/utils/useGridRootProps.js\";\nimport { gridPaginationModelSelector, gridPaginationRowCountSelector, gridPageCountSelector } from \"../hooks/features/pagination/gridPaginationSelector.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst GridPaginationRoot = styled(NotRendered)({\n  maxHeight: 'calc(100% + 1px)',\n  // border width\n  flexGrow: 1\n});\nfunction GridPagination() {\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const paginationModel = useGridSelector(apiRef, gridPaginationModelSelector);\n  const rowCount = useGridSelector(apiRef, gridPaginationRowCountSelector);\n  const pageCount = useGridSelector(apiRef, gridPageCountSelector);\n  const {\n    paginationMode,\n    loading\n  } = rootProps;\n  const disabled = rowCount === -1 && paginationMode === 'server' && loading;\n  const lastPage = React.useMemo(() => Math.max(0, pageCount - 1), [pageCount]);\n  const computedPage = React.useMemo(() => {\n    if (rowCount === -1) {\n      return paginationModel.page;\n    }\n    return paginationModel.page <= lastPage ? paginationModel.page : lastPage;\n  }, [lastPage, paginationModel.page, rowCount]);\n  const handlePageSizeChange = React.useCallback(pageSize => {\n    apiRef.current.setPageSize(pageSize);\n  }, [apiRef]);\n  const handlePageChange = React.useCallback((_, page) => {\n    apiRef.current.setPage(page);\n  }, [apiRef]);\n  const isPageSizeIncludedInPageSizeOptions = pageSize => {\n    for (let i = 0; i < rootProps.pageSizeOptions.length; i += 1) {\n      const option = rootProps.pageSizeOptions[i];\n      if (typeof option === 'number') {\n        if (option === pageSize) {\n          return true;\n        }\n      } else if (option.value === pageSize) {\n        return true;\n      }\n    }\n    return false;\n  };\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    const warnedOnceMissingInPageSizeOptions = React.useRef(false);\n    const pageSize = rootProps.paginationModel?.pageSize ?? paginationModel.pageSize;\n    if (!warnedOnceMissingInPageSizeOptions.current && !rootProps.autoPageSize && !isPageSizeIncludedInPageSizeOptions(pageSize)) {\n      console.warn([`MUI X: The page size \\`${paginationModel.pageSize}\\` is not present in the \\`pageSizeOptions\\`.`, `Add it to show the pagination select.`].join('\\n'));\n      warnedOnceMissingInPageSizeOptions.current = true;\n    }\n  }\n  const pageSizeOptions = isPageSizeIncludedInPageSizeOptions(paginationModel.pageSize) ? rootProps.pageSizeOptions : [];\n  return /*#__PURE__*/_jsx(GridPaginationRoot, {\n    as: rootProps.slots.basePagination,\n    count: rowCount,\n    page: computedPage,\n    rowsPerPageOptions: pageSizeOptions,\n    rowsPerPage: paginationModel.pageSize,\n    onPageChange: handlePageChange,\n    onRowsPerPageChange: handlePageSizeChange,\n    disabled: disabled\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GridPagination.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  component: PropTypes.elementType\n} : void 0;\nexport { GridPagination };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}