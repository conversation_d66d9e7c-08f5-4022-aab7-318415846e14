{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\", \"aria-label\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from '@mui/system';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { isOverflown } from \"../../utils/domUtils.js\";\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['columnHeaderTitle']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst GridColumnHeaderTitleRoot = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'ColumnHeaderTitle'\n})({\n  textOverflow: 'ellipsis',\n  overflow: 'hidden',\n  whiteSpace: 'nowrap',\n  fontWeight: 'var(--unstable_DataGrid-headWeight)',\n  lineHeight: 'normal'\n});\nconst ColumnHeaderInnerTitle = forwardRef(function ColumnHeaderInnerTitle(props, ref) {\n  // Tooltip adds aria-label to the props, which is not needed since the children prop is a string\n  // See https://github.com/mui/mui-x/pull/14482\n  const {\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const rootProps = useGridRootProps();\n  const classes = useUtilityClasses(rootProps);\n  return /*#__PURE__*/_jsx(GridColumnHeaderTitleRoot, _extends({\n    className: clsx(classes.root, className),\n    ownerState: rootProps\n  }, other, {\n    ref: ref\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") ColumnHeaderInnerTitle.displayName = \"ColumnHeaderInnerTitle\";\n// No React.memo here as if we display the sort icon, we need to recalculate the isOver\nfunction GridColumnHeaderTitle(props) {\n  const {\n    label,\n    description\n  } = props;\n  const rootProps = useGridRootProps();\n  const titleRef = React.useRef(null);\n  const [tooltip, setTooltip] = React.useState('');\n  const handleMouseOver = React.useCallback(() => {\n    if (!description && titleRef?.current) {\n      const isOver = isOverflown(titleRef.current);\n      if (isOver) {\n        setTooltip(label);\n      } else {\n        setTooltip('');\n      }\n    }\n  }, [description, label]);\n  return /*#__PURE__*/_jsx(rootProps.slots.baseTooltip, _extends({\n    title: description || tooltip\n  }, rootProps.slotProps?.baseTooltip, {\n    children: /*#__PURE__*/_jsx(ColumnHeaderInnerTitle, {\n      onMouseOver: handleMouseOver,\n      ref: titleRef,\n      children: label\n    })\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? GridColumnHeaderTitle.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  columnWidth: PropTypes.number.isRequired,\n  description: PropTypes.node,\n  label: PropTypes.string.isRequired\n} : void 0;\nexport { GridColumnHeaderTitle };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}