{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from '@mui/system';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { vars } from \"../../constants/cssVariables.js\";\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['overlay']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst GridOverlayRoot = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'Overlay'\n})({\n  width: '100%',\n  height: '100%',\n  display: 'flex',\n  gap: vars.spacing(1),\n  flexDirection: 'column',\n  alignSelf: 'center',\n  alignItems: 'center',\n  justifyContent: 'center',\n  textAlign: 'center',\n  textWrap: 'balance',\n  backgroundColor: vars.colors.background.backdrop\n});\nconst GridOverlay = forwardRef(function GridOverlay(props, ref) {\n  const {\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const rootProps = useGridRootProps();\n  const classes = useUtilityClasses(rootProps);\n  return /*#__PURE__*/_jsx(GridOverlayRoot, _extends({\n    className: clsx(classes.root, className),\n    ownerState: rootProps\n  }, other, {\n    ref: ref\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridOverlay.displayName = \"GridOverlay\";\nprocess.env.NODE_ENV !== \"production\" ? GridOverlay.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport { GridOverlay };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}