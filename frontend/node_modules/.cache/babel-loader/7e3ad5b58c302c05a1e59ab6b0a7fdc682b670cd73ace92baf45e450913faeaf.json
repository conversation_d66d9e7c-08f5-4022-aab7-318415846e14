{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    open\n  } = ownerState;\n  const slots = {\n    root: ['menuIcon', open && 'menuOpen'],\n    button: ['menuIconButton']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nexport const ColumnHeaderMenuIcon = /*#__PURE__*/React.memo(props => {\n  const {\n    colDef,\n    open,\n    columnMenuId,\n    columnMenuButtonId,\n    iconButtonRef\n  } = props;\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const ownerState = _extends({}, props, {\n    classes: rootProps.classes\n  });\n  const classes = useUtilityClasses(ownerState);\n  const handleMenuIconClick = React.useCallback(event => {\n    event.preventDefault();\n    event.stopPropagation();\n    apiRef.current.toggleColumnMenu(colDef.field);\n  }, [apiRef, colDef.field]);\n  const columnName = colDef.headerName ?? colDef.field;\n  return /*#__PURE__*/_jsx(\"div\", {\n    className: classes.root,\n    children: /*#__PURE__*/_jsx(rootProps.slots.baseTooltip, _extends({\n      title: apiRef.current.getLocaleText('columnMenuLabel'),\n      enterDelay: 1000\n    }, rootProps.slotProps?.baseTooltip, {\n      children: /*#__PURE__*/_jsx(rootProps.slots.baseIconButton, _extends({\n        ref: iconButtonRef,\n        tabIndex: -1,\n        className: classes.button,\n        \"aria-label\": apiRef.current.getLocaleText('columnMenuAriaLabel')(columnName),\n        size: \"small\",\n        onClick: handleMenuIconClick,\n        \"aria-haspopup\": \"menu\",\n        \"aria-expanded\": open,\n        \"aria-controls\": open ? columnMenuId : undefined,\n        id: columnMenuButtonId\n      }, rootProps.slotProps?.baseIconButton, {\n        children: /*#__PURE__*/_jsx(rootProps.slots.columnMenuIcon, {\n          fontSize: \"inherit\"\n        })\n      }))\n    }))\n  });\n});\nif (process.env.NODE_ENV !== \"production\") ColumnHeaderMenuIcon.displayName = \"ColumnHeaderMenuIcon\";", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}