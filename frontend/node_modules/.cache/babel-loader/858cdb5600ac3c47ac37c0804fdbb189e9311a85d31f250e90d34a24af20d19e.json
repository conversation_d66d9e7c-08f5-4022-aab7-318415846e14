{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"render\", \"onKeyDown\", \"onFocus\", \"disabled\", \"aria-disabled\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport useForkRef from '@mui/utils/useForkRef';\nimport useId from '@mui/utils/useId';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { useComponentRenderer } from '@mui/x-internals/useComponentRenderer';\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { useToolbarContext } from \"./ToolbarContext.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * A button for performing actions from the toolbar.\n * It renders the `baseIconButton` slot.\n *\n * Demos:\n *\n * - [Toolbar](https://mui.com/x/react-data-grid/components/toolbar/)\n *\n * API:\n *\n * - [ToolbarButton API](https://mui.com/x/api/data-grid/toolbar-button/)\n */\nconst ToolbarButton = forwardRef(function ToolbarButton(props, ref) {\n  const {\n      render,\n      onKeyDown,\n      onFocus,\n      disabled,\n      'aria-disabled': ariaDisabled\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const id = useId();\n  const rootProps = useGridRootProps();\n  const buttonRef = React.useRef(null);\n  const handleRef = useForkRef(buttonRef, ref);\n  const {\n    focusableItemId,\n    registerItem,\n    unregisterItem,\n    onItemKeyDown,\n    onItemFocus,\n    onItemDisabled\n  } = useToolbarContext();\n  const handleKeyDown = event => {\n    onItemKeyDown(event);\n    onKeyDown?.(event);\n  };\n  const handleFocus = event => {\n    onItemFocus(id);\n    onFocus?.(event);\n  };\n  React.useEffect(() => {\n    registerItem(id, buttonRef);\n    return () => unregisterItem(id);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  const previousDisabled = React.useRef(disabled);\n  React.useEffect(() => {\n    if (previousDisabled.current !== disabled && disabled === true) {\n      onItemDisabled(id, disabled);\n    }\n    previousDisabled.current = disabled;\n  }, [disabled, id, onItemDisabled]);\n  const previousAriaDisabled = React.useRef(ariaDisabled);\n  React.useEffect(() => {\n    if (previousAriaDisabled.current !== ariaDisabled && ariaDisabled === true) {\n      onItemDisabled(id, true);\n    }\n    previousAriaDisabled.current = ariaDisabled;\n  }, [ariaDisabled, id, onItemDisabled]);\n  const element = useComponentRenderer(rootProps.slots.baseIconButton, render, _extends({}, rootProps.slotProps?.baseIconButton, {\n    tabIndex: focusableItemId === id ? 0 : -1\n  }, other, {\n    disabled,\n    'aria-disabled': ariaDisabled,\n    onKeyDown: handleKeyDown,\n    onFocus: handleFocus,\n    ref: handleRef\n  }));\n  return /*#__PURE__*/_jsx(React.Fragment, {\n    children: element\n  });\n});\nif (process.env.NODE_ENV !== \"production\") ToolbarButton.displayName = \"ToolbarButton\";\nprocess.env.NODE_ENV !== \"production\" ? ToolbarButton.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  className: PropTypes.string,\n  color: PropTypes.oneOf(['default', 'inherit', 'primary']),\n  disabled: PropTypes.bool,\n  edge: PropTypes.oneOf(['end', 'start', false]),\n  id: PropTypes.string,\n  label: PropTypes.string,\n  /**\n   * A function to customize rendering of the component.\n   */\n  render: PropTypes.oneOfType([PropTypes.element, PropTypes.func]),\n  role: PropTypes.string,\n  size: PropTypes.oneOf(['large', 'medium', 'small']),\n  style: PropTypes.object,\n  tabIndex: PropTypes.number,\n  title: PropTypes.string,\n  touchRippleRef: PropTypes.any\n} : void 0;\nexport { ToolbarButton };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}