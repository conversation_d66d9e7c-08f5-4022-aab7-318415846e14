{"ast": null, "code": "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"direction\", \"index\", \"sortingOrder\", \"disabled\", \"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from '@mui/system';\nimport clsx from 'clsx';\nimport { useGridApiContext } from \"../hooks/utils/useGridApiContext.js\";\nimport { getDataGridUtilityClass } from \"../constants/gridClasses.js\";\nimport { useGridRootProps } from \"../hooks/utils/useGridRootProps.js\";\nimport { vars } from \"../constants/cssVariables.js\";\nimport { GridColumnUnsortedIcon } from \"./GridColumnUnsortedIcon.js\";\nimport { NotRendered } from \"../utils/assert.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['sortButton'],\n    icon: ['sortIcon']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst GridColumnSortButtonRoot = styled(NotRendered, {\n  name: 'MuiDataGrid',\n  slot: 'SortButton'\n})({\n  transition: vars.transition(['opacity'], {\n    duration: vars.transitions.duration.short,\n    easing: vars.transitions.easing.easeInOut\n  })\n});\nfunction getIcon(icons, direction, className, sortingOrder) {\n  let Icon;\n  const iconProps = {};\n  if (direction === 'asc') {\n    Icon = icons.columnSortedAscendingIcon;\n  } else if (direction === 'desc') {\n    Icon = icons.columnSortedDescendingIcon;\n  } else {\n    Icon = GridColumnUnsortedIcon;\n    iconProps.sortingOrder = sortingOrder;\n  }\n  return Icon ? /*#__PURE__*/_jsx(Icon, _extends({\n    fontSize: \"small\",\n    className: className\n  }, iconProps)) : null;\n}\nfunction GridColumnSortButton(props) {\n  const {\n      direction,\n      index,\n      sortingOrder,\n      disabled,\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const ownerState = _extends({}, props, {\n    classes: rootProps.classes\n  });\n  const classes = useUtilityClasses(ownerState);\n  const iconElement = getIcon(rootProps.slots, direction, classes.icon, sortingOrder);\n  if (!iconElement) {\n    return null;\n  }\n  const iconButton = /*#__PURE__*/_jsx(GridColumnSortButtonRoot, _extends({\n    as: rootProps.slots.baseIconButton,\n    ownerState: ownerState,\n    \"aria-label\": apiRef.current.getLocaleText('columnHeaderSortIconLabel'),\n    title: apiRef.current.getLocaleText('columnHeaderSortIconLabel'),\n    size: \"small\",\n    disabled: disabled,\n    className: clsx(classes.root, className)\n  }, rootProps.slotProps?.baseIconButton, other, {\n    children: iconElement\n  }));\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [index != null && /*#__PURE__*/_jsx(rootProps.slots.baseBadge, {\n      badgeContent: index,\n      color: \"default\",\n      overlap: \"circular\",\n      children: iconButton\n    }), index == null && iconButton]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GridColumnSortButton.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  direction: PropTypes.oneOf(['asc', 'desc']),\n  disabled: PropTypes.bool,\n  field: PropTypes.string.isRequired,\n  index: PropTypes.number,\n  onClick: PropTypes.func,\n  sortingOrder: PropTypes.arrayOf(PropTypes.oneOf(['asc', 'desc'])).isRequired\n} : void 0;\nexport { GridColumnSortButton };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}