{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\", \"rowCount\", \"visibleRowCount\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from '@mui/system';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { vars } from \"../constants/cssVariables.js\";\nimport { useGridApiContext } from \"../hooks/utils/useGridApiContext.js\";\nimport { getDataGridUtilityClass } from \"../constants/gridClasses.js\";\nimport { useGridRootProps } from \"../hooks/utils/useGridRootProps.js\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['rowCount']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst GridRowCountRoot = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'RowCount'\n})({\n  alignItems: 'center',\n  display: 'flex',\n  margin: vars.spacing(0, 2)\n});\nconst GridRowCount = forwardRef(function GridRowCount(props, ref) {\n  const {\n      className,\n      rowCount,\n      visibleRowCount\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const apiRef = useGridApiContext();\n  const ownerState = useGridRootProps();\n  const classes = useUtilityClasses(ownerState);\n  if (rowCount === 0) {\n    return null;\n  }\n  const text = visibleRowCount < rowCount ? apiRef.current.getLocaleText('footerTotalVisibleRows')(visibleRowCount, rowCount) : rowCount.toLocaleString();\n  return /*#__PURE__*/_jsxs(GridRowCountRoot, _extends({\n    className: clsx(classes.root, className),\n    ownerState: ownerState\n  }, other, {\n    ref: ref,\n    children: [apiRef.current.getLocaleText('footerTotalRows'), \" \", text]\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridRowCount.displayName = \"GridRowCount\";\nprocess.env.NODE_ENV !== \"production\" ? GridRowCount.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  rowCount: PropTypes.number.isRequired,\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  visibleRowCount: PropTypes.number.isRequired\n} : void 0;\nexport { GridRowCount };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}