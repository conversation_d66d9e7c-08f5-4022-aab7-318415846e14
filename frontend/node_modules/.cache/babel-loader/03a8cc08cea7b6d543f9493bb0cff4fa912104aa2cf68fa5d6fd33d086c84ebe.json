{"ast": null, "code": "import * as React from 'react';\nimport clsx from 'clsx';\nimport { gridClasses } from \"../constants/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst classes = {\n  root: gridClasses.scrollbarFiller,\n  header: gridClasses['scrollbarFiller--header'],\n  borderTop: gridClasses['scrollbarFiller--borderTop'],\n  borderBottom: gridClasses['scrollbarFiller--borderBottom'],\n  pinnedRight: gridClasses['scrollbarFiller--pinnedRight']\n};\nfunction GridScrollbarFillerCell({\n  header,\n  borderTop = true,\n  borderBottom,\n  pinnedRight\n}) {\n  return /*#__PURE__*/_jsx(\"div\", {\n    role: \"presentation\",\n    className: clsx(classes.root, header && classes.header, borderTop && classes.borderTop, borderBottom && classes.borderBottom, pinnedRight && classes.pinnedRight)\n  });\n}\nexport { GridScrollbarFillerCell };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}