{"ast": null, "code": "import * as React from 'react';\nimport { TimerBasedCleanupTracking } from \"../../utils/cleanupTracking/TimerBasedCleanupTracking.js\";\nimport { FinalizationRegistryBasedCleanupTracking } from \"../../utils/cleanupTracking/FinalizationRegistryBasedCleanupTracking.js\";\n// Based on https://github.com/Bnaya/use-dispose-uncommitted/blob/main/src/finalization-registry-based-impl.ts\n// Check https://github.com/facebook/react/issues/15317 to get more information\n\n// We use class to make it easier to detect in heap snapshots by name\nclass ObjectToBeRetainedByReact {\n  static create() {\n    return new ObjectToBeRetainedByReact();\n  }\n}\nconst registryContainer = {\n  current: createRegistry()\n};\nlet cleanupTokensCounter = 0;\nexport function useGridEvent(apiRef, eventName, handler, options) {\n  const objectRetainedByReact = React.useState(ObjectToBeRetainedByReact.create)[0];\n  const subscription = React.useRef(null);\n  const handlerRef = React.useRef(null);\n  handlerRef.current = handler;\n  const cleanupTokenRef = React.useRef(null);\n  if (!subscription.current && handlerRef.current) {\n    const enhancedHandler = (params, event, details) => {\n      if (!event.defaultMuiPrevented) {\n        handlerRef.current?.(params, event, details);\n      }\n    };\n    subscription.current = apiRef.current.subscribeEvent(eventName, enhancedHandler, options);\n    cleanupTokensCounter += 1;\n    cleanupTokenRef.current = {\n      cleanupToken: cleanupTokensCounter\n    };\n    registryContainer.current.register(objectRetainedByReact,\n    // The callback below will be called once this reference stops being retained\n    () => {\n      subscription.current?.();\n      subscription.current = null;\n      cleanupTokenRef.current = null;\n    }, cleanupTokenRef.current);\n  } else if (!handlerRef.current && subscription.current) {\n    subscription.current();\n    subscription.current = null;\n    if (cleanupTokenRef.current) {\n      registryContainer.current.unregister(cleanupTokenRef.current);\n      cleanupTokenRef.current = null;\n    }\n  }\n  React.useEffect(() => {\n    if (!subscription.current && handlerRef.current) {\n      const enhancedHandler = (params, event, details) => {\n        if (!event.defaultMuiPrevented) {\n          handlerRef.current?.(params, event, details);\n        }\n      };\n      subscription.current = apiRef.current.subscribeEvent(eventName, enhancedHandler, options);\n    }\n    if (cleanupTokenRef.current && registryContainer.current) {\n      // If the effect was called, it means that this render was committed\n      // so we can trust the cleanup function to remove the listener.\n      registryContainer.current.unregister(cleanupTokenRef.current);\n      cleanupTokenRef.current = null;\n    }\n    return () => {\n      subscription.current?.();\n      subscription.current = null;\n    };\n  }, [apiRef, eventName, options]);\n}\nconst OPTIONS_IS_FIRST = {\n  isFirst: true\n};\nexport function useGridEventPriority(apiRef, eventName, handler) {\n  useGridEvent(apiRef, eventName, handler, OPTIONS_IS_FIRST);\n}\n\n// TODO: move to @mui/x-data-grid/internals\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport function unstable_resetCleanupTracking() {\n  registryContainer.current?.reset();\n  registryContainer.current = createRegistry();\n}\n\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport const internal_registryContainer = registryContainer;\nfunction createRegistry() {\n  return typeof FinalizationRegistry !== 'undefined' ? new FinalizationRegistryBasedCleanupTracking() : new TimerBasedCleanupTracking();\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}