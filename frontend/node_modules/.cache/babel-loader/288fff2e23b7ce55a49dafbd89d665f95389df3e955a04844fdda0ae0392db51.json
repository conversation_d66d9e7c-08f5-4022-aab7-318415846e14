{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/Permissions.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Button, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Dialog, DialogTitle, DialogContent, DialogActions, TextField, IconButton, Tooltip, Chip } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, VpnKey as VpnKeyIcon } from '@mui/icons-material';\nimport { toast } from 'react-toastify';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Permissions = () => {\n  _s();\n  const [permissions, setPermissions] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [dialogOpen, setDialogOpen] = useState(false);\n  const [editingPermission, setEditingPermission] = useState(null);\n  const [formData, setFormData] = useState({\n    code: '',\n    name: '',\n    description: ''\n  });\n  useEffect(() => {\n    fetchPermissions();\n  }, []);\n  const fetchPermissions = async () => {\n    try {\n      const response = await api.get('/permissions/');\n      setPermissions(response.data.items || response.data);\n      setLoading(false);\n    } catch (error) {\n      toast.error('Erreur lors du chargement des permissions');\n      setLoading(false);\n    }\n  };\n  const handleOpenDialog = permission => {\n    if (permission) {\n      setEditingPermission(permission);\n      setFormData({\n        code: permission.code,\n        name: permission.name,\n        description: permission.description\n      });\n    } else {\n      setEditingPermission(null);\n      setFormData({\n        code: '',\n        name: '',\n        description: ''\n      });\n    }\n    setDialogOpen(true);\n  };\n  const handleCloseDialog = () => {\n    setDialogOpen(false);\n    setEditingPermission(null);\n    setFormData({\n      code: '',\n      name: '',\n      description: ''\n    });\n  };\n  const handleSubmit = async () => {\n    try {\n      if (editingPermission) {\n        await api.put(`/permissions/${editingPermission.id}`, formData);\n        toast.success('Permission modifiée avec succès');\n      } else {\n        await api.post('/permissions/', formData);\n        toast.success('Permission créée avec succès');\n      }\n      handleCloseDialog();\n      fetchPermissions();\n    } catch (error) {\n      toast.error('Erreur lors de la sauvegarde de la permission');\n    }\n  };\n  const handleDelete = async permissionId => {\n    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette permission ?')) {\n      try {\n        await api.delete(`/permissions/${permissionId}`);\n        toast.success('Permission supprimée avec succès');\n        fetchPermissions();\n      } catch (error) {\n        toast.error('Erreur lors de la suppression de la permission');\n      }\n    }\n  };\n  const getPermissionTypeColor = code => {\n    if (code.includes('create')) return 'success';\n    if (code.includes('read')) return 'info';\n    if (code.includes('update')) return 'warning';\n    if (code.includes('delete')) return 'error';\n    return 'default';\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        mt: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        children: \"Chargement...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(VpnKeyIcon, {\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          component: \"h1\",\n          children: \"Gestion des Permissions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 22\n        }, this),\n        onClick: () => handleOpenDialog(),\n        children: \"Nouvelle Permission\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Code\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Nom\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"center\",\n              children: \"Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: permissions.map(permission => /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                fontFamily: \"monospace\",\n                children: permission.code\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle1\",\n                fontWeight: \"bold\",\n                children: permission.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: permission.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: permission.code.split(':')[1] || 'other',\n                size: \"small\",\n                color: getPermissionTypeColor(permission.code),\n                variant: \"outlined\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"Modifier\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  color: \"primary\",\n                  onClick: () => handleOpenDialog(permission),\n                  children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 194,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"Supprimer\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  color: \"error\",\n                  onClick: () => handleDelete(permission.id),\n                  children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 202,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 17\n            }, this)]\n          }, permission.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: dialogOpen,\n      onClose: handleCloseDialog,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: editingPermission ? 'Modifier la permission' : 'Créer une nouvelle permission'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Code de la permission\",\n            value: formData.code,\n            onChange: e => setFormData(prev => ({\n              ...prev,\n              code: e.target.value\n            })),\n            margin: \"normal\",\n            required: true,\n            placeholder: \"ex: user:create, role:read\",\n            helperText: \"Format recommand\\xE9: ressource:action (ex: user:create)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Nom de la permission\",\n            value: formData.name,\n            onChange: e => setFormData(prev => ({\n              ...prev,\n              name: e.target.value\n            })),\n            margin: \"normal\",\n            required: true,\n            placeholder: \"ex: Cr\\xE9er un utilisateur\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Description\",\n            value: formData.description,\n            onChange: e => setFormData(prev => ({\n              ...prev,\n              description: e.target.value\n            })),\n            margin: \"normal\",\n            multiline: true,\n            rows: 3,\n            placeholder: \"Description d\\xE9taill\\xE9e de la permission\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseDialog,\n          children: \"Annuler\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmit,\n          variant: \"contained\",\n          disabled: !formData.code.trim() || !formData.name.trim(),\n          children: editingPermission ? 'Modifier' : 'Créer'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 138,\n    columnNumber: 5\n  }, this);\n};\n_s(Permissions, \"V68M/iClpPU4RGVHLhedtCIGi1A=\");\n_c = Permissions;\nexport default Permissions;\nvar _c;\n$RefreshReg$(_c, \"Permissions\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "IconButton", "<PERSON><PERSON><PERSON>", "Chip", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "VpnKey", "VpnKeyIcon", "toast", "jsxDEV", "_jsxDEV", "Permissions", "_s", "permissions", "setPermissions", "loading", "setLoading", "dialogOpen", "setDialogOpen", "editingPermission", "setEditingPermission", "formData", "setFormData", "code", "name", "description", "fetchPermissions", "response", "api", "get", "data", "items", "error", "handleOpenDialog", "permission", "handleCloseDialog", "handleSubmit", "put", "id", "success", "post", "handleDelete", "permissionId", "window", "confirm", "delete", "getPermissionTypeColor", "includes", "sx", "display", "justifyContent", "mt", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "p", "alignItems", "mb", "gap", "color", "variant", "component", "startIcon", "onClick", "align", "map", "fontFamily", "fontWeight", "label", "split", "size", "title", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "pt", "value", "onChange", "e", "prev", "target", "margin", "required", "placeholder", "helperText", "multiline", "rows", "disabled", "trim", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/Permissions.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  <PERSON>pography,\n  Button,\n  Paper,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  IconButton,\n  Tooltip,\n  Chip,\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Vpn<PERSON>ey as VpnKeyIcon,\n} from '@mui/icons-material';\nimport { toast } from 'react-toastify';\nimport apiService from '../services/api';\n\ninterface Permission {\n  id: number;\n  code: string;\n  name: string;\n  description: string;\n}\n\nconst Permissions: React.FC = () => {\n  const [permissions, setPermissions] = useState<Permission[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [dialogOpen, setDialogOpen] = useState(false);\n  const [editingPermission, setEditingPermission] = useState<Permission | null>(null);\n  const [formData, setFormData] = useState({\n    code: '',\n    name: '',\n    description: '',\n  });\n\n  useEffect(() => {\n    fetchPermissions();\n  }, []);\n\n  const fetchPermissions = async () => {\n    try {\n      const response = await api.get('/permissions/');\n      setPermissions(response.data.items || response.data);\n      setLoading(false);\n    } catch (error) {\n      toast.error('Erreur lors du chargement des permissions');\n      setLoading(false);\n    }\n  };\n\n  const handleOpenDialog = (permission?: Permission) => {\n    if (permission) {\n      setEditingPermission(permission);\n      setFormData({\n        code: permission.code,\n        name: permission.name,\n        description: permission.description,\n      });\n    } else {\n      setEditingPermission(null);\n      setFormData({\n        code: '',\n        name: '',\n        description: '',\n      });\n    }\n    setDialogOpen(true);\n  };\n\n  const handleCloseDialog = () => {\n    setDialogOpen(false);\n    setEditingPermission(null);\n    setFormData({\n      code: '',\n      name: '',\n      description: '',\n    });\n  };\n\n  const handleSubmit = async () => {\n    try {\n      if (editingPermission) {\n        await api.put(`/permissions/${editingPermission.id}`, formData);\n        toast.success('Permission modifiée avec succès');\n      } else {\n        await api.post('/permissions/', formData);\n        toast.success('Permission créée avec succès');\n      }\n      handleCloseDialog();\n      fetchPermissions();\n    } catch (error) {\n      toast.error('Erreur lors de la sauvegarde de la permission');\n    }\n  };\n\n  const handleDelete = async (permissionId: number) => {\n    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette permission ?')) {\n      try {\n        await api.delete(`/permissions/${permissionId}`);\n        toast.success('Permission supprimée avec succès');\n        fetchPermissions();\n      } catch (error) {\n        toast.error('Erreur lors de la suppression de la permission');\n      }\n    }\n  };\n\n  const getPermissionTypeColor = (code: string) => {\n    if (code.includes('create')) return 'success';\n    if (code.includes('read')) return 'info';\n    if (code.includes('update')) return 'warning';\n    if (code.includes('delete')) return 'error';\n    return 'default';\n  };\n\n  if (loading) {\n    return (\n      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>\n        <Typography>Chargement...</Typography>\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ p: 3 }}>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n          <VpnKeyIcon color=\"primary\" />\n          <Typography variant=\"h4\" component=\"h1\">\n            Gestion des Permissions\n          </Typography>\n        </Box>\n        <Button\n          variant=\"contained\"\n          startIcon={<AddIcon />}\n          onClick={() => handleOpenDialog()}\n        >\n          Nouvelle Permission\n        </Button>\n      </Box>\n\n      <TableContainer component={Paper}>\n        <Table>\n          <TableHead>\n            <TableRow>\n              <TableCell>Code</TableCell>\n              <TableCell>Nom</TableCell>\n              <TableCell>Description</TableCell>\n              <TableCell>Type</TableCell>\n              <TableCell align=\"center\">Actions</TableCell>\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {permissions.map((permission) => (\n              <TableRow key={permission.id}>\n                <TableCell>\n                  <Typography variant=\"body2\" fontFamily=\"monospace\">\n                    {permission.code}\n                  </Typography>\n                </TableCell>\n                <TableCell>\n                  <Typography variant=\"subtitle1\" fontWeight=\"bold\">\n                    {permission.name}\n                  </Typography>\n                </TableCell>\n                <TableCell>{permission.description}</TableCell>\n                <TableCell>\n                  <Chip\n                    label={permission.code.split(':')[1] || 'other'}\n                    size=\"small\"\n                    color={getPermissionTypeColor(permission.code) as any}\n                    variant=\"outlined\"\n                  />\n                </TableCell>\n                <TableCell align=\"center\">\n                  <Tooltip title=\"Modifier\">\n                    <IconButton\n                      color=\"primary\"\n                      onClick={() => handleOpenDialog(permission)}\n                    >\n                      <EditIcon />\n                    </IconButton>\n                  </Tooltip>\n                  <Tooltip title=\"Supprimer\">\n                    <IconButton\n                      color=\"error\"\n                      onClick={() => handleDelete(permission.id)}\n                    >\n                      <DeleteIcon />\n                    </IconButton>\n                  </Tooltip>\n                </TableCell>\n              </TableRow>\n            ))}\n          </TableBody>\n        </Table>\n      </TableContainer>\n\n      {/* Dialog pour créer/modifier une permission */}\n      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n        <DialogTitle>\n          {editingPermission ? 'Modifier la permission' : 'Créer une nouvelle permission'}\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 2 }}>\n            <TextField\n              fullWidth\n              label=\"Code de la permission\"\n              value={formData.code}\n              onChange={(e) => setFormData(prev => ({ ...prev, code: e.target.value }))}\n              margin=\"normal\"\n              required\n              placeholder=\"ex: user:create, role:read\"\n              helperText=\"Format recommandé: ressource:action (ex: user:create)\"\n            />\n            <TextField\n              fullWidth\n              label=\"Nom de la permission\"\n              value={formData.name}\n              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}\n              margin=\"normal\"\n              required\n              placeholder=\"ex: Créer un utilisateur\"\n            />\n            <TextField\n              fullWidth\n              label=\"Description\"\n              value={formData.description}\n              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}\n              margin=\"normal\"\n              multiline\n              rows={3}\n              placeholder=\"Description détaillée de la permission\"\n            />\n          </Box>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCloseDialog}>Annuler</Button>\n          <Button\n            onClick={handleSubmit}\n            variant=\"contained\"\n            disabled={!formData.code.trim() || !formData.name.trim()}\n          >\n            {editingPermission ? 'Modifier' : 'Créer'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default Permissions;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,UAAU,EACVC,OAAO,EACPC,IAAI,QACC,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,MAAM,IAAIC,UAAU,QACf,qBAAqB;AAC5B,SAASC,KAAK,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAUvC,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAe,EAAE,CAAC;EAChE,MAAM,CAACmC,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqC,UAAU,EAAEC,aAAa,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACuC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxC,QAAQ,CAAoB,IAAI,CAAC;EACnF,MAAM,CAACyC,QAAQ,EAAEC,WAAW,CAAC,GAAG1C,QAAQ,CAAC;IACvC2C,IAAI,EAAE,EAAE;IACRC,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE;EACf,CAAC,CAAC;EAEF5C,SAAS,CAAC,MAAM;IACd6C,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMC,GAAG,CAACC,GAAG,CAAC,eAAe,CAAC;MAC/Cf,cAAc,CAACa,QAAQ,CAACG,IAAI,CAACC,KAAK,IAAIJ,QAAQ,CAACG,IAAI,CAAC;MACpDd,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdxB,KAAK,CAACwB,KAAK,CAAC,2CAA2C,CAAC;MACxDhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiB,gBAAgB,GAAIC,UAAuB,IAAK;IACpD,IAAIA,UAAU,EAAE;MACdd,oBAAoB,CAACc,UAAU,CAAC;MAChCZ,WAAW,CAAC;QACVC,IAAI,EAAEW,UAAU,CAACX,IAAI;QACrBC,IAAI,EAAEU,UAAU,CAACV,IAAI;QACrBC,WAAW,EAAES,UAAU,CAACT;MAC1B,CAAC,CAAC;IACJ,CAAC,MAAM;MACLL,oBAAoB,CAAC,IAAI,CAAC;MAC1BE,WAAW,CAAC;QACVC,IAAI,EAAE,EAAE;QACRC,IAAI,EAAE,EAAE;QACRC,WAAW,EAAE;MACf,CAAC,CAAC;IACJ;IACAP,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMiB,iBAAiB,GAAGA,CAAA,KAAM;IAC9BjB,aAAa,CAAC,KAAK,CAAC;IACpBE,oBAAoB,CAAC,IAAI,CAAC;IAC1BE,WAAW,CAAC;MACVC,IAAI,EAAE,EAAE;MACRC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE;IACf,CAAC,CAAC;EACJ,CAAC;EAED,MAAMW,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,IAAIjB,iBAAiB,EAAE;QACrB,MAAMS,GAAG,CAACS,GAAG,CAAC,gBAAgBlB,iBAAiB,CAACmB,EAAE,EAAE,EAAEjB,QAAQ,CAAC;QAC/Db,KAAK,CAAC+B,OAAO,CAAC,iCAAiC,CAAC;MAClD,CAAC,MAAM;QACL,MAAMX,GAAG,CAACY,IAAI,CAAC,eAAe,EAAEnB,QAAQ,CAAC;QACzCb,KAAK,CAAC+B,OAAO,CAAC,8BAA8B,CAAC;MAC/C;MACAJ,iBAAiB,CAAC,CAAC;MACnBT,gBAAgB,CAAC,CAAC;IACpB,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdxB,KAAK,CAACwB,KAAK,CAAC,+CAA+C,CAAC;IAC9D;EACF,CAAC;EAED,MAAMS,YAAY,GAAG,MAAOC,YAAoB,IAAK;IACnD,IAAIC,MAAM,CAACC,OAAO,CAAC,uDAAuD,CAAC,EAAE;MAC3E,IAAI;QACF,MAAMhB,GAAG,CAACiB,MAAM,CAAC,gBAAgBH,YAAY,EAAE,CAAC;QAChDlC,KAAK,CAAC+B,OAAO,CAAC,kCAAkC,CAAC;QACjDb,gBAAgB,CAAC,CAAC;MACpB,CAAC,CAAC,OAAOM,KAAK,EAAE;QACdxB,KAAK,CAACwB,KAAK,CAAC,gDAAgD,CAAC;MAC/D;IACF;EACF,CAAC;EAED,MAAMc,sBAAsB,GAAIvB,IAAY,IAAK;IAC/C,IAAIA,IAAI,CAACwB,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,SAAS;IAC7C,IAAIxB,IAAI,CAACwB,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,MAAM;IACxC,IAAIxB,IAAI,CAACwB,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,SAAS;IAC7C,IAAIxB,IAAI,CAACwB,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;IAC3C,OAAO,SAAS;EAClB,CAAC;EAED,IAAIhC,OAAO,EAAE;IACX,oBACEL,OAAA,CAAC5B,GAAG;MAACkE,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,eAC5D1C,OAAA,CAAC3B,UAAU;QAAAqE,QAAA,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CAAC;EAEV;EAEA,oBACE9C,OAAA,CAAC5B,GAAG;IAACkE,EAAE,EAAE;MAAES,CAAC,EAAE;IAAE,CAAE;IAAAL,QAAA,gBAChB1C,OAAA,CAAC5B,GAAG;MAACkE,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEQ,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,gBACzF1C,OAAA,CAAC5B,GAAG;QAACkE,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAES,UAAU,EAAE,QAAQ;UAAEE,GAAG,EAAE;QAAE,CAAE;QAAAR,QAAA,gBACzD1C,OAAA,CAACH,UAAU;UAACsD,KAAK,EAAC;QAAS;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9B9C,OAAA,CAAC3B,UAAU;UAAC+E,OAAO,EAAC,IAAI;UAACC,SAAS,EAAC,IAAI;UAAAX,QAAA,EAAC;QAExC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACN9C,OAAA,CAAC1B,MAAM;QACL8E,OAAO,EAAC,WAAW;QACnBE,SAAS,eAAEtD,OAAA,CAACT,OAAO;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBS,OAAO,EAAEA,CAAA,KAAMhC,gBAAgB,CAAC,CAAE;QAAAmB,QAAA,EACnC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAEN9C,OAAA,CAACrB,cAAc;MAAC0E,SAAS,EAAE9E,KAAM;MAAAmE,QAAA,eAC/B1C,OAAA,CAACxB,KAAK;QAAAkE,QAAA,gBACJ1C,OAAA,CAACpB,SAAS;UAAA8D,QAAA,eACR1C,OAAA,CAACnB,QAAQ;YAAA6D,QAAA,gBACP1C,OAAA,CAACtB,SAAS;cAAAgE,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC3B9C,OAAA,CAACtB,SAAS;cAAAgE,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC1B9C,OAAA,CAACtB,SAAS;cAAAgE,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAClC9C,OAAA,CAACtB,SAAS;cAAAgE,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC3B9C,OAAA,CAACtB,SAAS;cAAC8E,KAAK,EAAC,QAAQ;cAAAd,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZ9C,OAAA,CAACvB,SAAS;UAAAiE,QAAA,EACPvC,WAAW,CAACsD,GAAG,CAAEjC,UAAU,iBAC1BxB,OAAA,CAACnB,QAAQ;YAAA6D,QAAA,gBACP1C,OAAA,CAACtB,SAAS;cAAAgE,QAAA,eACR1C,OAAA,CAAC3B,UAAU;gBAAC+E,OAAO,EAAC,OAAO;gBAACM,UAAU,EAAC,WAAW;gBAAAhB,QAAA,EAC/ClB,UAAU,CAACX;cAAI;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACZ9C,OAAA,CAACtB,SAAS;cAAAgE,QAAA,eACR1C,OAAA,CAAC3B,UAAU;gBAAC+E,OAAO,EAAC,WAAW;gBAACO,UAAU,EAAC,MAAM;gBAAAjB,QAAA,EAC9ClB,UAAU,CAACV;cAAI;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACZ9C,OAAA,CAACtB,SAAS;cAAAgE,QAAA,EAAElB,UAAU,CAACT;YAAW;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/C9C,OAAA,CAACtB,SAAS;cAAAgE,QAAA,eACR1C,OAAA,CAACX,IAAI;gBACHuE,KAAK,EAAEpC,UAAU,CAACX,IAAI,CAACgD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,OAAQ;gBAChDC,IAAI,EAAC,OAAO;gBACZX,KAAK,EAAEf,sBAAsB,CAACZ,UAAU,CAACX,IAAI,CAAS;gBACtDuC,OAAO,EAAC;cAAU;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eACZ9C,OAAA,CAACtB,SAAS;cAAC8E,KAAK,EAAC,QAAQ;cAAAd,QAAA,gBACvB1C,OAAA,CAACZ,OAAO;gBAAC2E,KAAK,EAAC,UAAU;gBAAArB,QAAA,eACvB1C,OAAA,CAACb,UAAU;kBACTgE,KAAK,EAAC,SAAS;kBACfI,OAAO,EAAEA,CAAA,KAAMhC,gBAAgB,CAACC,UAAU,CAAE;kBAAAkB,QAAA,eAE5C1C,OAAA,CAACP,QAAQ;oBAAAkD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACV9C,OAAA,CAACZ,OAAO;gBAAC2E,KAAK,EAAC,WAAW;gBAAArB,QAAA,eACxB1C,OAAA,CAACb,UAAU;kBACTgE,KAAK,EAAC,OAAO;kBACbI,OAAO,EAAEA,CAAA,KAAMxB,YAAY,CAACP,UAAU,CAACI,EAAE,CAAE;kBAAAc,QAAA,eAE3C1C,OAAA,CAACL,UAAU;oBAAAgD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA,GArCCtB,UAAU,CAACI,EAAE;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAsClB,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAGjB9C,OAAA,CAAClB,MAAM;MAACkF,IAAI,EAAEzD,UAAW;MAAC0D,OAAO,EAAExC,iBAAkB;MAACyC,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAAzB,QAAA,gBAC3E1C,OAAA,CAACjB,WAAW;QAAA2D,QAAA,EACTjC,iBAAiB,GAAG,wBAAwB,GAAG;MAA+B;QAAAkC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE,CAAC,eACd9C,OAAA,CAAChB,aAAa;QAAA0D,QAAA,eACZ1C,OAAA,CAAC5B,GAAG;UAACkE,EAAE,EAAE;YAAE8B,EAAE,EAAE;UAAE,CAAE;UAAA1B,QAAA,gBACjB1C,OAAA,CAACd,SAAS;YACRiF,SAAS;YACTP,KAAK,EAAC,uBAAuB;YAC7BS,KAAK,EAAE1D,QAAQ,CAACE,IAAK;YACrByD,QAAQ,EAAGC,CAAC,IAAK3D,WAAW,CAAC4D,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAE3D,IAAI,EAAE0D,CAAC,CAACE,MAAM,CAACJ;YAAM,CAAC,CAAC,CAAE;YAC1EK,MAAM,EAAC,QAAQ;YACfC,QAAQ;YACRC,WAAW,EAAC,4BAA4B;YACxCC,UAAU,EAAC;UAAuD;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,eACF9C,OAAA,CAACd,SAAS;YACRiF,SAAS;YACTP,KAAK,EAAC,sBAAsB;YAC5BS,KAAK,EAAE1D,QAAQ,CAACG,IAAK;YACrBwD,QAAQ,EAAGC,CAAC,IAAK3D,WAAW,CAAC4D,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAE1D,IAAI,EAAEyD,CAAC,CAACE,MAAM,CAACJ;YAAM,CAAC,CAAC,CAAE;YAC1EK,MAAM,EAAC,QAAQ;YACfC,QAAQ;YACRC,WAAW,EAAC;UAA0B;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACF9C,OAAA,CAACd,SAAS;YACRiF,SAAS;YACTP,KAAK,EAAC,aAAa;YACnBS,KAAK,EAAE1D,QAAQ,CAACI,WAAY;YAC5BuD,QAAQ,EAAGC,CAAC,IAAK3D,WAAW,CAAC4D,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAEzD,WAAW,EAAEwD,CAAC,CAACE,MAAM,CAACJ;YAAM,CAAC,CAAC,CAAE;YACjFK,MAAM,EAAC,QAAQ;YACfI,SAAS;YACTC,IAAI,EAAE,CAAE;YACRH,WAAW,EAAC;UAAwC;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChB9C,OAAA,CAACf,aAAa;QAAAyD,QAAA,gBACZ1C,OAAA,CAAC1B,MAAM;UAACiF,OAAO,EAAE9B,iBAAkB;UAAAiB,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACpD9C,OAAA,CAAC1B,MAAM;UACLiF,OAAO,EAAE7B,YAAa;UACtB0B,OAAO,EAAC,WAAW;UACnB4B,QAAQ,EAAE,CAACrE,QAAQ,CAACE,IAAI,CAACoE,IAAI,CAAC,CAAC,IAAI,CAACtE,QAAQ,CAACG,IAAI,CAACmE,IAAI,CAAC,CAAE;UAAAvC,QAAA,EAExDjC,iBAAiB,GAAG,UAAU,GAAG;QAAO;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAC5C,EAAA,CAjOID,WAAqB;AAAAiF,EAAA,GAArBjF,WAAqB;AAmO3B,eAAeA,WAAW;AAAC,IAAAiF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}