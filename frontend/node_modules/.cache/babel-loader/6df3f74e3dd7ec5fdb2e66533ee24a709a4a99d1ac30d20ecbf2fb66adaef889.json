{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\", \"children\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { styled } from '@mui/system';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { vars } from \"../../constants/cssVariables.js\";\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { Toolbar } from \"../toolbarV8/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['toolbarContainer']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst GridToolbarContainerRoot = styled(Toolbar, {\n  name: 'MuiDataGrid',\n  slot: 'ToolbarContainer',\n  shouldForwardProp: prop => prop !== 'ownerState'\n})({\n  display: 'flex',\n  alignItems: 'center',\n  flexWrap: 'wrap',\n  gap: vars.spacing(1),\n  padding: vars.spacing(0.5),\n  minHeight: 'auto'\n});\n\n/**\n * @deprecated Use the {@link https://mui.com/x/react-data-grid/components/toolbar/ Toolbar} component instead. This component will be removed in a future major release.\n */\nconst GridToolbarContainer = forwardRef(function GridToolbarContainer(props, ref) {\n  const {\n      className,\n      children\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const rootProps = useGridRootProps();\n  const classes = useUtilityClasses(rootProps);\n  if (!children) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(GridToolbarContainerRoot, _extends({\n    className: clsx(classes.root, className),\n    ownerState: rootProps\n  }, other, {\n    ref: ref,\n    children: children\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridToolbarContainer.displayName = \"GridToolbarContainer\";\nprocess.env.NODE_ENV !== \"production\" ? GridToolbarContainer.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport { GridToolbarContainer };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}