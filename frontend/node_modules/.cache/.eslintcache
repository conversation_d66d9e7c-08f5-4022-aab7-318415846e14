[{"/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/index.tsx": "1", "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/reportWebVitals.ts": "2", "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/App.tsx": "3", "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/contexts/AuthContext.tsx": "4", "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/Login.tsx": "5", "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/Dashboard.tsx": "6", "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/components/Layout.tsx": "7", "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/components/ProtectedRoute.tsx": "8", "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/services/api.ts": "9", "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/Users.tsx": "10", "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/Permissions.tsx": "11", "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/Roles.tsx": "12"}, {"size": 554, "mtime": 1751139685683, "results": "13", "hashOfConfig": "14"}, {"size": 425, "mtime": 1751139685684, "results": "15", "hashOfConfig": "14"}, {"size": 2970, "mtime": 1751142168151, "results": "16", "hashOfConfig": "14"}, {"size": 3197, "mtime": 1751139822740, "results": "17", "hashOfConfig": "14"}, {"size": 5158, "mtime": 1751140728545, "results": "18", "hashOfConfig": "14"}, {"size": 8062, "mtime": 1751141101888, "results": "19", "hashOfConfig": "14"}, {"size": 7058, "mtime": 1751139889662, "results": "20", "hashOfConfig": "14"}, {"size": 2375, "mtime": 1751139836739, "results": "21", "hashOfConfig": "14"}, {"size": 6047, "mtime": 1751140740552, "results": "22", "hashOfConfig": "14"}, {"size": 11130, "mtime": 1751142229209, "results": "23", "hashOfConfig": "14"}, {"size": 7885, "mtime": 1751142469523, "results": "24", "hashOfConfig": "14"}, {"size": 8342, "mtime": 1751142444512, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1idpcv8", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/index.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/reportWebVitals.ts", [], [], "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/App.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/contexts/AuthContext.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/Login.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/Dashboard.tsx", ["62"], [], "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/components/Layout.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/components/ProtectedRoute.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/services/api.ts", [], [], "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/Users.tsx", ["63", "64"], [], "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/Permissions.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/Roles.tsx", [], [], {"ruleId": "65", "severity": 1, "message": "66", "line": 4, "column": 3, "nodeType": "67", "messageId": "68", "endLine": 4, "endColumn": 7}, {"ruleId": "65", "severity": 1, "message": "69", "line": 18, "column": 3, "nodeType": "67", "messageId": "68", "endLine": 18, "endColumn": 10}, {"ruleId": "70", "severity": 1, "message": "71", "line": 64, "column": 6, "nodeType": "72", "endLine": 64, "endColumn": 30, "suggestions": "73"}, "@typescript-eslint/no-unused-vars", "'Grid' is defined but never used.", "Identifier", "unusedVar", "'Tooltip' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", "ArrayExpression", ["74"], {"desc": "75", "fix": "76"}, "Update the dependencies array to be: [fetchUsers, page, pageSize, search]", {"range": "77", "text": "78"}, [1594, 1618], "[fetchUsers, page, pageSize, search]"]