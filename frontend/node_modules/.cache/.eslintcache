[{"/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/index.tsx": "1", "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/reportWebVitals.ts": "2", "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/App.tsx": "3", "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/contexts/AuthContext.tsx": "4", "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/Login.tsx": "5", "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/Dashboard.tsx": "6", "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/components/Layout.tsx": "7", "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/components/ProtectedRoute.tsx": "8", "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/services/api.ts": "9", "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/Users.tsx": "10", "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/Permissions.tsx": "11", "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/Roles.tsx": "12", "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/ModernLogin.tsx": "13", "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/components/ModernLayout.tsx": "14", "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/ModernDashboard.tsx": "15", "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/TailwindDashboard.tsx": "16", "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/TailwindLogin.tsx": "17", "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/components/TailwindLayout.tsx": "18"}, {"size": 554, "mtime": 1751139685683, "results": "19", "hashOfConfig": "20"}, {"size": 425, "mtime": 1751139685684, "results": "21", "hashOfConfig": "20"}, {"size": 2718, "mtime": 1751143722500, "results": "22", "hashOfConfig": "20"}, {"size": 3197, "mtime": 1751139822740, "results": "23", "hashOfConfig": "20"}, {"size": 5158, "mtime": 1751140728545, "results": "24", "hashOfConfig": "20"}, {"size": 8062, "mtime": 1751141101888, "results": "25", "hashOfConfig": "20"}, {"size": 7058, "mtime": 1751139889662, "results": "26", "hashOfConfig": "20"}, {"size": 2375, "mtime": 1751139836739, "results": "27", "hashOfConfig": "20"}, {"size": 6047, "mtime": 1751140740552, "results": "28", "hashOfConfig": "20"}, {"size": 11130, "mtime": 1751142229209, "results": "29", "hashOfConfig": "20"}, {"size": 7891, "mtime": 1751142592155, "results": "30", "hashOfConfig": "20"}, {"size": 8372, "mtime": 1751142579867, "results": "31", "hashOfConfig": "20"}, {"size": 5443, "mtime": 1751143028210, "results": "32", "hashOfConfig": "20"}, {"size": 7190, "mtime": 1751142997446, "results": "33", "hashOfConfig": "20"}, {"size": 8419, "mtime": 1751143138073, "results": "34", "hashOfConfig": "20"}, {"size": 9442, "mtime": 1751143511916, "results": "35", "hashOfConfig": "20"}, {"size": 8216, "mtime": 1751143426572, "results": "36", "hashOfConfig": "20"}, {"size": 7668, "mtime": 1751143387075, "results": "37", "hashOfConfig": "20"}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1idpcv8", {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/index.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/reportWebVitals.ts", [], [], "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/App.tsx", ["92", "93", "94", "95"], [], "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/contexts/AuthContext.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/Login.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/Dashboard.tsx", ["96"], [], "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/components/Layout.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/components/ProtectedRoute.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/services/api.ts", [], [], "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/Users.tsx", ["97", "98"], [], "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/Permissions.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/Roles.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/ModernLogin.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/components/ModernLayout.tsx", ["99", "100"], [], "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/ModernDashboard.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/TailwindDashboard.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/TailwindLogin.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/components/TailwindLayout.tsx", [], [], {"ruleId": "101", "severity": 1, "message": "102", "line": 12, "column": 8, "nodeType": "103", "messageId": "104", "endLine": 12, "endColumn": 21}, {"ruleId": "101", "severity": 1, "message": "105", "line": 13, "column": 8, "nodeType": "103", "messageId": "104", "endLine": 13, "endColumn": 27}, {"ruleId": "106", "severity": 2, "message": "107", "line": 55, "column": 22, "nodeType": "108", "messageId": "109", "endLine": 55, "endColumn": 27}, {"ruleId": "106", "severity": 2, "message": "110", "line": 63, "column": 22, "nodeType": "108", "messageId": "109", "endLine": 63, "endColumn": 33}, {"ruleId": "101", "severity": 1, "message": "111", "line": 4, "column": 3, "nodeType": "103", "messageId": "104", "endLine": 4, "endColumn": 7}, {"ruleId": "101", "severity": 1, "message": "112", "line": 18, "column": 3, "nodeType": "103", "messageId": "104", "endLine": 18, "endColumn": 10}, {"ruleId": "113", "severity": 1, "message": "114", "line": 64, "column": 6, "nodeType": "115", "endLine": 64, "endColumn": 30, "suggestions": "116"}, {"ruleId": "101", "severity": 1, "message": "117", "line": 5, "column": 3, "nodeType": "103", "messageId": "104", "endLine": 5, "endColumn": 10}, {"ruleId": "101", "severity": 1, "message": "118", "line": 6, "column": 3, "nodeType": "103", "messageId": "104", "endLine": 6, "endColumn": 9}, "@typescript-eslint/no-unused-vars", "'TailwindRoles' is defined but never used.", "Identifier", "unusedVar", "'TailwindPermissions' is defined but never used.", "react/jsx-no-undef", "'Roles' is not defined.", "JSXIdentifier", "undefined", "'Permissions' is not defined.", "'Grid' is defined but never used.", "'Tooltip' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", "ArrayExpression", ["119"], "'Sidebar' is defined but never used.", "'Navbar' is defined but never used.", {"desc": "120", "fix": "121"}, "Update the dependencies array to be: [fetchUsers, page, pageSize, search]", {"range": "122", "text": "123"}, [1594, 1618], "[fetchUsers, page, pageSize, search]"]