[{"/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/index.tsx": "1", "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/reportWebVitals.ts": "2", "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/App.tsx": "3", "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/contexts/AuthContext.tsx": "4", "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/Login.tsx": "5", "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/Dashboard.tsx": "6", "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/components/Layout.tsx": "7", "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/components/ProtectedRoute.tsx": "8", "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/services/api.ts": "9", "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/Users.tsx": "10"}, {"size": 554, "mtime": 1751139685683, "results": "11", "hashOfConfig": "12"}, {"size": 425, "mtime": 1751139685684, "results": "13", "hashOfConfig": "12"}, {"size": 2374, "mtime": 1751140907333, "results": "14", "hashOfConfig": "12"}, {"size": 3197, "mtime": 1751139822740, "results": "15", "hashOfConfig": "12"}, {"size": 5158, "mtime": 1751140728545, "results": "16", "hashOfConfig": "12"}, {"size": 7984, "mtime": 1751140813420, "results": "17", "hashOfConfig": "12"}, {"size": 7058, "mtime": 1751139889662, "results": "18", "hashOfConfig": "12"}, {"size": 2375, "mtime": 1751139836739, "results": "19", "hashOfConfig": "12"}, {"size": 6047, "mtime": 1751140740552, "results": "20", "hashOfConfig": "12"}, {"size": 11097, "mtime": 1751140885415, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1idpcv8", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/index.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/reportWebVitals.ts", [], [], "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/App.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/contexts/AuthContext.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/Login.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/Dashboard.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/components/Layout.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/components/ProtectedRoute.tsx", [], [], "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/services/api.ts", [], [], "/Users/<USER>/Documents/augment-projects/Users_management/frontend/src/pages/Users.tsx", ["52", "53"], [], {"ruleId": "54", "severity": 1, "message": "55", "line": 8, "column": 3, "nodeType": "56", "messageId": "57", "endLine": 8, "endColumn": 13}, {"ruleId": "58", "severity": 1, "message": "59", "line": 63, "column": 6, "nodeType": "60", "endLine": 63, "endColumn": 30, "suggestions": "61"}, "@typescript-eslint/no-unused-vars", "'IconButton' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", "ArrayExpression", ["62"], {"desc": "63", "fix": "64"}, "Update the dependencies array to be: [fetchUsers, page, pageSize, search]", {"range": "65", "text": "66"}, [1596, 1620], "[fetchUsers, page, pageSize, search]"]