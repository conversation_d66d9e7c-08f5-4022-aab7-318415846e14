#!/usr/bin/env python3
"""
Script pour créer la base de données MySQL.
"""

import pymysql
import sys

def create_database():
    """
    Crée la base de données users_management si elle n'existe pas.
    """
    try:
        # Connexion à MySQL sans spécifier de base de données
        connection = pymysql.connect(
            host='localhost',
            port=8889,
            user='root',
            password='root',
            charset='utf8mb4'
        )
        
        print("✅ Connexion à MySQL réussie !")
        
        with connection.cursor() as cursor:
            # Créer la base de données
            cursor.execute("CREATE DATABASE IF NOT EXISTS users_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print("✅ Base de données 'users_management' créée avec succès !")
            
            # Vérifier que la base de données existe
            cursor.execute("SHOW DATABASES LIKE 'users_management'")
            result = cursor.fetchone()
            if result:
                print("✅ Base de données confirmée !")
            else:
                print("❌ Erreur : Base de données non trouvée après création")
                return False
        
        connection.close()
        return True
        
    except pymysql.Error as e:
        print(f"❌ Erreur MySQL : {e}")
        return False
    except Exception as e:
        print(f"❌ Erreur : {e}")
        return False

if __name__ == "__main__":
    print("🚀 Création de la base de données MySQL...")
    
    if create_database():
        print("\n🎉 Base de données créée avec succès !")
        print("Vous pouvez maintenant redémarrer le serveur FastAPI.")
    else:
        print("\n❌ Échec de la création de la base de données.")
        print("Vérifiez que MySQL est démarré et que les paramètres de connexion sont corrects.")
        sys.exit(1)
