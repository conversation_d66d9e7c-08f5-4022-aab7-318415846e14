{"version": 3, "file": "use-floating.cjs", "sources": ["../../src/hooks/use-floating.ts"], "sourcesContent": ["import type { ElementProps, Placement, ReferenceType, UseRoleProps } from \"@floating-ui/react\";\nimport {\n  autoUpdate,\n  safePolygon,\n  useClick,\n  useDismiss,\n  useFloating,\n  useHover,\n  useInteractions,\n  useRole,\n} from \"@floating-ui/react\";\nimport type { Dispatch, RefObject, SetStateAction } from \"react\";\nimport { getMiddleware, getPlacement } from \"../components/Floating/helpers\";\n\nexport type UseBaseFloatingParams = {\n  placement?: \"auto\" | Placement;\n  open: boolean;\n  arrowRef?: RefObject<HTMLDivElement>;\n  setOpen: Dispatch<SetStateAction<boolean>>;\n};\n\nexport const useBaseFLoating = <Type extends ReferenceType>({\n  open,\n  arrowRef,\n  placement = \"top\",\n  setOpen,\n}: UseBaseFloatingParams) => {\n  return useFloating<Type>({\n    placement: getPlacement({ placement }),\n    open,\n    onOpenChange: setOpen,\n    whileElementsMounted: autoUpdate,\n    middleware: getMiddleware({ placement, arrowRef }),\n  });\n};\n\nexport type UseFloatingInteractionsParams = {\n  context: ReturnType<typeof useFloating>[\"context\"];\n  trigger?: \"hover\" | \"click\";\n  role?: UseRoleProps[\"role\"];\n  interactions?: ElementProps[];\n};\n\nexport const useFloatingInteractions = ({\n  context,\n  trigger,\n  role = \"tooltip\",\n  interactions = [],\n}: UseFloatingInteractionsParams) => {\n  return useInteractions([\n    useClick(context, { enabled: trigger === \"click\" }),\n    useHover(context, {\n      enabled: trigger === \"hover\",\n      handleClose: safePolygon(),\n    }),\n    useDismiss(context),\n    useRole(context, { role }),\n    ...interactions,\n  ]);\n};\n"], "names": ["useFloating", "getPlacement", "autoUpdate", "getMiddleware", "useInteractions", "useClick", "useHover", "safePolygon", "useDismiss", "useRole"], "mappings": ";;;;;AAYY,MAAC,eAAe,GAAG,CAAC;AAChC,EAAE,IAAI;AACN,EAAE,QAAQ;AACV,EAAE,SAAS,GAAG,KAAK;AACnB,EAAE;AACF,CAAC,KAAK;AACN,EAAE,OAAOA,iBAAW,CAAC;AACrB,IAAI,SAAS,EAAEC,oBAAY,CAAC,EAAE,SAAS,EAAE,CAAC;AAC1C,IAAI,IAAI;AACR,IAAI,YAAY,EAAE,OAAO;AACzB,IAAI,oBAAoB,EAAEC,gBAAU;AACpC,IAAI,UAAU,EAAEC,qBAAa,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE;AACrD,GAAG,CAAC;AACJ;AACY,MAAC,uBAAuB,GAAG,CAAC;AACxC,EAAE,OAAO;AACT,EAAE,OAAO;AACT,EAAE,IAAI,GAAG,SAAS;AAClB,EAAE,YAAY,GAAG;AACjB,CAAC,KAAK;AACN,EAAE,OAAOC,qBAAe,CAAC;AACzB,IAAIC,cAAQ,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,OAAO,KAAK,OAAO,EAAE,CAAC;AACvD,IAAIC,cAAQ,CAAC,OAAO,EAAE;AACtB,MAAM,OAAO,EAAE,OAAO,KAAK,OAAO;AAClC,MAAM,WAAW,EAAEC,iBAAW;AAC9B,KAAK,CAAC;AACN,IAAIC,gBAAU,CAAC,OAAO,CAAC;AACvB,IAAIC,aAAO,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,CAAC;AAC9B,IAAI,GAAG;AACP,GAAG,CAAC;AACJ;;;;;"}