'use client';
'use strict';

var React = require('react');

function useWatchLocalStorageValue({
  key: watchKey,
  onChange
}) {
  function handleStorageChange({ key, newValue }) {
    if (key === watchKey) onChange(newValue);
  }
  React.useEffect(() => {
    window.addEventListener("storage", handleStorageChange);
    return () => window.removeEventListener("storage", handleStorageChange);
  }, []);
}

exports.useWatchLocalStorageValue = useWatchLocalStorageValue;
//# sourceMappingURL=use-watch-localstorage-value.cjs.map
