{"version": 3, "file": "use-floating.js", "sources": ["../../src/hooks/use-floating.ts"], "sourcesContent": ["import type { ElementProps, Placement, ReferenceType, UseRoleProps } from \"@floating-ui/react\";\nimport {\n  autoUpdate,\n  safePolygon,\n  useClick,\n  useDismiss,\n  useFloating,\n  useHover,\n  useInteractions,\n  useRole,\n} from \"@floating-ui/react\";\nimport type { Dispatch, RefObject, SetStateAction } from \"react\";\nimport { getMiddleware, getPlacement } from \"../components/Floating/helpers\";\n\nexport type UseBaseFloatingParams = {\n  placement?: \"auto\" | Placement;\n  open: boolean;\n  arrowRef?: RefObject<HTMLDivElement>;\n  setOpen: Dispatch<SetStateAction<boolean>>;\n};\n\nexport const useBaseFLoating = <Type extends ReferenceType>({\n  open,\n  arrowRef,\n  placement = \"top\",\n  setOpen,\n}: UseBaseFloatingParams) => {\n  return useFloating<Type>({\n    placement: getPlacement({ placement }),\n    open,\n    onOpenChange: setOpen,\n    whileElementsMounted: autoUpdate,\n    middleware: getMiddleware({ placement, arrowRef }),\n  });\n};\n\nexport type UseFloatingInteractionsParams = {\n  context: ReturnType<typeof useFloating>[\"context\"];\n  trigger?: \"hover\" | \"click\";\n  role?: UseRoleProps[\"role\"];\n  interactions?: ElementProps[];\n};\n\nexport const useFloatingInteractions = ({\n  context,\n  trigger,\n  role = \"tooltip\",\n  interactions = [],\n}: UseFloatingInteractionsParams) => {\n  return useInteractions([\n    useClick(context, { enabled: trigger === \"click\" }),\n    useHover(context, {\n      enabled: trigger === \"hover\",\n      handleClose: safePolygon(),\n    }),\n    useDismiss(context),\n    useRole(context, { role }),\n    ...interactions,\n  ]);\n};\n"], "names": [], "mappings": ";;;AAYY,MAAC,eAAe,GAAG,CAAC;AAChC,EAAE,IAAI;AACN,EAAE,QAAQ;AACV,EAAE,SAAS,GAAG,KAAK;AACnB,EAAE;AACF,CAAC,KAAK;AACN,EAAE,OAAO,WAAW,CAAC;AACrB,IAAI,SAAS,EAAE,YAAY,CAAC,EAAE,SAAS,EAAE,CAAC;AAC1C,IAAI,IAAI;AACR,IAAI,YAAY,EAAE,OAAO;AACzB,IAAI,oBAAoB,EAAE,UAAU;AACpC,IAAI,UAAU,EAAE,aAAa,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE;AACrD,GAAG,CAAC;AACJ;AACY,MAAC,uBAAuB,GAAG,CAAC;AACxC,EAAE,OAAO;AACT,EAAE,OAAO;AACT,EAAE,IAAI,GAAG,SAAS;AAClB,EAAE,YAAY,GAAG;AACjB,CAAC,KAAK;AACN,EAAE,OAAO,eAAe,CAAC;AACzB,IAAI,QAAQ,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,OAAO,KAAK,OAAO,EAAE,CAAC;AACvD,IAAI,QAAQ,CAAC,OAAO,EAAE;AACtB,MAAM,OAAO,EAAE,OAAO,KAAK,OAAO;AAClC,MAAM,WAAW,EAAE,WAAW;AAC9B,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,OAAO,CAAC;AACvB,IAAI,OAAO,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,CAAC;AAC9B,IAAI,GAAG;AACP,GAAG,CAAC;AACJ;;;;"}