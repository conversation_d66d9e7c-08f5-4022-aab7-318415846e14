import { useFloating, autoUpdate, useInteractions, useClick, useHover, useDismiss, useRole, safePolygon } from '@floating-ui/react';
import { getMiddleware, getPlacement } from '../components/Floating/helpers.js';

const useBaseFLoating = ({
  open,
  arrowRef,
  placement = "top",
  setOpen
}) => {
  return useFloating({
    placement: getPlacement({ placement }),
    open,
    onOpenChange: setOpen,
    whileElementsMounted: autoUpdate,
    middleware: getMiddleware({ placement, arrowRef })
  });
};
const useFloatingInteractions = ({
  context,
  trigger,
  role = "tooltip",
  interactions = []
}) => {
  return useInteractions([
    useClick(context, { enabled: trigger === "click" }),
    useHover(context, {
      enabled: trigger === "hover",
      handleClose: safePolygon()
    }),
    useDismiss(context),
    useRole(context, { role }),
    ...interactions
  ]);
};

export { useBaseFLoating, useFloatingInteractions };
//# sourceMappingURL=use-floating.js.map
