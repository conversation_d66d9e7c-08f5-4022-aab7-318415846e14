{"version": 3, "file": "Select.js", "sources": ["../../../src/components/Select/Select.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps, FC, ReactNode } from \"react\";\nimport { forwardRef } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type {\n  DynamicStringEnumKeysOf,\n  FlowbiteBoolean,\n  FlowbiteColors,\n  FlowbiteSizes,\n  ThemingProps,\n} from \"../../types\";\nimport { selectTheme } from \"./theme\";\n\nexport interface SelectTheme {\n  base: string;\n  addon: string;\n  field: {\n    base: string;\n    icon: {\n      base: string;\n      svg: string;\n    };\n    select: {\n      base: string;\n      withIcon: FlowbiteBoolean;\n      withAddon: FlowbiteBoolean;\n      withShadow: FlowbiteBoolean;\n      sizes: SelectSizes;\n      colors: SelectColors;\n    };\n  };\n}\n\nexport interface SelectColors extends Pick<FlowbiteColors, \"gray\" | \"info\" | \"failure\" | \"warning\" | \"success\"> {\n  [key: string]: string;\n}\n\nexport interface SelectSizes extends Pick<FlowbiteSizes, \"sm\" | \"md\" | \"lg\"> {\n  [key: string]: string;\n}\n\nexport interface SelectProps extends Omit<ComponentProps<\"select\">, \"color\" | \"ref\">, ThemingProps<SelectTheme> {\n  addon?: ReactNode;\n  color?: DynamicStringEnumKeysOf<SelectColors>;\n  icon?: FC<ComponentProps<\"svg\">>;\n  shadow?: boolean;\n  sizing?: DynamicStringEnumKeysOf<SelectSizes>;\n}\n\nexport const Select = forwardRef<HTMLSelectElement, SelectProps>((props, ref) => {\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [selectTheme, provider.theme?.select, props.theme],\n    [get(provider.clearTheme, \"select\"), props.clearTheme],\n    [get(provider.applyTheme, \"select\"), props.applyTheme],\n  );\n\n  const {\n    addon,\n    className,\n    color = \"gray\",\n    icon: Icon,\n    shadow,\n    sizing = \"md\",\n    ...restProps\n  } = resolveProps(props, provider.props?.select);\n\n  return (\n    <div className={twMerge(theme.base, className)}>\n      {addon && <span className={theme.addon}>{addon}</span>}\n      <div className={theme.field.base}>\n        {Icon && (\n          <div className={theme.field.icon.base}>\n            <Icon className={theme.field.icon.svg} />\n          </div>\n        )}\n        <select\n          ref={ref}\n          className={twMerge(\n            theme.field.select.base,\n            theme.field.select.colors[color],\n            theme.field.select.sizes[sizing],\n            theme.field.select.withIcon[Icon ? \"on\" : \"off\"],\n            theme.field.select.withAddon[addon ? \"on\" : \"off\"],\n            theme.field.select.withShadow[shadow ? \"on\" : \"off\"],\n          )}\n          {...restProps}\n        />\n      </div>\n    </div>\n  );\n});\n\nSelect.displayName = \"Select\";\n"], "names": [], "mappings": ";;;;;;;;;AAUY,MAAC,MAAM,GAAG,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACjD,EAAE,MAAM,QAAQ,GAAG,gBAAgB,EAAE;AACrC,EAAE,MAAM,KAAK,GAAG,eAAe;AAC/B,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC;AACtD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC1D,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE,KAAK,CAAC,UAAU;AACzD,GAAG;AACH,EAAE,MAAM;AACR,IAAI,KAAK;AACT,IAAI,SAAS;AACb,IAAI,KAAK,GAAG,MAAM;AAClB,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,MAAM;AACV,IAAI,MAAM,GAAG,IAAI;AACjB,IAAI,GAAG;AACP,GAAG,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC;AACjD,EAAE,uBAAuB,IAAI,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,QAAQ,EAAE;AAC5F,IAAI,KAAK,oBAAoB,GAAG,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;AACrF,oBAAoB,IAAI,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE;AACzE,MAAM,IAAI,oBAAoB,GAAG,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,kBAAkB,GAAG,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC;AACxJ,sBAAsB,GAAG;AACzB,QAAQ,QAAQ;AAChB,QAAQ;AACR,UAAU,GAAG;AACb,UAAU,SAAS,EAAE,OAAO;AAC5B,YAAY,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI;AACnC,YAAY,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC;AAC5C,YAAY,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC;AAC5C,YAAY,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,GAAG,KAAK,CAAC;AAC5D,YAAY,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC;AAC9D,YAAY,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,GAAG,KAAK;AAC/D,WAAW;AACX,UAAU,GAAG;AACb;AACA;AACA,KAAK,EAAE;AACP,GAAG,EAAE,CAAC;AACN,CAAC;AACD,MAAM,CAAC,WAAW,GAAG,QAAQ;;;;"}