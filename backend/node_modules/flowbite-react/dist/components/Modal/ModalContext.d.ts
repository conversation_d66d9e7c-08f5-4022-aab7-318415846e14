import type { ThemingProps } from "../../types";
import type { ModalTheme } from "./Modal";
export interface ModalContextValue extends ThemingProps<ModalTheme> {
    popup?: boolean;
    setHeaderId: (id: string | undefined) => void;
    onClose?: () => void;
}
export declare const ModalContext: import("react").Context<ModalContextValue | undefined>;
export declare function useModalContext(): ModalContextValue;
