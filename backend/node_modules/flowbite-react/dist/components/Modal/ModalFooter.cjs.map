{"version": 3, "file": "ModalFooter.cjs", "sources": ["../../../src/components/Modal/ModalFooter.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentProps } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { ThemingProps } from \"../../types\";\nimport { useModalContext } from \"./ModalContext\";\nimport { modalTheme } from \"./theme\";\n\nexport interface ModalFooterTheme {\n  base: string;\n  popup: string;\n}\n\nexport interface ModalFooterProps extends ComponentProps<\"div\">, ThemingProps<ModalFooterTheme> {}\n\nexport const ModalFooter = forwardRef<HTMLDivElement, ModalFooterProps>((props, ref) => {\n  const { theme: rootTheme, clearTheme: rootClearTheme, applyTheme: rootApplyTheme, popup } = useModalContext();\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [modalTheme.footer, provider.theme?.modal?.footer, rootTheme?.footer, props.theme],\n    [get(provider.clearTheme, \"modal.footer\"), get(rootClearTheme, \"footer\"), props.clearTheme],\n    [get(provider.applyTheme, \"modal.footer\"), get(rootApplyTheme, \"footer\"), props.applyTheme],\n  );\n\n  const { className, ...restProps } = resolveProps(props, provider.props?.modalFooter);\n\n  return <div ref={ref} className={twMerge(theme.base, !popup && theme.popup, className)} {...restProps} />;\n});\n\nModalFooter.displayName = \"ModalFooter\";\n"], "names": ["forwardRef", "useModalContext", "provider", "useThemeProvider", "theme", "useResolveTheme", "modalTheme", "get", "resolveProps", "jsx", "twMerge"], "mappings": ";;;;;;;;;;;;AAWY,MAAC,WAAW,GAAGA,gBAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACtD,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,KAAK,EAAE,GAAGC,4BAAe,EAAE;AAC/G,EAAE,MAAMC,UAAQ,GAAGC,yBAAgB,EAAE;AACrC,EAAE,MAAMC,OAAK,GAAGC,4BAAe;AAC/B,IAAI,CAACC,gBAAU,CAAC,MAAM,EAAEJ,UAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC;AACtF,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,cAAc,CAAC,EAAEK,OAAG,CAAC,cAAc,EAAE,QAAQ,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC/F,IAAI,CAACA,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,cAAc,CAAC,EAAEK,OAAG,CAAC,cAAc,EAAE,QAAQ,CAAC,EAAE,KAAK,CAAC,UAAU;AAC9F,GAAG;AACH,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,SAAS,EAAE,GAAGC,yBAAY,CAAC,KAAK,EAAEN,UAAQ,CAAC,KAAK,EAAE,WAAW,CAAC;AACtF,EAAE,uBAAuBO,cAAG,CAAC,KAAK,EAAE,EAAE,GAAG,EAAE,SAAS,EAAEC,qBAAO,CAACN,OAAK,CAAC,IAAI,EAAE,CAAC,KAAK,IAAIA,OAAK,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE,GAAG,SAAS,EAAE,CAAC;AAC5H,CAAC;AACD,WAAW,CAAC,WAAW,GAAG,aAAa;;;;"}