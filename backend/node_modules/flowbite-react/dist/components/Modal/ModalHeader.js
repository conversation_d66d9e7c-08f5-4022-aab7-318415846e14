'use client';
import { jsxs, jsx } from 'react/jsx-runtime';
import { forwardRef, useId, useLayoutEffect } from 'react';
import { get } from '../../helpers/get.js';
import { resolveProps } from '../../helpers/resolve-props.js';
import { useResolveTheme } from '../../helpers/resolve-theme.js';
import { twMerge } from '../../helpers/tailwind-merge.js';
import { OutlineXIcon } from '../../icons/outline-x-icon.js';
import { useThemeProvider } from '../../theme/provider.js';
import { useModalContext } from './ModalContext.js';
import { modalTheme } from './theme.js';

const ModalHeader = forwardRef((props, ref) => {
  const {
    theme: rootTheme,
    clearTheme: rootClearTheme,
    applyTheme: rootApplyTheme,
    popup,
    onClose,
    setHeaderId
  } = useModalContext();
  const provider = useThemeProvider();
  const theme = useResolveTheme(
    [modalTheme.header, provider.theme?.modal?.header, rootTheme?.header, props.theme],
    [get(provider.clearTheme, "modal.header"), get(rootClearTheme, "header"), props.clearTheme],
    [get(provider.applyTheme, "modal.header"), get(rootApplyTheme, "header"), props.applyTheme]
  );
  const {
    as: Component = "h3",
    children,
    className,
    id,
    ...restProps
  } = resolveProps(props, provider.props?.modalHeader);
  const innerHeaderId = useId();
  const headerId = id || innerHeaderId;
  useLayoutEffect(() => {
    setHeaderId(headerId);
    return () => setHeaderId(void 0);
  }, [headerId, setHeaderId]);
  return /* @__PURE__ */ jsxs("div", { ref, className: twMerge(theme.base, popup && theme.popup, className), ...restProps, children: [
    /* @__PURE__ */ jsx(Component, { id: headerId, className: theme.title, children }),
    /* @__PURE__ */ jsx("button", { "aria-label": "Close", className: theme.close.base, type: "button", onClick: onClose, children: /* @__PURE__ */ jsx(OutlineXIcon, { "aria-hidden": true, className: theme.close.icon }) })
  ] });
});
ModalHeader.displayName = "ModalHeader";

export { ModalHeader };
//# sourceMappingURL=ModalHeader.js.map
