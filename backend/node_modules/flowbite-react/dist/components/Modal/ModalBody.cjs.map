{"version": 3, "file": "ModalBody.cjs", "sources": ["../../../src/components/Modal/ModalBody.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentProps } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { ThemingProps } from \"../../types\";\nimport { useModalContext } from \"./ModalContext\";\nimport { modalTheme } from \"./theme\";\n\nexport interface ModalBodyTheme {\n  base: string;\n  popup: string;\n}\n\nexport interface ModalBodyProps extends ComponentProps<\"div\">, ThemingProps<ModalBodyTheme> {}\n\nexport const ModalBody = forwardRef<HTMLDivElement, ModalBodyProps>((props, ref) => {\n  const { theme: rootTheme, clearTheme: rootClearTheme, applyTheme: rootApplyTheme, popup } = useModalContext();\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [modalTheme.body, provider.theme?.modal?.body, rootTheme?.body, props.theme],\n    [get(provider.clearTheme, \"modal.body\"), get(rootClearTheme, \"body\"), props.clearTheme],\n    [get(provider.applyTheme, \"modal.body\"), get(rootApplyTheme, \"body\"), props.applyTheme],\n  );\n\n  const { className, ...restProps } = resolveProps(props, provider.props?.modalBody);\n\n  return <div ref={ref} className={twMerge(theme.base, popup && theme.popup, className)} {...restProps} />;\n});\n\nModalBody.displayName = \"ModalBody\";\n"], "names": ["forwardRef", "useModalContext", "provider", "useThemeProvider", "theme", "useResolveTheme", "modalTheme", "get", "resolveProps", "jsx", "twMerge"], "mappings": ";;;;;;;;;;;;AAWY,MAAC,SAAS,GAAGA,gBAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACpD,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,KAAK,EAAE,GAAGC,4BAAe,EAAE;AAC/G,EAAE,MAAMC,UAAQ,GAAGC,yBAAgB,EAAE;AACrC,EAAE,MAAMC,OAAK,GAAGC,4BAAe;AAC/B,IAAI,CAACC,gBAAU,CAAC,IAAI,EAAEJ,UAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC;AAChF,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,YAAY,CAAC,EAAEK,OAAG,CAAC,cAAc,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC3F,IAAI,CAACA,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,YAAY,CAAC,EAAEK,OAAG,CAAC,cAAc,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,UAAU;AAC1F,GAAG;AACH,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,SAAS,EAAE,GAAGC,yBAAY,CAAC,KAAK,EAAEN,UAAQ,CAAC,KAAK,EAAE,SAAS,CAAC;AACpF,EAAE,uBAAuBO,cAAG,CAAC,KAAK,EAAE,EAAE,GAAG,EAAE,SAAS,EAAEC,qBAAO,CAACN,OAAK,CAAC,IAAI,EAAE,KAAK,IAAIA,OAAK,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE,GAAG,SAAS,EAAE,CAAC;AAC3H,CAAC;AACD,SAAS,CAAC,WAAW,GAAG,WAAW;;;;"}