{"version": 3, "file": "ModalHeader.js", "sources": ["../../../src/components/Modal/ModalHeader.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, useId, useLayoutEffect, type ComponentProps, type ElementType } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { OutlineXIcon } from \"../../icons/outline-x-icon\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { ThemingProps } from \"../../types\";\nimport { useModalContext } from \"./ModalContext\";\nimport { modalTheme } from \"./theme\";\n\nexport interface ModalHeaderTheme {\n  base: string;\n  popup: string;\n  title: string;\n  close: {\n    base: string;\n    icon: string;\n  };\n}\n\nexport interface ModalHeaderProps extends ComponentProps<\"div\">, ThemingProps<ModalHeaderTheme> {\n  as?: ElementType;\n}\n\nexport const ModalHeader = forwardRef<HTMLDivElement, ModalHeaderProps>((props, ref) => {\n  const {\n    theme: rootTheme,\n    clearTheme: rootClearTheme,\n    applyTheme: rootApplyTheme,\n    popup,\n    onClose,\n    setHeaderId,\n  } = useModalContext();\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [modalTheme.header, provider.theme?.modal?.header, rootTheme?.header, props.theme],\n    [get(provider.clearTheme, \"modal.header\"), get(rootClearTheme, \"header\"), props.clearTheme],\n    [get(provider.applyTheme, \"modal.header\"), get(rootApplyTheme, \"header\"), props.applyTheme],\n  );\n\n  const {\n    as: Component = \"h3\",\n    children,\n    className,\n    id,\n    ...restProps\n  } = resolveProps(props, provider.props?.modalHeader);\n\n  const innerHeaderId = useId();\n  const headerId = id || innerHeaderId;\n\n  useLayoutEffect(() => {\n    setHeaderId(headerId);\n\n    return () => setHeaderId(undefined);\n  }, [headerId, setHeaderId]);\n\n  return (\n    <div ref={ref} className={twMerge(theme.base, popup && theme.popup, className)} {...restProps}>\n      <Component id={headerId} className={theme.title}>\n        {children}\n      </Component>\n      <button aria-label=\"Close\" className={theme.close.base} type=\"button\" onClick={onClose}>\n        <OutlineXIcon aria-hidden className={theme.close.icon} />\n      </button>\n    </div>\n  );\n});\n\nModalHeader.displayName = \"ModalHeader\";\n"], "names": [], "mappings": ";;;;;;;;;;;AAYY,MAAC,WAAW,GAAG,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACtD,EAAE,MAAM;AACR,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,UAAU,EAAE,cAAc;AAC9B,IAAI,UAAU,EAAE,cAAc;AAC9B,IAAI,KAAK;AACT,IAAI,OAAO;AACX,IAAI;AACJ,GAAG,GAAG,eAAe,EAAE;AACvB,EAAE,MAAM,QAAQ,GAAG,gBAAgB,EAAE;AACrC,EAAE,MAAM,KAAK,GAAG,eAAe;AAC/B,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC;AACtF,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,cAAc,CAAC,EAAE,GAAG,CAAC,cAAc,EAAE,QAAQ,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC/F,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,cAAc,CAAC,EAAE,GAAG,CAAC,cAAc,EAAE,QAAQ,CAAC,EAAE,KAAK,CAAC,UAAU;AAC9F,GAAG;AACH,EAAE,MAAM;AACR,IAAI,EAAE,EAAE,SAAS,GAAG,IAAI;AACxB,IAAI,QAAQ;AACZ,IAAI,SAAS;AACb,IAAI,EAAE;AACN,IAAI,GAAG;AACP,GAAG,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,WAAW,CAAC;AACtD,EAAE,MAAM,aAAa,GAAG,KAAK,EAAE;AAC/B,EAAE,MAAM,QAAQ,GAAG,EAAE,IAAI,aAAa;AACtC,EAAE,eAAe,CAAC,MAAM;AACxB,IAAI,WAAW,CAAC,QAAQ,CAAC;AACzB,IAAI,OAAO,MAAM,WAAW,CAAC,MAAM,CAAC;AACpC,GAAG,EAAE,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;AAC7B,EAAE,uBAAuB,IAAI,CAAC,KAAK,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE,GAAG,SAAS,EAAE,QAAQ,EAAE;AACrI,oBAAoB,GAAG,CAAC,SAAS,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,CAAC;AACtF,oBAAoB,GAAG,CAAC,QAAQ,EAAE,EAAE,YAAY,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,kBAAkB,GAAG,CAAC,YAAY,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE;AAC7N,GAAG,EAAE,CAAC;AACN,CAAC;AACD,WAAW,CAAC,WAAW,GAAG,aAAa;;;;"}