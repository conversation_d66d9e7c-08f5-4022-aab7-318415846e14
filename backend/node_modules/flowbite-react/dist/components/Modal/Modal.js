'use client';
import { jsx } from 'react/jsx-runtime';
import { useFloating, useMergeRefs, useClick, useDismiss, useRole, useInteractions, FloatingPortal, FloatingOverlay, FloatingFocusManager } from '@floating-ui/react';
import { forwardRef, useState } from 'react';
import { get } from '../../helpers/get.js';
import { resolveProps } from '../../helpers/resolve-props.js';
import { useResolveTheme } from '../../helpers/resolve-theme.js';
import { twMerge } from '../../helpers/tailwind-merge.js';
import { useThemeProvider } from '../../theme/provider.js';
import { ModalContext } from './ModalContext.js';
import { modalTheme } from './theme.js';

const Modal = forwardRef((props, ref) => {
  const [headerId, setHeaderId] = useState(void 0);
  const provider = useThemeProvider();
  const theme = useResolveTheme(
    [modalTheme, provider.theme?.modal, props.theme],
    [get(provider.clearTheme, "modal"), props.clearTheme],
    [get(provider.applyTheme, "modal"), props.applyTheme]
  );
  const {
    children,
    className,
    dismissible = false,
    onClose,
    popup,
    position = "center",
    root,
    show,
    size = "2xl",
    initialFocus,
    ...restProps
  } = resolveProps(props, provider.props?.modal);
  const { context } = useFloating({
    open: show,
    onOpenChange: () => onClose && onClose()
  });
  const mergedRef = useMergeRefs([context.refs.setFloating, ref]);
  const click = useClick(context);
  const dismiss = useDismiss(context, { outsidePressEvent: "mousedown", enabled: dismissible });
  const role = useRole(context);
  const { getFloatingProps } = useInteractions([click, dismiss, role]);
  if (!show) {
    return null;
  }
  return /* @__PURE__ */ jsx(
    ModalContext.Provider,
    {
      value: {
        theme: props.theme,
        clearTheme: props.clearTheme,
        applyTheme: props.applyTheme,
        popup,
        onClose,
        setHeaderId
      },
      children: /* @__PURE__ */ jsx(FloatingPortal, { root, children: /* @__PURE__ */ jsx(
        FloatingOverlay,
        {
          lockScroll: true,
          "data-testid": "modal-overlay",
          className: twMerge(
            theme.root.base,
            theme.root.positions[position],
            show ? theme.root.show.on : theme.root.show.off,
            className
          ),
          ...restProps,
          children: /* @__PURE__ */ jsx(FloatingFocusManager, { context, initialFocus, children: /* @__PURE__ */ jsx(
            "div",
            {
              ref: mergedRef,
              ...getFloatingProps(restProps),
              "aria-labelledby": headerId,
              className: twMerge(theme.content.base, theme.root.sizes[size]),
              children: /* @__PURE__ */ jsx("div", { className: theme.content.inner, children })
            }
          ) })
        }
      ) })
    }
  );
});
Modal.displayName = "Modal";

export { Modal };
//# sourceMappingURL=Modal.js.map
