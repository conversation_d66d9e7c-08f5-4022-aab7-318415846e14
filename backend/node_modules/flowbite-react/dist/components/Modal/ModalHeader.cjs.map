{"version": 3, "file": "ModalHeader.cjs", "sources": ["../../../src/components/Modal/ModalHeader.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, useId, useLayoutEffect, type ComponentProps, type ElementType } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { OutlineXIcon } from \"../../icons/outline-x-icon\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { ThemingProps } from \"../../types\";\nimport { useModalContext } from \"./ModalContext\";\nimport { modalTheme } from \"./theme\";\n\nexport interface ModalHeaderTheme {\n  base: string;\n  popup: string;\n  title: string;\n  close: {\n    base: string;\n    icon: string;\n  };\n}\n\nexport interface ModalHeaderProps extends ComponentProps<\"div\">, ThemingProps<ModalHeaderTheme> {\n  as?: ElementType;\n}\n\nexport const ModalHeader = forwardRef<HTMLDivElement, ModalHeaderProps>((props, ref) => {\n  const {\n    theme: rootTheme,\n    clearTheme: rootClearTheme,\n    applyTheme: rootApplyTheme,\n    popup,\n    onClose,\n    setHeaderId,\n  } = useModalContext();\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [modalTheme.header, provider.theme?.modal?.header, rootTheme?.header, props.theme],\n    [get(provider.clearTheme, \"modal.header\"), get(rootClearTheme, \"header\"), props.clearTheme],\n    [get(provider.applyTheme, \"modal.header\"), get(rootApplyTheme, \"header\"), props.applyTheme],\n  );\n\n  const {\n    as: Component = \"h3\",\n    children,\n    className,\n    id,\n    ...restProps\n  } = resolveProps(props, provider.props?.modalHeader);\n\n  const innerHeaderId = useId();\n  const headerId = id || innerHeaderId;\n\n  useLayoutEffect(() => {\n    setHeaderId(headerId);\n\n    return () => setHeaderId(undefined);\n  }, [headerId, setHeaderId]);\n\n  return (\n    <div ref={ref} className={twMerge(theme.base, popup && theme.popup, className)} {...restProps}>\n      <Component id={headerId} className={theme.title}>\n        {children}\n      </Component>\n      <button aria-label=\"Close\" className={theme.close.base} type=\"button\" onClick={onClose}>\n        <OutlineXIcon aria-hidden className={theme.close.icon} />\n      </button>\n    </div>\n  );\n});\n\nModalHeader.displayName = \"ModalHeader\";\n"], "names": ["forwardRef", "useModalContext", "provider", "useThemeProvider", "theme", "useResolveTheme", "modalTheme", "get", "resolveProps", "useId", "useLayoutEffect", "jsxs", "twMerge", "jsx", "OutlineXIcon"], "mappings": ";;;;;;;;;;;;;AAYY,MAAC,WAAW,GAAGA,gBAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACtD,EAAE,MAAM;AACR,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,UAAU,EAAE,cAAc;AAC9B,IAAI,UAAU,EAAE,cAAc;AAC9B,IAAI,KAAK;AACT,IAAI,OAAO;AACX,IAAI;AACJ,GAAG,GAAGC,4BAAe,EAAE;AACvB,EAAE,MAAMC,UAAQ,GAAGC,yBAAgB,EAAE;AACrC,EAAE,MAAMC,OAAK,GAAGC,4BAAe;AAC/B,IAAI,CAACC,gBAAU,CAAC,MAAM,EAAEJ,UAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC;AACtF,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,cAAc,CAAC,EAAEK,OAAG,CAAC,cAAc,EAAE,QAAQ,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC/F,IAAI,CAACA,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,cAAc,CAAC,EAAEK,OAAG,CAAC,cAAc,EAAE,QAAQ,CAAC,EAAE,KAAK,CAAC,UAAU;AAC9F,GAAG;AACH,EAAE,MAAM;AACR,IAAI,EAAE,EAAE,SAAS,GAAG,IAAI;AACxB,IAAI,QAAQ;AACZ,IAAI,SAAS;AACb,IAAI,EAAE;AACN,IAAI,GAAG;AACP,GAAG,GAAGC,yBAAY,CAAC,KAAK,EAAEN,UAAQ,CAAC,KAAK,EAAE,WAAW,CAAC;AACtD,EAAE,MAAM,aAAa,GAAGO,WAAK,EAAE;AAC/B,EAAE,MAAM,QAAQ,GAAG,EAAE,IAAI,aAAa;AACtC,EAAEC,qBAAe,CAAC,MAAM;AACxB,IAAI,WAAW,CAAC,QAAQ,CAAC;AACzB,IAAI,OAAO,MAAM,WAAW,CAAC,MAAM,CAAC;AACpC,GAAG,EAAE,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;AAC7B,EAAE,uBAAuBC,eAAI,CAAC,KAAK,EAAE,EAAE,GAAG,EAAE,SAAS,EAAEC,qBAAO,CAACR,OAAK,CAAC,IAAI,EAAE,KAAK,IAAIA,OAAK,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE,GAAG,SAAS,EAAE,QAAQ,EAAE;AACrI,oBAAoBS,cAAG,CAAC,SAAS,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAET,OAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,CAAC;AACtF,oBAAoBS,cAAG,CAAC,QAAQ,EAAE,EAAE,YAAY,EAAE,OAAO,EAAE,SAAS,EAAET,OAAK,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,kBAAkBS,cAAG,CAACC,yBAAY,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAEV,OAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE;AAC7N,GAAG,EAAE,CAAC;AACN,CAAC;AACD,WAAW,CAAC,WAAW,GAAG,aAAa;;;;"}