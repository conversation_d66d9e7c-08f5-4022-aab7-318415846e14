{"version": 3, "file": "Modal.js", "sources": ["../../../src/components/Modal/Modal.tsx"], "sourcesContent": ["\"use client\";\n\nimport {\n  FloatingFocusManager,\n  FloatingOverlay,\n  FloatingPortal,\n  useClick,\n  useDismiss,\n  useFloating,\n  useInteractions,\n  useMergeRefs,\n  useRole,\n} from \"@floating-ui/react\";\nimport type { MutableRefObject } from \"react\";\nimport { forwardRef, useState, type ComponentPropsWithoutRef } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type {\n  DynamicStringEnumKeysOf,\n  FlowbiteBoolean,\n  FlowbitePositions,\n  FlowbiteSizes,\n  ThemingProps,\n} from \"../../types\";\nimport type { ModalBodyTheme } from \"./ModalBody\";\nimport { ModalContext } from \"./ModalContext\";\nimport type { ModalFooterTheme } from \"./ModalFooter\";\nimport type { ModalHeaderTheme } from \"./ModalHeader\";\nimport { modalTheme } from \"./theme\";\n\nexport interface ModalTheme {\n  root: ModalRootTheme;\n  content: ModalContentTheme;\n  body: ModalBodyTheme;\n  header: ModalHeaderTheme;\n  footer: ModalFooterTheme;\n}\n\nexport interface ModalRootTheme {\n  base: string;\n  show: FlowbiteBoolean;\n  sizes: ModalSizes;\n  positions: ModalPositions;\n}\n\nexport interface ModalContentTheme {\n  base: string;\n  inner: string;\n}\n\nexport interface ModalPositions extends FlowbitePositions {\n  [key: string]: string;\n}\n\nexport interface ModalSizes extends Omit<FlowbiteSizes, \"xs\"> {\n  [key: string]: string;\n}\n\nexport interface ModalProps extends ComponentPropsWithoutRef<\"div\">, ThemingProps<ModalTheme> {\n  onClose?: () => void;\n  position?: DynamicStringEnumKeysOf<ModalPositions>;\n  popup?: boolean;\n  root?: HTMLElement;\n  show?: boolean;\n  size?: DynamicStringEnumKeysOf<ModalSizes>;\n  dismissible?: boolean;\n  initialFocus?: number | MutableRefObject<HTMLElement | null>;\n}\n\nexport const Modal = forwardRef<HTMLDivElement, ModalProps>((props, ref) => {\n  const [headerId, setHeaderId] = useState<string | undefined>(undefined);\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [modalTheme, provider.theme?.modal, props.theme],\n    [get(provider.clearTheme, \"modal\"), props.clearTheme],\n    [get(provider.applyTheme, \"modal\"), props.applyTheme],\n  );\n\n  const {\n    children,\n    className,\n    dismissible = false,\n    onClose,\n    popup,\n    position = \"center\",\n    root,\n    show,\n    size = \"2xl\",\n    initialFocus,\n    ...restProps\n  } = resolveProps(props, provider.props?.modal);\n\n  const { context } = useFloating({\n    open: show,\n    onOpenChange: () => onClose && onClose(),\n  });\n\n  const mergedRef = useMergeRefs([context.refs.setFloating, ref]);\n\n  const click = useClick(context);\n  const dismiss = useDismiss(context, { outsidePressEvent: \"mousedown\", enabled: dismissible });\n  const role = useRole(context);\n\n  const { getFloatingProps } = useInteractions([click, dismiss, role]);\n\n  if (!show) {\n    return null;\n  }\n\n  return (\n    <ModalContext.Provider\n      value={{\n        theme: props.theme,\n        clearTheme: props.clearTheme,\n        applyTheme: props.applyTheme,\n        popup,\n        onClose,\n        setHeaderId,\n      }}\n    >\n      <FloatingPortal root={root}>\n        <FloatingOverlay\n          lockScroll\n          data-testid=\"modal-overlay\"\n          className={twMerge(\n            theme.root.base,\n            theme.root.positions[position],\n            show ? theme.root.show.on : theme.root.show.off,\n            className,\n          )}\n          {...restProps}\n        >\n          <FloatingFocusManager context={context} initialFocus={initialFocus}>\n            <div\n              ref={mergedRef}\n              {...getFloatingProps(restProps)}\n              aria-labelledby={headerId}\n              className={twMerge(theme.content.base, theme.root.sizes[size])}\n            >\n              <div className={theme.content.inner}>{children}</div>\n            </div>\n          </FloatingFocusManager>\n        </FloatingOverlay>\n      </FloatingPortal>\n    </ModalContext.Provider>\n  );\n});\n\nModal.displayName = \"Modal\";\n"], "names": [], "mappings": ";;;;;;;;;;;AAsBY,MAAC,KAAK,GAAG,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AAChD,EAAE,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC;AAClD,EAAE,MAAM,QAAQ,GAAG,gBAAgB,EAAE;AACrC,EAAE,MAAM,KAAK,GAAG,eAAe;AAC/B,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC;AACpD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AACzD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,KAAK,CAAC,UAAU;AACxD,GAAG;AACH,EAAE,MAAM;AACR,IAAI,QAAQ;AACZ,IAAI,SAAS;AACb,IAAI,WAAW,GAAG,KAAK;AACvB,IAAI,OAAO;AACX,IAAI,KAAK;AACT,IAAI,QAAQ,GAAG,QAAQ;AACvB,IAAI,IAAI;AACR,IAAI,IAAI;AACR,IAAI,IAAI,GAAG,KAAK;AAChB,IAAI,YAAY;AAChB,IAAI,GAAG;AACP,GAAG,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC;AAChD,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,WAAW,CAAC;AAClC,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,YAAY,EAAE,MAAM,OAAO,IAAI,OAAO;AAC1C,GAAG,CAAC;AACJ,EAAE,MAAM,SAAS,GAAG,YAAY,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;AACjE,EAAE,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC;AACjC,EAAE,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,EAAE,EAAE,iBAAiB,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;AAC/F,EAAE,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC;AAC/B,EAAE,MAAM,EAAE,gBAAgB,EAAE,GAAG,eAAe,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;AACtE,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,IAAI;AACf;AACA,EAAE,uBAAuB,GAAG;AAC5B,IAAI,YAAY,CAAC,QAAQ;AACzB,IAAI;AACJ,MAAM,KAAK,EAAE;AACb,QAAQ,KAAK,EAAE,KAAK,CAAC,KAAK;AAC1B,QAAQ,UAAU,EAAE,KAAK,CAAC,UAAU;AACpC,QAAQ,UAAU,EAAE,KAAK,CAAC,UAAU;AACpC,QAAQ,KAAK;AACb,QAAQ,OAAO;AACf,QAAQ;AACR,OAAO;AACP,MAAM,QAAQ,kBAAkB,GAAG,CAAC,cAAc,EAAE,EAAE,IAAI,EAAE,QAAQ,kBAAkB,GAAG;AACzF,QAAQ,eAAe;AACvB,QAAQ;AACR,UAAU,UAAU,EAAE,IAAI;AAC1B,UAAU,aAAa,EAAE,eAAe;AACxC,UAAU,SAAS,EAAE,OAAO;AAC5B,YAAY,KAAK,CAAC,IAAI,CAAC,IAAI;AAC3B,YAAY,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;AAC1C,YAAY,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;AAC3D,YAAY;AACZ,WAAW;AACX,UAAU,GAAG,SAAS;AACtB,UAAU,QAAQ,kBAAkB,GAAG,CAAC,oBAAoB,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE,QAAQ,kBAAkB,GAAG;AACpH,YAAY,KAAK;AACjB,YAAY;AACZ,cAAc,GAAG,EAAE,SAAS;AAC5B,cAAc,GAAG,gBAAgB,CAAC,SAAS,CAAC;AAC5C,cAAc,iBAAiB,EAAE,QAAQ;AACzC,cAAc,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAC5E,cAAc,QAAQ,kBAAkB,GAAG,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,QAAQ,EAAE;AAC/F;AACA,WAAW,EAAE;AACb;AACA,OAAO,EAAE;AACT;AACA,GAAG;AACH,CAAC;AACD,KAAK,CAAC,WAAW,GAAG,OAAO;;;;"}