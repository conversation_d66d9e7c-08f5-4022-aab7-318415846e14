{"version": 3, "file": "ModalContext.cjs", "sources": ["../../../src/components/Modal/ModalContext.tsx"], "sourcesContent": ["\"use client\";\n\nimport { createContext, useContext } from \"react\";\nimport type { ThemingProps } from \"../../types\";\nimport type { ModalTheme } from \"./Modal\";\n\nexport interface ModalContextValue extends ThemingProps<ModalTheme> {\n  popup?: boolean;\n  setHeaderId: (id: string | undefined) => void;\n  onClose?: () => void;\n}\n\nexport const ModalContext = createContext<ModalContextValue | undefined>(undefined);\n\nexport function useModalContext(): ModalContextValue {\n  const context = useContext(ModalContext);\n\n  if (!context) {\n    throw new Error(\"useModalContext should be used within the ModalContext provider!\");\n  }\n\n  return context;\n}\n"], "names": ["createContext", "useContext"], "mappings": ";;;;AAGY,MAAC,YAAY,GAAGA,mBAAa,CAAC,MAAM;AACzC,SAAS,eAAe,GAAG;AAClC,EAAE,MAAM,OAAO,GAAGC,gBAAU,CAAC,YAAY,CAAC;AAC1C,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC;AACvF;AACA,EAAE,OAAO,OAAO;AAChB;;;;;"}