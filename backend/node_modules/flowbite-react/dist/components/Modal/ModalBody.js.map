{"version": 3, "file": "ModalBody.js", "sources": ["../../../src/components/Modal/ModalBody.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentProps } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { ThemingProps } from \"../../types\";\nimport { useModalContext } from \"./ModalContext\";\nimport { modalTheme } from \"./theme\";\n\nexport interface ModalBodyTheme {\n  base: string;\n  popup: string;\n}\n\nexport interface ModalBodyProps extends ComponentProps<\"div\">, ThemingProps<ModalBodyTheme> {}\n\nexport const ModalBody = forwardRef<HTMLDivElement, ModalBodyProps>((props, ref) => {\n  const { theme: rootTheme, clearTheme: rootClearTheme, applyTheme: rootApplyTheme, popup } = useModalContext();\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [modalTheme.body, provider.theme?.modal?.body, rootTheme?.body, props.theme],\n    [get(provider.clearTheme, \"modal.body\"), get(rootClearTheme, \"body\"), props.clearTheme],\n    [get(provider.applyTheme, \"modal.body\"), get(rootApplyTheme, \"body\"), props.applyTheme],\n  );\n\n  const { className, ...restProps } = resolveProps(props, provider.props?.modalBody);\n\n  return <div ref={ref} className={twMerge(theme.base, popup && theme.popup, className)} {...restProps} />;\n});\n\nModalBody.displayName = \"ModalBody\";\n"], "names": [], "mappings": ";;;;;;;;;;AAWY,MAAC,SAAS,GAAG,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACpD,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,KAAK,EAAE,GAAG,eAAe,EAAE;AAC/G,EAAE,MAAM,QAAQ,GAAG,gBAAgB,EAAE;AACrC,EAAE,MAAM,KAAK,GAAG,eAAe;AAC/B,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC;AAChF,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,YAAY,CAAC,EAAE,GAAG,CAAC,cAAc,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC3F,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,YAAY,CAAC,EAAE,GAAG,CAAC,cAAc,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,UAAU;AAC1F,GAAG;AACH,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,SAAS,EAAE,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,SAAS,CAAC;AACpF,EAAE,uBAAuB,GAAG,CAAC,KAAK,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE,GAAG,SAAS,EAAE,CAAC;AAC3H,CAAC;AACD,SAAS,CAAC,WAAW,GAAG,WAAW;;;;"}