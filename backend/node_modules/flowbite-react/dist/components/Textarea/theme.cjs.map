{"version": 3, "file": "theme.cjs", "sources": ["../../../src/components/Textarea/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { TextareaTheme } from \"./Textarea\";\n\nexport const textareaTheme = createTheme<TextareaTheme>({\n  base: \"block w-full rounded-lg border p-2.5 text-sm focus:outline-none focus:ring-1 disabled:cursor-not-allowed disabled:opacity-50\",\n  colors: {\n    gray: \"border-gray-300 bg-gray-50 text-gray-900 focus:border-primary-500 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-primary-500 dark:focus:ring-primary-500\",\n    info: \"border-cyan-500 bg-cyan-50 text-cyan-900 placeholder-cyan-700 focus:border-cyan-500 focus:ring-cyan-500 dark:border-cyan-400 dark:bg-cyan-100 dark:focus:border-cyan-500 dark:focus:ring-cyan-500\",\n    failure:\n      \"border-red-500 bg-red-50 text-red-900 placeholder-red-700 focus:border-red-500 focus:ring-red-500 dark:border-red-400 dark:bg-red-100 dark:focus:border-red-500 dark:focus:ring-red-500\",\n    warning:\n      \"border-yellow-500 bg-yellow-50 text-yellow-900 placeholder-yellow-700 focus:border-yellow-500 focus:ring-yellow-500 dark:border-yellow-400 dark:bg-yellow-100 dark:focus:border-yellow-500 dark:focus:ring-yellow-500\",\n    success:\n      \"border-green-500 bg-green-50 text-green-900 placeholder-green-700 focus:border-green-500 focus:ring-green-500 dark:border-green-400 dark:bg-green-100 dark:focus:border-green-500 dark:focus:ring-green-500\",\n  },\n  withShadow: {\n    on: \"shadow-sm dark:shadow-sm-light\",\n    off: \"\",\n  },\n});\n"], "names": ["createTheme"], "mappings": ";;;;AAEY,MAAC,aAAa,GAAGA,uBAAW,CAAC;AACzC,EAAE,IAAI,EAAE,8HAA8H;AACtI,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,oOAAoO;AAC9O,IAAI,IAAI,EAAE,mMAAmM;AAC7M,IAAI,OAAO,EAAE,yLAAyL;AACtM,IAAI,OAAO,EAAE,uNAAuN;AACpO,IAAI,OAAO,EAAE;AACb,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,EAAE,EAAE,gCAAgC;AACxC,IAAI,GAAG,EAAE;AACT;AACA,CAAC;;;;"}