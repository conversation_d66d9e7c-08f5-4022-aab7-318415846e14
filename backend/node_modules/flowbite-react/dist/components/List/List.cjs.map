{"version": 3, "file": "List.cjs", "sources": ["../../../src/components/List/List.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentProps, type PropsWithChildren } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { ThemingProps } from \"../../types\";\nimport type { ListItemTheme } from \"./ListItem\";\nimport { listTheme } from \"./theme\";\n\nexport interface ListTheme {\n  root: ListRootTheme;\n  item: ListItemTheme;\n}\n\nexport interface ListRootTheme {\n  base: string;\n  ordered: {\n    on: string;\n    off: string;\n  };\n  horizontal: string;\n  unstyled: string;\n  nested: string;\n}\n\nexport interface ListProps\n  extends PropsWithChildren<ComponentProps<\"ul\"> & ComponentProps<\"ol\">>,\n    ThemingProps<ListRootTheme> {\n  horizontal?: boolean;\n  nested?: boolean;\n  ordered?: boolean;\n  unstyled?: boolean;\n}\n\nexport const List = forwardRef<HTMLUListElement | HTMLOListElement, ListProps>((props, ref) => {\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [listTheme.root, provider.theme?.list?.root, props.theme],\n    [get(provider.clearTheme, \"list.root\"), props.clearTheme],\n    [get(provider.applyTheme, \"list.root\"), props.applyTheme],\n  );\n\n  const { className, horizontal, nested, ordered, unstyled, ...restProps } = resolveProps(props, provider.props?.list);\n\n  const Component = ordered ? \"ol\" : \"ul\";\n\n  return (\n    <Component\n      ref={ref as never}\n      className={twMerge(\n        theme.base,\n        theme.ordered[ordered ? \"on\" : \"off\"],\n        unstyled && theme.unstyled,\n        nested && theme.nested,\n        horizontal && theme.horizontal,\n        className,\n      )}\n      {...restProps}\n    />\n  );\n});\n\nList.displayName = \"List\";\n"], "names": ["forwardRef", "provider", "useThemeProvider", "theme", "useResolveTheme", "listTheme", "get", "resolveProps", "jsx", "twMerge"], "mappings": ";;;;;;;;;;;AAUY,MAAC,IAAI,GAAGA,gBAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AAC/C,EAAE,MAAMC,UAAQ,GAAGC,yBAAgB,EAAE;AACrC,EAAE,MAAMC,OAAK,GAAGC,4BAAe;AAC/B,IAAI,CAACC,eAAS,CAAC,IAAI,EAAEJ,UAAQ,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC;AAC7D,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC7D,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,KAAK,CAAC,UAAU;AAC5D,GAAG;AACH,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,SAAS,EAAE,GAAGM,yBAAY,CAAC,KAAK,EAAEN,UAAQ,CAAC,KAAK,EAAE,IAAI,CAAC;AACtH,EAAE,MAAM,SAAS,GAAG,OAAO,GAAG,IAAI,GAAG,IAAI;AACzC,EAAE,uBAAuBO,cAAG;AAC5B,IAAI,SAAS;AACb,IAAI;AACJ,MAAM,GAAG;AACT,MAAM,SAAS,EAAEC,qBAAO;AACxB,QAAQN,OAAK,CAAC,IAAI;AAClB,QAAQA,OAAK,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,GAAG,KAAK,CAAC;AAC7C,QAAQ,QAAQ,IAAIA,OAAK,CAAC,QAAQ;AAClC,QAAQ,MAAM,IAAIA,OAAK,CAAC,MAAM;AAC9B,QAAQ,UAAU,IAAIA,OAAK,CAAC,UAAU;AACtC,QAAQ;AACR,OAAO;AACP,MAAM,GAAG;AACT;AACA,GAAG;AACH,CAAC;AACD,IAAI,CAAC,WAAW,GAAG,MAAM;;;;"}