{"version": 3, "file": "ListItem.js", "sources": ["../../../src/components/List/ListItem.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentProps, type FC } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { ThemingProps } from \"../../types\";\nimport { listTheme } from \"./theme\";\n\nexport interface ListItemTheme {\n  icon: string;\n  withIcon: {\n    on: string;\n    off: string;\n  };\n}\n\nexport interface ListItemProps extends ComponentProps<\"li\">, ThemingProps<ListItemTheme> {\n  className?: string;\n  icon?: FC<ComponentProps<\"svg\">>;\n}\n\nexport const ListItem = forwardRef<HTMLLIElement, ListItemProps>((props, ref) => {\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [listTheme.item, provider.theme?.list?.item, props.theme],\n    [get(provider.clearTheme, \"list.item\"), props.clearTheme],\n    [get(provider.applyTheme, \"list.item\"), props.applyTheme],\n  );\n\n  const { children, className, icon: Icon, ...restProps } = resolveProps(props, provider.props?.listItem);\n\n  return (\n    <li ref={ref} className={twMerge(theme.withIcon[Icon ? \"on\" : \"off\"], className)} {...restProps}>\n      {Icon && <Icon className={twMerge(theme.icon)} />}\n      {children}\n    </li>\n  );\n});\n\nListItem.displayName = \"ListItem\";\n"], "names": [], "mappings": ";;;;;;;;;AAUY,MAAC,QAAQ,GAAG,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACnD,EAAE,MAAM,QAAQ,GAAG,gBAAgB,EAAE;AACrC,EAAE,MAAM,KAAK,GAAG,eAAe;AAC/B,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC;AAC7D,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC7D,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,KAAK,CAAC,UAAU;AAC5D,GAAG;AACH,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,SAAS,EAAE,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC;AACzG,EAAE,uBAAuB,IAAI,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,GAAG,KAAK,CAAC,EAAE,SAAS,CAAC,EAAE,GAAG,SAAS,EAAE,QAAQ,EAAE;AACvI,IAAI,IAAI,oBAAoB,GAAG,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;AACzE,IAAI;AACJ,GAAG,EAAE,CAAC;AACN,CAAC;AACD,QAAQ,CAAC,WAAW,GAAG,UAAU;;;;"}