{"version": 3, "file": "Navbar.cjs", "sources": ["../../../src/components/Navbar/Navbar.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps } from \"react\";\nimport { forwardRef, useState } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { FlowbiteBoolean, ThemingProps } from \"../../types\";\nimport type { NavbarBrandTheme } from \"./NavbarBrand\";\nimport type { NavbarCollapseTheme } from \"./NavbarCollapse\";\nimport { NavbarContext } from \"./NavbarContext\";\nimport type { NavbarLinkTheme } from \"./NavbarLink\";\nimport type { NavbarToggleThem } from \"./NavbarToggle\";\nimport { navbarTheme } from \"./theme\";\n\nexport interface NavbarTheme {\n  root: NavbarRootTheme;\n  brand: NavbarBrandTheme;\n  collapse: NavbarCollapseTheme;\n  link: NavbarLinkTheme;\n  toggle: NavbarToggleThem;\n}\n\nexport interface NavbarRootTheme {\n  base: string;\n  rounded: FlowbiteBoolean;\n  bordered: FlowbiteBoolean;\n  inner: {\n    base: string;\n    fluid: FlowbiteBoolean;\n  };\n}\n\nexport interface NavbarProps extends ComponentProps<\"nav\">, ThemingProps<NavbarTheme> {\n  menuOpen?: boolean;\n  fluid?: boolean;\n  rounded?: boolean;\n  border?: boolean;\n}\n\nexport const Navbar = forwardRef<HTMLElement, NavbarProps>((props, ref) => {\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [navbarTheme, provider.theme?.navbar, props.theme],\n    [get(provider.clearTheme, \"navbar\"), props.clearTheme],\n    [get(provider.applyTheme, \"navbar\"), props.applyTheme],\n  );\n\n  const {\n    border,\n    children,\n    className,\n    fluid = false,\n    menuOpen,\n    rounded,\n    ...restProps\n  } = resolveProps(props, provider.props?.navbar);\n\n  const [isOpen, setIsOpen] = useState(menuOpen);\n\n  return (\n    <NavbarContext.Provider\n      value={{ theme: props.theme, clearTheme: props.clearTheme, applyTheme: props.applyTheme, isOpen, setIsOpen }}\n    >\n      <nav\n        ref={ref}\n        className={twMerge(\n          theme.root.base,\n          theme.root.bordered[border ? \"on\" : \"off\"],\n          theme.root.rounded[rounded ? \"on\" : \"off\"],\n          className,\n        )}\n        {...restProps}\n      >\n        <div className={twMerge(theme.root.inner.base, theme.root.inner.fluid[fluid ? \"on\" : \"off\"])}>{children}</div>\n      </nav>\n    </NavbarContext.Provider>\n  );\n});\n\nNavbar.displayName = \"Navbar\";\n"], "names": ["forwardRef", "provider", "useThemeProvider", "theme", "useResolveTheme", "navbarTheme", "get", "resolveProps", "useState", "jsx", "NavbarContext", "twMerge"], "mappings": ";;;;;;;;;;;;AAWY,MAAC,MAAM,GAAGA,gBAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACjD,EAAE,MAAMC,UAAQ,GAAGC,yBAAgB,EAAE;AACrC,EAAE,MAAMC,OAAK,GAAGC,4BAAe;AAC/B,IAAI,CAACC,iBAAW,EAAEJ,UAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC;AACtD,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC1D,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE,KAAK,CAAC,UAAU;AACzD,GAAG;AACH,EAAE,MAAM;AACR,IAAI,MAAM;AACV,IAAI,QAAQ;AACZ,IAAI,SAAS;AACb,IAAI,KAAK,GAAG,KAAK;AACjB,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,GAAG;AACP,GAAG,GAAGM,yBAAY,CAAC,KAAK,EAAEN,UAAQ,CAAC,KAAK,EAAE,MAAM,CAAC;AACjD,EAAE,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,GAAGO,cAAQ,CAAC,QAAQ,CAAC;AAChD,EAAE,uBAAuBC,cAAG;AAC5B,IAAIC,2BAAa,CAAC,QAAQ;AAC1B,IAAI;AACJ,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE;AAClH,MAAM,QAAQ,kBAAkBD,cAAG;AACnC,QAAQ,KAAK;AACb,QAAQ;AACR,UAAU,GAAG;AACb,UAAU,SAAS,EAAEE,qBAAO;AAC5B,YAAYR,OAAK,CAAC,IAAI,CAAC,IAAI;AAC3B,YAAYA,OAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,GAAG,KAAK,CAAC;AACtD,YAAYA,OAAK,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,GAAG,KAAK,CAAC;AACtD,YAAY;AACZ,WAAW;AACX,UAAU,GAAG,SAAS;AACtB,UAAU,QAAQ,kBAAkBM,cAAG,CAAC,KAAK,EAAE,EAAE,SAAS,EAAEE,qBAAO,CAACR,OAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAEA,OAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE,QAAQ,EAAE;AACpJ;AACA;AACA;AACA,GAAG;AACH,CAAC;AACD,MAAM,CAAC,WAAW,GAAG,QAAQ;;;;"}