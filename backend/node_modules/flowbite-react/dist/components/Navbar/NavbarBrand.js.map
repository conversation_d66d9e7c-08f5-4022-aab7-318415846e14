{"version": 3, "file": "NavbarBrand.js", "sources": ["../../../src/components/Navbar/NavbarBrand.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentProps, type ElementType } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { ThemingProps } from \"../../types\";\nimport { useNavbarContext } from \"./NavbarContext\";\nimport { navbarTheme } from \"./theme\";\n\nexport interface NavbarBrandTheme {\n  base: string;\n}\n\nexport interface NavbarBrandProps extends ComponentProps<\"a\">, ThemingProps<NavbarBrandTheme> {\n  as?: ElementType;\n  href?: string;\n}\n\nexport const NavbarBrand = forwardRef<HTMLAnchorElement, NavbarBrandProps>((props, ref) => {\n  const { theme: rootTheme, clearTheme: rootClearTheme, applyTheme: rootApplyTheme } = useNavbarContext();\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [navbarTheme.brand, provider.theme?.navbar?.brand, rootTheme?.brand, props.theme],\n    [get(provider.clearTheme, \"navbar.brand\"), get(rootClearTheme, \"brand\"), props.clearTheme],\n    [get(provider.applyTheme, \"navbar.brand\"), get(rootApplyTheme, \"brand\"), props.applyTheme],\n  );\n\n  const { as: Component = \"a\", className, ...restProps } = resolveProps(props, provider.props?.navbarBrand);\n\n  return <Component ref={ref} className={twMerge(theme.base, className)} {...restProps} />;\n});\nNavbarBrand.displayName = \"NavbarBrand\";\n"], "names": [], "mappings": ";;;;;;;;;;AAWY,MAAC,WAAW,GAAG,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACtD,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,GAAG,gBAAgB,EAAE;AACzG,EAAE,MAAM,QAAQ,GAAG,gBAAgB,EAAE;AACrC,EAAE,MAAM,KAAK,GAAG,eAAe;AAC/B,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC;AACrF,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,cAAc,CAAC,EAAE,GAAG,CAAC,cAAc,EAAE,OAAO,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC9F,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,cAAc,CAAC,EAAE,GAAG,CAAC,cAAc,EAAE,OAAO,CAAC,EAAE,KAAK,CAAC,UAAU;AAC7F,GAAG;AACH,EAAE,MAAM,EAAE,EAAE,EAAE,SAAS,GAAG,GAAG,EAAE,SAAS,EAAE,GAAG,SAAS,EAAE,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,WAAW,CAAC;AAC3G,EAAE,uBAAuB,GAAG,CAAC,SAAS,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,GAAG,SAAS,EAAE,CAAC;AACzG,CAAC;AACD,WAAW,CAAC,WAAW,GAAG,aAAa;;;;"}