{"version": 3, "file": "NavbarLink.cjs", "sources": ["../../../src/components/Navbar/NavbarLink.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentProps, type ElementType, type MouseEvent } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { FlowbiteBoolean, ThemingProps } from \"../../types\";\nimport { useNavbarContext } from \"./NavbarContext\";\nimport { navbarTheme } from \"./theme\";\n\nexport interface NavbarLinkTheme {\n  base: string;\n  active: FlowbiteBoolean;\n  disabled: FlowbiteBoolean;\n}\n\nexport interface NavbarLinkProps extends ComponentProps<\"a\">, ThemingProps<NavbarLinkTheme> {\n  active?: boolean;\n  as?: ElementType;\n  disabled?: boolean;\n  href?: string;\n}\n\nexport const NavbarLink = forwardRef<HTMLLIElement, NavbarLinkProps>((props, ref) => {\n  const { theme: rootTheme, clearTheme: rootClearTheme, applyTheme: rootApplyTheme, setIsOpen } = useNavbarContext();\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [navbarTheme.link, provider.theme?.navbar?.link, rootTheme?.link, props.theme],\n    [get(provider.clearTheme, \"navbar.link\"), get(rootClearTheme, \"link\"), props.clearTheme],\n    [get(provider.applyTheme, \"navbar.link\"), get(rootApplyTheme, \"link\"), props.applyTheme],\n  );\n\n  const {\n    active,\n    as: Component = \"a\",\n    disabled,\n    children,\n    className,\n    onClick,\n    ...restProps\n  } = resolveProps(props, provider.props?.navbarLink);\n\n  function handleClick(event: MouseEvent<HTMLAnchorElement>) {\n    setIsOpen(false);\n    onClick?.(event);\n  }\n\n  return (\n    <li ref={ref}>\n      <Component\n        className={twMerge(\n          theme.base,\n          active && theme.active.on,\n          !active && !disabled && theme.active.off,\n          theme.disabled[disabled ? \"on\" : \"off\"],\n          className,\n        )}\n        onClick={handleClick}\n        {...restProps}\n      >\n        {children}\n      </Component>\n    </li>\n  );\n});\n\nNavbarLink.displayName = \"NavbarLink\";\n"], "names": ["forwardRef", "useNavbarContext", "provider", "useThemeProvider", "theme", "useResolveTheme", "navbarTheme", "get", "resolveProps", "jsx", "twMerge"], "mappings": ";;;;;;;;;;;;AAWY,MAAC,UAAU,GAAGA,gBAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACrD,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,SAAS,EAAE,GAAGC,8BAAgB,EAAE;AACpH,EAAE,MAAMC,UAAQ,GAAGC,yBAAgB,EAAE;AACrC,EAAE,MAAMC,OAAK,GAAGC,4BAAe;AAC/B,IAAI,CAACC,iBAAW,CAAC,IAAI,EAAEJ,UAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC;AAClF,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,aAAa,CAAC,EAAEK,OAAG,CAAC,cAAc,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC5F,IAAI,CAACA,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,aAAa,CAAC,EAAEK,OAAG,CAAC,cAAc,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,UAAU;AAC3F,GAAG;AACH,EAAE,MAAM;AACR,IAAI,MAAM;AACV,IAAI,EAAE,EAAE,SAAS,GAAG,GAAG;AACvB,IAAI,QAAQ;AACZ,IAAI,QAAQ;AACZ,IAAI,SAAS;AACb,IAAI,OAAO;AACX,IAAI,GAAG;AACP,GAAG,GAAGC,yBAAY,CAAC,KAAK,EAAEN,UAAQ,CAAC,KAAK,EAAE,UAAU,CAAC;AACrD,EAAE,SAAS,WAAW,CAAC,KAAK,EAAE;AAC9B,IAAI,SAAS,CAAC,KAAK,CAAC;AACpB,IAAI,OAAO,GAAG,KAAK,CAAC;AACpB;AACA,EAAE,uBAAuBO,cAAG,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,QAAQ,kBAAkBA,cAAG;AACvE,IAAI,SAAS;AACb,IAAI;AACJ,MAAM,SAAS,EAAEC,qBAAO;AACxB,QAAQN,OAAK,CAAC,IAAI;AAClB,QAAQ,MAAM,IAAIA,OAAK,CAAC,MAAM,CAAC,EAAE;AACjC,QAAQ,CAAC,MAAM,IAAI,CAAC,QAAQ,IAAIA,OAAK,CAAC,MAAM,CAAC,GAAG;AAChD,QAAQA,OAAK,CAAC,QAAQ,CAAC,QAAQ,GAAG,IAAI,GAAG,KAAK,CAAC;AAC/C,QAAQ;AACR,OAAO;AACP,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,GAAG,SAAS;AAClB,MAAM;AACN;AACA,GAAG,EAAE,CAAC;AACN,CAAC;AACD,UAAU,CAAC,WAAW,GAAG,YAAY;;;;"}