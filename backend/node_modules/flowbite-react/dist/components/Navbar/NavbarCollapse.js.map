{"version": 3, "file": "NavbarCollapse.js", "sources": ["../../../src/components/Navbar/NavbarCollapse.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentProps } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { FlowbiteBoolean, ThemingProps } from \"../../types\";\nimport { useNavbarContext } from \"./NavbarContext\";\nimport { navbarTheme } from \"./theme\";\n\nexport interface NavbarCollapseTheme {\n  base: string;\n  list: string;\n  hidden: FlowbiteBoolean;\n}\n\nexport interface NavbarCollapseProps extends ComponentProps<\"div\">, ThemingProps<NavbarCollapseTheme> {}\n\nexport const NavbarCollapse = forwardRef<HTMLDivElement, NavbarCollapseProps>((props, ref) => {\n  const { theme: rootTheme, clearTheme: rootClearTheme, applyTheme: rootApplyTheme, isOpen } = useNavbarContext();\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [navbarTheme.collapse, provider.theme?.navbar?.collapse, rootTheme?.collapse, props.theme],\n    [get(provider.clearTheme, \"navbar.collapse\"), get(rootClearTheme, \"collapse\"), props.clearTheme],\n    [get(provider.applyTheme, \"navbar.collapse\"), get(rootApplyTheme, \"collapse\"), props.applyTheme],\n  );\n\n  const { children, className, ...restProps } = resolveProps(props, provider.props?.navbarCollapse);\n\n  return (\n    <div\n      ref={ref}\n      data-testid=\"flowbite-navbar-collapse\"\n      className={twMerge(theme.base, theme.hidden[!isOpen ? \"on\" : \"off\"], className)}\n      {...restProps}\n    >\n      <ul className={theme.list}>{children}</ul>\n    </div>\n  );\n});\n\nNavbarCollapse.displayName = \"NavbarCollapse\";\n"], "names": [], "mappings": ";;;;;;;;;;AAWY,MAAC,cAAc,GAAG,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACzD,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,MAAM,EAAE,GAAG,gBAAgB,EAAE;AACjH,EAAE,MAAM,QAAQ,GAAG,gBAAgB,EAAE;AACrC,EAAE,MAAM,KAAK,GAAG,eAAe;AAC/B,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,CAAC,KAAK,CAAC;AAC9F,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,iBAAiB,CAAC,EAAE,GAAG,CAAC,cAAc,EAAE,UAAU,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AACpG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,iBAAiB,CAAC,EAAE,GAAG,CAAC,cAAc,EAAE,UAAU,CAAC,EAAE,KAAK,CAAC,UAAU;AACnG,GAAG;AACH,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,SAAS,EAAE,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,cAAc,CAAC;AACnG,EAAE,uBAAuB,GAAG;AAC5B,IAAI,KAAK;AACT,IAAI;AACJ,MAAM,GAAG;AACT,MAAM,aAAa,EAAE,0BAA0B;AAC/C,MAAM,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,IAAI,GAAG,KAAK,CAAC,EAAE,SAAS,CAAC;AACrF,MAAM,GAAG,SAAS;AAClB,MAAM,QAAQ,kBAAkB,GAAG,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE;AAC7E;AACA,GAAG;AACH,CAAC;AACD,cAAc,CAAC,WAAW,GAAG,gBAAgB;;;;"}