{"version": 3, "file": "NavbarToggle.cjs", "sources": ["../../../src/components/Navbar/NavbarToggle.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentProps, type FC } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { BarsIcon } from \"../../icons/bars-icon\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { ThemingProps } from \"../../types\";\nimport { useNavbarContext } from \"./NavbarContext\";\nimport { navbarTheme } from \"./theme\";\n\nexport interface NavbarToggleThem {\n  base: string;\n  icon: string;\n  title: string;\n}\n\nexport interface NavbarToggleProps extends ComponentProps<\"button\">, ThemingProps<NavbarToggleThem> {\n  barIcon?: FC<ComponentProps<\"svg\">>;\n}\n\nexport const NavbarToggle = forwardRef<HTMLButtonElement, NavbarToggleProps>((props, ref) => {\n  const {\n    theme: rootTheme,\n    clearTheme: rootClearTheme,\n    applyTheme: rootApplyTheme,\n    isOpen,\n    setIsOpen,\n  } = useNavbarContext();\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [navbarTheme.toggle, provider.theme?.navbar?.toggle, rootTheme?.toggle, props.theme],\n    [get(provider.clearTheme, \"navbar.toggle\"), get(rootClearTheme, \"toggle\"), props.clearTheme],\n    [get(provider.applyTheme, \"navbar.toggle\"), get(rootApplyTheme, \"toggle\"), props.applyTheme],\n  );\n\n  const { barIcon: BarIcon = BarsIcon, className, ...restProps } = resolveProps(props, provider.props?.navbarToggle);\n\n  function handleClick() {\n    setIsOpen(!isOpen);\n  }\n\n  return (\n    <button\n      ref={ref}\n      data-testid=\"flowbite-navbar-toggle\"\n      onClick={handleClick}\n      className={twMerge(theme.base, className)}\n      {...restProps}\n    >\n      <span className={theme.title}>Open main menu</span>\n      <BarIcon aria-hidden className={theme.icon} />\n    </button>\n  );\n});\n\nNavbarToggle.displayName = \"NavbarToggle\";\n"], "names": ["forwardRef", "useNavbarContext", "provider", "useThemeProvider", "theme", "useResolveTheme", "navbarTheme", "get", "BarsIcon", "resolveProps", "jsxs", "twMerge", "jsx"], "mappings": ";;;;;;;;;;;;;AAYY,MAAC,YAAY,GAAGA,gBAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACvD,EAAE,MAAM;AACR,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,UAAU,EAAE,cAAc;AAC9B,IAAI,UAAU,EAAE,cAAc;AAC9B,IAAI,MAAM;AACV,IAAI;AACJ,GAAG,GAAGC,8BAAgB,EAAE;AACxB,EAAE,MAAMC,UAAQ,GAAGC,yBAAgB,EAAE;AACrC,EAAE,MAAMC,OAAK,GAAGC,4BAAe;AAC/B,IAAI,CAACC,iBAAW,CAAC,MAAM,EAAEJ,UAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC;AACxF,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,eAAe,CAAC,EAAEK,OAAG,CAAC,cAAc,EAAE,QAAQ,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAChG,IAAI,CAACA,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,eAAe,CAAC,EAAEK,OAAG,CAAC,cAAc,EAAE,QAAQ,CAAC,EAAE,KAAK,CAAC,UAAU;AAC/F,GAAG;AACH,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,GAAGC,iBAAQ,EAAE,SAAS,EAAE,GAAG,SAAS,EAAE,GAAGC,yBAAY,CAAC,KAAK,EAAEP,UAAQ,CAAC,KAAK,EAAE,YAAY,CAAC;AACpH,EAAE,SAAS,WAAW,GAAG;AACzB,IAAI,SAAS,CAAC,CAAC,MAAM,CAAC;AACtB;AACA,EAAE,uBAAuBQ,eAAI;AAC7B,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,GAAG;AACT,MAAM,aAAa,EAAE,wBAAwB;AAC7C,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,SAAS,EAAEC,qBAAO,CAACP,OAAK,CAAC,IAAI,EAAE,SAAS,CAAC;AAC/C,MAAM,GAAG,SAAS;AAClB,MAAM,QAAQ,EAAE;AAChB,wBAAwBQ,cAAG,CAAC,MAAM,EAAE,EAAE,SAAS,EAAER,OAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,gBAAgB,EAAE,CAAC;AAC3F,wBAAwBQ,cAAG,CAAC,OAAO,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAER,OAAK,CAAC,IAAI,EAAE;AACnF;AACA;AACA,GAAG;AACH,CAAC;AACD,YAAY,CAAC,WAAW,GAAG,cAAc;;;;"}