{"version": 3, "file": "NavbarToggle.js", "sources": ["../../../src/components/Navbar/NavbarToggle.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentProps, type FC } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { BarsIcon } from \"../../icons/bars-icon\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { ThemingProps } from \"../../types\";\nimport { useNavbarContext } from \"./NavbarContext\";\nimport { navbarTheme } from \"./theme\";\n\nexport interface NavbarToggleThem {\n  base: string;\n  icon: string;\n  title: string;\n}\n\nexport interface NavbarToggleProps extends ComponentProps<\"button\">, ThemingProps<NavbarToggleThem> {\n  barIcon?: FC<ComponentProps<\"svg\">>;\n}\n\nexport const NavbarToggle = forwardRef<HTMLButtonElement, NavbarToggleProps>((props, ref) => {\n  const {\n    theme: rootTheme,\n    clearTheme: rootClearTheme,\n    applyTheme: rootApplyTheme,\n    isOpen,\n    setIsOpen,\n  } = useNavbarContext();\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [navbarTheme.toggle, provider.theme?.navbar?.toggle, rootTheme?.toggle, props.theme],\n    [get(provider.clearTheme, \"navbar.toggle\"), get(rootClearTheme, \"toggle\"), props.clearTheme],\n    [get(provider.applyTheme, \"navbar.toggle\"), get(rootApplyTheme, \"toggle\"), props.applyTheme],\n  );\n\n  const { barIcon: BarIcon = BarsIcon, className, ...restProps } = resolveProps(props, provider.props?.navbarToggle);\n\n  function handleClick() {\n    setIsOpen(!isOpen);\n  }\n\n  return (\n    <button\n      ref={ref}\n      data-testid=\"flowbite-navbar-toggle\"\n      onClick={handleClick}\n      className={twMerge(theme.base, className)}\n      {...restProps}\n    >\n      <span className={theme.title}>Open main menu</span>\n      <BarIcon aria-hidden className={theme.icon} />\n    </button>\n  );\n});\n\nNavbarToggle.displayName = \"NavbarToggle\";\n"], "names": [], "mappings": ";;;;;;;;;;;AAYY,MAAC,YAAY,GAAG,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACvD,EAAE,MAAM;AACR,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,UAAU,EAAE,cAAc;AAC9B,IAAI,UAAU,EAAE,cAAc;AAC9B,IAAI,MAAM;AACV,IAAI;AACJ,GAAG,GAAG,gBAAgB,EAAE;AACxB,EAAE,MAAM,QAAQ,GAAG,gBAAgB,EAAE;AACrC,EAAE,MAAM,KAAK,GAAG,eAAe;AAC/B,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC;AACxF,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,eAAe,CAAC,EAAE,GAAG,CAAC,cAAc,EAAE,QAAQ,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAChG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,eAAe,CAAC,EAAE,GAAG,CAAC,cAAc,EAAE,QAAQ,CAAC,EAAE,KAAK,CAAC,UAAU;AAC/F,GAAG;AACH,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,GAAG,QAAQ,EAAE,SAAS,EAAE,GAAG,SAAS,EAAE,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,YAAY,CAAC;AACpH,EAAE,SAAS,WAAW,GAAG;AACzB,IAAI,SAAS,CAAC,CAAC,MAAM,CAAC;AACtB;AACA,EAAE,uBAAuB,IAAI;AAC7B,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,GAAG;AACT,MAAM,aAAa,EAAE,wBAAwB;AAC7C,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC;AAC/C,MAAM,GAAG,SAAS;AAClB,MAAM,QAAQ,EAAE;AAChB,wBAAwB,GAAG,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,gBAAgB,EAAE,CAAC;AAC3F,wBAAwB,GAAG,CAAC,OAAO,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,IAAI,EAAE;AACnF;AACA;AACA,GAAG;AACH,CAAC;AACD,YAAY,CAAC,WAAW,GAAG,cAAc;;;;"}