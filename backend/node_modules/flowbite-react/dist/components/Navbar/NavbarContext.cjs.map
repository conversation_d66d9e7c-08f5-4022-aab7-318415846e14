{"version": 3, "file": "NavbarContext.cjs", "sources": ["../../../src/components/Navbar/NavbarContext.tsx"], "sourcesContent": ["\"use client\";\n\nimport { createContext, useContext } from \"react\";\nimport type { ThemingProps } from \"../../types\";\nimport type { NavbarTheme } from \"./Navbar\";\n\nexport interface NavbarContextValue extends ThemingProps<NavbarTheme> {\n  isOpen?: boolean;\n  setIsOpen: (isOpen: boolean) => void;\n}\n\nexport const NavbarContext = createContext<NavbarContextValue | undefined>(undefined);\n\nexport function useNavbarContext(): NavbarContextValue {\n  const context = useContext(NavbarContext);\n\n  if (!context) {\n    throw new Error(\"useNavBarContext should be used within the NavbarContext provider!\");\n  }\n\n  return context;\n}\n"], "names": ["createContext", "useContext"], "mappings": ";;;;AAGY,MAAC,aAAa,GAAGA,mBAAa,CAAC,MAAM;AAC1C,SAAS,gBAAgB,GAAG;AACnC,EAAE,MAAM,OAAO,GAAGC,gBAAU,CAAC,aAAa,CAAC;AAC3C,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,MAAM,IAAI,KAAK,CAAC,oEAAoE,CAAC;AACzF;AACA,EAAE,OAAO,OAAO;AAChB;;;;;"}