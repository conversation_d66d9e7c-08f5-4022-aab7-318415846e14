{"version": 3, "file": "Navbar.js", "sources": ["../../../src/components/Navbar/Navbar.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps } from \"react\";\nimport { forwardRef, useState } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { FlowbiteBoolean, ThemingProps } from \"../../types\";\nimport type { NavbarBrandTheme } from \"./NavbarBrand\";\nimport type { NavbarCollapseTheme } from \"./NavbarCollapse\";\nimport { NavbarContext } from \"./NavbarContext\";\nimport type { NavbarLinkTheme } from \"./NavbarLink\";\nimport type { NavbarToggleThem } from \"./NavbarToggle\";\nimport { navbarTheme } from \"./theme\";\n\nexport interface NavbarTheme {\n  root: NavbarRootTheme;\n  brand: NavbarBrandTheme;\n  collapse: NavbarCollapseTheme;\n  link: NavbarLinkTheme;\n  toggle: NavbarToggleThem;\n}\n\nexport interface NavbarRootTheme {\n  base: string;\n  rounded: FlowbiteBoolean;\n  bordered: FlowbiteBoolean;\n  inner: {\n    base: string;\n    fluid: FlowbiteBoolean;\n  };\n}\n\nexport interface NavbarProps extends ComponentProps<\"nav\">, ThemingProps<NavbarTheme> {\n  menuOpen?: boolean;\n  fluid?: boolean;\n  rounded?: boolean;\n  border?: boolean;\n}\n\nexport const Navbar = forwardRef<HTMLElement, NavbarProps>((props, ref) => {\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [navbarTheme, provider.theme?.navbar, props.theme],\n    [get(provider.clearTheme, \"navbar\"), props.clearTheme],\n    [get(provider.applyTheme, \"navbar\"), props.applyTheme],\n  );\n\n  const {\n    border,\n    children,\n    className,\n    fluid = false,\n    menuOpen,\n    rounded,\n    ...restProps\n  } = resolveProps(props, provider.props?.navbar);\n\n  const [isOpen, setIsOpen] = useState(menuOpen);\n\n  return (\n    <NavbarContext.Provider\n      value={{ theme: props.theme, clearTheme: props.clearTheme, applyTheme: props.applyTheme, isOpen, setIsOpen }}\n    >\n      <nav\n        ref={ref}\n        className={twMerge(\n          theme.root.base,\n          theme.root.bordered[border ? \"on\" : \"off\"],\n          theme.root.rounded[rounded ? \"on\" : \"off\"],\n          className,\n        )}\n        {...restProps}\n      >\n        <div className={twMerge(theme.root.inner.base, theme.root.inner.fluid[fluid ? \"on\" : \"off\"])}>{children}</div>\n      </nav>\n    </NavbarContext.Provider>\n  );\n});\n\nNavbar.displayName = \"Navbar\";\n"], "names": [], "mappings": ";;;;;;;;;;AAWY,MAAC,MAAM,GAAG,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACjD,EAAE,MAAM,QAAQ,GAAG,gBAAgB,EAAE;AACrC,EAAE,MAAM,KAAK,GAAG,eAAe;AAC/B,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC;AACtD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC1D,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE,KAAK,CAAC,UAAU;AACzD,GAAG;AACH,EAAE,MAAM;AACR,IAAI,MAAM;AACV,IAAI,QAAQ;AACZ,IAAI,SAAS;AACb,IAAI,KAAK,GAAG,KAAK;AACjB,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,GAAG;AACP,GAAG,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC;AACjD,EAAE,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC;AAChD,EAAE,uBAAuB,GAAG;AAC5B,IAAI,aAAa,CAAC,QAAQ;AAC1B,IAAI;AACJ,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE;AAClH,MAAM,QAAQ,kBAAkB,GAAG;AACnC,QAAQ,KAAK;AACb,QAAQ;AACR,UAAU,GAAG;AACb,UAAU,SAAS,EAAE,OAAO;AAC5B,YAAY,KAAK,CAAC,IAAI,CAAC,IAAI;AAC3B,YAAY,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,GAAG,KAAK,CAAC;AACtD,YAAY,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,GAAG,KAAK,CAAC;AACtD,YAAY;AACZ,WAAW;AACX,UAAU,GAAG,SAAS;AACtB,UAAU,QAAQ,kBAAkB,GAAG,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE,QAAQ,EAAE;AACpJ;AACA;AACA;AACA,GAAG;AACH,CAAC;AACD,MAAM,CAAC,WAAW,GAAG,QAAQ;;;;"}