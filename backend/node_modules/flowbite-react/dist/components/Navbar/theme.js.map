{"version": 3, "file": "theme.js", "sources": ["../../../src/components/Navbar/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { NavbarTheme } from \"./Navbar\";\n\nexport const navbarTheme = createTheme<NavbarTheme>({\n  root: {\n    base: \"bg-white px-2 py-2.5 sm:px-4 dark:border-gray-700 dark:bg-gray-800\",\n    rounded: {\n      on: \"rounded\",\n      off: \"\",\n    },\n    bordered: {\n      on: \"border\",\n      off: \"\",\n    },\n    inner: {\n      base: \"mx-auto flex flex-wrap items-center justify-between\",\n      fluid: {\n        on: \"\",\n        off: \"container\",\n      },\n    },\n  },\n  brand: {\n    base: \"flex items-center\",\n  },\n  collapse: {\n    base: \"w-full md:block md:w-auto\",\n    list: \"mt-4 flex flex-col md:mt-0 md:flex-row md:space-x-8 md:text-sm md:font-medium\",\n    hidden: {\n      on: \"hidden\",\n      off: \"\",\n    },\n  },\n  link: {\n    base: \"block py-2 pl-3 pr-4 md:p-0\",\n    active: {\n      on: \"bg-primary-700 text-white md:bg-transparent md:text-primary-700 dark:text-white\",\n      off: \"border-b border-gray-100 text-gray-700 hover:bg-gray-50 md:border-0 md:hover:bg-transparent md:hover:text-primary-700 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent md:dark:hover:text-white\",\n    },\n    disabled: {\n      on: \"text-gray-400 hover:cursor-not-allowed dark:text-gray-600\",\n      off: \"\",\n    },\n  },\n  toggle: {\n    base: \"inline-flex items-center rounded-lg p-2 text-sm text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 md:hidden dark:text-gray-400 dark:hover:bg-gray-700 dark:focus:ring-gray-600\",\n    icon: \"h-6 w-6 shrink-0\",\n    title: \"sr-only\",\n  },\n});\n"], "names": [], "mappings": ";;AAEY,MAAC,WAAW,GAAG,WAAW,CAAC;AACvC,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,oEAAoE;AAC9E,IAAI,OAAO,EAAE;AACb,MAAM,EAAE,EAAE,SAAS;AACnB,MAAM,GAAG,EAAE;AACX,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,EAAE,EAAE,QAAQ;AAClB,MAAM,GAAG,EAAE;AACX,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,IAAI,EAAE,qDAAqD;AACjE,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE,EAAE;AACd,QAAQ,GAAG,EAAE;AACb;AACA;AACA,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE;AACV,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,2BAA2B;AACrC,IAAI,IAAI,EAAE,+EAA+E;AACzF,IAAI,MAAM,EAAE;AACZ,MAAM,EAAE,EAAE,QAAQ;AAClB,MAAM,GAAG,EAAE;AACX;AACA,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,6BAA6B;AACvC,IAAI,MAAM,EAAE;AACZ,MAAM,EAAE,EAAE,iFAAiF;AAC3F,MAAM,GAAG,EAAE;AACX,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,EAAE,EAAE,2DAA2D;AACrE,MAAM,GAAG,EAAE;AACX;AACA,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,kNAAkN;AAC5N,IAAI,IAAI,EAAE,kBAAkB;AAC5B,IAAI,KAAK,EAAE;AACX;AACA,CAAC;;;;"}