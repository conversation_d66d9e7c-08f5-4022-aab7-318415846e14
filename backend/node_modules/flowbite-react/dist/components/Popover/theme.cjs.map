{"version": 3, "file": "theme.cjs", "sources": ["../../../src/components/Popover/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { PopoverTheme } from \"./Popover\";\n\nexport const popoverTheme = createTheme<PopoverTheme>({\n  base: \"absolute z-20 inline-block w-max max-w-[100vw] rounded-lg border border-gray-200 bg-white shadow-sm outline-none dark:border-gray-600 dark:bg-gray-800\",\n  inner: \"relative\",\n  content: \"z-10 overflow-hidden rounded-[7px]\",\n  arrow: {\n    base: \"absolute z-0 h-2 w-2 rotate-45 border border-gray-200 bg-white mix-blend-lighten dark:border-gray-600 dark:bg-gray-800 dark:mix-blend-color\",\n    placement: \"-4px\",\n  },\n});\n"], "names": ["createTheme"], "mappings": ";;;;AAEY,MAAC,YAAY,GAAGA,uBAAW,CAAC;AACxC,EAAE,IAAI,EAAE,wJAAwJ;AAChK,EAAE,KAAK,EAAE,UAAU;AACnB,EAAE,OAAO,EAAE,oCAAoC;AAC/C,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,6IAA6I;AACvJ,IAAI,SAAS,EAAE;AACf;AACA,CAAC;;;;"}