{"version": 3, "file": "Popover.cjs", "sources": ["../../../src/components/Popover/Popover.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { Placement } from \"@floating-ui/react\";\nimport { FloatingFocusManager, useMergeRefs } from \"@floating-ui/react\";\nimport type { ComponentProps, ComponentPropsWithRef, Dispatch, ReactNode, SetStateAction } from \"react\";\nimport { cloneElement, isValidElement, useMemo, useRef, useState } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { useBaseFLoating, useFloatingInteractions } from \"../../hooks/use-floating\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { ThemingProps } from \"../../types\";\nimport type { FloatingArrowTheme } from \"../Floating\";\nimport { getArrowPlacement } from \"../Floating/helpers\";\nimport { popoverTheme } from \"./theme\";\n\nexport interface PopoverTheme {\n  base: string;\n  inner: string;\n  content: string;\n  arrow: Omit<FloatingArrowTheme, \"style\">;\n}\n\nexport interface PopoverProps extends Omit<ComponentProps<\"div\">, \"content\" | \"style\">, ThemingProps<PopoverTheme> {\n  arrow?: boolean;\n  content: ReactNode;\n  placement?: \"auto\" | Placement;\n  trigger?: \"hover\" | \"click\";\n  initialOpen?: boolean;\n  open?: boolean;\n  onOpenChange?: Dispatch<SetStateAction<boolean>>;\n}\n\nexport function Popover(props: PopoverProps) {\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [popoverTheme, provider.theme?.popover, props.theme],\n    [get(provider.clearTheme, \"popover\"), props.clearTheme],\n    [get(provider.applyTheme, \"popover\"), props.applyTheme],\n  );\n\n  const {\n    children,\n    content,\n    arrow = true,\n    trigger = \"click\",\n    initialOpen,\n    open: controlledOpen,\n    onOpenChange: setControlledOpen,\n    placement: theirPlacement = \"bottom\",\n    ...restProps\n  } = resolveProps(props, provider.props?.popover);\n\n  const [uncontrolledOpen, setUncontrolledOpen] = useState<boolean>(Boolean(initialOpen));\n  const arrowRef = useRef<HTMLDivElement>(null);\n\n  const open = controlledOpen ?? uncontrolledOpen;\n  const setOpen = setControlledOpen ?? setUncontrolledOpen;\n\n  const floatingProps = useBaseFLoating({\n    open,\n    placement: theirPlacement,\n    arrowRef,\n    setOpen,\n  });\n\n  const {\n    floatingStyles,\n    context,\n    placement,\n    middlewareData: { arrow: { x: arrowX, y: arrowY } = {} },\n    refs,\n  } = floatingProps;\n\n  const { getFloatingProps, getReferenceProps } = useFloatingInteractions({\n    context,\n    role: \"dialog\",\n    trigger,\n  });\n\n  const childrenRef = (children as ComponentPropsWithRef<\"button\">).ref;\n  const ref = useMergeRefs([context.refs.setReference, childrenRef]);\n\n  if (!isValidElement(children)) {\n    throw Error(\"Invalid target element\");\n  }\n\n  const target = useMemo(() => {\n    return cloneElement(\n      children,\n      getReferenceProps({\n        ref,\n        \"data-testid\": \"flowbite-popover-target\",\n        ...children?.props,\n      }),\n    );\n  }, [children, ref, getReferenceProps]);\n\n  return (\n    <>\n      {target}\n      {open && (\n        <FloatingFocusManager context={context} modal>\n          <div\n            className={theme.base}\n            ref={refs.setFloating}\n            data-testid=\"flowbite-popover\"\n            {...restProps}\n            style={floatingStyles}\n            {...getFloatingProps()}\n          >\n            <div className={theme.inner}>\n              {arrow && (\n                <div\n                  className={theme.arrow.base}\n                  data-testid=\"flowbite-popover-arrow\"\n                  ref={arrowRef}\n                  style={{\n                    top: arrowY ?? \" \",\n                    left: arrowX ?? \" \",\n                    right: \" \",\n                    bottom: \" \",\n                    [getArrowPlacement({ placement })]: theme.arrow.placement,\n                  }}\n                >\n                  &nbsp;\n                </div>\n              )}\n              <div className={theme.content}>{content}</div>\n            </div>\n          </div>\n        </FloatingFocusManager>\n      )}\n    </>\n  );\n}\n\nPopover.displayName = \"Popover\";\n"], "names": ["provider", "useThemeProvider", "theme", "useResolveTheme", "popoverTheme", "get", "resolveProps", "useState", "useRef", "useBaseFLoating", "useFloatingInteractions", "useMergeRefs", "isValidElement", "useMemo", "cloneElement", "jsxs", "Fragment", "jsx", "FloatingFocusManager", "getArrowPlacement"], "mappings": ";;;;;;;;;;;;;AAYO,SAAS,OAAO,CAAC,KAAK,EAAE;AAC/B,EAAE,MAAMA,UAAQ,GAAGC,yBAAgB,EAAE;AACrC,EAAE,MAAMC,OAAK,GAAGC,4BAAe;AAC/B,IAAI,CAACC,kBAAY,EAAEJ,UAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC;AACxD,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,SAAS,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC3D,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,SAAS,CAAC,EAAE,KAAK,CAAC,UAAU;AAC1D,GAAG;AACH,EAAE,MAAM;AACR,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,KAAK,GAAG,IAAI;AAChB,IAAI,OAAO,GAAG,OAAO;AACrB,IAAI,WAAW;AACf,IAAI,IAAI,EAAE,cAAc;AACxB,IAAI,YAAY,EAAE,iBAAiB;AACnC,IAAI,SAAS,EAAE,cAAc,GAAG,QAAQ;AACxC,IAAI,GAAG;AACP,GAAG,GAAGM,yBAAY,CAAC,KAAK,EAAEN,UAAQ,CAAC,KAAK,EAAE,OAAO,CAAC;AAClD,EAAE,MAAM,CAAC,gBAAgB,EAAE,mBAAmB,CAAC,GAAGO,cAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;AAChF,EAAE,MAAM,QAAQ,GAAGC,YAAM,CAAC,IAAI,CAAC;AAC/B,EAAE,MAAM,IAAI,GAAG,cAAc,IAAI,gBAAgB;AACjD,EAAE,MAAM,OAAO,GAAG,iBAAiB,IAAI,mBAAmB;AAC1D,EAAE,MAAM,aAAa,GAAGC,2BAAe,CAAC;AACxC,IAAI,IAAI;AACR,IAAI,SAAS,EAAE,cAAc;AAC7B,IAAI,QAAQ;AACZ,IAAI;AACJ,GAAG,CAAC;AACJ,EAAE,MAAM;AACR,IAAI,cAAc;AAClB,IAAI,OAAO;AACX,IAAI,SAAS;AACb,IAAI,cAAc,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE;AAC5D,IAAI;AACJ,GAAG,GAAG,aAAa;AACnB,EAAE,MAAM,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,GAAGC,mCAAuB,CAAC;AAC1E,IAAI,OAAO;AACX,IAAI,IAAI,EAAE,QAAQ;AAClB,IAAI;AACJ,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,QAAQ,CAAC,GAAG;AAClC,EAAE,MAAM,GAAG,GAAGC,kBAAY,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;AACpE,EAAE,IAAI,CAACC,oBAAc,CAAC,QAAQ,CAAC,EAAE;AACjC,IAAI,MAAM,KAAK,CAAC,wBAAwB,CAAC;AACzC;AACA,EAAE,MAAM,MAAM,GAAGC,aAAO,CAAC,MAAM;AAC/B,IAAI,OAAOC,kBAAY;AACvB,MAAM,QAAQ;AACd,MAAM,iBAAiB,CAAC;AACxB,QAAQ,GAAG;AACX,QAAQ,aAAa,EAAE,yBAAyB;AAChD,QAAQ,GAAG,QAAQ,EAAE;AACrB,OAAO;AACP,KAAK;AACL,GAAG,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE,iBAAiB,CAAC,CAAC;AACxC,EAAE,uBAAuBC,eAAI,CAACC,mBAAQ,EAAE,EAAE,QAAQ,EAAE;AACpD,IAAI,MAAM;AACV,IAAI,IAAI,oBAAoBC,cAAG,CAACC,0BAAoB,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,kBAAkBD,cAAG;AAC3G,MAAM,KAAK;AACX,MAAM;AACN,QAAQ,SAAS,EAAEf,OAAK,CAAC,IAAI;AAC7B,QAAQ,GAAG,EAAE,IAAI,CAAC,WAAW;AAC7B,QAAQ,aAAa,EAAE,kBAAkB;AACzC,QAAQ,GAAG,SAAS;AACpB,QAAQ,KAAK,EAAE,cAAc;AAC7B,QAAQ,GAAG,gBAAgB,EAAE;AAC7B,QAAQ,QAAQ,kBAAkBa,eAAI,CAAC,KAAK,EAAE,EAAE,SAAS,EAAEb,OAAK,CAAC,KAAK,EAAE,QAAQ,EAAE;AAClF,UAAU,KAAK,oBAAoBe,cAAG;AACtC,YAAY,KAAK;AACjB,YAAY;AACZ,cAAc,SAAS,EAAEf,OAAK,CAAC,KAAK,CAAC,IAAI;AACzC,cAAc,aAAa,EAAE,wBAAwB;AACrD,cAAc,GAAG,EAAE,QAAQ;AAC3B,cAAc,KAAK,EAAE;AACrB,gBAAgB,GAAG,EAAE,MAAM,IAAI,GAAG;AAClC,gBAAgB,IAAI,EAAE,MAAM,IAAI,GAAG;AACnC,gBAAgB,KAAK,EAAE,GAAG;AAC1B,gBAAgB,MAAM,EAAE,GAAG;AAC3B,gBAAgB,CAACiB,yBAAiB,CAAC,EAAE,SAAS,EAAE,CAAC,GAAGjB,OAAK,CAAC,KAAK,CAAC;AAChE,eAAe;AACf,cAAc,QAAQ,EAAE;AACxB;AACA,WAAW;AACX,0BAA0Be,cAAG,CAAC,KAAK,EAAE,EAAE,SAAS,EAAEf,OAAK,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE;AACpF,SAAS,EAAE;AACX;AACA,KAAK,EAAE;AACP,GAAG,EAAE,CAAC;AACN;AACA,OAAO,CAAC,WAAW,GAAG,SAAS;;;;"}