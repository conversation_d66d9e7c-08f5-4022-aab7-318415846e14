{"version": 3, "file": "RatingAdvanced.js", "sources": ["../../../src/components/Rating/RatingAdvanced.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentProps } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { ThemingProps } from \"../../types\";\nimport { ratingAdvancedTheme } from \"./theme\";\n\nexport interface RatingAdvancedTheme {\n  base: string;\n  label: string;\n  progress: {\n    base: string;\n    fill: string;\n    label: string;\n  };\n}\n\nexport interface RatingAdvancedProps extends ComponentProps<\"div\">, ThemingProps<RatingAdvancedTheme> {\n  percentFilled?: number;\n}\n\nexport const RatingAdvanced = forwardRef<HTMLDivElement, RatingAdvancedProps>((props, ref) => {\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [ratingAdvancedTheme, provider.theme?.ratingAdvanced, props.theme],\n    [get(provider.clearTheme, \"ratingAdvanced\"), props.clearTheme],\n    [get(provider.applyTheme, \"ratingAdvanced\"), props.applyTheme],\n  );\n\n  const { children, className, percentFilled = 0, ...restProps } = resolveProps(props, provider.props?.ratingAdvanced);\n\n  return (\n    <div ref={ref} className={twMerge(theme.base, className)} {...restProps}>\n      <span className={theme.label}>{children}</span>\n      <div className={theme.progress.base}>\n        <div\n          className={theme.progress.fill}\n          data-testid=\"flowbite-rating-fill\"\n          style={{ width: `${percentFilled}%` }}\n        />\n      </div>\n      <span className={theme.progress.label}>{`${percentFilled}%`}</span>\n    </div>\n  );\n});\n\nRatingAdvanced.displayName = \"RatingAdvanced\";\n"], "names": [], "mappings": ";;;;;;;;;AAUY,MAAC,cAAc,GAAG,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACzD,EAAE,MAAM,QAAQ,GAAG,gBAAgB,EAAE;AACrC,EAAE,MAAM,KAAK,GAAG,eAAe;AAC/B,IAAI,CAAC,mBAAmB,EAAE,QAAQ,CAAC,KAAK,EAAE,cAAc,EAAE,KAAK,CAAC,KAAK,CAAC;AACtE,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,gBAAgB,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAClE,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,gBAAgB,CAAC,EAAE,KAAK,CAAC,UAAU;AACjE,GAAG;AACH,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,aAAa,GAAG,CAAC,EAAE,GAAG,SAAS,EAAE,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,cAAc,CAAC;AACtH,EAAE,uBAAuB,IAAI,CAAC,KAAK,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,GAAG,SAAS,EAAE,QAAQ,EAAE;AAC/G,oBAAoB,GAAG,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,CAAC;AACrE,oBAAoB,GAAG,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,kBAAkB,GAAG;AAC9F,MAAM,KAAK;AACX,MAAM;AACN,QAAQ,SAAS,EAAE,KAAK,CAAC,QAAQ,CAAC,IAAI;AACtC,QAAQ,aAAa,EAAE,sBAAsB;AAC7C,QAAQ,KAAK,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC;AAC3C;AACA,KAAK,EAAE,CAAC;AACR,oBAAoB,GAAG,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,EAAE,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC,EAAE;AAClG,GAAG,EAAE,CAAC;AACN,CAAC;AACD,cAAc,CAAC,WAAW,GAAG,gBAAgB;;;;"}