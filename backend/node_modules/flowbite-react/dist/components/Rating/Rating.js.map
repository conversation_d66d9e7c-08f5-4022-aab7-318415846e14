{"version": 3, "file": "Rating.js", "sources": ["../../../src/components/Rating/Rating.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentProps } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { DynamicStringEnumKeysOf, ThemingProps } from \"../../types\";\nimport { RatingContext } from \"./RatingContext\";\nimport type { RatingStarSizes, RatingStarTheme } from \"./RatingStar\";\nimport { ratingTheme } from \"./theme\";\n\nexport interface RatingTheme {\n  root: {\n    base: string;\n  };\n  star: RatingStarTheme;\n}\n\nexport interface RatingProps extends ComponentProps<\"div\">, ThemingProps<RatingTheme> {\n  size?: DynamicStringEnumKeysOf<RatingStarSizes>;\n}\n\nexport const Rating = forwardRef<HTMLDivElement, RatingProps>((props, ref) => {\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [ratingTheme, provider.theme?.rating, props.theme],\n    [get(provider.clearTheme, \"rating\"), props.clearTheme],\n    [get(provider.applyTheme, \"rating\"), props.applyTheme],\n  );\n\n  const { className, size = \"sm\", ...restProps } = resolveProps(props, provider.props?.rating);\n\n  return (\n    <RatingContext.Provider\n      value={{ theme: props.theme, clearTheme: props.clearTheme, applyTheme: props.applyTheme, size }}\n    >\n      <div ref={ref} className={twMerge(theme.root.base, className)} {...restProps} />\n    </RatingContext.Provider>\n  );\n});\n\nRating.displayName = \"Rating\";\n"], "names": [], "mappings": ";;;;;;;;;;AAWY,MAAC,MAAM,GAAG,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACjD,EAAE,MAAM,QAAQ,GAAG,gBAAgB,EAAE;AACrC,EAAE,MAAM,KAAK,GAAG,eAAe;AAC/B,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC;AACtD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC1D,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE,KAAK,CAAC,UAAU;AACzD,GAAG;AACH,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,GAAG,IAAI,EAAE,GAAG,SAAS,EAAE,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC;AAC9F,EAAE,uBAAuB,GAAG;AAC5B,IAAI,aAAa,CAAC,QAAQ;AAC1B,IAAI;AACJ,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE,IAAI,EAAE;AACrG,MAAM,QAAQ,kBAAkB,GAAG,CAAC,KAAK,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,GAAG,SAAS,EAAE;AAChH;AACA,GAAG;AACH,CAAC;AACD,MAAM,CAAC,WAAW,GAAG,QAAQ;;;;"}