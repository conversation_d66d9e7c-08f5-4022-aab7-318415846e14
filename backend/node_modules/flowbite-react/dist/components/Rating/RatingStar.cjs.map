{"version": 3, "file": "RatingStar.cjs", "sources": ["../../../src/components/Rating/RatingStar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentProps, type FC } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { StarIcon } from \"../../icons/star-icon\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { FlowbiteSizes, ThemingProps } from \"../../types\";\nimport { useRatingContext } from \"./RatingContext\";\nimport { ratingTheme } from \"./theme\";\n\nexport interface RatingStarTheme {\n  empty: string;\n  filled: string;\n  sizes: RatingStarSizes;\n}\n\nexport interface RatingStarSizes extends Pick<FlowbiteSizes, \"sm\" | \"md\" | \"lg\"> {\n  [key: string]: string;\n}\n\nexport interface RatingStarProps extends ComponentProps<\"svg\">, ThemingProps<RatingStarTheme> {\n  filled?: boolean;\n  starIcon?: FC<ComponentProps<\"svg\">>;\n}\n\nexport const RatingStar = forwardRef<SVGSVGElement, RatingStarProps>((props, ref) => {\n  const { theme: rootTheme, clearTheme: rootClearTheme, applyTheme: rootApplyTheme, size = \"sm\" } = useRatingContext();\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [ratingTheme.star, provider.theme?.rating?.star, rootTheme?.star, props.theme],\n    [get(provider.clearTheme, \"rating.star\"), get(rootClearTheme, \"star\"), props.clearTheme],\n    [get(provider.applyTheme, \"rating.star\"), get(rootApplyTheme, \"star\"), props.applyTheme],\n  );\n\n  const {\n    className,\n    filled = true,\n    starIcon: Icon = StarIcon,\n    ...restProps\n  } = resolveProps(props, provider.props?.ratingStar);\n\n  return (\n    <Icon\n      ref={ref}\n      data-testid=\"flowbite-rating-star\"\n      className={twMerge(theme.sizes[size], theme[filled ? \"filled\" : \"empty\"], className)}\n      {...restProps}\n    />\n  );\n});\n\nRatingStar.displayName = \"RatingStar\";\n"], "names": ["forwardRef", "useRatingContext", "provider", "useThemeProvider", "theme", "useResolveTheme", "ratingTheme", "get", "StarIcon", "resolveProps", "jsx", "twMerge"], "mappings": ";;;;;;;;;;;;;AAYY,MAAC,UAAU,GAAGA,gBAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACrD,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,IAAI,GAAG,IAAI,EAAE,GAAGC,8BAAgB,EAAE;AACtH,EAAE,MAAMC,UAAQ,GAAGC,yBAAgB,EAAE;AACrC,EAAE,MAAMC,OAAK,GAAGC,4BAAe;AAC/B,IAAI,CAACC,iBAAW,CAAC,IAAI,EAAEJ,UAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC;AAClF,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,aAAa,CAAC,EAAEK,OAAG,CAAC,cAAc,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC5F,IAAI,CAACA,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,aAAa,CAAC,EAAEK,OAAG,CAAC,cAAc,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,UAAU;AAC3F,GAAG;AACH,EAAE,MAAM;AACR,IAAI,SAAS;AACb,IAAI,MAAM,GAAG,IAAI;AACjB,IAAI,QAAQ,EAAE,IAAI,GAAGC,iBAAQ;AAC7B,IAAI,GAAG;AACP,GAAG,GAAGC,yBAAY,CAAC,KAAK,EAAEP,UAAQ,CAAC,KAAK,EAAE,UAAU,CAAC;AACrD,EAAE,uBAAuBQ,cAAG;AAC5B,IAAI,IAAI;AACR,IAAI;AACJ,MAAM,GAAG;AACT,MAAM,aAAa,EAAE,sBAAsB;AAC3C,MAAM,SAAS,EAAEC,qBAAO,CAACP,OAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAEA,OAAK,CAAC,MAAM,GAAG,QAAQ,GAAG,OAAO,CAAC,EAAE,SAAS,CAAC;AAC1F,MAAM,GAAG;AACT;AACA,GAAG;AACH,CAAC;AACD,UAAU,CAAC,WAAW,GAAG,YAAY;;;;"}