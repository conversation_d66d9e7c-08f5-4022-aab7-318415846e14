{"version": 3, "file": "RatingStar.js", "sources": ["../../../src/components/Rating/RatingStar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentProps, type FC } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { StarIcon } from \"../../icons/star-icon\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { FlowbiteSizes, ThemingProps } from \"../../types\";\nimport { useRatingContext } from \"./RatingContext\";\nimport { ratingTheme } from \"./theme\";\n\nexport interface RatingStarTheme {\n  empty: string;\n  filled: string;\n  sizes: RatingStarSizes;\n}\n\nexport interface RatingStarSizes extends Pick<FlowbiteSizes, \"sm\" | \"md\" | \"lg\"> {\n  [key: string]: string;\n}\n\nexport interface RatingStarProps extends ComponentProps<\"svg\">, ThemingProps<RatingStarTheme> {\n  filled?: boolean;\n  starIcon?: FC<ComponentProps<\"svg\">>;\n}\n\nexport const RatingStar = forwardRef<SVGSVGElement, RatingStarProps>((props, ref) => {\n  const { theme: rootTheme, clearTheme: rootClearTheme, applyTheme: rootApplyTheme, size = \"sm\" } = useRatingContext();\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [ratingTheme.star, provider.theme?.rating?.star, rootTheme?.star, props.theme],\n    [get(provider.clearTheme, \"rating.star\"), get(rootClearTheme, \"star\"), props.clearTheme],\n    [get(provider.applyTheme, \"rating.star\"), get(rootApplyTheme, \"star\"), props.applyTheme],\n  );\n\n  const {\n    className,\n    filled = true,\n    starIcon: Icon = StarIcon,\n    ...restProps\n  } = resolveProps(props, provider.props?.ratingStar);\n\n  return (\n    <Icon\n      ref={ref}\n      data-testid=\"flowbite-rating-star\"\n      className={twMerge(theme.sizes[size], theme[filled ? \"filled\" : \"empty\"], className)}\n      {...restProps}\n    />\n  );\n});\n\nRatingStar.displayName = \"RatingStar\";\n"], "names": [], "mappings": ";;;;;;;;;;;AAYY,MAAC,UAAU,GAAG,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACrD,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,IAAI,GAAG,IAAI,EAAE,GAAG,gBAAgB,EAAE;AACtH,EAAE,MAAM,QAAQ,GAAG,gBAAgB,EAAE;AACrC,EAAE,MAAM,KAAK,GAAG,eAAe;AAC/B,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC;AAClF,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,aAAa,CAAC,EAAE,GAAG,CAAC,cAAc,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC5F,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,aAAa,CAAC,EAAE,GAAG,CAAC,cAAc,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,UAAU;AAC3F,GAAG;AACH,EAAE,MAAM;AACR,IAAI,SAAS;AACb,IAAI,MAAM,GAAG,IAAI;AACjB,IAAI,QAAQ,EAAE,IAAI,GAAG,QAAQ;AAC7B,IAAI,GAAG;AACP,GAAG,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,UAAU,CAAC;AACrD,EAAE,uBAAuB,GAAG;AAC5B,IAAI,IAAI;AACR,IAAI;AACJ,MAAM,GAAG;AACT,MAAM,aAAa,EAAE,sBAAsB;AAC3C,MAAM,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,QAAQ,GAAG,OAAO,CAAC,EAAE,SAAS,CAAC;AAC1F,MAAM,GAAG;AACT;AACA,GAAG;AACH,CAAC;AACD,UAAU,CAAC,WAAW,GAAG,YAAY;;;;"}