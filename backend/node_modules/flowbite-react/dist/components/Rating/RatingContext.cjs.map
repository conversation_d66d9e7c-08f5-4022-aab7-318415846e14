{"version": 3, "file": "RatingContext.cjs", "sources": ["../../../src/components/Rating/RatingContext.tsx"], "sourcesContent": ["\"use client\";\n\nimport { createContext, useContext } from \"react\";\nimport type { DynamicStringEnumKeysOf, ThemingProps } from \"../../types\";\nimport type { RatingTheme } from \"./Rating\";\nimport type { RatingStarSizes } from \"./RatingStar\";\n\nexport interface RatingContextValue extends ThemingProps<RatingTheme> {\n  size?: DynamicStringEnumKeysOf<RatingStarSizes>;\n}\n\nexport const RatingContext = createContext<RatingContextValue | undefined>(undefined);\n\nexport function useRatingContext(): RatingContextValue {\n  const context = useContext(RatingContext);\n\n  if (!context) {\n    throw new Error(\"useRatingContext should be used within the RatingContext provider!\");\n  }\n\n  return context;\n}\n"], "names": ["createContext", "useContext"], "mappings": ";;;;AAGY,MAAC,aAAa,GAAGA,mBAAa,CAAC,MAAM;AAC1C,SAAS,gBAAgB,GAAG;AACnC,EAAE,MAAM,OAAO,GAAGC,gBAAU,CAAC,aAAa,CAAC;AAC3C,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,MAAM,IAAI,KAAK,CAAC,oEAAoE,CAAC;AACzF;AACA,EAAE,OAAO,OAAO;AAChB;;;;;"}