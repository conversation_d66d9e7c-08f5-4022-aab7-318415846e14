{"version": 3, "file": "theme.js", "sources": ["../../../src/components/Rating/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { RatingTheme } from \"./Rating\";\nimport type { RatingAdvancedTheme } from \"./RatingAdvanced\";\n\nexport const ratingTheme = createTheme<RatingTheme>({\n  root: {\n    base: \"flex items-center\",\n  },\n  star: {\n    empty: \"text-gray-300 dark:text-gray-500\",\n    filled: \"text-yellow-400\",\n    sizes: {\n      sm: \"h-5 w-5\",\n      md: \"h-7 w-7\",\n      lg: \"h-10 w-10\",\n    },\n  },\n});\n\nexport const ratingAdvancedTheme = createTheme<RatingAdvancedTheme>({\n  base: \"flex items-center\",\n  label: \"text-sm font-medium text-cyan-600 dark:text-cyan-500\",\n  progress: {\n    base: \"mx-4 h-5 w-2/4 rounded bg-gray-200 dark:bg-gray-700\",\n    fill: \"h-5 rounded bg-yellow-400\",\n    label: \"text-sm font-medium text-cyan-600 dark:text-cyan-500\",\n  },\n});\n"], "names": [], "mappings": ";;AAEY,MAAC,WAAW,GAAG,WAAW,CAAC;AACvC,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE;AACV,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,KAAK,EAAE,kCAAkC;AAC7C,IAAI,MAAM,EAAE,iBAAiB;AAC7B,IAAI,KAAK,EAAE;AACX,MAAM,EAAE,EAAE,SAAS;AACnB,MAAM,EAAE,EAAE,SAAS;AACnB,MAAM,EAAE,EAAE;AACV;AACA;AACA,CAAC;AACW,MAAC,mBAAmB,GAAG,WAAW,CAAC;AAC/C,EAAE,IAAI,EAAE,mBAAmB;AAC3B,EAAE,KAAK,EAAE,sDAAsD;AAC/D,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,qDAAqD;AAC/D,IAAI,IAAI,EAAE,2BAA2B;AACrC,IAAI,KAAK,EAAE;AACX;AACA,CAAC;;;;"}