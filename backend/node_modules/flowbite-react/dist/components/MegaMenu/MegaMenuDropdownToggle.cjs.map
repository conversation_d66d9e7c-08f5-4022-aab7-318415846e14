{"version": 3, "file": "MegaMenuDropdownToggle.cjs", "sources": ["../../../src/components/MegaMenu/MegaMenuDropdownToggle.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, useEffect, useId, useRef, useState, type ComponentProps } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { mergeRefs } from \"../../helpers/merge-refs\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { ThemingProps } from \"../../types\";\nimport { megaMenuTheme } from \"./theme\";\n\nexport interface MegaMenuDropdownToggleTheme {\n  base: string;\n}\n\nexport interface MegaMenuDropdownToggleProps\n  extends ComponentProps<\"button\">,\n    ThemingProps<MegaMenuDropdownToggleTheme> {}\n\nexport const MegaMenuDropdownToggle = forwardRef<HTMLButtonElement, MegaMenuDropdownToggleProps>((props, ref) => {\n  const id = useId();\n  const innerRef = useRef<HTMLButtonElement>(null);\n  const [controls, setControls] = useState<string | undefined>(undefined);\n  const [isExpanded, setExpanded] = useState<boolean | undefined>(undefined);\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [megaMenuTheme.dropdownToggle, provider.theme?.megaMenu?.dropdownToggle, props.theme],\n    [get(provider.clearTheme, \"megaMenu.dropdownToggle\"), props.clearTheme],\n    [get(provider.applyTheme, \"megaMenu.dropdownToggle\"), props.applyTheme],\n  );\n\n  const { className, ...restProps } = resolveProps(props, provider.props?.megaMenuDropdownToggle);\n\n  function findDropdown() {\n    const megaMenu = innerRef.current?.closest(\"nav\");\n\n    return megaMenu?.querySelector('[role=\"menu\"]');\n  }\n\n  function onClick() {\n    findDropdown()?.classList.toggle(\"hidden\");\n\n    setExpanded(!isExpanded);\n  }\n\n  useEffect(() => {\n    const dropdown = findDropdown();\n    const isDropdownHidden = dropdown?.classList.contains(\"hidden\");\n\n    setControls(dropdown?.id);\n    setExpanded(!isDropdownHidden);\n  }, []);\n\n  return (\n    <button\n      ref={mergeRefs([ref, innerRef])}\n      aria-controls={controls}\n      aria-expanded={isExpanded}\n      aria-haspopup=\"menu\"\n      id={id}\n      onClick={onClick}\n      className={twMerge(theme.base, className)}\n      {...restProps}\n    />\n  );\n});\n\nMegaMenuDropdownToggle.displayName = \"MegaMenuDropdownToggle\";\n"], "names": ["forwardRef", "useId", "useRef", "useState", "provider", "useThemeProvider", "theme", "useResolveTheme", "megaMenuTheme", "get", "resolveProps", "useEffect", "jsx", "mergeRefs", "twMerge"], "mappings": ";;;;;;;;;;;;AAWY,MAAC,sBAAsB,GAAGA,gBAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACjE,EAAE,MAAM,EAAE,GAAGC,WAAK,EAAE;AACpB,EAAE,MAAM,QAAQ,GAAGC,YAAM,CAAC,IAAI,CAAC;AAC/B,EAAE,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,GAAGC,cAAQ,CAAC,MAAM,CAAC;AAClD,EAAE,MAAM,CAAC,UAAU,EAAE,WAAW,CAAC,GAAGA,cAAQ,CAAC,MAAM,CAAC;AACpD,EAAE,MAAMC,UAAQ,GAAGC,yBAAgB,EAAE;AACrC,EAAE,MAAMC,OAAK,GAAGC,4BAAe;AAC/B,IAAI,CAACC,mBAAa,CAAC,cAAc,EAAEJ,UAAQ,CAAC,KAAK,EAAE,QAAQ,EAAE,cAAc,EAAE,KAAK,CAAC,KAAK,CAAC;AACzF,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,yBAAyB,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC3E,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,yBAAyB,CAAC,EAAE,KAAK,CAAC,UAAU;AAC1E,GAAG;AACH,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,SAAS,EAAE,GAAGM,yBAAY,CAAC,KAAK,EAAEN,UAAQ,CAAC,KAAK,EAAE,sBAAsB,CAAC;AACjG,EAAE,SAAS,YAAY,GAAG;AAC1B,IAAI,MAAM,QAAQ,GAAG,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC;AACrD,IAAI,OAAO,QAAQ,EAAE,aAAa,CAAC,eAAe,CAAC;AACnD;AACA,EAAE,SAAS,OAAO,GAAG;AACrB,IAAI,YAAY,EAAE,EAAE,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC;AAC9C,IAAI,WAAW,CAAC,CAAC,UAAU,CAAC;AAC5B;AACA,EAAEO,eAAS,CAAC,MAAM;AAClB,IAAI,MAAM,QAAQ,GAAG,YAAY,EAAE;AACnC,IAAI,MAAM,gBAAgB,GAAG,QAAQ,EAAE,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC;AACnE,IAAI,WAAW,CAAC,QAAQ,EAAE,EAAE,CAAC;AAC7B,IAAI,WAAW,CAAC,CAAC,gBAAgB,CAAC;AAClC,GAAG,EAAE,EAAE,CAAC;AACR,EAAE,uBAAuBC,cAAG;AAC5B,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,GAAG,EAAEC,mBAAS,CAAC,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;AACrC,MAAM,eAAe,EAAE,QAAQ;AAC/B,MAAM,eAAe,EAAE,UAAU;AACjC,MAAM,eAAe,EAAE,MAAM;AAC7B,MAAM,EAAE;AACR,MAAM,OAAO;AACb,MAAM,SAAS,EAAEC,qBAAO,CAACR,OAAK,CAAC,IAAI,EAAE,SAAS,CAAC;AAC/C,MAAM,GAAG;AACT;AACA,GAAG;AACH,CAAC;AACD,sBAAsB,CAAC,WAAW,GAAG,wBAAwB;;;;"}