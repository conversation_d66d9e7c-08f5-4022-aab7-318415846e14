{"version": 3, "file": "MegaMenuDropdown.cjs", "sources": ["../../../src/components/MegaMenu/MegaMenuDropdown.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useId, useRef, useState, type ComponentProps } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { ThemingProps } from \"../../types\";\nimport { Dropdown, type DropdownTheme } from \"../Dropdown\";\nimport { megaMenuTheme } from \"./theme\";\n\nexport interface MegaMenuDropdownTheme {\n  base: string;\n  toggle: DropdownTheme;\n}\n\nexport interface MegaMenuDropdownProps extends ComponentProps<\"div\">, ThemingProps<MegaMenuDropdownTheme> {\n  toggle?: JSX.Element;\n}\n\nexport function MegaMenuDropdown(props: MegaMenuDropdownProps) {\n  const [labelledBy, setLabelledBy] = useState<string | undefined>(undefined);\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [megaMenuTheme.dropdown, provider.theme?.megaMenu?.dropdown, props.theme],\n    [get(provider.clearTheme, \"megaMenu.dropdown\"), props.clearTheme],\n    [get(provider.applyTheme, \"megaMenu.dropdown\"), props.applyTheme],\n  );\n\n  const { children, className, toggle, ...restProps } = resolveProps(props, provider.props?.megaMenuDropdown);\n\n  if (toggle) {\n    return (\n      <Dropdown\n        inline\n        label={toggle}\n        placement=\"bottom\"\n        theme={theme.toggle}\n        className={twMerge(theme.base, className)}\n      >\n        {children}\n      </Dropdown>\n    );\n  }\n\n  const id = useId();\n  const ref = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    const toggle = ref.current?.closest(\"nav\")?.querySelector('[aria-haspopup=\"menu\"]');\n    setLabelledBy(toggle?.id);\n  }, []);\n\n  return (\n    <div\n      aria-labelledby={labelledBy}\n      id={id}\n      ref={ref}\n      role=\"menu\"\n      className={twMerge(theme.base, className)}\n      {...restProps}\n    >\n      {children}\n    </div>\n  );\n}\n\nMegaMenuDropdown.displayName = \"MegaMenuDropdown\";\n"], "names": ["useState", "provider", "useThemeProvider", "theme", "useResolveTheme", "megaMenuTheme", "get", "resolveProps", "jsx", "Dropdown", "twMerge", "useId", "useRef", "useEffect"], "mappings": ";;;;;;;;;;;;;;;;AAWO,SAAS,gBAAgB,CAAC,KAAK,EAAE;AACxC,EAAE,MAAM,CAAC,UAAU,EAAE,aAAa,CAAC,GAAGA,cAAQ,CAAC,MAAM,CAAC;AACtD,EAAE,MAAMC,UAAQ,GAAGC,yBAAgB,EAAE;AACrC,EAAE,MAAMC,OAAK,GAAGC,4BAAe;AAC/B,IAAI,CAACC,mBAAa,CAAC,QAAQ,EAAEJ,UAAQ,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,CAAC,KAAK,CAAC;AAC7E,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,mBAAmB,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AACrE,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,mBAAmB,CAAC,EAAE,KAAK,CAAC,UAAU;AACpE,GAAG;AACH,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,SAAS,EAAE,GAAGM,yBAAY,CAAC,KAAK,EAAEN,UAAQ,CAAC,KAAK,EAAE,gBAAgB,CAAC;AAC7G,EAAE,IAAI,MAAM,EAAE;AACd,IAAI,uBAAuBO,cAAG;AAC9B,MAAMC,iBAAQ;AACd,MAAM;AACN,QAAQ,MAAM,EAAE,IAAI;AACpB,QAAQ,KAAK,EAAE,MAAM;AACrB,QAAQ,SAAS,EAAE,QAAQ;AAC3B,QAAQ,KAAK,EAAEN,OAAK,CAAC,MAAM;AAC3B,QAAQ,SAAS,EAAEO,qBAAO,CAACP,OAAK,CAAC,IAAI,EAAE,SAAS,CAAC;AACjD,QAAQ;AACR;AACA,KAAK;AACL;AACA,EAAE,MAAM,EAAE,GAAGQ,WAAK,EAAE;AACpB,EAAE,MAAM,GAAG,GAAGC,YAAM,CAAC,IAAI,CAAC;AAC1B,EAAEC,eAAS,CAAC,MAAM;AAClB,IAAI,MAAM,OAAO,GAAG,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,EAAE,aAAa,CAAC,wBAAwB,CAAC;AACxF,IAAI,aAAa,CAAC,OAAO,EAAE,EAAE,CAAC;AAC9B,GAAG,EAAE,EAAE,CAAC;AACR,EAAE,uBAAuBL,cAAG;AAC5B,IAAI,KAAK;AACT,IAAI;AACJ,MAAM,iBAAiB,EAAE,UAAU;AACnC,MAAM,EAAE;AACR,MAAM,GAAG;AACT,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,SAAS,EAAEE,qBAAO,CAACP,OAAK,CAAC,IAAI,EAAE,SAAS,CAAC;AAC/C,MAAM,GAAG,SAAS;AAClB,MAAM;AACN;AACA,GAAG;AACH;AACA,gBAAgB,CAAC,WAAW,GAAG,kBAAkB;;;;"}