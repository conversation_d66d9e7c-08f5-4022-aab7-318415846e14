{"version": 3, "file": "theme.cjs", "sources": ["../../../src/components/MegaMenu/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { dropdownTheme } from \"../Dropdown/theme\";\nimport { navbarTheme } from \"../Navbar/theme\";\nimport type { MegaMenuTheme } from \"./MegaMenu\";\n\nexport const megaMenuTheme = createTheme<MegaMenuTheme>({\n  ...navbarTheme,\n  dropdown: {\n    base: \"\",\n    toggle: {\n      ...dropdownTheme,\n      floating: {\n        ...dropdownTheme.floating,\n        base: twMerge(dropdownTheme.floating.base, \"mt-2 block\"),\n        content: twMerge(dropdownTheme.floating.content, \"text-gray-500 dark:text-gray-400\"),\n        style: {\n          ...dropdownTheme.floating.style,\n          auto: twMerge(dropdownTheme.floating.style.auto, \"text-gray-500 dark:text-gray-400\"),\n        },\n      },\n      inlineWrapper: twMerge(dropdownTheme.inlineWrapper, \"flex w-full items-center justify-between\"),\n    },\n  },\n  dropdownToggle: {\n    base: twMerge(navbarTheme.link.base, navbarTheme.link.active.off, \"flex w-full items-center justify-between\"),\n  },\n});\n"], "names": ["createTheme", "navbarTheme", "dropdownTheme", "twMerge"], "mappings": ";;;;;;;AAKY,MAAC,aAAa,GAAGA,uBAAW,CAAC;AACzC,EAAE,GAAGC,mBAAW;AAChB,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,EAAE;AACZ,IAAI,MAAM,EAAE;AACZ,MAAM,GAAGC,mBAAa;AACtB,MAAM,QAAQ,EAAE;AAChB,QAAQ,GAAGA,mBAAa,CAAC,QAAQ;AACjC,QAAQ,IAAI,EAAEC,qBAAO,CAACD,mBAAa,CAAC,QAAQ,CAAC,IAAI,EAAE,YAAY,CAAC;AAChE,QAAQ,OAAO,EAAEC,qBAAO,CAACD,mBAAa,CAAC,QAAQ,CAAC,OAAO,EAAE,kCAAkC,CAAC;AAC5F,QAAQ,KAAK,EAAE;AACf,UAAU,GAAGA,mBAAa,CAAC,QAAQ,CAAC,KAAK;AACzC,UAAU,IAAI,EAAEC,qBAAO,CAACD,mBAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,kCAAkC;AAC7F;AACA,OAAO;AACP,MAAM,aAAa,EAAEC,qBAAO,CAACD,mBAAa,CAAC,aAAa,EAAE,0CAA0C;AACpG;AACA,GAAG;AACH,EAAE,cAAc,EAAE;AAClB,IAAI,IAAI,EAAEC,qBAAO,CAACF,mBAAW,CAAC,IAAI,CAAC,IAAI,EAAEA,mBAAW,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,0CAA0C;AAChH;AACA,CAAC;;;;"}