{"version": 3, "file": "MegaMenu.cjs", "sources": ["../../../src/components/MegaMenu/MegaMenu.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { NavbarProps, NavbarTheme } from \"../Navbar\";\nimport { Navbar } from \"../Navbar\";\nimport type { MegaMenuDropdownTheme } from \"./MegaMenuDropdown\";\nimport type { MegaMenuDropdownToggleTheme } from \"./MegaMenuDropdownToggle\";\nimport { megaMenuTheme } from \"./theme\";\n\nexport interface MegaMenuTheme extends NavbarTheme {\n  dropdown: MegaMenuDropdownTheme;\n  dropdownToggle: MegaMenuDropdownToggleTheme;\n}\n\nexport type MegaMenuProps = NavbarProps;\n\nexport const MegaMenu = forwardRef<HTMLElement, MegaMenuProps>((props, ref) => {\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [megaMenuTheme, provider.theme?.megaMenu, props.theme],\n    [get(provider.clearTheme, \"megaMenu\"), props.clearTheme],\n    [get(provider.applyTheme, \"megaMenu\"), props.applyTheme],\n  );\n\n  const mergedProps = resolveProps(props, provider.props?.megaMenu);\n\n  return <Navbar ref={ref} theme={theme} fluid {...mergedProps} />;\n});\n\nMegaMenu.displayName = \"MegaMenu\";\n"], "names": ["forwardRef", "provider", "useThemeProvider", "theme", "useResolveTheme", "megaMenuTheme", "get", "resolveProps", "jsx", "<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;AAUY,MAAC,QAAQ,GAAGA,gBAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACnD,EAAE,MAAMC,UAAQ,GAAGC,yBAAgB,EAAE;AACrC,EAAE,MAAMC,OAAK,GAAGC,4BAAe;AAC/B,IAAI,CAACC,mBAAa,EAAEJ,UAAQ,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,CAAC,KAAK,CAAC;AAC1D,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC5D,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE,KAAK,CAAC,UAAU;AAC3D,GAAG;AACH,EAAE,MAAM,WAAW,GAAGM,yBAAY,CAAC,KAAK,EAAEN,UAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC;AACnE,EAAE,uBAAuBO,cAAG,CAACC,aAAM,EAAE,EAAE,GAAG,SAAEN,OAAK,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,WAAW,EAAE,CAAC;AACjF,CAAC;AACD,QAAQ,CAAC,WAAW,GAAG,UAAU;;;;"}