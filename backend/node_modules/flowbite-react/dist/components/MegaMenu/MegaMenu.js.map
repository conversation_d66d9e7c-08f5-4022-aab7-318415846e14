{"version": 3, "file": "MegaMenu.js", "sources": ["../../../src/components/MegaMenu/MegaMenu.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { NavbarProps, NavbarTheme } from \"../Navbar\";\nimport { Navbar } from \"../Navbar\";\nimport type { MegaMenuDropdownTheme } from \"./MegaMenuDropdown\";\nimport type { MegaMenuDropdownToggleTheme } from \"./MegaMenuDropdownToggle\";\nimport { megaMenuTheme } from \"./theme\";\n\nexport interface MegaMenuTheme extends NavbarTheme {\n  dropdown: MegaMenuDropdownTheme;\n  dropdownToggle: MegaMenuDropdownToggleTheme;\n}\n\nexport type MegaMenuProps = NavbarProps;\n\nexport const MegaMenu = forwardRef<HTMLElement, MegaMenuProps>((props, ref) => {\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [megaMenuTheme, provider.theme?.megaMenu, props.theme],\n    [get(provider.clearTheme, \"megaMenu\"), props.clearTheme],\n    [get(provider.applyTheme, \"megaMenu\"), props.applyTheme],\n  );\n\n  const mergedProps = resolveProps(props, provider.props?.megaMenu);\n\n  return <Navbar ref={ref} theme={theme} fluid {...mergedProps} />;\n});\n\nMegaMenu.displayName = \"MegaMenu\";\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAUY,MAAC,QAAQ,GAAG,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACnD,EAAE,MAAM,QAAQ,GAAG,gBAAgB,EAAE;AACrC,EAAE,MAAM,KAAK,GAAG,eAAe;AAC/B,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,CAAC,KAAK,CAAC;AAC1D,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC5D,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE,KAAK,CAAC,UAAU;AAC3D,GAAG;AACH,EAAE,MAAM,WAAW,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC;AACnE,EAAE,uBAAuB,GAAG,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,WAAW,EAAE,CAAC;AACjF,CAAC;AACD,QAAQ,CAAC,WAAW,GAAG,UAAU;;;;"}