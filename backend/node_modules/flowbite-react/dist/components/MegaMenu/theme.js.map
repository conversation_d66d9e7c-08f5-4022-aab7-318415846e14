{"version": 3, "file": "theme.js", "sources": ["../../../src/components/MegaMenu/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { dropdownTheme } from \"../Dropdown/theme\";\nimport { navbarTheme } from \"../Navbar/theme\";\nimport type { MegaMenuTheme } from \"./MegaMenu\";\n\nexport const megaMenuTheme = createTheme<MegaMenuTheme>({\n  ...navbarTheme,\n  dropdown: {\n    base: \"\",\n    toggle: {\n      ...dropdownTheme,\n      floating: {\n        ...dropdownTheme.floating,\n        base: twMerge(dropdownTheme.floating.base, \"mt-2 block\"),\n        content: twMerge(dropdownTheme.floating.content, \"text-gray-500 dark:text-gray-400\"),\n        style: {\n          ...dropdownTheme.floating.style,\n          auto: twMerge(dropdownTheme.floating.style.auto, \"text-gray-500 dark:text-gray-400\"),\n        },\n      },\n      inlineWrapper: twMerge(dropdownTheme.inlineWrapper, \"flex w-full items-center justify-between\"),\n    },\n  },\n  dropdownToggle: {\n    base: twMerge(navbarTheme.link.base, navbarTheme.link.active.off, \"flex w-full items-center justify-between\"),\n  },\n});\n"], "names": [], "mappings": ";;;;;AAKY,MAAC,aAAa,GAAG,WAAW,CAAC;AACzC,EAAE,GAAG,WAAW;AAChB,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,EAAE;AACZ,IAAI,MAAM,EAAE;AACZ,MAAM,GAAG,aAAa;AACtB,MAAM,QAAQ,EAAE;AAChB,QAAQ,GAAG,aAAa,CAAC,QAAQ;AACjC,QAAQ,IAAI,EAAE,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,EAAE,YAAY,CAAC;AAChE,QAAQ,OAAO,EAAE,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,OAAO,EAAE,kCAAkC,CAAC;AAC5F,QAAQ,KAAK,EAAE;AACf,UAAU,GAAG,aAAa,CAAC,QAAQ,CAAC,KAAK;AACzC,UAAU,IAAI,EAAE,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,kCAAkC;AAC7F;AACA,OAAO;AACP,MAAM,aAAa,EAAE,OAAO,CAAC,aAAa,CAAC,aAAa,EAAE,0CAA0C;AACpG;AACA,GAAG;AACH,EAAE,cAAc,EAAE;AAClB,IAAI,IAAI,EAAE,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,0CAA0C;AAChH;AACA,CAAC;;;;"}