{"version": 3, "file": "Label.cjs", "sources": ["../../../src/components/Label/Label.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentProps } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { DynamicStringEnumKeysOf, FlowbiteStateColors, ThemingProps } from \"../../types\";\nimport { labelTheme } from \"./theme\";\n\nexport interface LabelTheme {\n  root: LabelRootTheme;\n}\n\nexport interface LabelRootTheme {\n  base: string;\n  colors: LabelColors;\n  disabled: string;\n}\n\nexport interface LabelColors extends FlowbiteStateColors {\n  [key: string]: string;\n  default: string;\n}\n\nexport interface LabelProps extends Omit<ComponentProps<\"label\">, \"color\">, ThemingProps<LabelTheme> {\n  color?: DynamicStringEnumKeysOf<LabelColors>;\n  disabled?: boolean;\n}\n\nexport const Label = forwardRef<HTMLLabelElement, LabelProps>((props, ref) => {\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [labelTheme, provider.theme?.label, props.theme],\n    [get(provider.clearTheme, \"label\"), props.clearTheme],\n    [get(provider.applyTheme, \"label\"), props.applyTheme],\n  );\n\n  const { className, color = \"default\", disabled = false, ...restProps } = resolveProps(props, provider.props?.label);\n\n  return (\n    <label\n      ref={ref}\n      className={twMerge(theme.root.base, theme.root.colors[color], disabled && theme.root.disabled, className)}\n      data-testid=\"flowbite-label\"\n      {...restProps}\n    />\n  );\n});\n\nLabel.displayName = \"Label\";\n"], "names": ["forwardRef", "provider", "useThemeProvider", "theme", "useResolveTheme", "labelTheme", "get", "resolveProps", "jsx", "twMerge"], "mappings": ";;;;;;;;;;;AAUY,MAAC,KAAK,GAAGA,gBAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AAChD,EAAE,MAAMC,UAAQ,GAAGC,yBAAgB,EAAE;AACrC,EAAE,MAAMC,OAAK,GAAGC,4BAAe;AAC/B,IAAI,CAACC,gBAAU,EAAEJ,UAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC;AACpD,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AACzD,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,KAAK,CAAC,UAAU;AACxD,GAAG;AACH,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK,GAAG,SAAS,EAAE,QAAQ,GAAG,KAAK,EAAE,GAAG,SAAS,EAAE,GAAGM,yBAAY,CAAC,KAAK,EAAEN,UAAQ,CAAC,KAAK,EAAE,KAAK,CAAC;AACrH,EAAE,uBAAuBO,cAAG;AAC5B,IAAI,OAAO;AACX,IAAI;AACJ,MAAM,GAAG;AACT,MAAM,SAAS,EAAEC,qBAAO,CAACN,OAAK,CAAC,IAAI,CAAC,IAAI,EAAEA,OAAK,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,QAAQ,IAAIA,OAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC;AAC/G,MAAM,aAAa,EAAE,gBAAgB;AACrC,MAAM,GAAG;AACT;AACA,GAAG;AACH,CAAC;AACD,KAAK,CAAC,WAAW,GAAG,OAAO;;;;"}