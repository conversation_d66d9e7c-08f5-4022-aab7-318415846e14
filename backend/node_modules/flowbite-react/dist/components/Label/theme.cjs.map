{"version": 3, "file": "theme.cjs", "sources": ["../../../src/components/Label/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { LabelTheme } from \"./Label\";\n\nexport const labelTheme = createTheme<LabelTheme>({\n  root: {\n    base: \"text-sm font-medium\",\n    disabled: \"opacity-50\",\n    colors: {\n      default: \"text-gray-900 dark:text-white\",\n      info: \"text-cyan-500 dark:text-cyan-600\",\n      failure: \"text-red-700 dark:text-red-500\",\n      warning: \"text-yellow-500 dark:text-yellow-600\",\n      success: \"text-green-700 dark:text-green-500\",\n    },\n  },\n});\n"], "names": ["createTheme"], "mappings": ";;;;AAEY,MAAC,UAAU,GAAGA,uBAAW,CAAC;AACtC,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,qBAAqB;AAC/B,IAAI,QAAQ,EAAE,YAAY;AAC1B,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,+BAA+B;AAC9C,MAAM,IAAI,EAAE,kCAAkC;AAC9C,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM,OAAO,EAAE,sCAAsC;AACrD,MAAM,OAAO,EAAE;AACf;AACA;AACA,CAAC;;;;"}