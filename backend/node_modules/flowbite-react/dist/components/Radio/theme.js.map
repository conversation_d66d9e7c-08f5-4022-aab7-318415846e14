{"version": 3, "file": "theme.js", "sources": ["../../../src/components/Radio/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { RadioTheme } from \"./Radio\";\n\nexport const radioTheme = createTheme<RadioTheme>({\n  base: \"h-4 w-4 appearance-none rounded-full border border-gray-300 bg-gray-100 bg-[length:1em_1em] bg-center bg-no-repeat checked:border-transparent checked:bg-current checked:bg-dot-icon focus:outline-none focus:ring-2 focus:ring-offset-2 dark:border-gray-600 dark:bg-gray-700 dark:checked:border-transparent dark:checked:bg-current\",\n  color: {\n    default: \"text-primary-600 focus:ring-primary-600 dark:ring-offset-gray-800 dark:focus:ring-primary-600\",\n    dark: \"text-gray-800 focus:ring-gray-800 dark:ring-offset-gray-800 dark:focus:ring-gray-800\",\n    failure: \"text-red-900 focus:ring-red-900 dark:ring-offset-red-900 dark:focus:ring-red-900\",\n    gray: \"text-gray-900 focus:ring-gray-900 dark:ring-offset-gray-900 dark:focus:ring-gray-900\",\n    info: \"text-cyan-800 focus:ring-cyan-800 dark:ring-offset-gray-800 dark:focus:ring-cyan-800\",\n    light: \"text-gray-900 focus:ring-gray-900 dark:ring-offset-gray-900 dark:focus:ring-gray-900\",\n    purple: \"text-purple-600 focus:ring-purple-600 dark:ring-offset-purple-600 dark:focus:ring-purple-600\",\n    success: \"text-green-800 focus:ring-green-800 dark:ring-offset-green-800 dark:focus:ring-green-800\",\n    warning: \"text-yellow-400 focus:ring-yellow-400 dark:ring-offset-yellow-400 dark:focus:ring-yellow-400\",\n    blue: \"text-blue-700 focus:ring-blue-600 dark:ring-offset-blue-700 dark:focus:ring-blue-700\",\n    cyan: \"text-cyan-600 focus:ring-cyan-600 dark:ring-offset-cyan-600 dark:focus:ring-cyan-600\",\n    green: \"text-green-600 focus:ring-green-600 dark:ring-offset-green-600 dark:focus:ring-green-600\",\n    indigo: \"text-indigo-700 focus:ring-indigo-700 dark:ring-offset-indigo-700 dark:focus:ring-indigo-700\",\n    lime: \"text-lime-700 focus:ring-lime-700 dark:ring-offset-lime-700 dark:focus:ring-lime-700\",\n    pink: \"text-pink-600 focus:ring-pink-600 dark:ring-offset-pink-600 dark:focus:ring-pink-600\",\n    red: \"text-red-600 focus:ring-red-600 dark:ring-offset-red-600 dark:focus:ring-red-600\",\n    teal: \"text-teal-600 focus:ring-teal-600 dark:ring-offset-teal-600 dark:focus:ring-teal-600\",\n    yellow: \"text-yellow-400 focus:ring-yellow-400 dark:ring-offset-yellow-400 dark:focus:ring-yellow-400\",\n  },\n});\n"], "names": [], "mappings": ";;AAEY,MAAC,UAAU,GAAG,WAAW,CAAC;AACtC,EAAE,IAAI,EAAE,wUAAwU;AAChV,EAAE,KAAK,EAAE;AACT,IAAI,OAAO,EAAE,+FAA+F;AAC5G,IAAI,IAAI,EAAE,sFAAsF;AAChG,IAAI,OAAO,EAAE,kFAAkF;AAC/F,IAAI,IAAI,EAAE,sFAAsF;AAChG,IAAI,IAAI,EAAE,sFAAsF;AAChG,IAAI,KAAK,EAAE,sFAAsF;AACjG,IAAI,MAAM,EAAE,8FAA8F;AAC1G,IAAI,OAAO,EAAE,0FAA0F;AACvG,IAAI,OAAO,EAAE,8FAA8F;AAC3G,IAAI,IAAI,EAAE,sFAAsF;AAChG,IAAI,IAAI,EAAE,sFAAsF;AAChG,IAAI,KAAK,EAAE,0FAA0F;AACrG,IAAI,MAAM,EAAE,8FAA8F;AAC1G,IAAI,IAAI,EAAE,sFAAsF;AAChG,IAAI,IAAI,EAAE,sFAAsF;AAChG,IAAI,GAAG,EAAE,kFAAkF;AAC3F,IAAI,IAAI,EAAE,sFAAsF;AAChG,IAAI,MAAM,EAAE;AACZ;AACA,CAAC;;;;"}