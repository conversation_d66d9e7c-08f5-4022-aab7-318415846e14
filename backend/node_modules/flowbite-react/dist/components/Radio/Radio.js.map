{"version": 3, "file": "Radio.js", "sources": ["../../../src/components/Radio/Radio.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps } from \"react\";\nimport { forwardRef } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { DynamicStringEnumKeysOf, FlowbiteColors, ThemingProps } from \"../../types\";\nimport { radioTheme } from \"./theme\";\n\nexport interface RadioTheme {\n  base: string;\n  color: FlowbiteColors;\n}\n\nexport interface RadioProps extends Omit<ComponentProps<\"input\">, \"ref\" | \"type\">, ThemingProps<RadioTheme> {\n  color?: DynamicStringEnumKeysOf<FlowbiteColors>;\n}\n\nexport const Radio = forwardRef<HTMLInputElement, RadioProps>((props, ref) => {\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [radioTheme, provider.theme?.radio, props.theme],\n    [get(provider.clearTheme, \"radio\"), props.clearTheme],\n    [get(provider.applyTheme, \"radio\"), props.applyTheme],\n  );\n\n  const { color = \"default\", className, ...restProps } = resolveProps(props, provider.props?.radio);\n\n  return <input ref={ref} type=\"radio\" className={twMerge(theme.base, theme.color[color], className)} {...restProps} />;\n});\n\nRadio.displayName = \"Radio\";\n"], "names": [], "mappings": ";;;;;;;;;AAUY,MAAC,KAAK,GAAG,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AAChD,EAAE,MAAM,QAAQ,GAAG,gBAAgB,EAAE;AACrC,EAAE,MAAM,KAAK,GAAG,eAAe;AAC/B,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC;AACpD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AACzD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,KAAK,CAAC,UAAU;AACxD,GAAG;AACH,EAAE,MAAM,EAAE,KAAK,GAAG,SAAS,EAAE,SAAS,EAAE,GAAG,SAAS,EAAE,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC;AACnG,EAAE,uBAAuB,GAAG,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,SAAS,CAAC,EAAE,GAAG,SAAS,EAAE,CAAC;AAC1I,CAAC;AACD,KAAK,CAAC,WAAW,GAAG,OAAO;;;;"}