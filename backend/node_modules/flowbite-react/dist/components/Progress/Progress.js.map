{"version": 3, "file": "Progress.js", "sources": ["../../../src/components/Progress/Progress.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps } from \"react\";\nimport { forwardRef, useId } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { DynamicStringEnumKeysOf, FlowbiteColors, FlowbiteSizes, ThemingProps } from \"../../types\";\nimport { progressTheme } from \"./theme\";\n\nexport interface ProgressTheme {\n  base: string;\n  label: string;\n  bar: string;\n  color: ProgressColor;\n  size: ProgressSizes;\n}\n\nexport interface ProgressColor\n  extends Pick<\n    FlowbiteColors,\n    \"dark\" | \"blue\" | \"red\" | \"green\" | \"yellow\" | \"indigo\" | \"purple\" | \"cyan\" | \"gray\" | \"lime\" | \"pink\" | \"teal\"\n  > {\n  [key: string]: string;\n  default: string;\n}\n\nexport interface ProgressSizes extends Pick<FlowbiteSizes, \"sm\" | \"md\" | \"lg\" | \"xl\"> {\n  [key: string]: string;\n}\n\nexport interface ProgressProps extends ComponentProps<\"div\">, ThemingProps<ProgressTheme> {\n  color?: DynamicStringEnumKeysOf<ProgressColor>;\n  labelProgress?: boolean;\n  labelText?: boolean;\n  progress: number;\n  progressLabelPosition?: \"inside\" | \"outside\";\n  size?: DynamicStringEnumKeysOf<ProgressSizes>;\n  textLabel?: string;\n  textLabelPosition?: \"inside\" | \"outside\";\n}\n\nexport const Progress = forwardRef<HTMLDivElement, ProgressProps>((props, ref) => {\n  const id = useId();\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [progressTheme, provider.theme?.progress, props.theme],\n    [get(provider.clearTheme, \"progress\"), props.clearTheme],\n    [get(provider.applyTheme, \"progress\"), props.applyTheme],\n  );\n\n  const {\n    className,\n    color = \"default\",\n    labelProgress = false,\n    labelText = false,\n    progress,\n    progressLabelPosition = \"inside\",\n    size = \"md\",\n    textLabel = \"progressbar\",\n    textLabelPosition = \"inside\",\n    ...restProps\n  } = resolveProps(props, provider.props?.progress);\n\n  return (\n    <div ref={ref} id={id} aria-label={textLabel} aria-valuenow={progress} role=\"progressbar\" {...restProps}>\n      {((textLabel && labelText && textLabelPosition === \"outside\") ||\n        (progress > 0 && labelProgress && progressLabelPosition === \"outside\")) && (\n        <div className={theme.label} data-testid=\"flowbite-progress-outer-label-container\">\n          {textLabel && labelText && textLabelPosition === \"outside\" && (\n            <span data-testid=\"flowbite-progress-outer-text-label\">{textLabel}</span>\n          )}\n          {labelProgress && progressLabelPosition === \"outside\" && (\n            <span data-testid=\"flowbite-progress-outer-progress-label\">{progress}%</span>\n          )}\n        </div>\n      )}\n      <div className={twMerge(theme.base, theme.size[size], className)}>\n        <div style={{ width: `${progress}%` }} className={twMerge(theme.bar, theme.color[color], theme.size[size])}>\n          {textLabel && labelText && textLabelPosition === \"inside\" && (\n            <span data-testid=\"flowbite-progress-inner-text-label\">{textLabel}</span>\n          )}\n          {progress > 0 && labelProgress && progressLabelPosition === \"inside\" && (\n            <span data-testid=\"flowbite-progress-inner-progress-label\">{progress}%</span>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n});\n\nProgress.displayName = \"Progress\";\n"], "names": [], "mappings": ";;;;;;;;;AAUY,MAAC,QAAQ,GAAG,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACnD,EAAE,MAAM,EAAE,GAAG,KAAK,EAAE;AACpB,EAAE,MAAM,QAAQ,GAAG,gBAAgB,EAAE;AACrC,EAAE,MAAM,KAAK,GAAG,eAAe;AAC/B,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,CAAC,KAAK,CAAC;AAC1D,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC5D,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE,KAAK,CAAC,UAAU;AAC3D,GAAG;AACH,EAAE,MAAM;AACR,IAAI,SAAS;AACb,IAAI,KAAK,GAAG,SAAS;AACrB,IAAI,aAAa,GAAG,KAAK;AACzB,IAAI,SAAS,GAAG,KAAK;AACrB,IAAI,QAAQ;AACZ,IAAI,qBAAqB,GAAG,QAAQ;AACpC,IAAI,IAAI,GAAG,IAAI;AACf,IAAI,SAAS,GAAG,aAAa;AAC7B,IAAI,iBAAiB,GAAG,QAAQ;AAChC,IAAI,GAAG;AACP,GAAG,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC;AACnD,EAAE,uBAAuB,IAAI,CAAC,KAAK,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,YAAY,EAAE,SAAS,EAAE,eAAe,EAAE,QAAQ,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,SAAS,EAAE,QAAQ,EAAE;AACjJ,IAAI,CAAC,SAAS,IAAI,SAAS,IAAI,iBAAiB,KAAK,SAAS,IAAI,QAAQ,GAAG,CAAC,IAAI,aAAa,IAAI,qBAAqB,KAAK,SAAS,qBAAqB,IAAI,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,EAAE,aAAa,EAAE,yCAAyC,EAAE,QAAQ,EAAE;AACrQ,MAAM,SAAS,IAAI,SAAS,IAAI,iBAAiB,KAAK,SAAS,oBAAoB,GAAG,CAAC,MAAM,EAAE,EAAE,aAAa,EAAE,oCAAoC,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC;AAC5K,MAAM,aAAa,IAAI,qBAAqB,KAAK,SAAS,oBAAoB,IAAI,CAAC,MAAM,EAAE,EAAE,aAAa,EAAE,wCAAwC,EAAE,QAAQ,EAAE;AAChK,QAAQ,QAAQ;AAChB,QAAQ;AACR,OAAO,EAAE;AACT,KAAK,EAAE,CAAC;AACR,oBAAoB,GAAG,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,EAAE,QAAQ,kBAAkB,IAAI,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE;AACvP,MAAM,SAAS,IAAI,SAAS,IAAI,iBAAiB,KAAK,QAAQ,oBAAoB,GAAG,CAAC,MAAM,EAAE,EAAE,aAAa,EAAE,oCAAoC,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC;AAC3K,MAAM,QAAQ,GAAG,CAAC,IAAI,aAAa,IAAI,qBAAqB,KAAK,QAAQ,oBAAoB,IAAI,CAAC,MAAM,EAAE,EAAE,aAAa,EAAE,wCAAwC,EAAE,QAAQ,EAAE;AAC/K,QAAQ,QAAQ;AAChB,QAAQ;AACR,OAAO,EAAE;AACT,KAAK,EAAE,CAAC,EAAE;AACV,GAAG,EAAE,CAAC;AACN,CAAC;AACD,QAAQ,CAAC,WAAW,GAAG,UAAU;;;;"}