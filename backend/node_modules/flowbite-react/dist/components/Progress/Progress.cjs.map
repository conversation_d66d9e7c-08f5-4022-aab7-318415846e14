{"version": 3, "file": "Progress.cjs", "sources": ["../../../src/components/Progress/Progress.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps } from \"react\";\nimport { forwardRef, useId } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { DynamicStringEnumKeysOf, FlowbiteColors, FlowbiteSizes, ThemingProps } from \"../../types\";\nimport { progressTheme } from \"./theme\";\n\nexport interface ProgressTheme {\n  base: string;\n  label: string;\n  bar: string;\n  color: ProgressColor;\n  size: ProgressSizes;\n}\n\nexport interface ProgressColor\n  extends Pick<\n    FlowbiteColors,\n    \"dark\" | \"blue\" | \"red\" | \"green\" | \"yellow\" | \"indigo\" | \"purple\" | \"cyan\" | \"gray\" | \"lime\" | \"pink\" | \"teal\"\n  > {\n  [key: string]: string;\n  default: string;\n}\n\nexport interface ProgressSizes extends Pick<FlowbiteSizes, \"sm\" | \"md\" | \"lg\" | \"xl\"> {\n  [key: string]: string;\n}\n\nexport interface ProgressProps extends ComponentProps<\"div\">, ThemingProps<ProgressTheme> {\n  color?: DynamicStringEnumKeysOf<ProgressColor>;\n  labelProgress?: boolean;\n  labelText?: boolean;\n  progress: number;\n  progressLabelPosition?: \"inside\" | \"outside\";\n  size?: DynamicStringEnumKeysOf<ProgressSizes>;\n  textLabel?: string;\n  textLabelPosition?: \"inside\" | \"outside\";\n}\n\nexport const Progress = forwardRef<HTMLDivElement, ProgressProps>((props, ref) => {\n  const id = useId();\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [progressTheme, provider.theme?.progress, props.theme],\n    [get(provider.clearTheme, \"progress\"), props.clearTheme],\n    [get(provider.applyTheme, \"progress\"), props.applyTheme],\n  );\n\n  const {\n    className,\n    color = \"default\",\n    labelProgress = false,\n    labelText = false,\n    progress,\n    progressLabelPosition = \"inside\",\n    size = \"md\",\n    textLabel = \"progressbar\",\n    textLabelPosition = \"inside\",\n    ...restProps\n  } = resolveProps(props, provider.props?.progress);\n\n  return (\n    <div ref={ref} id={id} aria-label={textLabel} aria-valuenow={progress} role=\"progressbar\" {...restProps}>\n      {((textLabel && labelText && textLabelPosition === \"outside\") ||\n        (progress > 0 && labelProgress && progressLabelPosition === \"outside\")) && (\n        <div className={theme.label} data-testid=\"flowbite-progress-outer-label-container\">\n          {textLabel && labelText && textLabelPosition === \"outside\" && (\n            <span data-testid=\"flowbite-progress-outer-text-label\">{textLabel}</span>\n          )}\n          {labelProgress && progressLabelPosition === \"outside\" && (\n            <span data-testid=\"flowbite-progress-outer-progress-label\">{progress}%</span>\n          )}\n        </div>\n      )}\n      <div className={twMerge(theme.base, theme.size[size], className)}>\n        <div style={{ width: `${progress}%` }} className={twMerge(theme.bar, theme.color[color], theme.size[size])}>\n          {textLabel && labelText && textLabelPosition === \"inside\" && (\n            <span data-testid=\"flowbite-progress-inner-text-label\">{textLabel}</span>\n          )}\n          {progress > 0 && labelProgress && progressLabelPosition === \"inside\" && (\n            <span data-testid=\"flowbite-progress-inner-progress-label\">{progress}%</span>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n});\n\nProgress.displayName = \"Progress\";\n"], "names": ["forwardRef", "useId", "provider", "useThemeProvider", "theme", "useResolveTheme", "progressTheme", "get", "resolveProps", "jsxs", "jsx", "twMerge"], "mappings": ";;;;;;;;;;;AAUY,MAAC,QAAQ,GAAGA,gBAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACnD,EAAE,MAAM,EAAE,GAAGC,WAAK,EAAE;AACpB,EAAE,MAAMC,UAAQ,GAAGC,yBAAgB,EAAE;AACrC,EAAE,MAAMC,OAAK,GAAGC,4BAAe;AAC/B,IAAI,CAACC,mBAAa,EAAEJ,UAAQ,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,CAAC,KAAK,CAAC;AAC1D,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC5D,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE,KAAK,CAAC,UAAU;AAC3D,GAAG;AACH,EAAE,MAAM;AACR,IAAI,SAAS;AACb,IAAI,KAAK,GAAG,SAAS;AACrB,IAAI,aAAa,GAAG,KAAK;AACzB,IAAI,SAAS,GAAG,KAAK;AACrB,IAAI,QAAQ;AACZ,IAAI,qBAAqB,GAAG,QAAQ;AACpC,IAAI,IAAI,GAAG,IAAI;AACf,IAAI,SAAS,GAAG,aAAa;AAC7B,IAAI,iBAAiB,GAAG,QAAQ;AAChC,IAAI,GAAG;AACP,GAAG,GAAGM,yBAAY,CAAC,KAAK,EAAEN,UAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC;AACnD,EAAE,uBAAuBO,eAAI,CAAC,KAAK,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,YAAY,EAAE,SAAS,EAAE,eAAe,EAAE,QAAQ,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,SAAS,EAAE,QAAQ,EAAE;AACjJ,IAAI,CAAC,SAAS,IAAI,SAAS,IAAI,iBAAiB,KAAK,SAAS,IAAI,QAAQ,GAAG,CAAC,IAAI,aAAa,IAAI,qBAAqB,KAAK,SAAS,qBAAqBA,eAAI,CAAC,KAAK,EAAE,EAAE,SAAS,EAAEL,OAAK,CAAC,KAAK,EAAE,aAAa,EAAE,yCAAyC,EAAE,QAAQ,EAAE;AACrQ,MAAM,SAAS,IAAI,SAAS,IAAI,iBAAiB,KAAK,SAAS,oBAAoBM,cAAG,CAAC,MAAM,EAAE,EAAE,aAAa,EAAE,oCAAoC,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC;AAC5K,MAAM,aAAa,IAAI,qBAAqB,KAAK,SAAS,oBAAoBD,eAAI,CAAC,MAAM,EAAE,EAAE,aAAa,EAAE,wCAAwC,EAAE,QAAQ,EAAE;AAChK,QAAQ,QAAQ;AAChB,QAAQ;AACR,OAAO,EAAE;AACT,KAAK,EAAE,CAAC;AACR,oBAAoBC,cAAG,CAAC,KAAK,EAAE,EAAE,SAAS,EAAEC,qBAAO,CAACP,OAAK,CAAC,IAAI,EAAEA,OAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,EAAE,QAAQ,kBAAkBK,eAAI,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,EAAE,SAAS,EAAEE,qBAAO,CAACP,OAAK,CAAC,GAAG,EAAEA,OAAK,CAAC,KAAK,CAAC,KAAK,CAAC,EAAEA,OAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE;AACvP,MAAM,SAAS,IAAI,SAAS,IAAI,iBAAiB,KAAK,QAAQ,oBAAoBM,cAAG,CAAC,MAAM,EAAE,EAAE,aAAa,EAAE,oCAAoC,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC;AAC3K,MAAM,QAAQ,GAAG,CAAC,IAAI,aAAa,IAAI,qBAAqB,KAAK,QAAQ,oBAAoBD,eAAI,CAAC,MAAM,EAAE,EAAE,aAAa,EAAE,wCAAwC,EAAE,QAAQ,EAAE;AAC/K,QAAQ,QAAQ;AAChB,QAAQ;AACR,OAAO,EAAE;AACT,KAAK,EAAE,CAAC,EAAE;AACV,GAAG,EAAE,CAAC;AACN,CAAC;AACD,QAAQ,CAAC,WAAW,GAAG,UAAU;;;;"}