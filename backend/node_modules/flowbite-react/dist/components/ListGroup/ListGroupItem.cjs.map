{"version": 3, "file": "ListGroupItem.cjs", "sources": ["../../../src/components/ListGroup/ListGroupItem.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentProps, type FC } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { FlowbiteBoolean, ThemingProps } from \"../../types\";\nimport { listGroupTheme } from \"./theme\";\n\nexport interface ListGroupItemTheme {\n  base: string;\n  link: {\n    base: string;\n    active: FlowbiteBoolean;\n    disabled: FlowbiteBoolean;\n    href: FlowbiteBoolean;\n    icon: string;\n  };\n}\n\ntype GenericLinkButtonProps = ComponentProps<\"a\"> & ComponentProps<\"button\">;\n\nexport interface ListGroupItemProps extends GenericLinkButtonProps, ThemingProps<ListGroupItemTheme> {\n  active?: boolean;\n  icon?: FC<ComponentProps<\"svg\">>;\n}\n\nexport const ListGroupItem = forwardRef<HTMLLIElement, ListGroupItemProps>((props, ref) => {\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [listGroupTheme.item, provider.theme?.listGroup?.item, props.theme],\n    [get(provider.clearTheme, \"listGroup.item\"), props.clearTheme],\n    [get(provider.applyTheme, \"listGroup.item\"), props.applyTheme],\n  );\n\n  const {\n    active: isActive,\n    children,\n    className,\n    href,\n    icon: Icon,\n    onClick,\n    disabled,\n    ...restProps\n  } = resolveProps(props, provider.props?.listGroupItem);\n\n  const isLink = typeof href !== \"undefined\";\n  const Component = isLink ? \"a\" : \"button\";\n\n  return (\n    <li ref={ref} className={twMerge(theme.base, className)}>\n      <Component\n        href={href}\n        onClick={onClick}\n        type={isLink ? undefined : \"button\"}\n        disabled={disabled}\n        className={twMerge(\n          theme.link.active[isActive ? \"on\" : \"off\"],\n          theme.link.disabled[disabled ? \"on\" : \"off\"],\n          theme.link.base,\n          theme.link.href[isLink ? \"on\" : \"off\"],\n        )}\n        {...restProps}\n      >\n        {Icon && <Icon aria-hidden data-testid=\"flowbite-list-group-item-icon\" className={theme.link.icon} />}\n        {children}\n      </Component>\n    </li>\n  );\n});\n\nListGroupItem.displayName = \"ListGroupItem\";\n"], "names": ["forwardRef", "provider", "useThemeProvider", "theme", "useResolveTheme", "listGroupTheme", "get", "resolveProps", "jsx", "twMerge", "jsxs"], "mappings": ";;;;;;;;;;;AAUY,MAAC,aAAa,GAAGA,gBAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACxD,EAAE,MAAMC,UAAQ,GAAGC,yBAAgB,EAAE;AACrC,EAAE,MAAMC,OAAK,GAAGC,4BAAe;AAC/B,IAAI,CAACC,oBAAc,CAAC,IAAI,EAAEJ,UAAQ,CAAC,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC;AACvE,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,gBAAgB,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAClE,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,gBAAgB,CAAC,EAAE,KAAK,CAAC,UAAU;AACjE,GAAG;AACH,EAAE,MAAM;AACR,IAAI,MAAM,EAAE,QAAQ;AACpB,IAAI,QAAQ;AACZ,IAAI,SAAS;AACb,IAAI,IAAI;AACR,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAGM,yBAAY,CAAC,KAAK,EAAEN,UAAQ,CAAC,KAAK,EAAE,aAAa,CAAC;AACxD,EAAE,MAAM,MAAM,GAAG,OAAO,IAAI,KAAK,WAAW;AAC5C,EAAE,MAAM,SAAS,GAAG,MAAM,GAAG,GAAG,GAAG,QAAQ;AAC3C,EAAE,uBAAuBO,cAAG,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,SAAS,EAAEC,qBAAO,CAACN,OAAK,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,QAAQ,kBAAkBO,eAAI;AACnH,IAAI,SAAS;AACb,IAAI;AACJ,MAAM,IAAI;AACV,MAAM,OAAO;AACb,MAAM,IAAI,EAAE,MAAM,GAAG,MAAM,GAAG,QAAQ;AACtC,MAAM,QAAQ;AACd,MAAM,SAAS,EAAED,qBAAO;AACxB,QAAQN,OAAK,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,GAAG,KAAK,CAAC;AAClD,QAAQA,OAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG,IAAI,GAAG,KAAK,CAAC;AACpD,QAAQA,OAAK,CAAC,IAAI,CAAC,IAAI;AACvB,QAAQA,OAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,KAAK;AAC7C,OAAO;AACP,MAAM,GAAG,SAAS;AAClB,MAAM,QAAQ,EAAE;AAChB,QAAQ,IAAI,oBAAoBK,cAAG,CAAC,IAAI,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,aAAa,EAAE,+BAA+B,EAAE,SAAS,EAAEL,OAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;AAC9I,QAAQ;AACR;AACA;AACA,GAAG,EAAE,CAAC;AACN,CAAC;AACD,aAAa,CAAC,WAAW,GAAG,eAAe;;;;"}