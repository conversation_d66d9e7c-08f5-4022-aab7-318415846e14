{"version": 3, "file": "ListGroup.cjs", "sources": ["../../../src/components/ListGroup/ListGroup.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentProps } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { ThemingProps } from \"../../types\";\nimport type { ListGroupItemTheme } from \"./ListGroupItem\";\nimport { listGroupTheme } from \"./theme\";\n\nexport interface ListGroupTheme {\n  root: ListGroupRootTheme;\n  item: ListGroupItemTheme;\n}\n\nexport interface ListGroupRootTheme {\n  base: string;\n}\n\nexport interface ListGroupProps extends ComponentProps<\"ul\">, ThemingProps<ListGroupRootTheme> {}\n\nexport const ListGroup = forwardRef<HTMLUListElement, ListGroupProps>((props, ref) => {\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [listGroupTheme.root, provider.theme?.listGroup?.root, props.theme],\n    [get(provider.clearTheme, \"listGroup.root\"), props.clearTheme],\n    [get(provider.applyTheme, \"listGroup.root\"), props.applyTheme],\n  );\n\n  const { className, ...restProps } = resolveProps(props, provider.props?.listGroup);\n\n  return <ul ref={ref} className={twMerge(theme.base, className)} {...restProps} />;\n});\n\nListGroup.displayName = \"ListGroup\";\n"], "names": ["forwardRef", "provider", "useThemeProvider", "theme", "useResolveTheme", "listGroupTheme", "get", "resolveProps", "jsx", "twMerge"], "mappings": ";;;;;;;;;;;AAUY,MAAC,SAAS,GAAGA,gBAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACpD,EAAE,MAAMC,UAAQ,GAAGC,yBAAgB,EAAE;AACrC,EAAE,MAAMC,OAAK,GAAGC,4BAAe;AAC/B,IAAI,CAACC,oBAAc,CAAC,IAAI,EAAEJ,UAAQ,CAAC,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC;AACvE,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,gBAAgB,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAClE,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,gBAAgB,CAAC,EAAE,KAAK,CAAC,UAAU;AACjE,GAAG;AACH,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,SAAS,EAAE,GAAGM,yBAAY,CAAC,KAAK,EAAEN,UAAQ,CAAC,KAAK,EAAE,SAAS,CAAC;AACpF,EAAE,uBAAuBO,cAAG,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,SAAS,EAAEC,qBAAO,CAACN,OAAK,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,GAAG,SAAS,EAAE,CAAC;AACpG,CAAC;AACD,SAAS,CAAC,WAAW,GAAG,WAAW;;;;"}