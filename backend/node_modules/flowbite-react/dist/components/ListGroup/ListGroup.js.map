{"version": 3, "file": "ListGroup.js", "sources": ["../../../src/components/ListGroup/ListGroup.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentProps } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { ThemingProps } from \"../../types\";\nimport type { ListGroupItemTheme } from \"./ListGroupItem\";\nimport { listGroupTheme } from \"./theme\";\n\nexport interface ListGroupTheme {\n  root: ListGroupRootTheme;\n  item: ListGroupItemTheme;\n}\n\nexport interface ListGroupRootTheme {\n  base: string;\n}\n\nexport interface ListGroupProps extends ComponentProps<\"ul\">, ThemingProps<ListGroupRootTheme> {}\n\nexport const ListGroup = forwardRef<HTMLUListElement, ListGroupProps>((props, ref) => {\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [listGroupTheme.root, provider.theme?.listGroup?.root, props.theme],\n    [get(provider.clearTheme, \"listGroup.root\"), props.clearTheme],\n    [get(provider.applyTheme, \"listGroup.root\"), props.applyTheme],\n  );\n\n  const { className, ...restProps } = resolveProps(props, provider.props?.listGroup);\n\n  return <ul ref={ref} className={twMerge(theme.base, className)} {...restProps} />;\n});\n\nListGroup.displayName = \"ListGroup\";\n"], "names": [], "mappings": ";;;;;;;;;AAUY,MAAC,SAAS,GAAG,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACpD,EAAE,MAAM,QAAQ,GAAG,gBAAgB,EAAE;AACrC,EAAE,MAAM,KAAK,GAAG,eAAe;AAC/B,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC;AACvE,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,gBAAgB,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAClE,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,gBAAgB,CAAC,EAAE,KAAK,CAAC,UAAU;AACjE,GAAG;AACH,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,SAAS,EAAE,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,SAAS,CAAC;AACpF,EAAE,uBAAuB,GAAG,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,GAAG,SAAS,EAAE,CAAC;AACpG,CAAC;AACD,SAAS,CAAC,WAAW,GAAG,WAAW;;;;"}