{"version": 3, "file": "theme.js", "sources": ["../../../src/components/ListGroup/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { ListGroupTheme } from \"./ListGroup\";\n\nexport const listGroupTheme = createTheme<ListGroupTheme>({\n  root: {\n    base: \"list-none rounded-lg border border-gray-200 bg-white text-left text-sm font-medium text-gray-900 dark:border-gray-600 dark:bg-gray-700 dark:text-white\",\n  },\n  item: {\n    base: \"[&>*]:first:rounded-t-lg [&>*]:last:rounded-b-lg [&>*]:last:border-b-0\",\n    link: {\n      base: \"flex w-full items-center border-b border-gray-200 px-4 py-2 dark:border-gray-600\",\n      active: {\n        off: \"hover:bg-gray-100 hover:text-cyan-700 focus:text-cyan-700 focus:outline-none focus:ring-2 focus:ring-cyan-700 dark:border-gray-600 dark:hover:bg-gray-600 dark:hover:text-white dark:focus:text-white dark:focus:ring-gray-500\",\n        on: \"bg-cyan-700 text-white dark:bg-gray-800\",\n      },\n      disabled: {\n        off: \"\",\n        on: \"cursor-not-allowed bg-gray-100 text-gray-900 hover:bg-gray-100 hover:text-gray-900 focus:text-gray-900\",\n      },\n      href: {\n        off: \"\",\n        on: \"\",\n      },\n      icon: \"mr-2 h-4 w-4 fill-current\",\n    },\n  },\n});\n"], "names": [], "mappings": ";;AAEY,MAAC,cAAc,GAAG,WAAW,CAAC;AAC1C,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE;AACV,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,wEAAwE;AAClF,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,kFAAkF;AAC9F,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,gOAAgO;AAC7O,QAAQ,EAAE,EAAE;AACZ,OAAO;AACP,MAAM,QAAQ,EAAE;AAChB,QAAQ,GAAG,EAAE,EAAE;AACf,QAAQ,EAAE,EAAE;AACZ,OAAO;AACP,MAAM,IAAI,EAAE;AACZ,QAAQ,GAAG,EAAE,EAAE;AACf,QAAQ,EAAE,EAAE;AACZ,OAAO;AACP,MAAM,IAAI,EAAE;AACZ;AACA;AACA,CAAC;;;;"}