{"version": 3, "file": "Tabs.js", "sources": ["../../../src/components/Tabs/Tabs.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps, KeyboardEvent, PropsWithChildren, ReactElement } from \"react\";\nimport { Children, forwardRef, useEffect, useId, useImperativeHandle, useMemo, useRef, useState } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { FlowbiteBoolean, ThemingProps } from \"../../types\";\nimport type { TabItemProps } from \"./TabItem\";\nimport { tabsTheme } from \"./theme\";\n\nexport interface TabsTheme {\n  base: string;\n  tablist: {\n    base: string;\n    variant: TabStyles;\n    tabitem: {\n      base: string;\n      variant: TabStyleItem<TabStyles>;\n      icon: string;\n    };\n  };\n  tabitemcontainer: {\n    base: string;\n    variant: TabStyles;\n  };\n  tabpanel: string;\n}\n\nexport interface TabStyles {\n  default: string;\n  fullWidth: string;\n  pills: string;\n  underline: string;\n}\n\nexport interface TabStyleItemProps {\n  base: string;\n  active: FlowbiteBoolean;\n}\n\nexport type TabStyleItem<Type> = {\n  [K in keyof Type]: TabStyleItemProps;\n};\n\nexport type TabItemStatus = \"active\" | \"notActive\";\n\ninterface TabEventProps {\n  target: number;\n}\n\ninterface TabKeyboardEventProps extends TabEventProps {\n  event: KeyboardEvent<HTMLButtonElement>;\n}\n\nexport interface TabsProps extends Omit<ComponentProps<\"div\">, \"ref\">, ThemingProps<TabsTheme> {\n  onActiveTabChange?: (activeTab: number) => void;\n  variant?: keyof TabStyles;\n}\n\nexport interface TabsRef {\n  setActiveTab: (activeTab: number) => void;\n}\n\nexport const Tabs = forwardRef<TabsRef, TabsProps>((props, ref) => {\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [tabsTheme, provider.theme?.tabs, props.theme],\n    [get(provider.clearTheme, \"tabs\"), props.clearTheme],\n    [get(provider.applyTheme, \"tabs\"), props.applyTheme],\n  );\n\n  const {\n    children,\n    className,\n    onActiveTabChange,\n    variant = \"default\",\n    ...restProps\n  } = resolveProps(props, provider.props?.tabs);\n\n  const id = useId();\n  const tabs = useMemo(\n    () =>\n      Children.map(Children.toArray(children) as ReactElement<PropsWithChildren<TabItemProps>>[], ({ props }) => props),\n    [children],\n  );\n  const tabRefs = useRef<HTMLButtonElement[]>([]);\n  const [activeTab, setActiveTab] = useState(\n    Math.max(\n      0,\n      tabs.findIndex((tab) => tab.active),\n    ),\n  );\n  const [focusedTab, setFocusedTab] = useState(-1);\n\n  function setActiveTabWithCallback(activeTab: number) {\n    setActiveTab(activeTab);\n    if (onActiveTabChange) onActiveTabChange(activeTab);\n  }\n\n  function handleClick({ target }: TabEventProps): void {\n    setActiveTabWithCallback(target);\n    setFocusedTab(target);\n  }\n\n  function handleKeyboard({ event, target }: TabKeyboardEventProps): void {\n    if (event.key === \"ArrowLeft\") {\n      setFocusedTab(Math.max(0, focusedTab - 1));\n    }\n\n    if (event.key === \"ArrowRight\") {\n      setFocusedTab(Math.min(tabs.length - 1, focusedTab + 1));\n    }\n\n    if (event.key === \"Enter\") {\n      setActiveTabWithCallback(target);\n      setFocusedTab(target);\n    }\n  }\n\n  const tabItemStyle = theme.tablist.tabitem.variant[variant];\n  const tabItemContainerStyle = theme.tabitemcontainer.variant[variant];\n\n  useEffect(() => {\n    tabRefs.current[focusedTab]?.focus();\n  }, [focusedTab]);\n\n  useImperativeHandle(ref, () => ({\n    setActiveTab: setActiveTabWithCallback,\n  }));\n\n  return (\n    <div className={twMerge(theme.base, className)}>\n      <div\n        aria-label=\"Tabs\"\n        role=\"tablist\"\n        className={twMerge(theme.tablist.base, theme.tablist.variant[variant], className)}\n        {...restProps}\n      >\n        {tabs.map((tab, index) => (\n          <button\n            key={index}\n            type=\"button\"\n            aria-controls={`${id}-tabpanel-${index}`}\n            aria-selected={index === activeTab}\n            className={twMerge(\n              theme.tablist.tabitem.base,\n              tabItemStyle.base,\n              index === activeTab && tabItemStyle.active.on,\n              index !== activeTab && !tab.disabled && tabItemStyle.active.off,\n            )}\n            disabled={tab.disabled}\n            id={`${id}-tab-${index}`}\n            onClick={() => handleClick({ target: index })}\n            onKeyDown={(event) => handleKeyboard({ event, target: index })}\n            ref={(element) => {\n              tabRefs.current[index] = element as HTMLButtonElement;\n            }}\n            role=\"tab\"\n            tabIndex={index === focusedTab ? 0 : -1}\n            style={{ zIndex: index === focusedTab ? 2 : 1 }}\n          >\n            {tab.icon && <tab.icon className={theme.tablist.tabitem.icon} />}\n            {tab.title}\n          </button>\n        ))}\n      </div>\n      <div className={twMerge(theme.tabitemcontainer.base, tabItemContainerStyle)}>\n        {tabs.map((tab, index) => (\n          <div\n            key={index}\n            aria-labelledby={`${id}-tab-${index}`}\n            className={theme.tabpanel}\n            hidden={index !== activeTab}\n            id={`${id}-tabpanel-${index}`}\n            role=\"tabpanel\"\n            tabIndex={0}\n          >\n            {tab.children}\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n});\n\nTabs.displayName = \"Tabs\";\n"], "names": [], "mappings": ";;;;;;;;;AAUY,MAAC,IAAI,GAAG,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AAC/C,EAAE,MAAM,QAAQ,GAAG,gBAAgB,EAAE;AACrC,EAAE,MAAM,KAAK,GAAG,eAAe;AAC/B,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC;AAClD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AACxD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,UAAU;AACvD,GAAG;AACH,EAAE,MAAM;AACR,IAAI,QAAQ;AACZ,IAAI,SAAS;AACb,IAAI,iBAAiB;AACrB,IAAI,OAAO,GAAG,SAAS;AACvB,IAAI,GAAG;AACP,GAAG,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC;AAC/C,EAAE,MAAM,EAAE,GAAG,KAAK,EAAE;AACpB,EAAE,MAAM,IAAI,GAAG,OAAO;AACtB,IAAI,MAAM,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,MAAM,CAAC;AACjF,IAAI,CAAC,QAAQ;AACb,GAAG;AACH,EAAE,MAAM,OAAO,GAAG,MAAM,CAAC,EAAE,CAAC;AAC5B,EAAE,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,QAAQ;AAC5C,IAAI,IAAI,CAAC,GAAG;AACZ,MAAM,CAAC;AACP,MAAM,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM;AACxC;AACA,GAAG;AACH,EAAE,MAAM,CAAC,UAAU,EAAE,aAAa,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC;AAClD,EAAE,SAAS,wBAAwB,CAAC,UAAU,EAAE;AAChD,IAAI,YAAY,CAAC,UAAU,CAAC;AAC5B,IAAI,IAAI,iBAAiB,EAAE,iBAAiB,CAAC,UAAU,CAAC;AACxD;AACA,EAAE,SAAS,WAAW,CAAC,EAAE,MAAM,EAAE,EAAE;AACnC,IAAI,wBAAwB,CAAC,MAAM,CAAC;AACpC,IAAI,aAAa,CAAC,MAAM,CAAC;AACzB;AACA,EAAE,SAAS,cAAc,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE;AAC7C,IAAI,IAAI,KAAK,CAAC,GAAG,KAAK,WAAW,EAAE;AACnC,MAAM,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC;AAChD;AACA,IAAI,IAAI,KAAK,CAAC,GAAG,KAAK,YAAY,EAAE;AACpC,MAAM,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC;AAC9D;AACA,IAAI,IAAI,KAAK,CAAC,GAAG,KAAK,OAAO,EAAE;AAC/B,MAAM,wBAAwB,CAAC,MAAM,CAAC;AACtC,MAAM,aAAa,CAAC,MAAM,CAAC;AAC3B;AACA;AACA,EAAE,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;AAC7D,EAAE,MAAM,qBAAqB,GAAG,KAAK,CAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC;AACvE,EAAE,SAAS,CAAC,MAAM;AAClB,IAAI,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE;AACxC,GAAG,EAAE,CAAC,UAAU,CAAC,CAAC;AAClB,EAAE,mBAAmB,CAAC,GAAG,EAAE,OAAO;AAClC,IAAI,YAAY,EAAE;AAClB,GAAG,CAAC,CAAC;AACL,EAAE,uBAAuB,IAAI,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,QAAQ,EAAE;AAC5F,oBAAoB,GAAG;AACvB,MAAM,KAAK;AACX,MAAM;AACN,QAAQ,YAAY,EAAE,MAAM;AAC5B,QAAQ,IAAI,EAAE,SAAS;AACvB,QAAQ,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,SAAS,CAAC;AACzF,QAAQ,GAAG,SAAS;AACpB,QAAQ,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,qBAAqB,IAAI;AAC/D,UAAU,QAAQ;AAClB,UAAU;AACV,YAAY,IAAI,EAAE,QAAQ;AAC1B,YAAY,eAAe,EAAE,CAAC,EAAE,EAAE,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;AACtD,YAAY,eAAe,EAAE,KAAK,KAAK,SAAS;AAChD,YAAY,SAAS,EAAE,OAAO;AAC9B,cAAc,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI;AACxC,cAAc,YAAY,CAAC,IAAI;AAC/B,cAAc,KAAK,KAAK,SAAS,IAAI,YAAY,CAAC,MAAM,CAAC,EAAE;AAC3D,cAAc,KAAK,KAAK,SAAS,IAAI,CAAC,GAAG,CAAC,QAAQ,IAAI,YAAY,CAAC,MAAM,CAAC;AAC1E,aAAa;AACb,YAAY,QAAQ,EAAE,GAAG,CAAC,QAAQ;AAClC,YAAY,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACpC,YAAY,OAAO,EAAE,MAAM,WAAW,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;AACzD,YAAY,SAAS,EAAE,CAAC,KAAK,KAAK,cAAc,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;AAC1E,YAAY,GAAG,EAAE,CAAC,OAAO,KAAK;AAC9B,cAAc,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,OAAO;AAC9C,aAAa;AACb,YAAY,IAAI,EAAE,KAAK;AACvB,YAAY,QAAQ,EAAE,KAAK,KAAK,UAAU,GAAG,CAAC,GAAG,EAAE;AACnD,YAAY,KAAK,EAAE,EAAE,MAAM,EAAE,KAAK,KAAK,UAAU,GAAG,CAAC,GAAG,CAAC,EAAE;AAC3D,YAAY,QAAQ,EAAE;AACtB,cAAc,GAAG,CAAC,IAAI,oBAAoB,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;AAClG,cAAc,GAAG,CAAC;AAClB;AACA,WAAW;AACX,UAAU;AACV,SAAS;AACT;AACA,KAAK;AACL,oBAAoB,GAAG,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,EAAE,qBAAqB,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,qBAAqB,GAAG;AAC/J,MAAM,KAAK;AACX,MAAM;AACN,QAAQ,iBAAiB,EAAE,CAAC,EAAE,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC/C,QAAQ,SAAS,EAAE,KAAK,CAAC,QAAQ;AACjC,QAAQ,MAAM,EAAE,KAAK,KAAK,SAAS;AACnC,QAAQ,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;AACrC,QAAQ,IAAI,EAAE,UAAU;AACxB,QAAQ,QAAQ,EAAE,CAAC;AACnB,QAAQ,QAAQ,EAAE,GAAG,CAAC;AACtB,OAAO;AACP,MAAM;AACN,KAAK,CAAC,EAAE;AACR,GAAG,EAAE,CAAC;AACN,CAAC;AACD,IAAI,CAAC,WAAW,GAAG,MAAM;;;;"}