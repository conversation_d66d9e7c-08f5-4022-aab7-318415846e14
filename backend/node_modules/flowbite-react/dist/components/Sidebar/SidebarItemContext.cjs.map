{"version": 3, "file": "SidebarItemContext.cjs", "sources": ["../../../src/components/Sidebar/SidebarItemContext.tsx"], "sourcesContent": ["\"use client\";\n\nimport { createContext, useContext } from \"react\";\n\nexport interface SidebarItemContextValue {\n  isInsideCollapse: boolean;\n}\n\nexport const SidebarItemContext = createContext<SidebarItemContextValue | undefined>(undefined);\n\nexport function useSidebarItemContext(): SidebarItemContextValue {\n  const context = useContext(SidebarItemContext);\n\n  if (!context) {\n    throw new Error(\"useSidebarItemContext should be used within the SidebarItemContext provider!\");\n  }\n\n  return context;\n}\n"], "names": ["createContext", "useContext"], "mappings": ";;;;AAGY,MAAC,kBAAkB,GAAGA,mBAAa,CAAC,MAAM;AAC/C,SAAS,qBAAqB,GAAG;AACxC,EAAE,MAAM,OAAO,GAAGC,gBAAU,CAAC,kBAAkB,CAAC;AAChD,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,MAAM,IAAI,KAAK,CAAC,8EAA8E,CAAC;AACnG;AACA,EAAE,OAAO,OAAO;AAChB;;;;;"}