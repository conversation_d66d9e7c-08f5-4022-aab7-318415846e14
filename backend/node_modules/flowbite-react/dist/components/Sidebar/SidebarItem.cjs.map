{"version": 3, "file": "SidebarItem.cjs", "sources": ["../../../src/components/Sidebar/SidebarItem.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps, ElementType, FC, PropsWithChildren, ReactNode } from \"react\";\nimport { forwardRef, useId } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { DynamicStringEnumKeysOf, FlowbiteColors, ThemingProps } from \"../../types\";\nimport { Badge } from \"../Badge\";\nimport { Tooltip } from \"../Tooltip\";\nimport { useSidebarContext } from \"./SidebarContext\";\nimport { useSidebarItemContext } from \"./SidebarItemContext\";\nimport { sidebarTheme } from \"./theme\";\n\nexport interface SidebarItemTheme {\n  active: string;\n  base: string;\n  collapsed: {\n    insideCollapse: string;\n    noIcon: string;\n  };\n  content: {\n    base: string;\n  };\n  icon: {\n    base: string;\n    active: string;\n  };\n  label: string;\n  listItem: string;\n}\n\nexport interface SidebarItemProps extends Omit<ComponentProps<\"div\">, \"ref\">, ThemingProps<SidebarItemTheme> {\n  active?: boolean;\n  as?: ElementType;\n  href?: string;\n  icon?: FC<ComponentProps<\"svg\">>;\n  label?: string;\n  labelColor?: DynamicStringEnumKeysOf<SidebarItemLabelColors>;\n}\n\nexport interface SidebarItemLabelColors extends Pick<FlowbiteColors, \"gray\"> {\n  [key: string]: string;\n}\n\nexport const SidebarItem = forwardRef<Element, SidebarItemProps>((props, ref) => {\n  const id = useId();\n  const { theme: rootTheme, clearTheme: rootClearTheme, applyTheme: rootApplyTheme, isCollapsed } = useSidebarContext();\n  const { isInsideCollapse } = useSidebarItemContext();\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [sidebarTheme.item, provider.theme?.sidebar?.item, rootTheme?.item, props.theme],\n    [get(provider.clearTheme, \"sidebar.item\"), get(rootClearTheme, \"item\"), props.clearTheme],\n    [get(provider.applyTheme, \"sidebar.item\"), get(rootApplyTheme, \"item\"), props.applyTheme],\n  );\n\n  const {\n    active: isActive,\n    as: Component = \"a\",\n    children,\n    className,\n    icon: Icon,\n    label,\n    labelColor = \"info\",\n    ...restProps\n  } = resolveProps(props, provider.props?.sidebarItem);\n\n  return (\n    <ListItem theme={theme} className={theme.listItem} id={id} isCollapsed={isCollapsed} tooltipChildren={children}>\n      <Component\n        aria-labelledby={`flowbite-sidebar-item-${id}`}\n        ref={ref}\n        className={twMerge(\n          theme.base,\n          isActive && theme.active,\n          !isCollapsed && isInsideCollapse && theme.collapsed.insideCollapse,\n          className,\n        )}\n        {...restProps}\n      >\n        {Icon && (\n          <Icon\n            aria-hidden\n            data-testid=\"flowbite-sidebar-item-icon\"\n            className={twMerge(theme.icon.base, isActive && theme.icon.active)}\n          />\n        )}\n        {isCollapsed && !Icon && (\n          <span className={theme.collapsed.noIcon}>{(children as string).charAt(0).toLocaleUpperCase() ?? \"?\"}</span>\n        )}\n        {!isCollapsed && (\n          <Children id={id} theme={theme}>\n            {children}\n          </Children>\n        )}\n        {!isCollapsed && label && (\n          <Badge color={labelColor} data-testid=\"flowbite-sidebar-label\" hidden={isCollapsed} className={theme.label}>\n            {label}\n          </Badge>\n        )}\n      </Component>\n    </ListItem>\n  );\n});\n\nSidebarItem.displayName = \"SidebarItem\";\n\nfunction ListItem({\n  id,\n  theme,\n  isCollapsed,\n  tooltipChildren,\n  children: wrapperChildren,\n  ...props\n}: PropsWithChildren<{\n  id: string;\n  theme: SidebarItemTheme;\n  isCollapsed: boolean;\n  tooltipChildren: ReactNode | undefined;\n  className?: string;\n}>) {\n  return (\n    <li {...props}>\n      {isCollapsed ? (\n        <Tooltip\n          content={\n            <Children id={id} theme={theme}>\n              {tooltipChildren}\n            </Children>\n          }\n          placement=\"right\"\n        >\n          {wrapperChildren}\n        </Tooltip>\n      ) : (\n        wrapperChildren\n      )}\n    </li>\n  );\n}\n\nListItem.displayName = \"SidebarItem.ListItem\";\n\nfunction Children({ id, theme, children }: PropsWithChildren<{ id: string; theme: SidebarItemTheme }>) {\n  return (\n    <span\n      data-testid=\"flowbite-sidebar-item-content\"\n      id={`flowbite-sidebar-item-${id}`}\n      className={twMerge(theme.content.base)}\n    >\n      {children}\n    </span>\n  );\n}\n\nListItem.displayName = \"SidebarItem.Children\";\n"], "names": ["forwardRef", "useId", "useSidebarContext", "useSidebarItemContext", "provider", "useThemeProvider", "theme", "useResolveTheme", "sidebarTheme", "get", "resolveProps", "jsx", "jsxs", "twMerge", "Badge", "<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;AAcY,MAAC,WAAW,GAAGA,gBAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACtD,EAAE,MAAM,EAAE,GAAGC,WAAK,EAAE;AACpB,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,WAAW,EAAE,GAAGC,gCAAiB,EAAE;AACvH,EAAE,MAAM,EAAE,gBAAgB,EAAE,GAAGC,wCAAqB,EAAE;AACtD,EAAE,MAAMC,UAAQ,GAAGC,yBAAgB,EAAE;AACrC,EAAE,MAAMC,OAAK,GAAGC,4BAAe;AAC/B,IAAI,CAACC,kBAAY,CAAC,IAAI,EAAEJ,UAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC;AACpF,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,cAAc,CAAC,EAAEK,OAAG,CAAC,cAAc,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC7F,IAAI,CAACA,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,cAAc,CAAC,EAAEK,OAAG,CAAC,cAAc,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,UAAU;AAC5F,GAAG;AACH,EAAE,MAAM;AACR,IAAI,MAAM,EAAE,QAAQ;AACpB,IAAI,EAAE,EAAE,SAAS,GAAG,GAAG;AACvB,IAAI,QAAQ;AACZ,IAAI,SAAS;AACb,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,KAAK;AACT,IAAI,UAAU,GAAG,MAAM;AACvB,IAAI,GAAG;AACP,GAAG,GAAGC,yBAAY,CAAC,KAAK,EAAEN,UAAQ,CAAC,KAAK,EAAE,WAAW,CAAC;AACtD,EAAE,uBAAuBO,cAAG,CAAC,QAAQ,EAAE,SAAEL,OAAK,EAAE,SAAS,EAAEA,OAAK,CAAC,QAAQ,EAAE,EAAE,EAAE,WAAW,EAAE,eAAe,EAAE,QAAQ,EAAE,QAAQ,kBAAkBM,eAAI;AACrJ,IAAI,SAAS;AACb,IAAI;AACJ,MAAM,iBAAiB,EAAE,CAAC,sBAAsB,EAAE,EAAE,CAAC,CAAC;AACtD,MAAM,GAAG;AACT,MAAM,SAAS,EAAEC,qBAAO;AACxB,QAAQP,OAAK,CAAC,IAAI;AAClB,QAAQ,QAAQ,IAAIA,OAAK,CAAC,MAAM;AAChC,QAAQ,CAAC,WAAW,IAAI,gBAAgB,IAAIA,OAAK,CAAC,SAAS,CAAC,cAAc;AAC1E,QAAQ;AACR,OAAO;AACP,MAAM,GAAG,SAAS;AAClB,MAAM,QAAQ,EAAE;AAChB,QAAQ,IAAI,oBAAoBK,cAAG;AACnC,UAAU,IAAI;AACd,UAAU;AACV,YAAY,aAAa,EAAE,IAAI;AAC/B,YAAY,aAAa,EAAE,4BAA4B;AACvD,YAAY,SAAS,EAAEE,qBAAO,CAACP,OAAK,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,IAAIA,OAAK,CAAC,IAAI,CAAC,MAAM;AAC7E;AACA,SAAS;AACT,QAAQ,WAAW,IAAI,CAAC,IAAI,oBAAoBK,cAAG,CAAC,MAAM,EAAE,EAAE,SAAS,EAAEL,OAAK,CAAC,SAAS,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,iBAAiB,EAAE,IAAI,GAAG,EAAE,CAAC;AAC3J,QAAQ,CAAC,WAAW,oBAAoBK,cAAG,CAAC,QAAQ,EAAE,EAAE,EAAE,SAAEL,OAAK,EAAE,QAAQ,EAAE,CAAC;AAC9E,QAAQ,CAAC,WAAW,IAAI,KAAK,oBAAoBK,cAAG,CAACG,WAAK,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,wBAAwB,EAAE,MAAM,EAAE,WAAW,EAAE,SAAS,EAAER,OAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE;AACxL;AACA;AACA,GAAG,EAAE,CAAC;AACN,CAAC;AACD,WAAW,CAAC,WAAW,GAAG,aAAa;AACvC,SAAS,QAAQ,CAAC;AAClB,EAAE,EAAE;AACJ,EAAE,KAAK;AACP,EAAE,WAAW;AACb,EAAE,eAAe;AACjB,EAAE,QAAQ,EAAE,eAAe;AAC3B,EAAE,GAAG;AACL,CAAC,EAAE;AACH,EAAE,uBAAuBK,cAAG,CAAC,IAAI,EAAE,EAAE,GAAG,KAAK,EAAE,QAAQ,EAAE,WAAW,mBAAmBA,cAAG;AAC1F,IAAII,eAAO;AACX,IAAI;AACJ,MAAM,OAAO,kBAAkBJ,cAAG,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,eAAe,EAAE,CAAC;AACtF,MAAM,SAAS,EAAE,OAAO;AACxB,MAAM,QAAQ,EAAE;AAChB;AACA,GAAG,GAAG,eAAe,EAAE,CAAC;AACxB;AACA,QAAQ,CAAC,WAAW,GAAG,sBAAsB;AAC7C,SAAS,QAAQ,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE;AAC3C,EAAE,uBAAuBA,cAAG;AAC5B,IAAI,MAAM;AACV,IAAI;AACJ,MAAM,aAAa,EAAE,+BAA+B;AACpD,MAAM,EAAE,EAAE,CAAC,sBAAsB,EAAE,EAAE,CAAC,CAAC;AACvC,MAAM,SAAS,EAAEE,qBAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;AAC5C,MAAM;AACN;AACA,GAAG;AACH;AACA,QAAQ,CAAC,WAAW,GAAG,sBAAsB;;;;"}