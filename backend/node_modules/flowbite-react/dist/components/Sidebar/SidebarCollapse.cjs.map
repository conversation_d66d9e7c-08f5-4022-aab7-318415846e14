{"version": 3, "file": "SidebarCollapse.cjs", "sources": ["../../../src/components/Sidebar/SidebarCollapse.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps, FC, PropsWithChildren, ReactElement } from \"react\";\nimport { forwardRef, useEffect, useId, useState } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { ChevronDownIcon } from \"../../icons/chevron-down-icon\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { FlowbiteBoolean, ThemingProps } from \"../../types\";\nimport { Tooltip } from \"../Tooltip\";\nimport { useSidebarContext } from \"./SidebarContext\";\nimport type { SidebarItemProps } from \"./SidebarItem\";\nimport { SidebarItemContext } from \"./SidebarItemContext\";\nimport { sidebarTheme } from \"./theme\";\n\nexport interface SidebarCollapseTheme {\n  button: string;\n  icon: {\n    base: string;\n    open: FlowbiteBoolean;\n  };\n  label: {\n    base: string;\n    title: string;\n    icon: {\n      base: string;\n      open: FlowbiteBoolean;\n    };\n  };\n  list: string;\n}\n\nexport interface SidebarCollapseProps\n  extends Pick<SidebarItemProps, \"active\" | \"as\" | \"href\" | \"icon\" | \"label\" | \"labelColor\">,\n    ComponentProps<\"button\">,\n    ThemingProps<SidebarCollapseTheme> {\n  onClick?: ComponentProps<\"button\">[\"onClick\"];\n  open?: boolean;\n  chevronIcon?: FC<ComponentProps<\"svg\">>;\n  renderChevronIcon?: (theme: SidebarCollapseTheme, open: boolean) => ReactElement;\n}\n\nexport const SidebarCollapse = forwardRef<HTMLLIElement, SidebarCollapseProps>((props, ref) => {\n  const { theme: rootTheme, clearTheme: rootClearTheme, applyTheme: rootApplyTheme, isCollapsed } = useSidebarContext();\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [sidebarTheme.collapse, provider.theme?.sidebar?.collapse, rootTheme?.collapse, props.theme],\n    [get(provider.clearTheme, \"sidebar.collapse\"), get(rootClearTheme, \"collapse\"), props.clearTheme],\n    [get(provider.applyTheme, \"sidebar.collapse\"), get(rootApplyTheme, \"collapse\"), props.applyTheme],\n  );\n\n  const {\n    children,\n    className,\n    icon: Icon,\n    label,\n    chevronIcon: ChevronIcon = ChevronDownIcon,\n    renderChevronIcon,\n    open = false,\n    ...restProps\n  } = resolveProps(props, provider.props?.sidebarCollapse);\n\n  const id = useId();\n  const [isOpen, setOpen] = useState(open);\n\n  useEffect(() => setOpen(open), [open]);\n\n  function Wrapper({ children }: PropsWithChildren) {\n    if (isCollapsed && !isOpen) {\n      return (\n        <Tooltip content={label} placement=\"right\">\n          {children}\n        </Tooltip>\n      );\n    }\n\n    return children;\n  }\n\n  return (\n    <li ref={ref}>\n      <Wrapper>\n        <button\n          id={`flowbite-sidebar-collapse-${id}`}\n          onClick={() => setOpen(!isOpen)}\n          title={label}\n          type=\"button\"\n          className={twMerge(theme.button, className)}\n          {...restProps}\n        >\n          {Icon && (\n            <Icon\n              aria-hidden\n              data-testid=\"flowbite-sidebar-collapse-icon\"\n              className={twMerge(theme.icon.base, theme.icon.open[isOpen ? \"on\" : \"off\"])}\n            />\n          )}\n          {isCollapsed ? (\n            <span className={theme.label.title}>{label}</span>\n          ) : (\n            <>\n              <span data-testid=\"flowbite-sidebar-collapse-label\" className={theme.label.base}>\n                {label}\n              </span>\n              {renderChevronIcon ? (\n                renderChevronIcon(theme, isOpen)\n              ) : (\n                <ChevronIcon\n                  aria-hidden\n                  className={twMerge(theme.label.icon.base, theme.label.icon.open[isOpen ? \"on\" : \"off\"])}\n                />\n              )}\n            </>\n          )}\n        </button>\n        <ul aria-labelledby={`flowbite-sidebar-collapse-${id}`} hidden={!isOpen} className={theme.list}>\n          <SidebarItemContext.Provider value={{ isInsideCollapse: true }}>{children}</SidebarItemContext.Provider>\n        </ul>\n      </Wrapper>\n    </li>\n  );\n});\n\nSidebarCollapse.displayName = \"SidebarCollapse\";\n"], "names": ["forwardRef", "useSidebarContext", "provider", "useThemeProvider", "theme", "useResolveTheme", "sidebarTheme", "get", "ChevronDownIcon", "resolveProps", "useId", "useState", "useEffect", "jsx", "<PERSON><PERSON><PERSON>", "jsxs", "twMerge", "Fragment", "SidebarItemContext"], "mappings": ";;;;;;;;;;;;;;;AAcY,MAAC,eAAe,GAAGA,gBAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AAC1D,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,WAAW,EAAE,GAAGC,gCAAiB,EAAE;AACvH,EAAE,MAAMC,UAAQ,GAAGC,yBAAgB,EAAE;AACrC,EAAE,MAAMC,OAAK,GAAGC,4BAAe;AAC/B,IAAI,CAACC,kBAAY,CAAC,QAAQ,EAAEJ,UAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,CAAC,KAAK,CAAC;AAChG,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,kBAAkB,CAAC,EAAEK,OAAG,CAAC,cAAc,EAAE,UAAU,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AACrG,IAAI,CAACA,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,kBAAkB,CAAC,EAAEK,OAAG,CAAC,cAAc,EAAE,UAAU,CAAC,EAAE,KAAK,CAAC,UAAU;AACpG,GAAG;AACH,EAAE,MAAM;AACR,IAAI,QAAQ;AACZ,IAAI,SAAS;AACb,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,KAAK;AACT,IAAI,WAAW,EAAE,WAAW,GAAGC,+BAAe;AAC9C,IAAI,iBAAiB;AACrB,IAAI,IAAI,GAAG,KAAK;AAChB,IAAI,GAAG;AACP,GAAG,GAAGC,yBAAY,CAAC,KAAK,EAAEP,UAAQ,CAAC,KAAK,EAAE,eAAe,CAAC;AAC1D,EAAE,MAAM,EAAE,GAAGQ,WAAK,EAAE;AACpB,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAGC,cAAQ,CAAC,IAAI,CAAC;AAC1C,EAAEC,eAAS,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;AACxC,EAAE,SAAS,OAAO,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,EAAE;AAC5C,IAAI,IAAI,WAAW,IAAI,CAAC,MAAM,EAAE;AAChC,MAAM,uBAAuBC,cAAG,CAACC,eAAO,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC;AACtG;AACA,IAAI,OAAO,SAAS;AACpB;AACA,EAAE,uBAAuBD,cAAG,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,QAAQ,kBAAkBE,eAAI,CAAC,OAAO,EAAE,EAAE,QAAQ,EAAE;AAC9F,oBAAoBA,eAAI;AACxB,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,EAAE,EAAE,CAAC,0BAA0B,EAAE,EAAE,CAAC,CAAC;AAC7C,QAAQ,OAAO,EAAE,MAAM,OAAO,CAAC,CAAC,MAAM,CAAC;AACvC,QAAQ,KAAK,EAAE,KAAK;AACpB,QAAQ,IAAI,EAAE,QAAQ;AACtB,QAAQ,SAAS,EAAEC,qBAAO,CAACZ,OAAK,CAAC,MAAM,EAAE,SAAS,CAAC;AACnD,QAAQ,GAAG,SAAS;AACpB,QAAQ,QAAQ,EAAE;AAClB,UAAU,IAAI,oBAAoBS,cAAG;AACrC,YAAY,IAAI;AAChB,YAAY;AACZ,cAAc,aAAa,EAAE,IAAI;AACjC,cAAc,aAAa,EAAE,gCAAgC;AAC7D,cAAc,SAAS,EAAEG,qBAAO,CAACZ,OAAK,CAAC,IAAI,CAAC,IAAI,EAAEA,OAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,KAAK,CAAC;AACxF;AACA,WAAW;AACX,UAAU,WAAW,mBAAmBS,cAAG,CAAC,MAAM,EAAE,EAAE,SAAS,EAAET,OAAK,CAAC,KAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,mBAAmBW,eAAI,CAACE,mBAAQ,EAAE,EAAE,QAAQ,EAAE;AACpJ,4BAA4BJ,cAAG,CAAC,MAAM,EAAE,EAAE,aAAa,EAAE,iCAAiC,EAAE,SAAS,EAAET,OAAK,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;AAC3I,YAAY,iBAAiB,GAAG,iBAAiB,CAACA,OAAK,EAAE,MAAM,CAAC,mBAAmBS,cAAG;AACtF,cAAc,WAAW;AACzB,cAAc;AACd,gBAAgB,aAAa,EAAE,IAAI;AACnC,gBAAgB,SAAS,EAAEG,qBAAO,CAACZ,OAAK,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAEA,OAAK,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,KAAK,CAAC;AACtG;AACA;AACA,WAAW,EAAE;AACb;AACA;AACA,KAAK;AACL,oBAAoBS,cAAG,CAAC,IAAI,EAAE,EAAE,iBAAiB,EAAE,CAAC,0BAA0B,EAAE,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,MAAM,EAAE,SAAS,EAAET,OAAK,CAAC,IAAI,EAAE,QAAQ,kBAAkBS,cAAG,CAACK,qCAAkB,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,EAAE,gBAAgB,EAAE,IAAI,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE;AAC3O,GAAG,EAAE,CAAC,EAAE,CAAC;AACT,CAAC;AACD,eAAe,CAAC,WAAW,GAAG,iBAAiB;;;;"}