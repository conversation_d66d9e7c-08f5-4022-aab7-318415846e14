{"version": 3, "file": "SidebarCollapse.js", "sources": ["../../../src/components/Sidebar/SidebarCollapse.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps, FC, PropsWithChildren, ReactElement } from \"react\";\nimport { forwardRef, useEffect, useId, useState } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { ChevronDownIcon } from \"../../icons/chevron-down-icon\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { FlowbiteBoolean, ThemingProps } from \"../../types\";\nimport { Tooltip } from \"../Tooltip\";\nimport { useSidebarContext } from \"./SidebarContext\";\nimport type { SidebarItemProps } from \"./SidebarItem\";\nimport { SidebarItemContext } from \"./SidebarItemContext\";\nimport { sidebarTheme } from \"./theme\";\n\nexport interface SidebarCollapseTheme {\n  button: string;\n  icon: {\n    base: string;\n    open: FlowbiteBoolean;\n  };\n  label: {\n    base: string;\n    title: string;\n    icon: {\n      base: string;\n      open: FlowbiteBoolean;\n    };\n  };\n  list: string;\n}\n\nexport interface SidebarCollapseProps\n  extends Pick<SidebarItemProps, \"active\" | \"as\" | \"href\" | \"icon\" | \"label\" | \"labelColor\">,\n    ComponentProps<\"button\">,\n    ThemingProps<SidebarCollapseTheme> {\n  onClick?: ComponentProps<\"button\">[\"onClick\"];\n  open?: boolean;\n  chevronIcon?: FC<ComponentProps<\"svg\">>;\n  renderChevronIcon?: (theme: SidebarCollapseTheme, open: boolean) => ReactElement;\n}\n\nexport const SidebarCollapse = forwardRef<HTMLLIElement, SidebarCollapseProps>((props, ref) => {\n  const { theme: rootTheme, clearTheme: rootClearTheme, applyTheme: rootApplyTheme, isCollapsed } = useSidebarContext();\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [sidebarTheme.collapse, provider.theme?.sidebar?.collapse, rootTheme?.collapse, props.theme],\n    [get(provider.clearTheme, \"sidebar.collapse\"), get(rootClearTheme, \"collapse\"), props.clearTheme],\n    [get(provider.applyTheme, \"sidebar.collapse\"), get(rootApplyTheme, \"collapse\"), props.applyTheme],\n  );\n\n  const {\n    children,\n    className,\n    icon: Icon,\n    label,\n    chevronIcon: ChevronIcon = ChevronDownIcon,\n    renderChevronIcon,\n    open = false,\n    ...restProps\n  } = resolveProps(props, provider.props?.sidebarCollapse);\n\n  const id = useId();\n  const [isOpen, setOpen] = useState(open);\n\n  useEffect(() => setOpen(open), [open]);\n\n  function Wrapper({ children }: PropsWithChildren) {\n    if (isCollapsed && !isOpen) {\n      return (\n        <Tooltip content={label} placement=\"right\">\n          {children}\n        </Tooltip>\n      );\n    }\n\n    return children;\n  }\n\n  return (\n    <li ref={ref}>\n      <Wrapper>\n        <button\n          id={`flowbite-sidebar-collapse-${id}`}\n          onClick={() => setOpen(!isOpen)}\n          title={label}\n          type=\"button\"\n          className={twMerge(theme.button, className)}\n          {...restProps}\n        >\n          {Icon && (\n            <Icon\n              aria-hidden\n              data-testid=\"flowbite-sidebar-collapse-icon\"\n              className={twMerge(theme.icon.base, theme.icon.open[isOpen ? \"on\" : \"off\"])}\n            />\n          )}\n          {isCollapsed ? (\n            <span className={theme.label.title}>{label}</span>\n          ) : (\n            <>\n              <span data-testid=\"flowbite-sidebar-collapse-label\" className={theme.label.base}>\n                {label}\n              </span>\n              {renderChevronIcon ? (\n                renderChevronIcon(theme, isOpen)\n              ) : (\n                <ChevronIcon\n                  aria-hidden\n                  className={twMerge(theme.label.icon.base, theme.label.icon.open[isOpen ? \"on\" : \"off\"])}\n                />\n              )}\n            </>\n          )}\n        </button>\n        <ul aria-labelledby={`flowbite-sidebar-collapse-${id}`} hidden={!isOpen} className={theme.list}>\n          <SidebarItemContext.Provider value={{ isInsideCollapse: true }}>{children}</SidebarItemContext.Provider>\n        </ul>\n      </Wrapper>\n    </li>\n  );\n});\n\nSidebarCollapse.displayName = \"SidebarCollapse\";\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAcY,MAAC,eAAe,GAAG,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AAC1D,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,WAAW,EAAE,GAAG,iBAAiB,EAAE;AACvH,EAAE,MAAM,QAAQ,GAAG,gBAAgB,EAAE;AACrC,EAAE,MAAM,KAAK,GAAG,eAAe;AAC/B,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,CAAC,KAAK,CAAC;AAChG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,kBAAkB,CAAC,EAAE,GAAG,CAAC,cAAc,EAAE,UAAU,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AACrG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,kBAAkB,CAAC,EAAE,GAAG,CAAC,cAAc,EAAE,UAAU,CAAC,EAAE,KAAK,CAAC,UAAU;AACpG,GAAG;AACH,EAAE,MAAM;AACR,IAAI,QAAQ;AACZ,IAAI,SAAS;AACb,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,KAAK;AACT,IAAI,WAAW,EAAE,WAAW,GAAG,eAAe;AAC9C,IAAI,iBAAiB;AACrB,IAAI,IAAI,GAAG,KAAK;AAChB,IAAI,GAAG;AACP,GAAG,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,eAAe,CAAC;AAC1D,EAAE,MAAM,EAAE,GAAG,KAAK,EAAE;AACpB,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC;AAC1C,EAAE,SAAS,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;AACxC,EAAE,SAAS,OAAO,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,EAAE;AAC5C,IAAI,IAAI,WAAW,IAAI,CAAC,MAAM,EAAE;AAChC,MAAM,uBAAuB,GAAG,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC;AACtG;AACA,IAAI,OAAO,SAAS;AACpB;AACA,EAAE,uBAAuB,GAAG,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,QAAQ,kBAAkB,IAAI,CAAC,OAAO,EAAE,EAAE,QAAQ,EAAE;AAC9F,oBAAoB,IAAI;AACxB,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,EAAE,EAAE,CAAC,0BAA0B,EAAE,EAAE,CAAC,CAAC;AAC7C,QAAQ,OAAO,EAAE,MAAM,OAAO,CAAC,CAAC,MAAM,CAAC;AACvC,QAAQ,KAAK,EAAE,KAAK;AACpB,QAAQ,IAAI,EAAE,QAAQ;AACtB,QAAQ,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,SAAS,CAAC;AACnD,QAAQ,GAAG,SAAS;AACpB,QAAQ,QAAQ,EAAE;AAClB,UAAU,IAAI,oBAAoB,GAAG;AACrC,YAAY,IAAI;AAChB,YAAY;AACZ,cAAc,aAAa,EAAE,IAAI;AACjC,cAAc,aAAa,EAAE,gCAAgC;AAC7D,cAAc,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,KAAK,CAAC;AACxF;AACA,WAAW;AACX,UAAU,WAAW,mBAAmB,GAAG,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,mBAAmB,IAAI,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE;AACpJ,4BAA4B,GAAG,CAAC,MAAM,EAAE,EAAE,aAAa,EAAE,iCAAiC,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;AAC3I,YAAY,iBAAiB,GAAG,iBAAiB,CAAC,KAAK,EAAE,MAAM,CAAC,mBAAmB,GAAG;AACtF,cAAc,WAAW;AACzB,cAAc;AACd,gBAAgB,aAAa,EAAE,IAAI;AACnC,gBAAgB,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,KAAK,CAAC;AACtG;AACA;AACA,WAAW,EAAE;AACb;AACA;AACA,KAAK;AACL,oBAAoB,GAAG,CAAC,IAAI,EAAE,EAAE,iBAAiB,EAAE,CAAC,0BAA0B,EAAE,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,CAAC,IAAI,EAAE,QAAQ,kBAAkB,GAAG,CAAC,kBAAkB,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,EAAE,gBAAgB,EAAE,IAAI,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE;AAC3O,GAAG,EAAE,CAAC,EAAE,CAAC;AACT,CAAC;AACD,eAAe,CAAC,WAAW,GAAG,iBAAiB;;;;"}