{"version": 3, "file": "SidebarItems.js", "sources": ["../../../src/components/Sidebar/SidebarItems.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentProps } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { ThemingProps } from \"../../types\";\nimport { useSidebarContext } from \"./SidebarContext\";\nimport { sidebarTheme } from \"./theme\";\n\nexport interface SidebarItemsTheme {\n  base: string;\n}\n\nexport interface SidebarItemsProps extends ComponentProps<\"div\">, ThemingProps<SidebarItemsTheme> {}\n\nexport const SidebarItems = forwardRef<HTMLDivElement, SidebarItemsProps>((props, ref) => {\n  const { theme: rootTheme, clearTheme: rootClearTheme, applyTheme: rootApplyTheme } = useSidebarContext();\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [sidebarTheme.items, provider.theme?.sidebar?.items, rootTheme?.items, props.theme],\n    [get(provider.clearTheme, \"sidebar.items\"), get(rootClearTheme, \"items\"), props.clearTheme],\n    [get(provider.applyTheme, \"sidebar.items\"), get(rootApplyTheme, \"items\"), props.applyTheme],\n  );\n\n  const { className, ...restProps } = resolveProps(props, provider.props?.sidebarItems);\n\n  return (\n    <div ref={ref} className={twMerge(theme.base, className)} data-testid=\"flowbite-sidebar-items\" {...restProps} />\n  );\n});\n\nSidebarItems.displayName = \"SidebarItems\";\n"], "names": [], "mappings": ";;;;;;;;;;AAWY,MAAC,YAAY,GAAG,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACvD,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,GAAG,iBAAiB,EAAE;AAC1G,EAAE,MAAM,QAAQ,GAAG,gBAAgB,EAAE;AACrC,EAAE,MAAM,KAAK,GAAG,eAAe;AAC/B,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC;AACvF,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,eAAe,CAAC,EAAE,GAAG,CAAC,cAAc,EAAE,OAAO,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC/F,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,eAAe,CAAC,EAAE,GAAG,CAAC,cAAc,EAAE,OAAO,CAAC,EAAE,KAAK,CAAC,UAAU;AAC9F,GAAG;AACH,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,SAAS,EAAE,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,YAAY,CAAC;AACvF,EAAE,uBAAuB,GAAG,CAAC,KAAK,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,aAAa,EAAE,wBAAwB,EAAE,GAAG,SAAS,EAAE,CAAC;AAC9I,CAAC;AACD,YAAY,CAAC,WAAW,GAAG,cAAc;;;;"}