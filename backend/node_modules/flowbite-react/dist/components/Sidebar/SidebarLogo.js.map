{"version": 3, "file": "SidebarLogo.js", "sources": ["../../../src/components/Sidebar/SidebarLogo.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps } from \"react\";\nimport { forwardRef, useId } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { FlowbiteBoolean, ThemingProps } from \"../../types\";\nimport { useSidebarContext } from \"./SidebarContext\";\nimport { sidebarTheme } from \"./theme\";\n\nexport interface SidebarLogoTheme {\n  base: string;\n  collapsed: FlowbiteBoolean;\n  img: string;\n}\n\nexport interface SidebarLogoProps extends ComponentProps<\"a\">, ThemingProps<SidebarLogoTheme> {\n  href: string;\n  img: string;\n  imgAlt?: string;\n}\n\nexport const SidebarLogo = forwardRef<HTMLAnchorElement, SidebarLogoProps>((props, ref) => {\n  const id = useId();\n  const { theme: rootTheme, clearTheme: rootClearTheme, applyTheme: rootApplyTheme, isCollapsed } = useSidebarContext();\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [sidebarTheme.logo, provider.theme?.sidebar?.logo, rootTheme?.logo, props.theme],\n    [get(provider.clearTheme, \"sidebar.logo\"), get(rootClearTheme, \"logo\"), props.clearTheme],\n    [get(provider.applyTheme, \"sidebar.logo\"), get(rootApplyTheme, \"logo\"), props.applyTheme],\n  );\n\n  const {\n    children,\n    className,\n    href,\n    img,\n    imgAlt = \"\",\n    ...restProps\n  } = resolveProps(props, provider.props?.sidebarLogo);\n\n  return (\n    <a\n      ref={ref}\n      aria-labelledby={`flowbite-sidebar-logo-${id}`}\n      href={href}\n      className={twMerge(theme.base, className)}\n      {...restProps}\n    >\n      <img alt={imgAlt} src={img} className={theme.img} />\n      <span className={theme.collapsed[isCollapsed ? \"on\" : \"off\"]} id={`flowbite-sidebar-logo-${id}`}>\n        {children}\n      </span>\n    </a>\n  );\n});\n\nSidebarLogo.displayName = \"SidebarLogo\";\n"], "names": [], "mappings": ";;;;;;;;;;AAWY,MAAC,WAAW,GAAG,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACtD,EAAE,MAAM,EAAE,GAAG,KAAK,EAAE;AACpB,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,WAAW,EAAE,GAAG,iBAAiB,EAAE;AACvH,EAAE,MAAM,QAAQ,GAAG,gBAAgB,EAAE;AACrC,EAAE,MAAM,KAAK,GAAG,eAAe;AAC/B,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC;AACpF,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,cAAc,CAAC,EAAE,GAAG,CAAC,cAAc,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC7F,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,cAAc,CAAC,EAAE,GAAG,CAAC,cAAc,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,UAAU;AAC5F,GAAG;AACH,EAAE,MAAM;AACR,IAAI,QAAQ;AACZ,IAAI,SAAS;AACb,IAAI,IAAI;AACR,IAAI,GAAG;AACP,IAAI,MAAM,GAAG,EAAE;AACf,IAAI,GAAG;AACP,GAAG,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,WAAW,CAAC;AACtD,EAAE,uBAAuB,IAAI;AAC7B,IAAI,GAAG;AACP,IAAI;AACJ,MAAM,GAAG;AACT,MAAM,iBAAiB,EAAE,CAAC,sBAAsB,EAAE,EAAE,CAAC,CAAC;AACtD,MAAM,IAAI;AACV,MAAM,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC;AAC/C,MAAM,GAAG,SAAS;AAClB,MAAM,QAAQ,EAAE;AAChB,wBAAwB,GAAG,CAAC,KAAK,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,SAAS,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC;AACnF,wBAAwB,GAAG,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,WAAW,GAAG,IAAI,GAAG,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,sBAAsB,EAAE,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE;AAC3I;AACA;AACA,GAAG;AACH,CAAC;AACD,WAAW,CAAC,WAAW,GAAG,aAAa;;;;"}