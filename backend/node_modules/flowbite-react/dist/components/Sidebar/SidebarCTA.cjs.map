{"version": 3, "file": "SidebarCTA.cjs", "sources": ["../../../src/components/Sidebar/SidebarCTA.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentProps } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { DynamicStringEnumKeysOf, FlowbiteColors, ThemingProps } from \"../../types\";\nimport { useSidebarContext } from \"./SidebarContext\";\nimport { sidebarTheme } from \"./theme\";\n\nexport interface SidebarCTATheme {\n  base: string;\n  color: SidebarCTAColors;\n}\n\nexport interface SidebarCTAProps extends Omit<ComponentProps<\"div\">, \"color\">, ThemingProps<SidebarCTATheme> {\n  color?: DynamicStringEnumKeysOf<SidebarCTAColors>;\n}\n\nexport interface SidebarCTAColors\n  extends Pick<\n    FlowbiteColors,\n    \"blue\" | \"dark\" | \"failure\" | \"gray\" | \"green\" | \"light\" | \"purple\" | \"red\" | \"success\" | \"warning\" | \"yellow\"\n  > {\n  [key: string]: string;\n}\n\nexport const SidebarCTA = forwardRef<HTMLDivElement, SidebarCTAProps>((props, ref) => {\n  const { theme: rootTheme, clearTheme: rootClearTheme, applyTheme: rootApplyTheme, isCollapsed } = useSidebarContext();\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [sidebarTheme.cta, provider.theme?.sidebar?.cta, rootTheme?.cta, props.theme],\n    [get(provider.clearTheme, \"sidebar.cta\"), get(rootClearTheme, \"cta\"), props.clearTheme],\n    [get(provider.applyTheme, \"sidebar.cta\"), get(rootApplyTheme, \"cta\"), props.applyTheme],\n  );\n\n  const { color = \"info\", className, ...restProps } = resolveProps(props, provider.props?.sidebarCTA);\n\n  return (\n    <div\n      ref={ref}\n      data-testid=\"sidebar-cta\"\n      hidden={isCollapsed}\n      className={twMerge(theme.base, theme.color[color], className)}\n      {...restProps}\n    />\n  );\n});\n\nSidebarCTA.displayName = \"SidebarCTA\";\n"], "names": ["forwardRef", "useSidebarContext", "provider", "useThemeProvider", "theme", "useResolveTheme", "sidebarTheme", "get", "resolveProps", "jsx", "twMerge"], "mappings": ";;;;;;;;;;;;AAWY,MAAC,UAAU,GAAGA,gBAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACrD,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,WAAW,EAAE,GAAGC,gCAAiB,EAAE;AACvH,EAAE,MAAMC,UAAQ,GAAGC,yBAAgB,EAAE;AACrC,EAAE,MAAMC,OAAK,GAAGC,4BAAe;AAC/B,IAAI,CAACC,kBAAY,CAAC,GAAG,EAAEJ,UAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,KAAK,CAAC,KAAK,CAAC;AACjF,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,aAAa,CAAC,EAAEK,OAAG,CAAC,cAAc,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC3F,IAAI,CAACA,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,aAAa,CAAC,EAAEK,OAAG,CAAC,cAAc,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,UAAU;AAC1F,GAAG;AACH,EAAE,MAAM,EAAE,KAAK,GAAG,MAAM,EAAE,SAAS,EAAE,GAAG,SAAS,EAAE,GAAGC,yBAAY,CAAC,KAAK,EAAEN,UAAQ,CAAC,KAAK,EAAE,UAAU,CAAC;AACrG,EAAE,uBAAuBO,cAAG;AAC5B,IAAI,KAAK;AACT,IAAI;AACJ,MAAM,GAAG;AACT,MAAM,aAAa,EAAE,aAAa;AAClC,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,SAAS,EAAEC,qBAAO,CAACN,OAAK,CAAC,IAAI,EAAEA,OAAK,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,SAAS,CAAC;AACnE,MAAM,GAAG;AACT;AACA,GAAG;AACH,CAAC;AACD,UAAU,CAAC,WAAW,GAAG,YAAY;;;;"}