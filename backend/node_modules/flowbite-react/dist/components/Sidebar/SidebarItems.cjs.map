{"version": 3, "file": "SidebarItems.cjs", "sources": ["../../../src/components/Sidebar/SidebarItems.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentProps } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { ThemingProps } from \"../../types\";\nimport { useSidebarContext } from \"./SidebarContext\";\nimport { sidebarTheme } from \"./theme\";\n\nexport interface SidebarItemsTheme {\n  base: string;\n}\n\nexport interface SidebarItemsProps extends ComponentProps<\"div\">, ThemingProps<SidebarItemsTheme> {}\n\nexport const SidebarItems = forwardRef<HTMLDivElement, SidebarItemsProps>((props, ref) => {\n  const { theme: rootTheme, clearTheme: rootClearTheme, applyTheme: rootApplyTheme } = useSidebarContext();\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [sidebarTheme.items, provider.theme?.sidebar?.items, rootTheme?.items, props.theme],\n    [get(provider.clearTheme, \"sidebar.items\"), get(rootClearTheme, \"items\"), props.clearTheme],\n    [get(provider.applyTheme, \"sidebar.items\"), get(rootApplyTheme, \"items\"), props.applyTheme],\n  );\n\n  const { className, ...restProps } = resolveProps(props, provider.props?.sidebarItems);\n\n  return (\n    <div ref={ref} className={twMerge(theme.base, className)} data-testid=\"flowbite-sidebar-items\" {...restProps} />\n  );\n});\n\nSidebarItems.displayName = \"SidebarItems\";\n"], "names": ["forwardRef", "useSidebarContext", "provider", "useThemeProvider", "theme", "useResolveTheme", "sidebarTheme", "get", "resolveProps", "jsx", "twMerge"], "mappings": ";;;;;;;;;;;;AAWY,MAAC,YAAY,GAAGA,gBAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACvD,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,GAAGC,gCAAiB,EAAE;AAC1G,EAAE,MAAMC,UAAQ,GAAGC,yBAAgB,EAAE;AACrC,EAAE,MAAMC,OAAK,GAAGC,4BAAe;AAC/B,IAAI,CAACC,kBAAY,CAAC,KAAK,EAAEJ,UAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC;AACvF,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,eAAe,CAAC,EAAEK,OAAG,CAAC,cAAc,EAAE,OAAO,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC/F,IAAI,CAACA,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,eAAe,CAAC,EAAEK,OAAG,CAAC,cAAc,EAAE,OAAO,CAAC,EAAE,KAAK,CAAC,UAAU;AAC9F,GAAG;AACH,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,SAAS,EAAE,GAAGC,yBAAY,CAAC,KAAK,EAAEN,UAAQ,CAAC,KAAK,EAAE,YAAY,CAAC;AACvF,EAAE,uBAAuBO,cAAG,CAAC,KAAK,EAAE,EAAE,GAAG,EAAE,SAAS,EAAEC,qBAAO,CAACN,OAAK,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,aAAa,EAAE,wBAAwB,EAAE,GAAG,SAAS,EAAE,CAAC;AAC9I,CAAC;AACD,YAAY,CAAC,WAAW,GAAG,cAAc;;;;"}