{"version": 3, "file": "Sidebar.cjs", "sources": ["../../../src/components/Sidebar/Sidebar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentProps, type ElementType } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { FlowbiteBoolean, ThemingProps } from \"../../types\";\nimport type { SidebarCollapseTheme } from \"./SidebarCollapse\";\nimport { SidebarContext } from \"./SidebarContext\";\nimport type { SidebarCTATheme } from \"./SidebarCTA\";\nimport type { SidebarItemTheme } from \"./SidebarItem\";\nimport type { SidebarItemGroupTheme } from \"./SidebarItemGroup\";\nimport type { SidebarItemsTheme } from \"./SidebarItems\";\nimport type { SidebarLogoTheme } from \"./SidebarLogo\";\nimport { sidebarTheme } from \"./theme\";\n\nexport interface SidebarTheme {\n  root: {\n    base: string;\n    collapsed: FlowbiteBoolean;\n    inner: string;\n  };\n  collapse: SidebarCollapseTheme;\n  cta: SidebarCTATheme;\n  item: SidebarItemTheme;\n  items: SidebarItemsTheme;\n  itemGroup: SidebarItemGroupTheme;\n  logo: SidebarLogoTheme;\n}\n\nexport interface SidebarProps extends ComponentProps<\"div\">, ThemingProps<SidebarTheme> {\n  as?: ElementType;\n  collapseBehavior?: \"collapse\" | \"hide\";\n  collapsed?: boolean;\n}\n\nexport const Sidebar = forwardRef<HTMLElement, SidebarProps>((props, ref) => {\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [sidebarTheme, provider.theme?.sidebar, props.theme],\n    [get(provider.clearTheme, \"sidebar\"), props.clearTheme],\n    [get(provider.applyTheme, \"sidebar\"), props.applyTheme],\n  );\n\n  const {\n    as: Component = \"nav\",\n    children,\n    className,\n    collapseBehavior = \"collapse\",\n    collapsed: isCollapsed = false,\n    ...restProps\n  } = resolveProps(props, provider.props?.sidebar);\n\n  return (\n    <SidebarContext.Provider\n      value={{ theme: props.theme, clearTheme: props.clearTheme, applyTheme: props.applyTheme, isCollapsed }}\n    >\n      <Component\n        ref={ref}\n        aria-label=\"Sidebar\"\n        hidden={isCollapsed && collapseBehavior === \"hide\"}\n        className={twMerge(theme.root.base, theme.root.collapsed[isCollapsed ? \"on\" : \"off\"], className)}\n        {...restProps}\n      >\n        <div className={theme.root.inner}>{children}</div>\n      </Component>\n    </SidebarContext.Provider>\n  );\n});\n\nSidebar.displayName = \"Sidebar\";\n"], "names": ["forwardRef", "provider", "useThemeProvider", "theme", "useResolveTheme", "sidebarTheme", "get", "resolveProps", "jsx", "SidebarContext", "twMerge"], "mappings": ";;;;;;;;;;;;AAWY,MAAC,OAAO,GAAGA,gBAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AAClD,EAAE,MAAMC,UAAQ,GAAGC,yBAAgB,EAAE;AACrC,EAAE,MAAMC,OAAK,GAAGC,4BAAe;AAC/B,IAAI,CAACC,kBAAY,EAAEJ,UAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC;AACxD,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,SAAS,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC3D,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,SAAS,CAAC,EAAE,KAAK,CAAC,UAAU;AAC1D,GAAG;AACH,EAAE,MAAM;AACR,IAAI,EAAE,EAAE,SAAS,GAAG,KAAK;AACzB,IAAI,QAAQ;AACZ,IAAI,SAAS;AACb,IAAI,gBAAgB,GAAG,UAAU;AACjC,IAAI,SAAS,EAAE,WAAW,GAAG,KAAK;AAClC,IAAI,GAAG;AACP,GAAG,GAAGM,yBAAY,CAAC,KAAK,EAAEN,UAAQ,CAAC,KAAK,EAAE,OAAO,CAAC;AAClD,EAAE,uBAAuBO,cAAG;AAC5B,IAAIC,6BAAc,CAAC,QAAQ;AAC3B,IAAI;AACJ,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE,WAAW,EAAE;AAC5G,MAAM,QAAQ,kBAAkBD,cAAG;AACnC,QAAQ,SAAS;AACjB,QAAQ;AACR,UAAU,GAAG;AACb,UAAU,YAAY,EAAE,SAAS;AACjC,UAAU,MAAM,EAAE,WAAW,IAAI,gBAAgB,KAAK,MAAM;AAC5D,UAAU,SAAS,EAAEE,qBAAO,CAACP,OAAK,CAAC,IAAI,CAAC,IAAI,EAAEA,OAAK,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,IAAI,GAAG,KAAK,CAAC,EAAE,SAAS,CAAC;AAC1G,UAAU,GAAG,SAAS;AACtB,UAAU,QAAQ,kBAAkBK,cAAG,CAAC,KAAK,EAAE,EAAE,SAAS,EAAEL,OAAK,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,EAAE;AACxF;AACA;AACA;AACA,GAAG;AACH,CAAC;AACD,OAAO,CAAC,WAAW,GAAG,SAAS;;;;"}