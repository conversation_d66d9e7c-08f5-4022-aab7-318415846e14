{"version": 3, "file": "SidebarItemGroup.js", "sources": ["../../../src/components/Sidebar/SidebarItemGroup.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentProps } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { ThemingProps } from \"../../types\";\nimport { useSidebarContext } from \"./SidebarContext\";\nimport { SidebarItemContext } from \"./SidebarItemContext\";\nimport { sidebarTheme } from \"./theme\";\n\nexport interface SidebarItemGroupTheme {\n  base: string;\n}\n\nexport interface SidebarItemGroupProps extends ComponentProps<\"ul\">, ThemingProps<SidebarItemGroupTheme> {}\n\nexport const SidebarItemGroup = forwardRef<HTMLUListElement, SidebarItemGroupProps>((props, ref) => {\n  const { theme: rootTheme, clearTheme: rootClearTheme, applyTheme: rootApplyTheme } = useSidebarContext();\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [sidebarTheme.itemGroup, provider.theme?.sidebar?.itemGroup, rootTheme?.itemGroup, props.theme],\n    [get(provider.clearTheme, \"sidebar.itemGroup\"), get(rootClearTheme, \"itemGroup\"), props.clearTheme],\n    [get(provider.applyTheme, \"sidebar.itemGroup\"), get(rootApplyTheme, \"itemGroup\"), props.applyTheme],\n  );\n\n  const { className, ...restProps } = resolveProps(props, provider.props?.sidebarItemGroup);\n\n  return (\n    <SidebarItemContext.Provider value={{ isInsideCollapse: false }}>\n      <ul\n        ref={ref}\n        data-testid=\"flowbite-sidebar-item-group\"\n        className={twMerge(theme.base, className)}\n        {...restProps}\n      />\n    </SidebarItemContext.Provider>\n  );\n});\n\nSidebarItemGroup.displayName = \"SidebarItemGroup\";\n"], "names": [], "mappings": ";;;;;;;;;;;AAYY,MAAC,gBAAgB,GAAG,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AAC3D,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,GAAG,iBAAiB,EAAE;AAC1G,EAAE,MAAM,QAAQ,GAAG,gBAAgB,EAAE;AACrC,EAAE,MAAM,KAAK,GAAG,eAAe;AAC/B,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC;AACnG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,mBAAmB,CAAC,EAAE,GAAG,CAAC,cAAc,EAAE,WAAW,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AACvG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,mBAAmB,CAAC,EAAE,GAAG,CAAC,cAAc,EAAE,WAAW,CAAC,EAAE,KAAK,CAAC,UAAU;AACtG,GAAG;AACH,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,SAAS,EAAE,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,gBAAgB,CAAC;AAC3F,EAAE,uBAAuB,GAAG,CAAC,kBAAkB,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,EAAE,gBAAgB,EAAE,KAAK,EAAE,EAAE,QAAQ,kBAAkB,GAAG;AAC7H,IAAI,IAAI;AACR,IAAI;AACJ,MAAM,GAAG;AACT,MAAM,aAAa,EAAE,6BAA6B;AAClD,MAAM,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC;AAC/C,MAAM,GAAG;AACT;AACA,GAAG,EAAE,CAAC;AACN,CAAC;AACD,gBAAgB,CAAC,WAAW,GAAG,kBAAkB;;;;"}