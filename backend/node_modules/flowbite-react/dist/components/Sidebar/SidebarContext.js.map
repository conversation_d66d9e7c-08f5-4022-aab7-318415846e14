{"version": 3, "file": "SidebarContext.js", "sources": ["../../../src/components/Sidebar/SidebarContext.tsx"], "sourcesContent": ["\"use client\";\n\nimport { createContext, useContext } from \"react\";\nimport type { ThemingProps } from \"../../types\";\nimport type { SidebarTheme } from \"./Sidebar\";\n\nexport interface SidebarContextValue extends ThemingProps<SidebarTheme> {\n  isCollapsed: boolean;\n}\n\nexport const SidebarContext = createContext<SidebarContextValue | undefined>(undefined);\n\nexport function useSidebarContext(): SidebarContextValue {\n  const context = useContext(SidebarContext);\n\n  if (!context) {\n    throw new Error(\"useSidebarContext should be used within the SidebarContext provider!\");\n  }\n\n  return context;\n}\n"], "names": [], "mappings": ";;AAGY,MAAC,cAAc,GAAG,aAAa,CAAC,MAAM;AAC3C,SAAS,iBAAiB,GAAG;AACpC,EAAE,MAAM,OAAO,GAAG,UAAU,CAAC,cAAc,CAAC;AAC5C,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,MAAM,IAAI,KAAK,CAAC,sEAAsE,CAAC;AAC3F;AACA,EAAE,OAAO,OAAO;AAChB;;;;"}