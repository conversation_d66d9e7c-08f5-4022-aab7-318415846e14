{"version": 3, "file": "SidebarItemGroup.cjs", "sources": ["../../../src/components/Sidebar/SidebarItemGroup.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentProps } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { ThemingProps } from \"../../types\";\nimport { useSidebarContext } from \"./SidebarContext\";\nimport { SidebarItemContext } from \"./SidebarItemContext\";\nimport { sidebarTheme } from \"./theme\";\n\nexport interface SidebarItemGroupTheme {\n  base: string;\n}\n\nexport interface SidebarItemGroupProps extends ComponentProps<\"ul\">, ThemingProps<SidebarItemGroupTheme> {}\n\nexport const SidebarItemGroup = forwardRef<HTMLUListElement, SidebarItemGroupProps>((props, ref) => {\n  const { theme: rootTheme, clearTheme: rootClearTheme, applyTheme: rootApplyTheme } = useSidebarContext();\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [sidebarTheme.itemGroup, provider.theme?.sidebar?.itemGroup, rootTheme?.itemGroup, props.theme],\n    [get(provider.clearTheme, \"sidebar.itemGroup\"), get(rootClearTheme, \"itemGroup\"), props.clearTheme],\n    [get(provider.applyTheme, \"sidebar.itemGroup\"), get(rootApplyTheme, \"itemGroup\"), props.applyTheme],\n  );\n\n  const { className, ...restProps } = resolveProps(props, provider.props?.sidebarItemGroup);\n\n  return (\n    <SidebarItemContext.Provider value={{ isInsideCollapse: false }}>\n      <ul\n        ref={ref}\n        data-testid=\"flowbite-sidebar-item-group\"\n        className={twMerge(theme.base, className)}\n        {...restProps}\n      />\n    </SidebarItemContext.Provider>\n  );\n});\n\nSidebarItemGroup.displayName = \"SidebarItemGroup\";\n"], "names": ["forwardRef", "useSidebarContext", "provider", "useThemeProvider", "theme", "useResolveTheme", "sidebarTheme", "get", "resolveProps", "jsx", "SidebarItemContext", "twMerge"], "mappings": ";;;;;;;;;;;;;AAYY,MAAC,gBAAgB,GAAGA,gBAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AAC3D,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,GAAGC,gCAAiB,EAAE;AAC1G,EAAE,MAAMC,UAAQ,GAAGC,yBAAgB,EAAE;AACrC,EAAE,MAAMC,OAAK,GAAGC,4BAAe;AAC/B,IAAI,CAACC,kBAAY,CAAC,SAAS,EAAEJ,UAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC;AACnG,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,mBAAmB,CAAC,EAAEK,OAAG,CAAC,cAAc,EAAE,WAAW,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AACvG,IAAI,CAACA,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,mBAAmB,CAAC,EAAEK,OAAG,CAAC,cAAc,EAAE,WAAW,CAAC,EAAE,KAAK,CAAC,UAAU;AACtG,GAAG;AACH,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,SAAS,EAAE,GAAGC,yBAAY,CAAC,KAAK,EAAEN,UAAQ,CAAC,KAAK,EAAE,gBAAgB,CAAC;AAC3F,EAAE,uBAAuBO,cAAG,CAACC,qCAAkB,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,EAAE,gBAAgB,EAAE,KAAK,EAAE,EAAE,QAAQ,kBAAkBD,cAAG;AAC7H,IAAI,IAAI;AACR,IAAI;AACJ,MAAM,GAAG;AACT,MAAM,aAAa,EAAE,6BAA6B;AAClD,MAAM,SAAS,EAAEE,qBAAO,CAACP,OAAK,CAAC,IAAI,EAAE,SAAS,CAAC;AAC/C,MAAM,GAAG;AACT;AACA,GAAG,EAAE,CAAC;AACN,CAAC;AACD,gBAAgB,CAAC,WAAW,GAAG,kBAAkB;;;;"}