{"version": 3, "file": "TableCell.js", "sources": ["../../../src/components/Table/TableCell.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentPropsWithRef } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { ThemingProps } from \"../../types\";\nimport { useTableBodyContext } from \"./TableBodyContext\";\nimport { useTableContext } from \"./TableContext\";\nimport { tableTheme } from \"./theme\";\n\nexport interface TableCellTheme {\n  base: string;\n}\n\nexport interface TableCellProps extends ComponentPropsWithRef<\"td\">, ThemingProps<TableCellTheme> {}\n\nexport const TableCell = forwardRef<HTMLTableCellElement, TableCellProps>((props, ref) => {\n  const { theme: rootTheme, clearTheme: rootClearTheme, applyTheme: rootApplyTheme } = useTableContext();\n  const { theme: bodyTheme, clearTheme: bodyClearTheme, applyTheme: bodyApplyTheme } = useTableBodyContext();\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [tableTheme.body.cell, provider.theme?.table?.body?.cell, rootTheme?.body?.cell, bodyTheme?.cell, props.theme],\n    [\n      get(provider.clearTheme, \"table.body.cell\"),\n      get(rootClearTheme, \"body.cell\"),\n      get(bodyClearTheme, \"cell\"),\n      props.clearTheme,\n    ],\n    [\n      get(provider.applyTheme, \"table.body.cell\"),\n      get(rootApplyTheme, \"body.cell\"),\n      get(bodyApplyTheme, \"cell\"),\n      props.applyTheme,\n    ],\n  );\n\n  const { className, ...restProps } = resolveProps(props, provider.props?.tableCell);\n\n  return <td ref={ref} className={twMerge(theme.base, className)} {...restProps} />;\n});\n\nTableCell.displayName = \"TableCell\";\n"], "names": [], "mappings": ";;;;;;;;;;;AAYY,MAAC,SAAS,GAAG,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACpD,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,GAAG,eAAe,EAAE;AACxG,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,GAAG,mBAAmB,EAAE;AAC5G,EAAE,MAAM,QAAQ,GAAG,gBAAgB,EAAE;AACrC,EAAE,MAAM,KAAK,GAAG,eAAe;AAC/B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC;AAClH,IAAI;AACJ,MAAM,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,iBAAiB,CAAC;AACjD,MAAM,GAAG,CAAC,cAAc,EAAE,WAAW,CAAC;AACtC,MAAM,GAAG,CAAC,cAAc,EAAE,MAAM,CAAC;AACjC,MAAM,KAAK,CAAC;AACZ,KAAK;AACL,IAAI;AACJ,MAAM,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,iBAAiB,CAAC;AACjD,MAAM,GAAG,CAAC,cAAc,EAAE,WAAW,CAAC;AACtC,MAAM,GAAG,CAAC,cAAc,EAAE,MAAM,CAAC;AACjC,MAAM,KAAK,CAAC;AACZ;AACA,GAAG;AACH,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,SAAS,EAAE,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,SAAS,CAAC;AACpF,EAAE,uBAAuB,GAAG,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,GAAG,SAAS,EAAE,CAAC;AACpG,CAAC;AACD,SAAS,CAAC,WAAW,GAAG,WAAW;;;;"}