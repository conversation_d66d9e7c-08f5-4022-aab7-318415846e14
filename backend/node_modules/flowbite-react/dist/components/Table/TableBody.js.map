{"version": 3, "file": "TableBody.js", "sources": ["../../../src/components/Table/TableBody.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentPropsWithRef } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { ThemingProps } from \"../../types\";\nimport { TableBodyContext } from \"./TableBodyContext\";\nimport type { TableCellTheme } from \"./TableCell\";\nimport { useTableContext } from \"./TableContext\";\nimport { tableTheme } from \"./theme\";\n\nexport interface TableBodyTheme {\n  base: string;\n  cell: TableCellTheme;\n}\n\nexport interface TableBodyProps extends ComponentPropsWithRef<\"tbody\">, ThemingProps<TableBodyTheme> {}\n\nexport const TableBody = forwardRef<HTMLTableSectionElement, TableBodyProps>((props, ref) => {\n  const { theme: rootTheme, clearTheme: rootClearTheme, applyTheme: rootApplyTheme } = useTableContext();\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [tableTheme.body, provider.theme?.table?.body, rootTheme?.body, props.theme],\n    [get(provider.clearTheme, \"table.body\"), get(rootClearTheme, \"body\"), props.clearTheme],\n    [get(provider.applyTheme, \"table.body\"), get(rootApplyTheme, \"body\"), props.applyTheme],\n  );\n\n  const { className, ...restProps } = resolveProps(props, provider.props?.tableBody);\n\n  return (\n    <TableBodyContext.Provider\n      value={{ theme: props.theme, clearTheme: props.clearTheme, applyTheme: props.applyTheme }}\n    >\n      <tbody ref={ref} className={twMerge(theme.base, className)} {...restProps} />\n    </TableBodyContext.Provider>\n  );\n});\n\nTableBody.displayName = \"TableBody\";\n"], "names": [], "mappings": ";;;;;;;;;;;AAYY,MAAC,SAAS,GAAG,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACpD,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,GAAG,eAAe,EAAE;AACxG,EAAE,MAAM,QAAQ,GAAG,gBAAgB,EAAE;AACrC,EAAE,MAAM,KAAK,GAAG,eAAe;AAC/B,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC;AAChF,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,YAAY,CAAC,EAAE,GAAG,CAAC,cAAc,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC3F,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,YAAY,CAAC,EAAE,GAAG,CAAC,cAAc,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,UAAU;AAC1F,GAAG;AACH,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,SAAS,EAAE,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,SAAS,CAAC;AACpF,EAAE,uBAAuB,GAAG;AAC5B,IAAI,gBAAgB,CAAC,QAAQ;AAC7B,IAAI;AACJ,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE;AAC/F,MAAM,QAAQ,kBAAkB,GAAG,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,GAAG,SAAS,EAAE;AAC7G;AACA,GAAG;AACH,CAAC;AACD,SAAS,CAAC,WAAW,GAAG,WAAW;;;;"}