{"version": 3, "file": "TableHeadContext.js", "sources": ["../../../src/components/Table/TableHeadContext.tsx"], "sourcesContent": ["\"use client\";\n\nimport { createContext, useContext } from \"react\";\nimport type { ThemingProps } from \"../../types\";\nimport type { TableHeadTheme } from \"./TableHead\";\n\nexport type TableHeadContextValue = ThemingProps<TableHeadTheme>;\n\nexport const TableHeadContext = createContext<TableHeadContextValue | undefined>(undefined);\n\nexport function useTableHeadContext(): TableHeadContextValue {\n  const context = useContext(TableHeadContext);\n\n  if (!context) {\n    throw new Error(\"useTableHeadContext should be used within the TableHeadContext provider!\");\n  }\n\n  return context;\n}\n"], "names": [], "mappings": ";;AAGY,MAAC,gBAAgB,GAAG,aAAa,CAAC,MAAM;AAC7C,SAAS,mBAAmB,GAAG;AACtC,EAAE,MAAM,OAAO,GAAG,UAAU,CAAC,gBAAgB,CAAC;AAC9C,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,MAAM,IAAI,KAAK,CAAC,0EAA0E,CAAC;AAC/F;AACA,EAAE,OAAO,OAAO;AAChB;;;;"}