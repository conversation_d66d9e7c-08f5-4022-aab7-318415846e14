{"version": 3, "file": "TableHead.js", "sources": ["../../../src/components/Table/TableHead.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentPropsWithRef } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { ThemingProps } from \"../../types\";\nimport { useTableContext } from \"./TableContext\";\nimport type { TableHeadCellTheme } from \"./TableHeadCell\";\nimport { TableHeadContext } from \"./TableHeadContext\";\nimport { tableTheme } from \"./theme\";\n\nexport interface TableHeadTheme {\n  base: string;\n  cell: TableHeadCellTheme;\n}\n\nexport interface TableHeadProps extends ComponentPropsWithRef<\"thead\">, ThemingProps<TableHeadTheme> {}\n\nexport const TableHead = forwardRef<HTMLTableSectionElement, TableHeadProps>((props, ref) => {\n  const { theme: rootTheme, clearTheme: rootClearTheme, applyTheme: rootApplyTheme } = useTableContext();\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [tableTheme.head, provider.theme?.table?.head, rootTheme?.head, props.theme],\n    [get(provider.clearTheme, \"table.head\"), get(rootClearTheme, \"head\"), props.clearTheme],\n    [get(provider.applyTheme, \"table.head\"), get(rootApplyTheme, \"head\"), props.applyTheme],\n  );\n\n  const { className, ...restProps } = resolveProps(props, provider.props?.tableHead);\n\n  return (\n    <TableHeadContext.Provider\n      value={{ theme: props.theme, clearTheme: props.clearTheme, applyTheme: props.applyTheme }}\n    >\n      <thead ref={ref} className={twMerge(theme.base, className)} {...restProps} />\n    </TableHeadContext.Provider>\n  );\n});\n\nTableHead.displayName = \"TableHead\";\n"], "names": [], "mappings": ";;;;;;;;;;;AAYY,MAAC,SAAS,GAAG,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACpD,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,GAAG,eAAe,EAAE;AACxG,EAAE,MAAM,QAAQ,GAAG,gBAAgB,EAAE;AACrC,EAAE,MAAM,KAAK,GAAG,eAAe;AAC/B,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC;AAChF,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,YAAY,CAAC,EAAE,GAAG,CAAC,cAAc,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC3F,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,YAAY,CAAC,EAAE,GAAG,CAAC,cAAc,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,UAAU;AAC1F,GAAG;AACH,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,SAAS,EAAE,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,SAAS,CAAC;AACpF,EAAE,uBAAuB,GAAG;AAC5B,IAAI,gBAAgB,CAAC,QAAQ;AAC7B,IAAI;AACJ,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE;AAC/F,MAAM,QAAQ,kBAAkB,GAAG,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,GAAG,SAAS,EAAE;AAC7G;AACA,GAAG;AACH,CAAC;AACD,SAAS,CAAC,WAAW,GAAG,WAAW;;;;"}