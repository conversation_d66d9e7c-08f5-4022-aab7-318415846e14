{"version": 3, "file": "TableHeadCell.js", "sources": ["../../../src/components/Table/TableHeadCell.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentPropsWithRef } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { ThemingProps } from \"../../types\";\nimport { useTableContext } from \"./TableContext\";\nimport { useTableHeadContext } from \"./TableHeadContext\";\nimport { tableTheme } from \"./theme\";\n\nexport interface TableHeadCellTheme {\n  base: string;\n}\n\nexport interface TableHeadCellProps extends ComponentPropsWithRef<\"th\">, ThemingProps<TableHeadCellTheme> {}\n\nexport const TableHeadCell = forwardRef<HTMLTableCellElement, TableHeadCellProps>((props, ref) => {\n  const { theme: rootTheme, clearTheme: rootClearTheme, applyTheme: rootApplyTheme } = useTableContext();\n  const { theme: headTheme, clearTheme: headClearTheme, applyTheme: headApplyTheme } = useTableHeadContext();\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [tableTheme.head.cell, provider.theme?.table?.head?.cell, rootTheme?.head?.cell, headTheme?.cell, props.theme],\n    [\n      get(provider.clearTheme, \"table.head.cell\"),\n      get(rootClearTheme, \"head.cell\"),\n      get(headClearTheme, \"cell\"),\n      props.clearTheme,\n    ],\n    [\n      get(provider.applyTheme, \"table.head.cell\"),\n      get(rootApplyTheme, \"head.cell\"),\n      get(headApplyTheme, \"cell\"),\n      props.applyTheme,\n    ],\n  );\n\n  const { className, ...restProps } = resolveProps(props, provider.props?.tableHeadCell);\n\n  return <th ref={ref} className={twMerge(theme.base, className)} {...restProps} />;\n});\n\nTableHeadCell.displayName = \"TableHeadCell\";\n"], "names": [], "mappings": ";;;;;;;;;;;AAYY,MAAC,aAAa,GAAG,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACxD,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,GAAG,eAAe,EAAE;AACxG,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,GAAG,mBAAmB,EAAE;AAC5G,EAAE,MAAM,QAAQ,GAAG,gBAAgB,EAAE;AACrC,EAAE,MAAM,KAAK,GAAG,eAAe;AAC/B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC;AAClH,IAAI;AACJ,MAAM,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,iBAAiB,CAAC;AACjD,MAAM,GAAG,CAAC,cAAc,EAAE,WAAW,CAAC;AACtC,MAAM,GAAG,CAAC,cAAc,EAAE,MAAM,CAAC;AACjC,MAAM,KAAK,CAAC;AACZ,KAAK;AACL,IAAI;AACJ,MAAM,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,iBAAiB,CAAC;AACjD,MAAM,GAAG,CAAC,cAAc,EAAE,WAAW,CAAC;AACtC,MAAM,GAAG,CAAC,cAAc,EAAE,MAAM,CAAC;AACjC,MAAM,KAAK,CAAC;AACZ;AACA,GAAG;AACH,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,SAAS,EAAE,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,aAAa,CAAC;AACxF,EAAE,uBAAuB,GAAG,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,GAAG,SAAS,EAAE,CAAC;AACpG,CAAC;AACD,aAAa,CAAC,WAAW,GAAG,eAAe;;;;"}