{"version": 3, "file": "theme.js", "sources": ["../../../src/components/RangeSlider/theme.ts"], "sourcesContent": ["import { createTheme } from \"../../helpers/create-theme\";\nimport type { RangeSliderTheme } from \"./RangeSlider\";\n\nexport const rangeSliderTheme = createTheme<RangeSliderTheme>({\n  root: {\n    base: \"flex\",\n  },\n  field: {\n    base: \"relative w-full\",\n    input: {\n      base: \"w-full cursor-pointer appearance-none rounded-lg bg-gray-200 dark:bg-gray-700\",\n      sizes: {\n        sm: \"h-1\",\n        md: \"h-2\",\n        lg: \"h-3\",\n      },\n    },\n  },\n});\n"], "names": [], "mappings": ";;AAEY,MAAC,gBAAgB,GAAG,WAAW,CAAC;AAC5C,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE;AACV,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,iBAAiB;AAC3B,IAAI,KAAK,EAAE;AACX,MAAM,IAAI,EAAE,+EAA+E;AAC3F,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE,KAAK;AACjB,QAAQ,EAAE,EAAE,KAAK;AACjB,QAAQ,EAAE,EAAE;AACZ;AACA;AACA;AACA,CAAC;;;;"}