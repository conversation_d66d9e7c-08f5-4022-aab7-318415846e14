{"version": 3, "file": "RangeSlider.js", "sources": ["../../../src/components/RangeSlider/RangeSlider.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps } from \"react\";\nimport { forwardRef } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { DynamicStringEnumKeysOf, ThemingProps } from \"../../types\";\nimport type { TextInputSizes } from \"../TextInput\";\nimport { rangeSliderTheme } from \"./theme\";\n\nexport interface RangeSliderTheme {\n  root: RangeSliderRootTheme;\n  field: RangeSliderFieldTheme;\n}\n\nexport interface RangeSliderRootTheme {\n  base: string;\n}\n\nexport interface RangeSliderFieldTheme {\n  base: string;\n  input: {\n    base: string;\n    sizes: TextInputSizes;\n  };\n}\n\nexport interface RangeSliderProps\n  extends Omit<ComponentProps<\"input\">, \"ref\" | \"type\">,\n    ThemingProps<RangeSliderTheme> {\n  sizing?: DynamicStringEnumKeysOf<TextInputSizes>;\n}\n\nexport const RangeSlider = forwardRef<HTMLInputElement, RangeSliderProps>((props, ref) => {\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [rangeSliderTheme, provider.theme?.rangeSlider, props.theme],\n    [get(provider.clearTheme, \"rangeSlider\"), props.clearTheme],\n    [get(provider.applyTheme, \"rangeSlider\"), props.applyTheme],\n  );\n\n  const { className, sizing = \"md\", ...restProps } = resolveProps(props, provider.props?.rangeSlider);\n\n  return (\n    <div data-testid=\"flowbite-range-slider\" className={twMerge(theme.root.base, className)}>\n      <div className={theme.field.base}>\n        <input\n          ref={ref}\n          type=\"range\"\n          className={twMerge(theme.field.input.base, theme.field.input.sizes[sizing])}\n          {...restProps}\n        />\n      </div>\n    </div>\n  );\n});\n\nRangeSlider.displayName = \"RangeSlider\";\n"], "names": [], "mappings": ";;;;;;;;;AAUY,MAAC,WAAW,GAAG,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACtD,EAAE,MAAM,QAAQ,GAAG,gBAAgB,EAAE;AACrC,EAAE,MAAM,KAAK,GAAG,eAAe;AAC/B,IAAI,CAAC,gBAAgB,EAAE,QAAQ,CAAC,KAAK,EAAE,WAAW,EAAE,KAAK,CAAC,KAAK,CAAC;AAChE,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,aAAa,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC/D,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,aAAa,CAAC,EAAE,KAAK,CAAC,UAAU;AAC9D,GAAG;AACH,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,GAAG,IAAI,EAAE,GAAG,SAAS,EAAE,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,WAAW,CAAC;AACrG,EAAE,uBAAuB,GAAG,CAAC,KAAK,EAAE,EAAE,aAAa,EAAE,uBAAuB,EAAE,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,QAAQ,kBAAkB,GAAG,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,kBAAkB,GAAG;AAC/N,IAAI,OAAO;AACX,IAAI;AACJ,MAAM,GAAG;AACT,MAAM,IAAI,EAAE,OAAO;AACnB,MAAM,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AACjF,MAAM,GAAG;AACT;AACA,GAAG,EAAE,CAAC,EAAE,CAAC;AACT,CAAC;AACD,WAAW,CAAC,WAAW,GAAG,aAAa;;;;"}