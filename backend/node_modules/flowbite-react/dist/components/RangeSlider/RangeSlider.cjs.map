{"version": 3, "file": "RangeSlider.cjs", "sources": ["../../../src/components/RangeSlider/RangeSlider.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps } from \"react\";\nimport { forwardRef } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { DynamicStringEnumKeysOf, ThemingProps } from \"../../types\";\nimport type { TextInputSizes } from \"../TextInput\";\nimport { rangeSliderTheme } from \"./theme\";\n\nexport interface RangeSliderTheme {\n  root: RangeSliderRootTheme;\n  field: RangeSliderFieldTheme;\n}\n\nexport interface RangeSliderRootTheme {\n  base: string;\n}\n\nexport interface RangeSliderFieldTheme {\n  base: string;\n  input: {\n    base: string;\n    sizes: TextInputSizes;\n  };\n}\n\nexport interface RangeSliderProps\n  extends Omit<ComponentProps<\"input\">, \"ref\" | \"type\">,\n    ThemingProps<RangeSliderTheme> {\n  sizing?: DynamicStringEnumKeysOf<TextInputSizes>;\n}\n\nexport const RangeSlider = forwardRef<HTMLInputElement, RangeSliderProps>((props, ref) => {\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [rangeSliderTheme, provider.theme?.rangeSlider, props.theme],\n    [get(provider.clearTheme, \"rangeSlider\"), props.clearTheme],\n    [get(provider.applyTheme, \"rangeSlider\"), props.applyTheme],\n  );\n\n  const { className, sizing = \"md\", ...restProps } = resolveProps(props, provider.props?.rangeSlider);\n\n  return (\n    <div data-testid=\"flowbite-range-slider\" className={twMerge(theme.root.base, className)}>\n      <div className={theme.field.base}>\n        <input\n          ref={ref}\n          type=\"range\"\n          className={twMerge(theme.field.input.base, theme.field.input.sizes[sizing])}\n          {...restProps}\n        />\n      </div>\n    </div>\n  );\n});\n\nRangeSlider.displayName = \"RangeSlider\";\n"], "names": ["forwardRef", "provider", "useThemeProvider", "theme", "useResolveTheme", "rangeSliderTheme", "get", "resolveProps", "jsx", "twMerge"], "mappings": ";;;;;;;;;;;AAUY,MAAC,WAAW,GAAGA,gBAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACtD,EAAE,MAAMC,UAAQ,GAAGC,yBAAgB,EAAE;AACrC,EAAE,MAAMC,OAAK,GAAGC,4BAAe;AAC/B,IAAI,CAACC,sBAAgB,EAAEJ,UAAQ,CAAC,KAAK,EAAE,WAAW,EAAE,KAAK,CAAC,KAAK,CAAC;AAChE,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,aAAa,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC/D,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,aAAa,CAAC,EAAE,KAAK,CAAC,UAAU;AAC9D,GAAG;AACH,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,GAAG,IAAI,EAAE,GAAG,SAAS,EAAE,GAAGM,yBAAY,CAAC,KAAK,EAAEN,UAAQ,CAAC,KAAK,EAAE,WAAW,CAAC;AACrG,EAAE,uBAAuBO,cAAG,CAAC,KAAK,EAAE,EAAE,aAAa,EAAE,uBAAuB,EAAE,SAAS,EAAEC,qBAAO,CAACN,OAAK,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,QAAQ,kBAAkBK,cAAG,CAAC,KAAK,EAAE,EAAE,SAAS,EAAEL,OAAK,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,kBAAkBK,cAAG;AAC/N,IAAI,OAAO;AACX,IAAI;AACJ,MAAM,GAAG;AACT,MAAM,IAAI,EAAE,OAAO;AACnB,MAAM,SAAS,EAAEC,qBAAO,CAACN,OAAK,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAEA,OAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AACjF,MAAM,GAAG;AACT;AACA,GAAG,EAAE,CAAC,EAAE,CAAC;AACT,CAAC;AACD,WAAW,CAAC,WAAW,GAAG,aAAa;;;;"}