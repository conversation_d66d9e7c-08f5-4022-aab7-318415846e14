{"version": 3, "file": "Toast.js", "sources": ["../../../src/components/Toast/Toast.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ComponentProps } from \"react\";\nimport { forwardRef, useState } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { ThemingProps } from \"../../types\";\nimport { toastTheme } from \"./theme\";\nimport { ToastContext } from \"./ToastContext\";\n\nexport interface ToastTheme {\n  root: {\n    base: string;\n    closed: string;\n  };\n  toggle: {\n    base: string;\n    icon: string;\n  };\n}\n\nexport type ToastDuration = 75 | 100 | 150 | 200 | 300 | 500 | 700 | 1000;\n\nexport interface ToastProps extends ComponentProps<\"div\">, ThemingProps<ToastTheme> {\n  duration?: ToastDuration;\n}\n\nconst durationClasses: Record<ToastDuration, string> = {\n  75: \"duration-75\",\n  100: \"duration-100\",\n  150: \"duration-150\",\n  200: \"duration-200\",\n  300: \"duration-300\",\n  500: \"duration-500\",\n  700: \"duration-700\",\n  1000: \"duration-1000\",\n};\n\nexport const Toast = forwardRef<HTMLDivElement, ToastProps>((props, ref) => {\n  const [isClosed, setIsClosed] = useState(false);\n  const [isRemoved, setIsRemoved] = useState(false);\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [toastTheme, provider.theme?.toast, props.theme],\n    [get(provider.clearTheme, \"toast\"), props.clearTheme],\n    [get(provider.applyTheme, \"toast\"), props.applyTheme],\n  );\n\n  const { className, duration = 300, ...restProps } = resolveProps(props, provider.props?.toast);\n\n  if (isRemoved) {\n    return null;\n  }\n\n  return (\n    <ToastContext.Provider\n      value={{\n        theme: props.theme,\n        clearTheme: props.clearTheme,\n        applyTheme: props.applyTheme,\n        duration,\n        isClosed,\n        isRemoved,\n        setIsClosed,\n        setIsRemoved,\n      }}\n    >\n      <div\n        ref={ref}\n        data-testid=\"flowbite-toast\"\n        role=\"alert\"\n        className={twMerge(theme.root.base, durationClasses[duration], isClosed && theme.root.closed, className)}\n        {...restProps}\n      />\n    </ToastContext.Provider>\n  );\n});\n\nToast.displayName = \"Toast\";\n"], "names": [], "mappings": ";;;;;;;;;;AAWA,MAAM,eAAe,GAAG;AACxB,EAAE,EAAE,EAAE,aAAa;AACnB,EAAE,GAAG,EAAE,cAAc;AACrB,EAAE,GAAG,EAAE,cAAc;AACrB,EAAE,GAAG,EAAE,cAAc;AACrB,EAAE,GAAG,EAAE,cAAc;AACrB,EAAE,GAAG,EAAE,cAAc;AACrB,EAAE,GAAG,EAAE,cAAc;AACrB,EAAE,GAAG,EAAE;AACP,CAAC;AACW,MAAC,KAAK,GAAG,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AAChD,EAAE,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC;AACjD,EAAE,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC;AACnD,EAAE,MAAM,QAAQ,GAAG,gBAAgB,EAAE;AACrC,EAAE,MAAM,KAAK,GAAG,eAAe;AAC/B,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC;AACpD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AACzD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,KAAK,CAAC,UAAU;AACxD,GAAG;AACH,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,GAAG,GAAG,EAAE,GAAG,SAAS,EAAE,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC;AAChG,EAAE,IAAI,SAAS,EAAE;AACjB,IAAI,OAAO,IAAI;AACf;AACA,EAAE,uBAAuB,GAAG;AAC5B,IAAI,YAAY,CAAC,QAAQ;AACzB,IAAI;AACJ,MAAM,KAAK,EAAE;AACb,QAAQ,KAAK,EAAE,KAAK,CAAC,KAAK;AAC1B,QAAQ,UAAU,EAAE,KAAK,CAAC,UAAU;AACpC,QAAQ,UAAU,EAAE,KAAK,CAAC,UAAU;AACpC,QAAQ,QAAQ;AAChB,QAAQ,QAAQ;AAChB,QAAQ,SAAS;AACjB,QAAQ,WAAW;AACnB,QAAQ;AACR,OAAO;AACP,MAAM,QAAQ,kBAAkB,GAAG;AACnC,QAAQ,KAAK;AACb,QAAQ;AACR,UAAU,GAAG;AACb,UAAU,aAAa,EAAE,gBAAgB;AACzC,UAAU,IAAI,EAAE,OAAO;AACvB,UAAU,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,eAAe,CAAC,QAAQ,CAAC,EAAE,QAAQ,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC;AAClH,UAAU,GAAG;AACb;AACA;AACA;AACA,GAAG;AACH,CAAC;AACD,KAAK,CAAC,WAAW,GAAG,OAAO;;;;"}