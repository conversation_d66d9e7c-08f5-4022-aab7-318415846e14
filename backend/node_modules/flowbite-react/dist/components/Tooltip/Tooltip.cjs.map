{"version": 3, "file": "Tooltip.cjs", "sources": ["../../../src/components/Tooltip/Tooltip.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { Placement } from \"@floating-ui/core\";\nimport type { ComponentProps, ReactNode } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { ThemingProps } from \"../../types\";\nimport { Floating, type FloatingTheme } from \"../Floating\";\nimport { tooltipTheme } from \"./theme\";\n\nexport type TooltipTheme = FloatingTheme;\n\nexport interface TooltipProps extends Omit<ComponentProps<\"div\">, \"content\" | \"style\">, ThemingProps<TooltipTheme> {\n  animation?: false | `duration-${number}`;\n  arrow?: boolean;\n  content: ReactNode;\n  placement?: \"auto\" | Placement;\n  style?: \"dark\" | \"light\" | \"auto\";\n  trigger?: \"hover\" | \"click\";\n}\n\n/**\n * @see https://floating-ui.com/docs/react-dom-interactions\n */\nexport function Tooltip(props: TooltipProps) {\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [tooltipTheme, provider.theme?.tooltip, props.theme],\n    [get(provider.clearTheme, \"tooltip\"), props.clearTheme],\n    [get(provider.applyTheme, \"tooltip\"), props.applyTheme],\n  );\n\n  const {\n    animation = \"duration-300\",\n    arrow = true,\n    children,\n    className,\n    content,\n    placement = \"top\",\n    style = \"dark\",\n    trigger = \"hover\",\n    ...restProps\n  } = resolveProps(props, provider.props?.tooltip);\n\n  return (\n    <Floating\n      animation={animation}\n      arrow={arrow}\n      content={content}\n      placement={placement}\n      style={style}\n      theme={theme}\n      trigger={trigger}\n      className={className}\n      {...restProps}\n    >\n      {children}\n    </Floating>\n  );\n}\n\nTooltip.displayName = \"Tooltip\";\n"], "names": ["provider", "useThemeProvider", "theme", "useResolveTheme", "tooltipTheme", "get", "resolveProps", "jsx", "Floating"], "mappings": ";;;;;;;;;;AASO,SAAS,OAAO,CAAC,KAAK,EAAE;AAC/B,EAAE,MAAMA,UAAQ,GAAGC,yBAAgB,EAAE;AACrC,EAAE,MAAMC,OAAK,GAAGC,4BAAe;AAC/B,IAAI,CAACC,kBAAY,EAAEJ,UAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC;AACxD,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,SAAS,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC3D,IAAI,CAACK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,SAAS,CAAC,EAAE,KAAK,CAAC,UAAU;AAC1D,GAAG;AACH,EAAE,MAAM;AACR,IAAI,SAAS,GAAG,cAAc;AAC9B,IAAI,KAAK,GAAG,IAAI;AAChB,IAAI,QAAQ;AACZ,IAAI,SAAS;AACb,IAAI,OAAO;AACX,IAAI,SAAS,GAAG,KAAK;AACrB,IAAI,KAAK,GAAG,MAAM;AAClB,IAAI,OAAO,GAAG,OAAO;AACrB,IAAI,GAAG;AACP,GAAG,GAAGM,yBAAY,CAAC,KAAK,EAAEN,UAAQ,CAAC,KAAK,EAAE,OAAO,CAAC;AAClD,EAAE,uBAAuBO,cAAG;AAC5B,IAAIC,iBAAQ;AACZ,IAAI;AACJ,MAAM,SAAS;AACf,MAAM,KAAK;AACX,MAAM,OAAO;AACb,MAAM,SAAS;AACf,MAAM,KAAK;AACX,aAAMN,OAAK;AACX,MAAM,OAAO;AACb,MAAM,SAAS;AACf,MAAM,GAAG,SAAS;AAClB,MAAM;AACN;AACA,GAAG;AACH;AACA,OAAO,CAAC,WAAW,GAAG,SAAS;;;;"}