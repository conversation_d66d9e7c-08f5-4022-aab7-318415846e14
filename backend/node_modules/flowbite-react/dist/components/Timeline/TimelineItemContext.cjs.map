{"version": 3, "file": "TimelineItemContext.cjs", "sources": ["../../../src/components/Timeline/TimelineItemContext.tsx"], "sourcesContent": ["\"use client\";\n\nimport { createContext, useContext } from \"react\";\nimport type { ThemingProps } from \"../../types\";\nimport type { TimelineItemTheme } from \"./TimelineItem\";\n\nexport type TimelineItemContextValue = ThemingProps<TimelineItemTheme>;\n\nexport const TimelineItemContext = createContext<TimelineItemContextValue | undefined>(undefined);\n\nexport function useTimelineItemContext(): TimelineItemContextValue {\n  const context = useContext(TimelineItemContext);\n\n  if (!context) {\n    throw new Error(\"useTimelineItemContext should be used within the TimelineItemContext provider!\");\n  }\n\n  return context;\n}\n"], "names": ["createContext", "useContext"], "mappings": ";;;;AAGY,MAAC,mBAAmB,GAAGA,mBAAa,CAAC,MAAM;AAChD,SAAS,sBAAsB,GAAG;AACzC,EAAE,MAAM,OAAO,GAAGC,gBAAU,CAAC,mBAAmB,CAAC;AACjD,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,MAAM,IAAI,KAAK,CAAC,gFAAgF,CAAC;AACrG;AACA,EAAE,OAAO,OAAO;AAChB;;;;;"}