{"version": 3, "file": "TimelineContent.js", "sources": ["../../../src/components/Timeline/TimelineContent.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentProps } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { ThemingProps } from \"../../types\";\nimport { timelineTheme } from \"./theme\";\nimport type { TimelineBodyTheme } from \"./TimelineBody\";\nimport { TimelineContentContext } from \"./TimelineContentContext\";\nimport { useTimelineContext } from \"./TimelineContext\";\nimport { useTimelineItemContext } from \"./TimelineItemContext\";\nimport type { TimelineTimeTheme } from \"./TimelineTime\";\nimport type { TimelineTitleTheme } from \"./TimelineTitle\";\n\nexport interface TimelineContentTheme {\n  root: {\n    base: string;\n    horizontal: string;\n    vertical: string;\n  };\n  time: TimelineTitleTheme;\n  title: TimelineTimeTheme;\n  body: TimelineBodyTheme;\n}\n\nexport interface TimelineContentProps extends ComponentProps<\"div\">, ThemingProps<TimelineContentTheme> {}\n\nexport const TimelineContent = forwardRef<HTMLDivElement, TimelineContentProps>((props, ref) => {\n  const { theme: rootTheme, clearTheme: rootClearTheme, applyTheme: rootApplyTheme, horizontal } = useTimelineContext();\n  const { theme: itemTheme, clearTheme: itemClearTheme, applyTheme: itemApplyTheme } = useTimelineItemContext();\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [\n      timelineTheme.item.content,\n      provider.theme?.timeline?.item?.content,\n      rootTheme?.item?.content,\n      itemTheme?.content,\n      props.theme,\n    ],\n    [\n      get(provider.clearTheme, \"timeline.item.content\"),\n      get(rootClearTheme, \"item.content\"),\n      get(itemClearTheme, \"content\"),\n      props.clearTheme,\n    ],\n    [\n      get(provider.applyTheme, \"timeline.item.content\"),\n      get(rootApplyTheme, \"item.content\"),\n      get(itemApplyTheme, \"content\"),\n      props.applyTheme,\n    ],\n  );\n\n  const { className, ...restProps } = resolveProps(props, provider.props?.timelineContent);\n\n  return (\n    <TimelineContentContext.Provider\n      value={{ theme: props.theme, clearTheme: props.clearTheme, applyTheme: props.applyTheme }}\n    >\n      <div\n        ref={ref}\n        data-testid=\"timeline-content\"\n        className={twMerge(theme.root.base, horizontal ? theme.root.horizontal : theme.root.vertical, className)}\n        {...restProps}\n      />\n    </TimelineContentContext.Provider>\n  );\n});\n\nTimelineContent.displayName = \"TimelineContent\";\n"], "names": [], "mappings": ";;;;;;;;;;;;AAaY,MAAC,eAAe,GAAG,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AAC1D,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,GAAG,kBAAkB,EAAE;AACvH,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,GAAG,sBAAsB,EAAE;AAC/G,EAAE,MAAM,QAAQ,GAAG,gBAAgB,EAAE;AACrC,EAAE,MAAM,KAAK,GAAG,eAAe;AAC/B,IAAI;AACJ,MAAM,aAAa,CAAC,IAAI,CAAC,OAAO;AAChC,MAAM,QAAQ,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO;AAC7C,MAAM,SAAS,EAAE,IAAI,EAAE,OAAO;AAC9B,MAAM,SAAS,EAAE,OAAO;AACxB,MAAM,KAAK,CAAC;AACZ,KAAK;AACL,IAAI;AACJ,MAAM,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,uBAAuB,CAAC;AACvD,MAAM,GAAG,CAAC,cAAc,EAAE,cAAc,CAAC;AACzC,MAAM,GAAG,CAAC,cAAc,EAAE,SAAS,CAAC;AACpC,MAAM,KAAK,CAAC;AACZ,KAAK;AACL,IAAI;AACJ,MAAM,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,uBAAuB,CAAC;AACvD,MAAM,GAAG,CAAC,cAAc,EAAE,cAAc,CAAC;AACzC,MAAM,GAAG,CAAC,cAAc,EAAE,SAAS,CAAC;AACpC,MAAM,KAAK,CAAC;AACZ;AACA,GAAG;AACH,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,SAAS,EAAE,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,eAAe,CAAC;AAC1F,EAAE,uBAAuB,GAAG;AAC5B,IAAI,sBAAsB,CAAC,QAAQ;AACnC,IAAI;AACJ,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE;AAC/F,MAAM,QAAQ,kBAAkB,GAAG;AACnC,QAAQ,KAAK;AACb,QAAQ;AACR,UAAU,GAAG;AACb,UAAU,aAAa,EAAE,kBAAkB;AAC3C,UAAU,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC;AAClH,UAAU,GAAG;AACb;AACA;AACA;AACA,GAAG;AACH,CAAC;AACD,eAAe,CAAC,WAAW,GAAG,iBAAiB;;;;"}