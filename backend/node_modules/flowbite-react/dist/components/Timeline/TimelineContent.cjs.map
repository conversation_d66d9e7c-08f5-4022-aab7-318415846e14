{"version": 3, "file": "TimelineContent.cjs", "sources": ["../../../src/components/Timeline/TimelineContent.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentProps } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { ThemingProps } from \"../../types\";\nimport { timelineTheme } from \"./theme\";\nimport type { TimelineBodyTheme } from \"./TimelineBody\";\nimport { TimelineContentContext } from \"./TimelineContentContext\";\nimport { useTimelineContext } from \"./TimelineContext\";\nimport { useTimelineItemContext } from \"./TimelineItemContext\";\nimport type { TimelineTimeTheme } from \"./TimelineTime\";\nimport type { TimelineTitleTheme } from \"./TimelineTitle\";\n\nexport interface TimelineContentTheme {\n  root: {\n    base: string;\n    horizontal: string;\n    vertical: string;\n  };\n  time: TimelineTitleTheme;\n  title: TimelineTimeTheme;\n  body: TimelineBodyTheme;\n}\n\nexport interface TimelineContentProps extends ComponentProps<\"div\">, ThemingProps<TimelineContentTheme> {}\n\nexport const TimelineContent = forwardRef<HTMLDivElement, TimelineContentProps>((props, ref) => {\n  const { theme: rootTheme, clearTheme: rootClearTheme, applyTheme: rootApplyTheme, horizontal } = useTimelineContext();\n  const { theme: itemTheme, clearTheme: itemClearTheme, applyTheme: itemApplyTheme } = useTimelineItemContext();\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [\n      timelineTheme.item.content,\n      provider.theme?.timeline?.item?.content,\n      rootTheme?.item?.content,\n      itemTheme?.content,\n      props.theme,\n    ],\n    [\n      get(provider.clearTheme, \"timeline.item.content\"),\n      get(rootClearTheme, \"item.content\"),\n      get(itemClearTheme, \"content\"),\n      props.clearTheme,\n    ],\n    [\n      get(provider.applyTheme, \"timeline.item.content\"),\n      get(rootApplyTheme, \"item.content\"),\n      get(itemApplyTheme, \"content\"),\n      props.applyTheme,\n    ],\n  );\n\n  const { className, ...restProps } = resolveProps(props, provider.props?.timelineContent);\n\n  return (\n    <TimelineContentContext.Provider\n      value={{ theme: props.theme, clearTheme: props.clearTheme, applyTheme: props.applyTheme }}\n    >\n      <div\n        ref={ref}\n        data-testid=\"timeline-content\"\n        className={twMerge(theme.root.base, horizontal ? theme.root.horizontal : theme.root.vertical, className)}\n        {...restProps}\n      />\n    </TimelineContentContext.Provider>\n  );\n});\n\nTimelineContent.displayName = \"TimelineContent\";\n"], "names": ["forwardRef", "useTimelineContext", "useTimelineItemContext", "provider", "useThemeProvider", "theme", "useResolveTheme", "timelineTheme", "get", "resolveProps", "jsx", "TimelineContentContext", "twMerge"], "mappings": ";;;;;;;;;;;;;;AAaY,MAAC,eAAe,GAAGA,gBAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AAC1D,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,GAAGC,kCAAkB,EAAE;AACvH,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,GAAGC,0CAAsB,EAAE;AAC/G,EAAE,MAAMC,UAAQ,GAAGC,yBAAgB,EAAE;AACrC,EAAE,MAAMC,OAAK,GAAGC,4BAAe;AAC/B,IAAI;AACJ,MAAMC,mBAAa,CAAC,IAAI,CAAC,OAAO;AAChC,MAAMJ,UAAQ,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO;AAC7C,MAAM,SAAS,EAAE,IAAI,EAAE,OAAO;AAC9B,MAAM,SAAS,EAAE,OAAO;AACxB,MAAM,KAAK,CAAC;AACZ,KAAK;AACL,IAAI;AACJ,MAAMK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,uBAAuB,CAAC;AACvD,MAAMK,OAAG,CAAC,cAAc,EAAE,cAAc,CAAC;AACzC,MAAMA,OAAG,CAAC,cAAc,EAAE,SAAS,CAAC;AACpC,MAAM,KAAK,CAAC;AACZ,KAAK;AACL,IAAI;AACJ,MAAMA,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,uBAAuB,CAAC;AACvD,MAAMK,OAAG,CAAC,cAAc,EAAE,cAAc,CAAC;AACzC,MAAMA,OAAG,CAAC,cAAc,EAAE,SAAS,CAAC;AACpC,MAAM,KAAK,CAAC;AACZ;AACA,GAAG;AACH,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,SAAS,EAAE,GAAGC,yBAAY,CAAC,KAAK,EAAEN,UAAQ,CAAC,KAAK,EAAE,eAAe,CAAC;AAC1F,EAAE,uBAAuBO,cAAG;AAC5B,IAAIC,6CAAsB,CAAC,QAAQ;AACnC,IAAI;AACJ,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE;AAC/F,MAAM,QAAQ,kBAAkBD,cAAG;AACnC,QAAQ,KAAK;AACb,QAAQ;AACR,UAAU,GAAG;AACb,UAAU,aAAa,EAAE,kBAAkB;AAC3C,UAAU,SAAS,EAAEE,qBAAO,CAACP,OAAK,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,GAAGA,OAAK,CAAC,IAAI,CAAC,UAAU,GAAGA,OAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC;AAClH,UAAU,GAAG;AACb;AACA;AACA;AACA,GAAG;AACH,CAAC;AACD,eAAe,CAAC,WAAW,GAAG,iBAAiB;;;;"}