{"version": 3, "file": "TimelineTime.cjs", "sources": ["../../../src/components/Timeline/TimelineTime.tsx"], "sourcesContent": ["\"use client\";\n\nimport { forwardRef, type ComponentProps } from \"react\";\nimport { get } from \"../../helpers/get\";\nimport { resolveProps } from \"../../helpers/resolve-props\";\nimport { useResolveTheme } from \"../../helpers/resolve-theme\";\nimport { twMerge } from \"../../helpers/tailwind-merge\";\nimport { useThemeProvider } from \"../../theme/provider\";\nimport type { ThemingProps } from \"../../types\";\nimport { timelineTheme } from \"./theme\";\nimport { useTimelineContentContext } from \"./TimelineContentContext\";\nimport { useTimelineContext } from \"./TimelineContext\";\nimport { useTimelineItemContext } from \"./TimelineItemContext\";\n\nexport interface TimelineTimeTheme {\n  base: string;\n}\n\nexport interface TimelineTimeProps extends ComponentProps<\"time\">, ThemingProps<TimelineTimeTheme> {}\n\nexport const TimelineTime = forwardRef<HTMLTimeElement, TimelineTimeProps>((props, ref) => {\n  const { theme: rootTheme, clearTheme: rootClearTheme, applyTheme: rootApplyTheme } = useTimelineContext();\n  const { theme: itemTheme, clearTheme: itemClearTheme, applyTheme: itemApplyTheme } = useTimelineItemContext();\n  const {\n    theme: contentTheme,\n    clearTheme: contentClearTheme,\n    applyTheme: contentApplyTheme,\n  } = useTimelineContentContext();\n\n  const provider = useThemeProvider();\n  const theme = useResolveTheme(\n    [\n      timelineTheme.item.content.time,\n      provider.theme?.timeline?.item?.content?.time,\n      rootTheme?.item?.content?.time,\n      itemTheme?.content?.time,\n      contentTheme?.time,\n      props.theme,\n    ],\n    [\n      get(provider.clearTheme, \"timeline.item.content.time\"),\n      get(rootClearTheme, \"item.content.time\"),\n      get(itemClearTheme, \"content.time\"),\n      get(contentClearTheme, \"time\"),\n      props.clearTheme,\n    ],\n    [\n      get(provider.applyTheme, \"timeline.item.content.time\"),\n      get(rootApplyTheme, \"item.content.time\"),\n      get(itemApplyTheme, \"content.time\"),\n      get(contentApplyTheme, \"time\"),\n      props.applyTheme,\n    ],\n  );\n\n  const { className, ...restProps } = resolveProps(props, provider.props?.timelineTime);\n\n  return <time ref={ref} className={twMerge(theme.base, className)} {...restProps} />;\n});\n\nTimelineTime.displayName = \"TimelineTime\";\n"], "names": ["forwardRef", "useTimelineContext", "useTimelineItemContext", "useTimelineContentContext", "provider", "useThemeProvider", "theme", "useResolveTheme", "timelineTheme", "get", "resolveProps", "jsx", "twMerge"], "mappings": ";;;;;;;;;;;;;;AAaY,MAAC,YAAY,GAAGA,gBAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACvD,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,GAAGC,kCAAkB,EAAE;AAC3G,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,GAAGC,0CAAsB,EAAE;AAC/G,EAAE,MAAM;AACR,IAAI,KAAK,EAAE,YAAY;AACvB,IAAI,UAAU,EAAE,iBAAiB;AACjC,IAAI,UAAU,EAAE;AAChB,GAAG,GAAGC,gDAAyB,EAAE;AACjC,EAAE,MAAMC,UAAQ,GAAGC,yBAAgB,EAAE;AACrC,EAAE,MAAMC,OAAK,GAAGC,4BAAe;AAC/B,IAAI;AACJ,MAAMC,mBAAa,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI;AACrC,MAAMJ,UAAQ,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI;AACnD,MAAM,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI;AACpC,MAAM,SAAS,EAAE,OAAO,EAAE,IAAI;AAC9B,MAAM,YAAY,EAAE,IAAI;AACxB,MAAM,KAAK,CAAC;AACZ,KAAK;AACL,IAAI;AACJ,MAAMK,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,4BAA4B,CAAC;AAC5D,MAAMK,OAAG,CAAC,cAAc,EAAE,mBAAmB,CAAC;AAC9C,MAAMA,OAAG,CAAC,cAAc,EAAE,cAAc,CAAC;AACzC,MAAMA,OAAG,CAAC,iBAAiB,EAAE,MAAM,CAAC;AACpC,MAAM,KAAK,CAAC;AACZ,KAAK;AACL,IAAI;AACJ,MAAMA,OAAG,CAACL,UAAQ,CAAC,UAAU,EAAE,4BAA4B,CAAC;AAC5D,MAAMK,OAAG,CAAC,cAAc,EAAE,mBAAmB,CAAC;AAC9C,MAAMA,OAAG,CAAC,cAAc,EAAE,cAAc,CAAC;AACzC,MAAMA,OAAG,CAAC,iBAAiB,EAAE,MAAM,CAAC;AACpC,MAAM,KAAK,CAAC;AACZ;AACA,GAAG;AACH,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,SAAS,EAAE,GAAGC,yBAAY,CAAC,KAAK,EAAEN,UAAQ,CAAC,KAAK,EAAE,YAAY,CAAC;AACvF,EAAE,uBAAuBO,cAAG,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,SAAS,EAAEC,qBAAO,CAACN,OAAK,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,GAAG,SAAS,EAAE,CAAC;AACtG,CAAC;AACD,YAAY,CAAC,WAAW,GAAG,cAAc;;;;"}