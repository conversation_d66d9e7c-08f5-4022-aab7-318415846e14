{"name": "flowbite-react", "version": "0.11.8", "description": "Official React components built for Flowbite and Tailwind CSS", "keywords": ["design-system", "flowbite", "flowbite-react", "react", "tailwind", "tailwindcss", "tailwind-css"], "homepage": "https://flowbite-react.com", "bugs": "https://github.com/themesberg/flowbite-react/issues", "repository": {"type": "git", "url": "git+https://github.com/themesberg/flowbite-react.git", "directory": "packages/ui"}, "license": "MIT", "sideEffects": false, "type": "module", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "./cli/*": {"import": {"types": "./dist/cli/*.d.ts", "default": "./dist/cli/*.js"}, "require": {"types": "./dist/cli/*.d.cts", "default": "./dist/cli/*.cjs"}}, "./cli/commands/*": {"import": {"types": "./dist/cli/commands/*.d.ts", "default": "./dist/cli/commands/*.js"}, "require": {"types": "./dist/cli/commands/*.d.cts", "default": "./dist/cli/commands/*.cjs"}}, "./cli/commands/plugins/*": {"import": {"types": "./dist/cli/commands/plugins/*.d.ts", "default": "./dist/cli/commands/plugins/*.js"}, "require": {"types": "./dist/cli/commands/plugins/*.d.cts", "default": "./dist/cli/commands/plugins/*.cjs"}}, "./cli/utils/* ": {"import": {"types": "./dist/cli/utils/*.d.ts", "default": "./dist/cli/utils/*.js"}, "require": {"types": "./dist/cli/utils/*.d.cts", "default": "./dist/cli/utils/*.cjs"}}, "./components/*": {"import": {"types": "./dist/components/*/index.d.ts", "default": "./dist/components/*/index.js"}, "require": {"types": "./dist/components/*/index.d.cts", "default": "./dist/components/*/index.cjs"}}, "./helpers/*": {"import": {"types": "./dist/helpers/*.d.ts", "default": "./dist/helpers/*.js"}, "require": {"types": "./dist/helpers/*.d.cts", "default": "./dist/helpers/*.cjs"}}, "./hooks/*": {"import": {"types": "./dist/hooks/*.d.ts", "default": "./dist/hooks/*.js"}, "require": {"types": "./dist/hooks/*.d.cts", "default": "./dist/hooks/*.cjs"}}, "./icons": {"import": {"types": "./dist/icons/index.d.ts", "default": "./dist/icons/index.js"}, "require": {"types": "./dist/icons/index.d.cts", "default": "./dist/icons/index.cjs"}}, "./icons/*": {"import": {"types": "./dist/icons/*.d.ts", "default": "./dist/icons/*.js"}, "require": {"types": "./dist/icons/*.d.cts", "default": "./dist/icons/*.cjs"}}, "./plugin": {"import": {"types": "./dist/plugin/index.d.ts", "default": "./dist/plugin/index.js"}, "require": {"types": "./dist/plugin/index.d.cts", "default": "./dist/plugin/index.cjs"}}, "./plugin/*": {"import": {"types": "./dist/plugin/*.d.ts", "default": "./dist/plugin/*.js"}, "require": {"types": "./dist/plugin/*.d.cts", "default": "./dist/plugin/*.cjs"}}, "./plugin/tailwindcss": {"import": {"types": "./dist/plugin/tailwindcss/index.d.ts", "default": "./dist/plugin/tailwindcss/index.js"}, "require": {"types": "./dist/plugin/tailwindcss/index.d.cts", "default": "./dist/plugin/tailwindcss/index.cjs"}}, "./plugin/tailwindcss/*": {"import": {"types": "./dist/plugin/tailwindcss/*.d.ts", "default": "./dist/plugin/tailwindcss/*.js"}, "require": {"types": "./dist/plugin/tailwindcss/*.d.cts", "default": "./dist/plugin/tailwindcss/*.cjs"}}, "./theme": {"import": {"types": "./dist/theme/index.d.ts", "default": "./dist/theme/index.js"}, "require": {"types": "./dist/theme/index.d.cts", "default": "./dist/theme/index.cjs"}}, "./theme/*": {"import": {"types": "./dist/theme/*.d.ts", "default": "./dist/theme/*.js"}, "require": {"types": "./dist/theme/*.d.cts", "default": "./dist/theme/*.cjs"}}, "./types": {"import": {"types": "./dist/types/index.d.ts", "default": "./dist/types/index.js"}, "require": {"types": "./dist/types/index.d.cts", "default": "./dist/types/index.cjs"}}, "./package.json": "./package.json", "./schema.json": "./schema.json"}, "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "bin": {"flowbite-react": "./dist/cli/bin.js"}, "files": ["dist", "schema.json"], "scripts": {"postpublish": "clean-package restore"}, "dependencies": {"@floating-ui/core": "1.6.9", "@floating-ui/react": "0.27.3", "@iarna/toml": "2.2.5", "@typescript-eslint/typescript-estree": "8.26.0", "chokidar": "4.0.3", "classnames": "2.5.1", "comment-json": "4.2.5", "debounce": "2.2.0", "deepmerge-ts": "7.1.4", "klona": "2.0.6", "package-manager-detector": "0.2.9", "recast": "0.23.11", "tailwind-merge-v2": "npm:tailwind-merge@2.6.0", "tailwind-merge-v3": "npm:tailwind-merge@3.0.1"}, "peerDependencies": {"react": "^18 || ^19", "react-dom": "^18 || ^19", "tailwindcss": "^3 || ^4"}}