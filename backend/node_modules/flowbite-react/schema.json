{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"components": {"description": "List of component names to generate styles for. Empty array enables automatic detection. \nSee https://flowbite-react.com/docs/customize/config#components for more details.", "type": "array", "items": {"type": "string", "enum": ["*", "Accordion", "Accordi<PERSON><PERSON><PERSON><PERSON>", "Accordion<PERSON><PERSON>le", "<PERSON><PERSON>", "Avatar", "AvatarGroup", "AvatarGroupCounter", "Badge", "Banner", "BannerCollapseButton", "Blockquote", "Breadcrumb", "BreadcrumbItem", "<PERSON><PERSON>", "ButtonGroup", "Card", "Carousel", "Checkbox", "Clipboard", "ClipboardWithIcon", "ClipboardWithIconText", "DarkThemeToggle", "Datepicker", "Drawer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DrawerItems", "Dropdown", "DropdownDivider", "DropdownHeader", "DropdownItem", "FileInput", "FloatingLabel", "Footer", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>er<PERSON><PERSON><PERSON>", "FooterDivider", "FooterIcon", "FooterLink", "FooterLinkGroup", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "HR", "HRIcon", "HRSquare", "HRText", "<PERSON><PERSON><PERSON>med", "HelperText", "Kbd", "Label", "List", "ListItem", "ListGroup", "ListGroupItem", "MegaMenu", "MegaMenuDropdown", "MegaMenuDropdownToggle", "Modal", "ModalBody", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NavbarCollapse", "NavbarLink", "Navbar<PERSON><PERSON><PERSON>", "Pagination", "PaginationButton", "Popover", "Progress", "Radio", "RangeSlider", "Rating", "RatingAdvanced", "RatingStar", "Select", "Sidebar", "SidebarCTA", "SidebarCollapse", "SidebarItem", "SidebarItemGroup", "SidebarItems", "SidebarLogo", "Spinner", "Table", "TableBody", "TableCell", "TableHead", "TableHeadCell", "TableRow", "TabItem", "Tabs", "TextInput", "Textarea", "Timeline", "TimelineBody", "TimelineContent", "TimelineItem", "TimelinePoint", "TimelineTime", "TimelineTitle", "Toast", "ToastToggle", "ToggleSwitch", "<PERSON><PERSON><PERSON>"]}, "uniqueItems": true}, "dark": {"description": "Whether to generate dark mode styles. \nSee https://flowbite-react.com/docs/customize/config#dark for more details.", "type": "boolean", "default": true}, "path": {"description": "Path where components will be created, relative to the project root. \nSee https://flowbite-react.com/docs/customize/config#path for more details.", "type": "string", "default": "src/components"}, "prefix": {"description": "Optional prefix to apply to all Tailwind CSS classes. \nSee https://flowbite-react.com/docs/customize/config#prefix for more details.", "type": "string"}, "rsc": {"description": "Whether to include the 'use client' directive for React Server Components. \nSee https://flowbite-react.com/docs/customize/config#rsc for more details.", "type": "boolean", "default": true}, "tsx": {"description": "Whether to use TypeScript (.tsx) or JavaScript (.jsx) for component creation. \nSee https://flowbite-react.com/docs/customize/config#tsx for more details.", "type": "boolean", "default": true}}, "required": ["components", "dark", "path", "prefix", "rsc", "tsx"]}