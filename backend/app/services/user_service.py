from typing import Optional, List
from sqlalchemy.orm import Session, joinedload
from fastapi import HTT<PERSON>Exception, status

from ..models import User, Role
from ..schemas.user import UserCreate, UserUpdate
from ..auth.password import get_password_hash
from ..utils.pagination import paginate, PaginatedResponse


class UserService:
    def __init__(self, db: Session):
        self.db = db

    def get_user_by_id(self, user_id: int) -> Optional[User]:
        """
        Récupère un utilisateur par son ID.
        """
        return self.db.query(User).options(
            joinedload(User.role).joinedload(Role.permissions)
        ).filter(User.id == user_id).first()

    def get_user_by_email(self, email: str) -> Optional[User]:
        """
        Récupère un utilisateur par son email.
        """
        return self.db.query(User).options(
            joinedload(User.role).joinedload(Role.permissions)
        ).filter(User.email == email).first()

    def get_users(self, page: int = 1, size: int = 20, search: Optional[str] = None) -> PaginatedResponse:
        """
        Récupère la liste des utilisateurs avec pagination.
        """
        query = self.db.query(User).options(
            joinedload(User.role).joinedload(Role.permissions)
        )
        
        if search:
            search_filter = f"%{search}%"
            query = query.filter(
                (User.email.ilike(search_filter)) |
                (User.first_name.ilike(search_filter)) |
                (User.last_name.ilike(search_filter))
            )
        
        query = query.order_by(User.created_at.desc())
        return paginate(query, page, size)

    def create_user(self, user_data: UserCreate) -> User:
        """
        Crée un nouvel utilisateur.
        """
        # Vérifier si l'email existe déjà
        existing_user = self.get_user_by_email(user_data.email)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )
        
        # Vérifier que le rôle existe si spécifié
        if user_data.role_id:
            role = self.db.query(Role).filter(Role.id == user_data.role_id).first()
            if not role:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Role not found"
                )
        
        # Créer l'utilisateur
        hashed_password = get_password_hash(user_data.password)
        db_user = User(
            email=user_data.email,
            hashed_password=hashed_password,
            first_name=user_data.first_name,
            last_name=user_data.last_name,
            is_active=user_data.is_active,
            role_id=user_data.role_id
        )
        
        self.db.add(db_user)
        self.db.commit()
        self.db.refresh(db_user)
        
        return self.get_user_by_id(db_user.id)

    def update_user(self, user_id: int, user_data: UserUpdate) -> User:
        """
        Met à jour un utilisateur.
        """
        user = self.get_user_by_id(user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Vérifier l'unicité de l'email si modifié
        if user_data.email and user_data.email != user.email:
            existing_user = self.get_user_by_email(user_data.email)
            if existing_user:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Email already registered"
                )
        
        # Vérifier que le rôle existe si spécifié
        if user_data.role_id is not None:
            if user_data.role_id != 0:  # 0 pour supprimer le rôle
                role = self.db.query(Role).filter(Role.id == user_data.role_id).first()
                if not role:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Role not found"
                    )
        
        # Mettre à jour les champs
        update_data = user_data.dict(exclude_unset=True)
        
        if 'password' in update_data:
            update_data['hashed_password'] = get_password_hash(update_data.pop('password'))
        
        if 'role_id' in update_data and update_data['role_id'] == 0:
            update_data['role_id'] = None
        
        for field, value in update_data.items():
            setattr(user, field, value)
        
        self.db.commit()
        self.db.refresh(user)
        
        return self.get_user_by_id(user.id)

    def delete_user(self, user_id: int) -> bool:
        """
        Supprime un utilisateur.
        """
        user = self.get_user_by_id(user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        self.db.delete(user)
        self.db.commit()
        return True
