from typing import Optional, List
from sqlalchemy.orm import Session, joinedload
from fastapi import HTTPException, status

from ..models import Role, Permission
from ..schemas.role import RoleCreate, RoleUpdate
from ..utils.pagination import paginate, PaginatedResponse


class RoleService:
    def __init__(self, db: Session):
        self.db = db

    def get_role_by_id(self, role_id: int) -> Optional[Role]:
        """
        Récupère un rôle par son ID.
        """
        return self.db.query(Role).options(
            joinedload(Role.permissions)
        ).filter(Role.id == role_id).first()

    def get_role_by_name(self, name: str) -> Optional[Role]:
        """
        Récupère un rôle par son nom.
        """
        return self.db.query(Role).options(
            joinedload(Role.permissions)
        ).filter(Role.name == name).first()

    def get_roles(self, page: int = 1, size: int = 20, search: Optional[str] = None) -> PaginatedResponse:
        """
        Récup<PERSON> la liste des rôles avec pagination.
        """
        query = self.db.query(Role).options(
            joinedload(Role.permissions)
        )
        
        if search:
            search_filter = f"%{search}%"
            query = query.filter(
                (Role.name.ilike(search_filter)) |
                (Role.description.ilike(search_filter))
            )
        
        query = query.order_by(Role.name)
        return paginate(query, page, size)

    def get_all_roles(self) -> List[Role]:
        """
        Récupère tous les rôles (pour les listes déroulantes).
        """
        return self.db.query(Role).options(
            joinedload(Role.permissions)
        ).order_by(Role.name).all()

    def create_role(self, role_data: RoleCreate) -> Role:
        """
        Crée un nouveau rôle.
        """
        # Vérifier si le nom existe déjà
        existing_role = self.get_role_by_name(role_data.name)
        if existing_role:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Role name already exists"
            )
        
        # Créer le rôle
        db_role = Role(
            name=role_data.name,
            description=role_data.description
        )
        
        # Ajouter les permissions si spécifiées
        if role_data.permission_ids:
            permissions = self.db.query(Permission).filter(
                Permission.id.in_(role_data.permission_ids)
            ).all()
            
            if len(permissions) != len(role_data.permission_ids):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="One or more permissions not found"
                )
            
            db_role.permissions = permissions
        
        self.db.add(db_role)
        self.db.commit()
        self.db.refresh(db_role)
        
        return self.get_role_by_id(db_role.id)

    def update_role(self, role_id: int, role_data: RoleUpdate) -> Role:
        """
        Met à jour un rôle.
        """
        role = self.get_role_by_id(role_id)
        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Role not found"
            )
        
        # Vérifier l'unicité du nom si modifié
        if role_data.name and role_data.name != role.name:
            existing_role = self.get_role_by_name(role_data.name)
            if existing_role:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Role name already exists"
                )
        
        # Mettre à jour les champs de base
        update_data = role_data.dict(exclude_unset=True, exclude={'permission_ids'})
        for field, value in update_data.items():
            setattr(role, field, value)
        
        # Mettre à jour les permissions si spécifiées
        if role_data.permission_ids is not None:
            if role_data.permission_ids:
                permissions = self.db.query(Permission).filter(
                    Permission.id.in_(role_data.permission_ids)
                ).all()
                
                if len(permissions) != len(role_data.permission_ids):
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="One or more permissions not found"
                    )
                
                role.permissions = permissions
            else:
                role.permissions = []
        
        self.db.commit()
        self.db.refresh(role)
        
        return self.get_role_by_id(role.id)

    def delete_role(self, role_id: int) -> bool:
        """
        Supprime un rôle.
        """
        role = self.get_role_by_id(role_id)
        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Role not found"
            )
        
        # Vérifier qu'aucun utilisateur n'utilise ce rôle
        if role.users:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete role: users are assigned to this role"
            )
        
        self.db.delete(role)
        self.db.commit()
        return True
