from typing import Optional, List
from sqlalchemy.orm import Session
from fastapi import HTTPEx<PERSON>, status

from ..models import Permission
from ..schemas.permission import PermissionCreate, PermissionUpdate
from ..utils.pagination import paginate, PaginatedResponse


class PermissionService:
    def __init__(self, db: Session):
        self.db = db

    def get_permission_by_id(self, permission_id: int) -> Optional[Permission]:
        """
        Récupère une permission par son ID.
        """
        return self.db.query(Permission).filter(Permission.id == permission_id).first()

    def get_permission_by_code(self, code: str) -> Optional[Permission]:
        """
        Récupère une permission par son code.
        """
        return self.db.query(Permission).filter(Permission.code == code).first()

    def get_permissions(self, page: int = 1, size: int = 20, search: Optional[str] = None) -> PaginatedResponse:
        """
        Récupère la liste des permissions avec pagination.
        """
        query = self.db.query(Permission)
        
        if search:
            search_filter = f"%{search}%"
            query = query.filter(
                (Permission.code.ilike(search_filter)) |
                (Permission.name.ilike(search_filter)) |
                (Permission.description.ilike(search_filter))
            )
        
        query = query.order_by(Permission.code)
        return paginate(query, page, size)

    def get_all_permissions(self) -> List[Permission]:
        """
        Récupère toutes les permissions (pour les listes déroulantes).
        """
        return self.db.query(Permission).order_by(Permission.code).all()

    def create_permission(self, permission_data: PermissionCreate) -> Permission:
        """
        Crée une nouvelle permission.
        """
        # Vérifier si le code existe déjà
        existing_permission = self.get_permission_by_code(permission_data.code)
        if existing_permission:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Permission code already exists"
            )
        
        # Créer la permission
        db_permission = Permission(
            code=permission_data.code,
            name=permission_data.name,
            description=permission_data.description
        )
        
        self.db.add(db_permission)
        self.db.commit()
        self.db.refresh(db_permission)
        
        return db_permission

    def update_permission(self, permission_id: int, permission_data: PermissionUpdate) -> Permission:
        """
        Met à jour une permission.
        """
        permission = self.get_permission_by_id(permission_id)
        if not permission:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Permission not found"
            )
        
        # Vérifier l'unicité du code si modifié
        if permission_data.code and permission_data.code != permission.code:
            existing_permission = self.get_permission_by_code(permission_data.code)
            if existing_permission:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Permission code already exists"
                )
        
        # Mettre à jour les champs
        update_data = permission_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(permission, field, value)
        
        self.db.commit()
        self.db.refresh(permission)
        
        return permission

    def delete_permission(self, permission_id: int) -> bool:
        """
        Supprime une permission.
        """
        permission = self.get_permission_by_id(permission_id)
        if not permission:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Permission not found"
            )
        
        # Vérifier qu'aucun rôle n'utilise cette permission
        if permission.roles:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete permission: roles are using this permission"
            )
        
        self.db.delete(permission)
        self.db.commit()
        return True

    def create_default_permissions(self) -> List[Permission]:
        """
        Crée les permissions par défaut du système.
        """
        default_permissions = [
            {"code": "user:create", "name": "Créer des utilisateurs", "description": "Permet de créer de nouveaux utilisateurs"},
            {"code": "user:read", "name": "Lire les utilisateurs", "description": "Permet de consulter la liste des utilisateurs"},
            {"code": "user:update", "name": "Modifier des utilisateurs", "description": "Permet de modifier les informations des utilisateurs"},
            {"code": "user:delete", "name": "Supprimer des utilisateurs", "description": "Permet de supprimer des utilisateurs"},
            {"code": "role:create", "name": "Créer des rôles", "description": "Permet de créer de nouveaux rôles"},
            {"code": "role:read", "name": "Lire les rôles", "description": "Permet de consulter la liste des rôles"},
            {"code": "role:update", "name": "Modifier des rôles", "description": "Permet de modifier les rôles"},
            {"code": "role:delete", "name": "Supprimer des rôles", "description": "Permet de supprimer des rôles"},
            {"code": "permission:create", "name": "Créer des permissions", "description": "Permet de créer de nouvelles permissions"},
            {"code": "permission:read", "name": "Lire les permissions", "description": "Permet de consulter la liste des permissions"},
            {"code": "permission:update", "name": "Modifier des permissions", "description": "Permet de modifier les permissions"},
            {"code": "permission:delete", "name": "Supprimer des permissions", "description": "Permet de supprimer des permissions"},
        ]
        
        created_permissions = []
        for perm_data in default_permissions:
            existing = self.get_permission_by_code(perm_data["code"])
            if not existing:
                permission = Permission(**perm_data)
                self.db.add(permission)
                created_permissions.append(permission)
        
        if created_permissions:
            self.db.commit()
            for perm in created_permissions:
                self.db.refresh(perm)
        
        return created_permissions
