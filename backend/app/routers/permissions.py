from typing import Optional, List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from ..database import get_db
from ..schemas.permission import PermissionCreate, PermissionUpdate, PermissionResponse
from ..services.permission_service import PermissionService
from ..auth.dependencies import require_permissions
from ..models import User
from ..utils.pagination import PaginatedResponse

router = APIRouter(prefix="/permissions", tags=["Permissions"])


@router.get("", response_model=PaginatedResponse[PermissionResponse])
def get_permissions(
    page: int = Query(1, ge=1, description="Numéro de page"),
    size: int = Query(20, ge=1, le=100, description="Taille de la page"),
    search: Optional[str] = Query(None, description="Recherche par code, nom ou description"),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["permission:read"]))
):
    """
    Récupère la liste des permissions avec pagination et recherche.
    """
    permission_service = PermissionService(db)
    result = permission_service.get_permissions(page=page, size=size, search=search)
    
    return PaginatedResponse(
        items=[PermissionResponse.from_orm(permission) for permission in result.items],
        total=result.total,
        page=result.page,
        size=result.size,
        pages=result.pages,
        has_next=result.has_next,
        has_prev=result.has_prev
    )


@router.get("/all", response_model=List[PermissionResponse])
def get_all_permissions(
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["permission:read"]))
):
    """
    Récupère toutes les permissions (pour les listes déroulantes).
    """
    permission_service = PermissionService(db)
    permissions = permission_service.get_all_permissions()
    return [PermissionResponse.from_orm(permission) for permission in permissions]


@router.get("/{permission_id}", response_model=PermissionResponse)
def get_permission(
    permission_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["permission:read"]))
):
    """
    Récupère une permission par son ID.
    """
    permission_service = PermissionService(db)
    permission = permission_service.get_permission_by_id(permission_id)
    
    if not permission:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Permission not found"
        )
    
    return PermissionResponse.from_orm(permission)


@router.post("", response_model=PermissionResponse, status_code=status.HTTP_201_CREATED)
def create_permission(
    permission_data: PermissionCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["permission:create"]))
):
    """
    Crée une nouvelle permission.
    """
    permission_service = PermissionService(db)
    permission = permission_service.create_permission(permission_data)
    return PermissionResponse.from_orm(permission)


@router.put("/{permission_id}", response_model=PermissionResponse)
def update_permission(
    permission_id: int,
    permission_data: PermissionUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["permission:update"]))
):
    """
    Met à jour une permission.
    """
    permission_service = PermissionService(db)
    permission = permission_service.update_permission(permission_id, permission_data)
    return PermissionResponse.from_orm(permission)


@router.delete("/{permission_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_permission(
    permission_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["permission:delete"]))
):
    """
    Supprime une permission.
    """
    permission_service = PermissionService(db)
    permission_service.delete_permission(permission_id)
    return None
