from typing import Optional, List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from ..database import get_db
from ..schemas.role import RoleCreate, RoleUpdate, RoleResponse
from ..services.role_service import RoleService
from ..auth.dependencies import require_permissions
from ..models import User
from ..utils.pagination import PaginatedResponse

router = APIRouter(prefix="/roles", tags=["Roles"])


@router.get("", response_model=PaginatedResponse[RoleResponse])
def get_roles(
    page: int = Query(1, ge=1, description="Numéro de page"),
    size: int = Query(20, ge=1, le=100, description="Taille de la page"),
    search: Optional[str] = Query(None, description="Recherche par nom ou description"),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["role:read"]))
):
    """
    Récupère la liste des rôles avec pagination et recherche.
    """
    role_service = RoleService(db)
    result = role_service.get_roles(page=page, size=size, search=search)
    
    return PaginatedResponse(
        items=[RoleResponse.from_orm(role) for role in result.items],
        total=result.total,
        page=result.page,
        size=result.size,
        pages=result.pages,
        has_next=result.has_next,
        has_prev=result.has_prev
    )


@router.get("/all", response_model=List[RoleResponse])
def get_all_roles(
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["role:read"]))
):
    """
    Récupère tous les rôles (pour les listes déroulantes).
    """
    role_service = RoleService(db)
    roles = role_service.get_all_roles()
    return [RoleResponse.from_orm(role) for role in roles]


@router.get("/{role_id}", response_model=RoleResponse)
def get_role(
    role_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["role:read"]))
):
    """
    Récupère un rôle par son ID.
    """
    role_service = RoleService(db)
    role = role_service.get_role_by_id(role_id)
    
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found"
        )
    
    return RoleResponse.from_orm(role)


@router.post("", response_model=RoleResponse, status_code=status.HTTP_201_CREATED)
def create_role(
    role_data: RoleCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["role:create"]))
):
    """
    Crée un nouveau rôle.
    """
    role_service = RoleService(db)
    role = role_service.create_role(role_data)
    return RoleResponse.from_orm(role)


@router.put("/{role_id}", response_model=RoleResponse)
def update_role(
    role_id: int,
    role_data: RoleUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["role:update"]))
):
    """
    Met à jour un rôle.
    """
    role_service = RoleService(db)
    role = role_service.update_role(role_id, role_data)
    return RoleResponse.from_orm(role)


@router.delete("/{role_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_role(
    role_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["role:delete"]))
):
    """
    Supprime un rôle.
    """
    role_service = RoleService(db)
    role_service.delete_role(role_id)
    return None
