from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from ..database import get_db
from ..schemas.user import UserCreate, UserUpdate, UserResponse
from ..services.user_service import UserService
from ..auth.dependencies import require_permissions
from ..models import User
from ..utils.pagination import PaginationParams, PaginatedResponse

router = APIRouter(prefix="/users", tags=["Users"])


@router.get("", response_model=PaginatedResponse[UserResponse])
def get_users(
    page: int = Query(1, ge=1, description="Numéro de page"),
    size: int = Query(20, ge=1, le=100, description="Taille de la page"),
    search: Optional[str] = Query(None, description="Recherche par email, prénom ou nom"),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["user:read"]))
):
    """
    Récupère la liste des utilisateurs avec pagination et recherche.
    """
    user_service = UserService(db)
    result = user_service.get_users(page=page, size=size, search=search)
    
    # Convertir les utilisateurs en réponse
    users_response = []
    for user in result.items:
        permissions = []
        if user.role:
            permissions = [perm.code for perm in user.role.permissions]
        
        users_response.append(UserResponse(
            id=user.id,
            email=user.email,
            first_name=user.first_name,
            last_name=user.last_name,
            is_active=user.is_active,
            is_superuser=user.is_superuser,
            role_id=user.role_id,
            role=user.role,
            created_at=user.created_at,
            updated_at=user.updated_at,
            last_login=user.last_login,
            full_name=user.full_name,
            permissions=permissions
        ))
    
    return PaginatedResponse(
        items=users_response,
        total=result.total,
        page=result.page,
        size=result.size,
        pages=result.pages,
        has_next=result.has_next,
        has_prev=result.has_prev
    )


@router.get("/{user_id}", response_model=UserResponse)
def get_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["user:read"]))
):
    """
    Récupère un utilisateur par son ID.
    """
    user_service = UserService(db)
    user = user_service.get_user_by_id(user_id)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    permissions = []
    if user.role:
        permissions = [perm.code for perm in user.role.permissions]
    
    return UserResponse(
        id=user.id,
        email=user.email,
        first_name=user.first_name,
        last_name=user.last_name,
        is_active=user.is_active,
        is_superuser=user.is_superuser,
        role_id=user.role_id,
        role=user.role,
        created_at=user.created_at,
        updated_at=user.updated_at,
        last_login=user.last_login,
        full_name=user.full_name,
        permissions=permissions
    )


@router.post("", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
def create_user(
    user_data: UserCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["user:create"]))
):
    """
    Crée un nouvel utilisateur.
    """
    user_service = UserService(db)
    user = user_service.create_user(user_data)
    
    permissions = []
    if user.role:
        permissions = [perm.code for perm in user.role.permissions]
    
    return UserResponse(
        id=user.id,
        email=user.email,
        first_name=user.first_name,
        last_name=user.last_name,
        is_active=user.is_active,
        is_superuser=user.is_superuser,
        role_id=user.role_id,
        role=user.role,
        created_at=user.created_at,
        updated_at=user.updated_at,
        last_login=user.last_login,
        full_name=user.full_name,
        permissions=permissions
    )


@router.put("/{user_id}", response_model=UserResponse)
def update_user(
    user_id: int,
    user_data: UserUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["user:update"]))
):
    """
    Met à jour un utilisateur.
    """
    user_service = UserService(db)
    user = user_service.update_user(user_id, user_data)
    
    permissions = []
    if user.role:
        permissions = [perm.code for perm in user.role.permissions]
    
    return UserResponse(
        id=user.id,
        email=user.email,
        first_name=user.first_name,
        last_name=user.last_name,
        is_active=user.is_active,
        is_superuser=user.is_superuser,
        role_id=user.role_id,
        role=user.role,
        created_at=user.created_at,
        updated_at=user.updated_at,
        last_login=user.last_login,
        full_name=user.full_name,
        permissions=permissions
    )


@router.delete("/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions(["user:delete"]))
):
    """
    Supprime un utilisateur.
    """
    # Empêcher la suppression de son propre compte
    if user_id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete your own account"
        )
    
    user_service = UserService(db)
    user_service.delete_user(user_id)
    return None
